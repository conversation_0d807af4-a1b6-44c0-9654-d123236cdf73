using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.UICore;
using ESS.Pollbook.Hardware.Storage;
using System.Threading.Tasks;
using static ESS.Pollbook.Components.Repository.PQC.PQCRepository;

namespace ESS.Pollbook.Facade.PQC
{
    public interface IPQCFacade
    {
        Task<PqcStatus> ValidateAndInitConnection(string pqc);

        Task SyncSecondaryTransactionLogAsync(string password);

        Task<bool> CloseConnectionsAsync();
        Task CloseDatabaseConnectionAsync(SqliteDatabaseType sqliteType);

        StorageLocator GetStorageLocator();

        bool CheckForAvailableElectionFiles();

        ConnectionResponse CreateEssSqliteWriterConnection(SqliteDatabaseType sqliteDatabaseType, string password);
    }
}
