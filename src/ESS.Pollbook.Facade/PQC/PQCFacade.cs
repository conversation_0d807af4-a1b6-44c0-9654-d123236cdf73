using ESS.Pollbook.Components.Repository.CommonEnumerationValue;
using ESS.Pollbook.Components.Repository.PollbookTransaction;
using ESS.Pollbook.Components.Repository.PQC;
using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Core.UICore;
using ESS.Pollbook.Facade.Checklist;
using ESS.Pollbook.Facade.Configuration;
using ESS.Pollbook.Facade.Device;
using ESS.Pollbook.Facade.HostSync;
using ESS.Pollbook.Facade.PollbookDefinedText;
using ESS.Pollbook.Facade.Pollworker;
using ESS.Pollbook.Hardware.Storage;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using ESS.Pollbook.Facade.ElectionJurisdictionEnum;
using static ESS.Pollbook.Components.Repository.PQC.PQCRepository;

namespace ESS.Pollbook.Facade.PQC
{
   public class PQCFacade : IPQCFacade
   {
      private readonly ICommonEnumerationValueRepository _commonEnumerationValueRepository;
      private readonly IPollbookConfigurationFacade _configurationFacade;
      private readonly IDeviceFacade _deviceFacade;
      private readonly IEssLogger _essLogger;
      private readonly IHostSyncFacade _hostSyncFacade;
      private readonly IPollbookDefinedTextFacade _pollbookDefinedTextFacade;
      private readonly IPollworkerConfigurationFacade _pollworkerConfigurationFacade;
      private readonly IPollbookTransactionRepository _pollbookTransactionRepository;
      private readonly IPQCRepository _pqcRepository;
      private readonly IPrinterFacade _printerFacade;
      private readonly IChecklistFacade _checklistFacade;
      private readonly IVoterJurisdictionEnumFacade _voterJurisdictionEnumFacade;

      public PQCFacade(
	      IPollbookConfigurationFacade pollbookConfigurationFacade,
	      ICommonEnumerationValueRepository commonEnumerationValueRepository,
	      IPollbookDefinedTextFacade pollbookDefinedTextFacade,
	      IHostSyncFacade hostSyncFacade,
	      IPrinterFacade printerFacade,
	      IDeviceFacade deviceFacade,
	      IPQCRepository pqcRepository,
	      IPollbookTransactionRepository pollbookTransactionRepository,
	      IPollworkerConfigurationFacade pollworkerConfigurationFacade,
	      IChecklistFacade checklistFacade,
	      IEssLogger essLogger,
         IVoterJurisdictionEnumFacade voterJurisdictionEnumFacade)
      {
         _pollbookDefinedTextFacade = pollbookDefinedTextFacade;
         _configurationFacade = pollbookConfigurationFacade;
         _pollworkerConfigurationFacade = pollworkerConfigurationFacade;
         _hostSyncFacade = hostSyncFacade;
         _printerFacade = printerFacade;
         _deviceFacade = deviceFacade;
         _checklistFacade = checklistFacade;

         _commonEnumerationValueRepository = commonEnumerationValueRepository;
         _pqcRepository = pqcRepository;
         _pollbookTransactionRepository = pollbookTransactionRepository;
         _voterJurisdictionEnumFacade = voterJurisdictionEnumFacade;
         _essLogger = essLogger;
      }

      public async Task<PqcStatus> ValidateAndInitConnection(string pqc)
      {
         try
         {
            var connectionStatus = await _pqcRepository.InitConnection(pqc);

            var locator = _pqcRepository.GetStorageLocator();

            if (connectionStatus == PqcStatus.Correct)
            {
               var sw = new Stopwatch();
               SystemDetails.IsPQCVerified = true;

               // Load Configuration
               sw.Start();
               await _configurationFacade.RefreshAsync(true);
               _essLogger.LogInformation(
	               $"ValidateAndInitConnection - refresh config [Elapsed time:{sw.ElapsedMilliseconds}ms]");

               // This is where the configuration is first loaded, so prior to this we are not able to determine the SystemConfiguration.
               sw.Restart();
               SystemConfiguration.ElectionConfiguration = _configurationFacade.GetPollbookConfiguration();
               SystemConfiguration.PollworkerConfiguration = await _pollworkerConfigurationFacade.GetPollworkerConfigurationAsync();
               _essLogger.LogInformation(
	               $"ValidateAndInitConnection - loading configurations [Elapsed time:{sw.ElapsedMilliseconds}ms]");

               // ------------------------------------------------------------------------
               // All initialization code needs to go AFTER configuration loads
               // ------------------------------------------------------------------------

               Task.Run(() => _pollbookTransactionRepository.BuildVoterBallotList());

               if (SystemConfiguration.ElectionConfiguration.EnablePollWorkerManagement &&
                   string.IsNullOrEmpty(locator.GetPollworkerDataPath()))
               {
                  _essLogger.LogDebug("Missing Polldata_PollworkerManagement database.");
                  SystemDetails.IsPQCVerified = false;
                  return PqcStatus.Incorrect;
               }

               if (SystemConfiguration.ElectionConfiguration.VerifyElectronicSignatures &&
                   string.IsNullOrEmpty(locator.GetSignatureDataPath()))
               {
                  _essLogger.LogDebug("Missing Polldata_Signature database.");
                  SystemDetails.IsPQCVerified = false;
                  return PqcStatus.Incorrect;
               }

               sw.Restart();
               if (SystemConfiguration.ElectionConfiguration.EnableMultilingual)
               {
                  await _pollbookDefinedTextFacade.GetPollbookDefinedTextLanguagesAsync();
                  _pollbookDefinedTextFacade.SetPollbookDefinedTextCurrentLanguage("English");
               }

               _essLogger.LogInformation(
	               $"ValidateAndInitConnection - language initialization [Elapsed time:{sw.ElapsedMilliseconds}ms]");

               SystemDetails.IsTestMode = _deviceFacade.VerifyTestMode();

               sw.Restart();
               _hostSyncFacade.TurnOnApiMonitor();
               await _hostSyncFacade.SetHostConnectedStatus();
               _essLogger.LogInformation(
	               $"ValidateAndInitConnection - SetHostConnectedStatus [Elapsed time:{sw.ElapsedMilliseconds}ms]");

               sw.Restart();
               await _configurationFacade.CheckAndApplyConfigurationsUpdatesAsync();
               _essLogger.LogInformation(
	               $"ValidateAndInitConnection - configuration updates [Elapsed time:{sw.ElapsedMilliseconds}ms]");

               sw.Restart();
               await _checklistFacade.LoadChecklistTables();
               _essLogger.LogInformation(
	               $"ValidateAndInitConnection - loading checklists [Elapsed time:{sw.ElapsedMilliseconds}ms]");

               sw.Restart();
               _printerFacade.ChangePrinterSettings();
               _essLogger.LogInformation(
	               $"ValidateAndInitConnection - change printer settings [Elapsed time:{sw.ElapsedMilliseconds}ms]");

               sw.Restart();
               await _voterJurisdictionEnumFacade.GetEarlyBallotIssuedIdAsync(
                  (int)EssEnumeration.AbsenteeStatus, 
                  (int)AbsenteeStatus.EarlyVoteIssued);
               _essLogger.LogDebug($"ValidateAndInitConnection - early ballot issued Enum Cache [Elapsed time:{sw.ElapsedMilliseconds}ms]");
               
               DefinedText.PollbookDefinedTextDtos =
                   (await _pollbookDefinedTextFacade.LoadPollbookDefinedTextFromDb()).ToList();

               var commonEnum = await _commonEnumerationValueRepository.GetCommonEnumerationValueAsync(
                   (int)AddressType.Residential,
                   (int)EssEnumeration.AddressType);

               SystemConfiguration.ResidentialAddressTypeCode = commonEnum.EnumerationValueId;

               sw.Stop();
            }

            return connectionStatus;
         }
         catch (Exception ex)
         {
            var logProps = new Dictionary<string, string> { { "Action", "PQCFacade.ValidateAndInitConnection" } };
            _essLogger.LogError(ex, logProps);
         }

         return PqcStatus.Incorrect;
      }

      public async Task SyncSecondaryTransactionLogAsync(string password)
      {
         await _pollbookTransactionRepository.EnsureSecondaryTransactionLogMatches(password);
      }

      public async Task<bool> CloseConnectionsAsync()
      {
         return await _pqcRepository.CloseConnectionsAsync();
      }

      public async Task CloseDatabaseConnectionAsync(SqliteDatabaseType sqliteType)
      {
         await _pqcRepository.CloseDatabaseConnectionAsync(sqliteType);
      }

      public StorageLocator GetStorageLocator()
      {
         return _pqcRepository.GetStorageLocator();
      }

      public bool CheckForAvailableElectionFiles()
      {
         var usbDrives = DriveSearcher.GetUsbDrives();
         if (usbDrives.Count == 0)
         {
            var prop = new Dictionary<string, string> { { "Action", nameof(CheckForAvailableElectionFiles) } };
            _essLogger.LogError("Error loading election. No usb drives found.", prop);
            return false;
         }

         var dataFilesPath = StorageLocator.GetUsbExpressPollLoadFiles();
         if (dataFilesPath.Count != 1)
         {
            var prop = new Dictionary<string, string> { { "Action", nameof(CheckForAvailableElectionFiles) } };
            _essLogger.LogError($"Error loading election. Too many files.[{dataFilesPath.Count}]", prop);
            return false;
         }

         return true;
      }

      public ConnectionResponse CreateEssSqliteWriterConnection(SqliteDatabaseType sqliteDatabaseType, string password)
      {
         return Task.Run(() => _pqcRepository.CreateEssSqliteWriterConnection(sqliteDatabaseType, password)).Result;
      }
   }
}