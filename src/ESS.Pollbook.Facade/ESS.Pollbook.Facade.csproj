<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
	  <TargetFramework>net48</TargetFramework>
	  <Authors>Election Systems and Software</Authors>
	  <Company>Election Systems and Software</Company>
	  <Copyright>Election Systems and Software</Copyright>
	  <AssemblyVersion>7.2.9.0</AssemblyVersion>
	  <FileVersion>7.2.9.0</FileVersion>
	  <CodeAnalysisRuleSet>..\.sonarlint\express-poll-branchescsharp.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Maintenance\PDCFacade.cs" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="ClosedXML" Version="0.104.2" />
    <PackageReference Include="ManagedNativeWifi" Version="2.7.1" />
    <PackageReference Include="NClone" Version="1.2.0" />
    <PackageReference Include="PdfPrintingNet" Version="5.2.8" />
    <PackageReference Include="System.Text.Json" Version="8.0.5" />
    <PackageReference Include="SSH.NET" Version="2024.2.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ESS.ELLEGO.Rest\ESS.ELLEGO.Rest.csproj" />
    <ProjectReference Include="..\ESS.ELLEGO.ServiceBus.Core\ESS.ELLEGO.ServiceBus.Core.csproj" />
    <ProjectReference Include="..\ESS.Pollbook.Components.Business\ESS.Pollbook.Components.Business.csproj" />
    <ProjectReference Include="..\ESS.Pollbook.Core\ESS.Pollbook.Core.csproj" />
    <ProjectReference Include="..\ESS.Pollbook.MasterProcessor.Abstractions\ESS.Pollbook.MasterProcessor.Abstractions.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="System.IO.Compression" />
    <Reference Include="System.Management" />
    <Reference Include="System.Windows.Forms">
      <HintPath>..\..\..\..\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Windows.Forms.dll</HintPath>
    </Reference>
  </ItemGroup>

    <ItemGroup>
        <AssemblyAttribute Include="System.Runtime.CompilerServices.InternalsVisibleToAttribute">
            <_Parameter1>ESS.Pollbook.Facade.Test.dll</_Parameter1>
        </AssemblyAttribute>
    </ItemGroup>

    <ItemGroup>
      <EmbeddedResource Update="Properties\Resources.resx">
        <Generator>ResXFileCodeGenerator</Generator>
        <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      </EmbeddedResource>
    </ItemGroup>

</Project>