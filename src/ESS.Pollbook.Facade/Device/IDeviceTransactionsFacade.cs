using ESS.Pollbook.Core.DTO;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ESS.Pollbook.Facade.Device
{
    public interface IDeviceTransactionsFacade
    {
        Task<float> GetDownloadTransactionsStatusAsync();
        Task<float> GetUploadTransactionsStatusAsync();
        Task<string> GetLastDownloadSyncTimeAsync();
        Task<IEnumerable<TransactionProgressDTO>> GetTransactionProgress();
        Task<long> GetDownloadExceptionCount();
        Task<float> GetAppliedSyncStatus();
    }
}
