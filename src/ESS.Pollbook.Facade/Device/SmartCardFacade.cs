using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Windows.Devices.SmartCards;
using ESS.Pollbook.Components.Repository.Device;
using ESS.Pollbook.Core.Logging;

namespace ESS.Pollbook.Facade.Device
{
   public class SmartCardFacade : ISmartCardFacade
   {
      private readonly IEssLogger _essLogger;
      private readonly ISmartCardRepository _smartCardRepository;
      private readonly CardReaderWatcher _watcher;

      private readonly Core.Common.Debounce.DebounceDispatcher _debounceDispatcher = new Core.Common.Debounce.DebounceDispatcher(1000);

      public event EventHandler DisconnectedEvent;
      public event EventHandler ConnectedEvent;

      private bool? _isSmartCardHelperServiceInstalled = null;

     public SmartCardFacade(ISmartCardRepository smartCardRepository, IEssLogger essLogger)
      {
         _essLogger = essLogger;
         _smartCardRepository = smartCardRepository;

         _watcher = new CardReaderWatcher();
         _watcher.DisconnectedEvent += DisconnectedEventHandler;
         _watcher.ConnectedEvent += ConnectedEventHandler;
      }

      public bool IsSmartCardServiceInstalled()
      {
         _essLogger.LogDebug("** Testing if smart card service installed.");

         try
         {
            if(!_isSmartCardHelperServiceInstalled.HasValue)
               _isSmartCardHelperServiceInstalled = _smartCardRepository.IsServiceInstalled();

            return _isSmartCardHelperServiceInstalled.Value;
         }
         catch (Exception ex)
         {
            var prop = new Dictionary<string, string> { { "Action", nameof(IsSmartCardServiceInstalled) } };
            _essLogger.LogError(ex, prop);
            return false;
         }
      }

      public void StartSmartCardService()
      {
         _essLogger.LogDebug("** Starting smart card service.");
         try
         {
            _smartCardRepository.StartSmartCardService();
         }
         catch (Exception ex)
         {
            var prop = new Dictionary<string, string> { { "Action", nameof(StartSmartCardService) } };
            _essLogger.LogError(ex, prop);
         }
      }

      public void StopSmartCardService()
      {
         _essLogger.LogDebug("** Stopping smart card service.");
         try
         {
            _smartCardRepository.StopSmartCardService();
         }
         catch (Exception ex)
         {
            var prop = new Dictionary<string, string> { { "Action", nameof(StopSmartCardService) } };
            _essLogger.LogError(ex, prop);
         }
      }

      public void RestartSmartCardService()
      {
         _essLogger.LogDebug("** Restarting smart card service.");
         try
         {
            _smartCardRepository.RestartSmartCardService();
         }
         catch (Exception ex)
         {
            var prop = new Dictionary<string, string> { { "Action", nameof(RestartSmartCardService) } };
            _essLogger.LogError(ex, prop);
         }
      }

      public int CurrentSmartCardServiceStatus()
      {
         _essLogger.LogDebug("** Testing smart card status.");
         try
         {
            return _smartCardRepository.CurrentStatus();
         }
         catch (Exception ex)
         {
            var prop = new Dictionary<string, string> { { "Action", nameof(CurrentSmartCardServiceStatus) } };
            _essLogger.LogError(ex, prop);
            return -1;
         }
      }

      public bool IsSmartCardHelperServiceRunning()
      {
         if (CurrentSmartCardServiceStatus() == 4) return true;

         for (var i = 0; i < 4; i++)
         {
            RestartSmartCardService();
            if (CurrentSmartCardServiceStatus() == 4) return true;
         }

         return false;
      }

      public async Task<SmartCardReader> GetSmartCardReaderAsync()
      {
         return await _smartCardRepository.GetSmartCardReaderAsync();
      }

      public async Task<bool> IsSmartCardReaderAttachedAsync()
      {
         _essLogger.LogDebug("** Testing card reader is attached.");
         return await _smartCardRepository.IsSmartCardReaderAttachedAsync();
      }

      public async Task<bool> IsCardPresentAsync()
      {
         _essLogger.LogDebug("** Testing card present in reader.");
         return await _smartCardRepository.IsCardPresentAsync();
      }

      public async Task<bool> IsVoterCardAsync()
      {
         try
         {
            _essLogger.LogDebug("** Test smart card type - is it voter card.");
            return await _smartCardRepository.IsVoterCardAsync();
         }
         catch (Exception ex)
         {
            var prop = new Dictionary<string, string> { { "Action", nameof(IsVoterCardAsync) } };
            _essLogger.LogError(ex, prop);
            return false;
         }
      }

      public async Task<bool> WriteToCardAsync(string ballotStyleId, int provisionalMode = 0, string provisionalCode = null)
      {
         _essLogger.LogDebug($"** Writing to card [{ballotStyleId}, {provisionalMode}]");
         try
         {
            if (provisionalMode == 0) provisionalCode = string.Empty;

            var ret = await _smartCardRepository.WriteToCardAsync(ballotStyleId, provisionalMode, provisionalCode);
            if (ret != 0)
            {
               _essLogger.LogError($"Failed to write to activation card. [{ret}]");
            }

            return ret == 0;
         }
         catch (Exception ex)
         {
            var prop = new Dictionary<string, string> { { "Action", nameof(WriteToCardAsync) } };
            _essLogger.LogError(ex, prop);

            return false;
         }
      }

      private async void DisconnectedEventHandler(object sender, EventArgs e)
      {
         await _debounceDispatcher.DebounceAsync(async () =>
         {
            var isConnected = await IsSmartCardReaderAttachedAsync();

            if (!isConnected)
               DisconnectedEvent?.Invoke(this, e);
         });
      }

      private async void ConnectedEventHandler(object sender, EventArgs e)
      {
         await _debounceDispatcher.DebounceAsync(async () =>
         {
            var isConnected = await IsSmartCardReaderAttachedAsync();

            if (isConnected)
               ConnectedEvent?.Invoke(this, e);
         });
      }

      public void StopTheWatcher()
      {
         _watcher.DisconnectedEvent -= DisconnectedEventHandler;
         _watcher.ConnectedEvent -= ConnectedEventHandler;
         _watcher.Stop();
      }
   }
}
