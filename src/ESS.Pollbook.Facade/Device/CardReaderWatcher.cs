using System;
using System.Management;
using ESS.Pollbook.Core.Common;

namespace ESS.Pollbook.Facade.Device
{
   public class CardReaderWatcher
   {
      private readonly ManagementEventWatcher _watcher;

      public event EventHandler DisconnectedEvent;
      public event EventHandler ConnectedEvent;

      public CardReaderWatcher()
      {
         _watcher = new ManagementEventWatcher();
         var query = new WqlEventQuery("SELECT * FROM Win32_DeviceChangeEvent WHERE EventType = 2 OR EventType = 3 ");

         _watcher.EventArrived += (s, e) =>
         {
            var eventType = (EventType)Convert.ToInt16(e.NewEvent.Properties["EventType"].Value);
            switch (eventType)
            {
               case EventType.Inserted:
                  ConnectedEvent?.Invoke(this, e);
                  break;
               case EventType.Removed:
                  DisconnectedEvent?.Invoke(this, e);
                  break;
               default:
                  break;
            }
         };

         _watcher.Query= query;
         _watcher.Start();
      }

      public void Stop()
      {
         _watcher.Stop();
      }
   }
}
