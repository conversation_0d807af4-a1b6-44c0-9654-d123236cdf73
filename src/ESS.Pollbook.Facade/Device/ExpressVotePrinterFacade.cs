using ESS.Pollbook.Components.Repository.Configuration;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Interface;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.PollPlace;
using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Ports;
using System.Linq;
using System.Text;

namespace ESS.Pollbook.Facade.Device
{
   public class ExpressVotePrinterFacade : IExpressVotePrinterFacade
   {
      private readonly IPollbookConfigurationRepository _pollbookConfigurationRepo;
      private readonly IPollPlaceFacade _pollPlaceFacade;
      private readonly ISystemManagement _systemManagement;
      private PollbookConfigurationDto _configuration;
      private readonly IEssLogger _logger;

      /// <summary>Printer serial port</summary>
      private SerialPort _serialPort;

      #region Constants

      /**
       * initialize printer "ESC @"
       */
      private static readonly byte[] CMD_PRINTER_INIT = {0x1B, 0x40};

      /**
       * request printer status command
       */
      private static readonly byte[] CMD_PRINTER_STATUS = {0x10, 0x04, 0x01};

      /**
       * request paper status command
       */
      private static readonly byte[] CMD_PAPER_STATUS = {0x10, 0x04, 0x04};

      /**
       * select page mode "ESC L"
       */
      private static readonly byte[] CMD_PAGE_MODE = {0x1B, 0x4C};

      /**
       * select character font "ESC M n"
       */
      private static readonly byte[] CMD_SET_FONT_A = {0x1B, 0x4D, 0x05, 0x01}; // 2 parameters?

      /**
       * set basic calculated pitch "GS P x y"
       */
      private static readonly byte[] CMD_SET_BASIC_PITCH_CB_CB = {0x1D, 0x50, 0xCB, 0xCB}; // 203, 203 DPI

      /**
       * set print region in page mode "ESC W xL xH yL yH dxL dxH dyL dyH"
       */
      private static readonly byte[] CMD_SET_PRINT_REGION_PAGE_MODE_DEFAULT =
      {
         0x1B, 0x57, 0x00, 0x00, 0x11, 0x00, 0x20, 0x03, 0x2C, 0x01
      };

      /**
       * select character print direction in page mode "ESC T n"
       */
      private static readonly byte[] CMD_SET_PRINT_DIRECTION_PAGE_MODE_LEFT_TO_RIGHT = {0x1B, 0x54, 0x00};

      /**
       * set absolute position for character vertical direction in page mode "GS $ nL nH"
       */
      private static readonly byte[] CMD_SET_POSITION_VERTICAL_PAGE_MODE = {0x1D, 0x24};

      /**
       * select HRI character print position "GS H n"
       */
      private static readonly byte[] CMD_SET_HRI_CHARACTER_POSITION_ZERO = {0x1D, 0x48, 0x00};

      /**
       * set barcode height "GS h n" 52 dots
       */
      private static readonly byte[] CMD_SET_BARCODE_HEIGHT_52 = {0x1D, 0x68, 0x34};

      /**
       * set barcode horizontal size "GS w n" 3
       */
      private static readonly byte[] CMD_SET_BARCODE_WIDTH_3 = {0x1D, 0x77, 0x03};

      /**
       * print barcode "GS k m n d1 d2" Barcode code 128
       */
      private static readonly byte[] CMD_BARCODE_CODE128 = {0x1D, 0x6B, 0x49};

      /**
       * print barcode "GS k m n d1 d2" Barcode code 128 type C
       */
      private static readonly byte[] CMD_BARCODE_CODE128_TYPE_C = {0x7B, 0x43};

      /**
       * line feed "LF"
       */
      private static readonly byte[] CMD_LF = {0x0A};

      /**
       * cut paper "GS V m n"
       */
      private static readonly byte[] CMD_FORMFEED = {0x1D, 0x56, 0x42, 0x00};

      /**
       * specify emphasized characters "ESC E n"
       */
      private static readonly byte[] CMD_SET_EMPHASIZED = {0x1B, 0x45, 0x01};

      /**
       * set absolute position "ESC $ nL nH"
       */
      private static readonly byte[] CMD_SET_POSITION_ZERO = {0x1B, 0x24, 0x00, 0x00};

      /**
       * cancel character set
       */
      private static readonly byte[] CMD_SET_CHARACTER_SET_CANCEL = {0x1B, 0x25, 0x00};

      /**
       * set character set
       */
      private static readonly byte[] CMD_SET_CHARACTER_SET = {0x1B, 0x25, 0x01};

      /**
       * set character right space "ESC SP n"
       */
      private static readonly byte[] CMD_SET_CHARACTER_RIGHT_SPACE = {0x1B, 0x20};

      /**
       * set character right space
       */
      private static readonly byte[] CMD_SET_CHARACTER_RIGHT_SPACE_ZERO = {0x1B, 0x20, 0x00};

      /**
       * set line feed amount "ESC 3 n"
       */
      private static readonly byte[] CMD_SET_LINE_FEED = {0x1B, 0x33};

      /**
       * print and recover to page mode "FF"
       */
      private static readonly byte CMD_PRINT_AND_RECOVER = 0x0C;

      /**
       * query printer hardware version command
       */
      private static readonly byte[] CMD_GET_PRINTER_DATA =
      {
         0x1D, 0x99, 0x42, 0x45, 0x92, 0x9A, 0x35
      };

      /**
       * Part of the version string provided by Microcom printer
       */
      private static readonly string MICROCOM_FIRMWARE = "Main Firmware:";

      /**
       * character set defined as c1c2[x1 d1...d(yXx1)]...[a xd1...d(y*ax)]
       */
      private static readonly byte[] CMD_SET_CHARACTER_SET_7F_7F_03 =
      {
         0x1B, 0x26, 0x03, 0x7F, 0x7F, 0x0C, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03,
         0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03,
         0x00, 0x00, 0x03, 0x00, 0x00, 0x03
      };

      /**
       * character set defined as c1c2[x1 d1...d(yXx1)]...[a xd1...d(y*ax)]
       */
      private static readonly byte[] CMD_SET_CHARACTER_SET_7F_7F_60 =
      {
         0x1B, 0x26, 0x03, 0x7F, 0x7F, 0x0C, 0x60, 0x00, 0x00, 0x60, 0x00, 0x00, 0x60, 0x00, 0x00, 0x60, 0x00, 0x00,
         0x60, 0x00, 0x00, 0x60, 0x00, 0x00, 0x60, 0x00, 0x00, 0x60, 0x00, 0x00, 0x60, 0x00, 0x00, 0x60, 0x00, 0x00,
         0x60, 0x00, 0x00, 0x60, 0x00, 0x00
      };

      /**
       * character set defined by Microcom to print (-) character
       */
      private static readonly byte[] CMD_CHARACTER_SET_7F =
      {
         0x7F, 0x7F, 0x7F, 0x7F, 0x7F, 0x7F, 0x7F, 0x7F, 0x7F, 0x7F, 0x7F, 0x7F, 0x7F, 0x7F, 0x7F, 0x7F, 0x7F, 0x7F,
         0x7F, 0x7F, 0x7F, 0x7F, 0x7F, 0x7F, 0x7F, 0x7F, 0x7F, 0x7F, 0x7F, 0x7F, 0x7F, 0x7F, 0x7F, 0x7F, 0x7F, 0x7F,
         0x7F, 0x7F, 0x7F, 0x7F, 0x7F, 0x7F, 0x7F, 0x7F, 0x7F
      };

      /**
       * set absolute position for new line "ESC $ nL nH"
       */
      private static readonly byte[] CMD_SET_POSITION_INITIAL_BOX_ONE = {0x1B, 0x24, 0x1F, 0x01};

      /**
       * set absolute position for new line "ESC $ nL nH"
       */
      private static readonly byte[] CMD_SET_POSITION_INITIAL_BOX_TWO = {0x1B, 0x24, 0xB7, 0x01};

      /**
       * set absolute position for new line "ESC $ nL nH"
       */
      private static readonly byte[] CMD_SET_POSITION_REVIEW_BOX = {0x1B, 0x24, 0x69, 0x02};

      /**
       * character set defined by Microcom to print box characters
       */
      private static readonly byte[] CMD_CHARACTER_SET_60_6B =
      {
         0x1B, 0x26, 0x03, 0x60, 0x6B,

         // 0x60 - blanks
         0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
         0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
         0x00,

         // 0x61 1 pixel border size. do not remove
         // 0x0C,  0x80, 0x00, 0x00,  0x80, 0x00, 0x00,  0x80, 0x00, 0x00,  0x80, 0x00, 0x00,
         //  0x80, 0x00, 0x00,  0x80, 0x00, 0x00,  0x80, 0x00, 0x00,  0x80, 0x00, 0x00, 
         // 0x80,
         // 0x00, 0x00,  0x80, 0x00, 0x00,  0x80, 0x00, 0x00,  0x80, 0x00, 0x00,

         // 0x61 - 2 pixels border size. do not remove
         // 0x0C,  0xC0, 0x00, 0x00,  0xC0, 0x00, 0x00,  0xC0, 0x00, 0x00,  0xC0, 0x00, 0x00,
         //  0xC0, 0x00, 0x00,  0xC0, 0x00, 0x00,  0xC0, 0x00, 0x00,  0xC0, 0x00, 0x00, 
         // 0xC0,
         // 0x00, 0x00,  0xC0, 0x00, 0x00,  0xC0, 0x00, 0x00,  0xC0, 0x00, 0x00,

         // 0x61 - 3 pixels
         0x0C, 0xE0, 0x00, 0x00, 0xE0, 0x00, 0x00, 0xE0, 0x00, 0x00, 0xE0, 0x00, 0x00,
         0xE0, 0x00, 0x00, 0xE0, 0x00, 0x00, 0xE0, 0x00, 0x00, 0xE0, 0x00, 0x00, 0xE0,
         0x00, 0x00, 0xE0, 0x00, 0x00, 0xE0, 0x00, 0x00, 0xE0, 0x00, 0x00,


         // 0x62 1 pixel border size. do not remove
         // 0x0C,  0xFF,  0xFF,  0xFF,  0x80, 0x00, 0x00,  0x80, 0x00, 0x00,  0x80,
         // 0x00, 0x00,  0x80, 0x00, 0x00,  0x80, 0x00, 0x00,  0x80, 0x00, 0x00,  0x80, 0x00,
         // 0x00,
         //  0x80, 0x00, 0x00,  0x80, 0x00, 0x00,  0x80, 0x00, 0x00,  0x80, 0x00, 0x00,

         // 0x62 - 2 pixels border size. do not remove
         // 0x0C,  0xFF,  0xFF,  0xFF,  0xFF,  0xFF,  0xFF,  0xC0, 0x00, 0x00,
         //  0xC0, 0x00, 0x00,  0xC0, 0x00, 0x00,  0xC0, 0x00, 0x00,  0xC0, 0x00, 0x00, 
         // 0xC0,
         // 0x00, 0x00,  0xC0, 0x00, 0x00,  0xC0, 0x00, 0x00,  0xC0, 0x00, 0x00,  0xC0, 0x00,
         // 0x00,

         // 0x62 - 3 pixels
         0x0C, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
         0xE0, 0x00, 0x00, 0xE0, 0x00, 0x00, 0xE0, 0x00, 0x00, 0xE0, 0x00, 0x00,
         0xE0, 0x00, 0x00, 0xE0, 0x00, 0x00, 0xE0, 0x00, 0x00, 0xE0, 0x00, 0x00, 0xE0,
         0x00, 0x00,

         // 0x63 1 pixel border size. do not remove
         // 0x0C,  0x80, 0x00, 0x00,  0x80, 0x00, 0x00,  0x80, 0x00, 0x00,  0x80, 0x00, 0x00,
         //  0x80, 0x00, 0x00,  0x80, 0x00, 0x00,  0x80, 0x00, 0x00,  0xFF,  0xFF,
         //  0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,

         // 0x63 - 2 pixels border size. do not remove
         // 0x0C,  0xC0, 0x00, 0x00,  0xC0, 0x00, 0x00,  0xC0, 0x00, 0x00,  0xC0, 0x00, 0x00,
         //  0xC0, 0x00, 0x00,  0xC0, 0x00, 0x00,  0xFF,  0xFF,  0xFF,  0xFF,
         //  0xFF,  0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,

         // 0x63 - 3 pixels
         0x0C, 0xE0, 0x00, 0x00, 0xE0, 0x00, 0x00, 0xE0, 0x00, 0x00, 0xE0, 0x00, 0x00,
         0xE0, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
         0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,


         // 0x64 1 pixel border size. do not remove
         // 0x0C,  0xFF,  0xFF,  0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
         // 0x00,
         // 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
         // 0x00, 0x00, 0x00, 0x00,

         // 0x64 - 2 pixels border size. do not remove
         // 0x0C,  0xFF,  0xFF,  0xFF,  0xFF,  0xFF,  0xFF, 0x00, 0x00, 0x00, 0x00,
         // 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
         // 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,

         // 0x64 - 3 pixels
         0x0C, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
         0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
         0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,


         // 0x65 1 pixel border size. do not remove
         // 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
         // 0x00, 0x00, 0x00, 0x00,  0xFF,  0xFF,  0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
         // 0x00,
         // 0x00, 0x00, 0x00, 0x00,

         // 0x65 - 2 pixels border size. do not remove
         // 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
         // 0x00,  0xFF,  0xFF,  0xFF,  0xFF,  0xFF,  0xFF, 0x00, 0x00, 0x00, 0x00,
         // 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,

         // 0x65 - 3 pixels
         0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF,
         0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00,
         0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,


         // 0x66 1 pixel border size. do not remove
         // 0x0C,  0xFF, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01,
         // 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01,
         // 0x00, 0x00,

         // 0x66 - 2 pixels border size. do not remove
         // 0x0C,  0xFF, 0x00, 0x00,  0xFF, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00,
         // 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00,
         // 0x03, 0x00, 0x00,

         // 0x66 - 3 pixels
         0x0C, 0xFF, 0x00, 0x00, 0xFF, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x07, 0x00, 0x00, 0x07, 0x00,
         0x00, 0x07, 0x00, 0x00, 0x07, 0x00, 0x00, 0x07, 0x00, 0x00, 0x07, 0x00, 0x00, 0x07, 0x00, 0x00, 0x07, 0x00,
         0x00, 0x07, 0x00, 0x00,

         // 0x67 1 pixel border size. do not remove
         // 0x0C, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0x00,
         // 0x00, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0x00,
         // 0x00,

         // 0x67 - 2 pixels border size. do not remove
         // 0x0C, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00,
         // 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00,
         // 0x00,

         // 0x67 - 3 pixels
         0x0C, 0x07, 0x00, 0x00, 0x07, 0x00, 0x00, 0x07, 0x00, 0x00, 0x07, 0x00, 0x00, 0x07, 0x00, 0x00, 0x07, 0x00,
         0x00, 0x07, 0x00, 0x00, 0x07, 0x00, 0x00, 0x07, 0x00, 0x00, 0x07, 0x00, 0x00, 0x07, 0x00, 0x00, 0x07, 0x00,
         0x00,


         // 0x68 1 pixel border size. do not remove
         // 0x0C, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0x00,
         // 0x00, 0x01, 0x00, 0x00,  0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
         // 0x00, 0x00,

         // 0x68 - 2 pixels border size. do not remove
         // 0x0C, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00,
         // 0x00,  0xFF, 0x00, 0x00,  0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
         // 0x00, 0x00, 0x00,

         // 0x68 - 3 pixels
         0x0C, 0x07, 0x00, 0x00, 0x07, 0x00, 0x00, 0x07, 0x00, 0x00, 0x07, 0x00, 0x00, 0x07, 0x00, 0x00, 0xFF,
         0x00, 0x00, 0xFF, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
         0x00, 0x00, 0x00, 0x00,


         // 0x69 1 pixel border size. do not remove
         // 0x0C,  0x80, 0x00, 0x00,  0x80, 0x00, 0x00,  0x80, 0x00, 0x00,  0xFF,  0xFF,
         //  0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
         // 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,

         // 0x69 - 2 pixels border size. do not remove
         // 0x0C,  0xC0, 0x00, 0x00,  0xC0, 0x00, 0x00,  0xFF,  0xFF,  0xFF,  0xFF,
         //  0xFF,  0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
         // 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,

         // 0x69 - 3 pixels
         0x0C, 0xE0, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
         0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
         0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,


         // 0x6A 1 pixel border size. do not remove
         // 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  0xFF,  0xFF,
         //  0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
         // 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,

         // 0x6A - 2 pixels border size. do not remove
         // 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  0xFF,  0xFF,  0xFF,  0xFF,  0xFF,
         //  0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
         // 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,

         // 0x6A - 3 pixels
         0x0C, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
         0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
         0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,


         // 0x6B 1 pixel border size. do not remove
         // 0x0C, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00,  0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
         // 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
         // 0x00, 0x00,

         // 0x6B - 2 pixels border size. do not remove
         // 0x0C, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00,  0xFF, 0x00, 0x00,  0xFF, 0x00, 0x00, 0x00, 0x00, 0x00,
         // 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
         // 0x00, 0x00, 0x00,

         // 0x6B - 3 pixels
         0x0C, 0x07, 0x00, 0x00, 0xFF, 0x00, 0x00, 0xFF, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x00, 0x00,
         0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
         0x00, 0x00, 0x00, 0x00
      };

      /**
       * character set defined by Microcom to print box characters
       */
      private static readonly byte[] CMD_CHARACTER_SET_60_6B_PROVISIONAL =
      {
         0x1B, 0x26, 0x03, 0x60, 0x6B,

         // 0x60 - filled black
         0x0C, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
         0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
         0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,

         // 0x61 1 pixel border size. do not remove
         // 0x0C,  0x80, 0x00, 0x00,  0x80, 0x00, 0x00,  0x80, 0x00, 0x00,  0x80, 0x00, 0x00,
         //  0x80, 0x00, 0x00,  0x80, 0x00, 0x00,  0x80, 0x00, 0x00,  0x80, 0x00, 0x00, 
         // 0x80,
         // 0x00, 0x00,  0x80, 0x00, 0x00,  0x80, 0x00, 0x00,  0x80, 0x00, 0x00,

         // 0x61 - 2 pixels border size. do not remove
         // 0x0C,  0xC0, 0x00, 0x00,  0xC0, 0x00, 0x00,  0xC0, 0x00, 0x00,  0xC0, 0x00, 0x00,
         //  0xC0, 0x00, 0x00,  0xC0, 0x00, 0x00,  0xC0, 0x00, 0x00,  0xC0, 0x00, 0x00, 
         // 0xC0,
         // 0x00, 0x00,  0xC0, 0x00, 0x00,  0xC0, 0x00, 0x00,  0xC0, 0x00, 0x00,

         // 0x61 - 3 pixels
         0x0C, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
         0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
         0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,


         // 0x62 1 pixel border size. do not remove
         // 0x0C,  0xFF,  0xFF,  0xFF,  0x80, 0x00, 0x00,  0x80, 0x00, 0x00,  0x80,
         // 0x00, 0x00,  0x80, 0x00, 0x00,  0x80, 0x00, 0x00,  0x80, 0x00, 0x00,  0x80, 0x00,
         // 0x00,
         //  0x80, 0x00, 0x00,  0x80, 0x00, 0x00,  0x80, 0x00, 0x00,  0x80, 0x00, 0x00,

         // 0x62 - 2 pixels border size. do not remove
         // 0x0C,  0xFF,  0xFF,  0xFF,  0xFF,  0xFF,  0xFF,  0xC0, 0x00, 0x00,
         //  0xC0, 0x00, 0x00,  0xC0, 0x00, 0x00,  0xC0, 0x00, 0x00,  0xC0, 0x00, 0x00, 
         // 0xC0,
         // 0x00, 0x00,  0xC0, 0x00, 0x00,  0xC0, 0x00, 0x00,  0xC0, 0x00, 0x00,  0xC0, 0x00,
         // 0x00,

         // 0x62 - 3 pixels
         0x0C, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
         0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
         0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,

         // 0x63 1 pixel border size. do not remove
         // 0x0C,  0x80, 0x00, 0x00,  0x80, 0x00, 0x00,  0x80, 0x00, 0x00,  0x80, 0x00, 0x00,
         //  0x80, 0x00, 0x00,  0x80, 0x00, 0x00,  0x80, 0x00, 0x00,  0xFF,  0xFF,
         //  0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,

         // 0x63 - 2 pixels border size. do not remove
         // 0x0C,  0xC0, 0x00, 0x00,  0xC0, 0x00, 0x00,  0xC0, 0x00, 0x00,  0xC0, 0x00, 0x00,
         //  0xC0, 0x00, 0x00,  0xC0, 0x00, 0x00,  0xFF,  0xFF,  0xFF,  0xFF,
         //  0xFF,  0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,

         // 0x63 - 3 pixels
         0x0C, 0xE0, 0x00, 0x00, 0xE0, 0x00, 0x00, 0xE0, 0x00, 0x00, 0xE0, 0x00, 0x00,
         0xE0, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
         0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,


         // 0x64 1 pixel border size. do not remove
         // 0x0C,  0xFF,  0xFF,  0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
         // 0x00,
         // 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
         // 0x00, 0x00, 0x00, 0x00,

         // 0x64 - 2 pixels border size. do not remove
         // 0x0C,  0xFF,  0xFF,  0xFF,  0xFF,  0xFF,  0xFF, 0x00, 0x00, 0x00, 0x00,
         // 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
         // 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,

         // 0x64 - 3 pixels
         0x0C, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
         0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
         0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,


         // 0x65 1 pixel border size. do not remove
         // 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
         // 0x00, 0x00, 0x00, 0x00,  0xFF,  0xFF,  0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
         // 0x00,
         // 0x00, 0x00, 0x00, 0x00,

         // 0x65 - 2 pixels border size. do not remove
         // 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
         // 0x00,  0xFF,  0xFF,  0xFF,  0xFF,  0xFF,  0xFF, 0x00, 0x00, 0x00, 0x00,
         // 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,

         // 0x65 - 3 pixels
         0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
         0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
         0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,


         // 0x66 1 pixel border size. do not remove
         // 0x0C,  0xFF, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01,
         // 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01,
         // 0x00, 0x00,

         // 0x66 - 2 pixels border size. do not remove
         // 0x0C,  0xFF, 0x00, 0x00,  0xFF, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00,
         // 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00,
         // 0x03, 0x00, 0x00,

         // 0x66 - 3 pixels
         0x0C, 0xFF, 0x00, 0x00, 0xFF, 0x00, 0x00, 0xFF, 0x00, 0x00, 0xFF, 0x00, 0x00,
         0xFF, 0x00, 0x00, 0xFF, 0x00, 0x00, 0xFF, 0x00, 0x00, 0xFF, 0x00, 0x00,
         0xFF, 0x00, 0x00, 0xFF, 0x00, 0x00, 0xFF, 0x00, 0x00, 0xFF, 0x00, 0x00,

         // 0x67 1 pixel border size. do not remove
         // 0x0C, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0x00,
         // 0x00, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0x00,
         // 0x00,

         // 0x67 - 2 pixels border size. do not remove
         // 0x0C, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00,
         // 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00,
         // 0x00,

         // 0x67 - 3 pixels
         0x0C, 0xFF, 0x00, 0x00, 0xFF, 0x00, 0x00, 0xFF, 0x00, 0x00, 0xFF, 0x00, 0x00,
         0xFF, 0x00, 0x00, 0xFF, 0x00, 0x00, 0xFF, 0x00, 0x00, 0xFF, 0x00, 0x00,
         0xFF, 0x00, 0x00, 0xFF, 0x00, 0x00, 0xFF, 0x00, 0x00, 0xFF, 0x00, 0x00,


         // 0x68 1 pixel border size. do not remove
         // 0x0C, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0x00,
         // 0x00, 0x01, 0x00, 0x00,  0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
         // 0x00, 0x00,

         // 0x68 - 2 pixels border size. do not remove
         // 0x0C, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00,
         // 0x00,  0xFF, 0x00, 0x00,  0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
         // 0x00, 0x00, 0x00,

         // 0x68 - 3 pixels
         0x0C, 0xFF, 0x00, 0x00, 0xFF, 0x00, 0x00, 0xFF, 0x00, 0x00, 0xFF, 0x00, 0x00,
         0xFF, 0x00, 0x00, 0xFF, 0x00, 0x00, 0xFF, 0x00, 0x00, 0xFF, 0x00, 0x00,
         0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,


         // 0x69 1 pixel border size. do not remove
         // 0x0C,  0x80, 0x00, 0x00,  0x80, 0x00, 0x00,  0x80, 0x00, 0x00,  0xFF,  0xFF,
         //  0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
         // 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,

         // 0x69 - 2 pixels border size. do not remove
         // 0x0C,  0xC0, 0x00, 0x00,  0xC0, 0x00, 0x00,  0xFF,  0xFF,  0xFF,  0xFF,
         //  0xFF,  0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
         // 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,

         // 0x69 - 3 pixels
         0x0C, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
         0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
         0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,


         // 0x6A 1 pixel border size. do not remove
         // 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  0xFF,  0xFF,
         //  0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
         // 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,

         // 0x6A - 2 pixels border size. do not remove
         // 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  0xFF,  0xFF,  0xFF,  0xFF,  0xFF,
         //  0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
         // 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,

         // 0x6A - 3 pixels
         0x0C, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
         0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
         0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,


         // 0x6B 1 pixel border size. do not remove
         // 0x0C, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00,  0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
         // 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
         // 0x00, 0x00,

         // 0x6B - 2 pixels border size. do not remove
         // 0x0C, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00,  0xFF, 0x00, 0x00,  0xFF, 0x00, 0x00, 0x00, 0x00, 0x00,
         // 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
         // 0x00, 0x00, 0x00,

         // 0x6B - 3 pixels
         0x0C, 0xFF, 0x00, 0x00, 0xFF, 0x00, 0x00, 0xFF, 0x00, 0x00, 0xFF, 0x00, 0x00,
         0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
         0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
      };

      /**
       * initial box row 1
       */
      private static readonly byte[] CMD_INITIAL_BOX_ROW_ONE =
      {
         0x62, 0x61, 0x61, 0x61, 0x61, 0x61, 0x61, 0x61, 0x61, 0x61, 0x63
      };

      /**
       * initial box row 2
       */
      private static readonly byte[] CMD_INITIAL_BOX_ROW_TWO =
      {
         0x64, 0x60, 0x60, 0x60, 0x60, 0x60, 0x60, 0x60, 0x60, 0x60, 0x65
      };

      /**
       * initial box row 3
       */
      private static readonly byte[] CMD_INITIAL_BOX_ROW_THREE =
      {
         0x66, 0x67, 0x67, 0x67, 0x67, 0x67, 0x67, 0x67, 0x67, 0x67, 0x68
      };

      /**
       * review box row 1
       */
      private static readonly byte[] CMD_REVIEW_BOX_ROW_ONE = {0x62, 0x61, 0x61, 0x61, 0x69};

      /**
       * review box row 2
       */
      private static readonly byte[] CMD_REVIEW_BOX_ROW_TWO = {0x64, 0x60, 0x60, 0x60, 0x6A};

      /**
       * review box row 3
       */
      private static readonly byte[] CMD_REVIEW_BOX_ROW_THREE = {0x66, 0x67, 0x67, 0x67, 0x6B};

      /**
       * review box row 1
       */
      private static readonly byte[] CMD_PROVISIONAL_REVIEW_BOX_ROW_ONE = {0x92, 0x91, 0x91, 0x91, 0x99};

      /**
       * review box row 2
       */
      private static readonly byte[] CMD_PROVISIONAL_REVIEW_BOX_ROW_TWO = {0x94, 0x90, 0x90, 0x90, 0x9A};

      /**
       * review box row 3
       */
      private static readonly byte[] CMD_PROVISIONAL_REVIEW_BOX_ROW_THREE = {0x96, 0x97, 0x97, 0x97, 0x9B};

      /**
       * set absolute position for new line "ESC $ nL nH"
       */
      private static readonly byte[] CMD_SET_POSITION_INITIAL_LABEL_ONE = {0x1B, 0x24, 0x58, 0x01};

      /**
       * set absolute position for new line "ESC $ nL nH"
       */
      private static readonly byte[] CMD_SET_POSITION_INITIAL_LABEL_TWO = {0x1B, 0x24, 0xF0, 0x01};

      /**
       * set absolute position for new line "ESC $ nL nH"
       */
      private static readonly byte[] CMD_SET_POSITION_REVIEW_LABEL = {0x1B, 0x24, 0x7C, 0x02};

      /**
       * set absolute position for new line "ESC $ nL nH"
       */
      private static readonly byte[] CMD_SET_POSITION_INITIAL_CONTENT_ONE = {0x1B, 0x24, 0x3B, 0x01};

      /**
       * set absolute position for new line "ESC $ nL nH"
       */
      private static readonly byte[] CMD_SET_POSITION_INITIAL_CONTENT_TWO = {0x1B, 0x24, 0xC8, 0x01};

      /**
       * set absolute position for new line "ESC $ nL nH"
       */
      private static readonly byte[] CMD_SET_POSITION_REVIEW_CONTENT = {0x1B, 0x24, 0x73, 0x02};

      /**
       * the cover open bit of the printer status
       */
      private static readonly byte COVER_OPEN = 0x01;

      /**
       * paper did not feed correctly
       */
      private static readonly byte MISFEED = 0x10;

      /**
       * the right sensor is covered (paper upside down)
       */
      private static readonly byte RIGHT_PAPER_COVERED = 0x20;

      /**
       * the left sensor is covered (from PAPER_STATUS)
       */
      private static readonly byte LEFT_PAPER_COVERED = 0x0C;

      /**
       * maximum string length to print
       */
      private static readonly int MAX_PRINTED_LENGTH = 36;

      /**
       * initial box 1 id
       */
      private const int INITIAL_BOX_ONE = 1;

      /**
       * initial box 2 id
       */
      private const int INITIAL_BOX_TWO = 2;

      /**
       * review box id
       */
      private const int REVIEW_BOX = 3;

      #endregion

      private bool _printerStatus;

      #region Constructor

      public ExpressVotePrinterFacade(ISystemManagement systemManagement,
         IPollbookConfigurationRepository pollbookConfigurationRepo, IPollPlaceFacade pollPlaceFacade,
         IEssLogger logger)
      {
         _systemManagement = systemManagement;
         _pollbookConfigurationRepo = pollbookConfigurationRepo;
         _pollPlaceFacade = pollPlaceFacade;
         _logger = logger;
      }

      #endregion

      #region Public Methods & Properties

      /// <summary>Helper method to verify printer is attached</summary>
      /// <returns>true if printer is attached</returns>
      public bool Verify()
      {
         if (!SystemDetails.CanVerifyEVPrinter)
            return _printerStatus;

         // the return status
         _printerStatus = true;

         var status = GetStatus();

         if (ExpressVoterPrinterStatusEnum.OFFLINE.Equals(status))
            _printerStatus = false;

         return _printerStatus;
      }

      public bool IsProvisional { get; set; }

      /// <summary>Open the printer.</summary>
      public void Open()
      {
         // close existing interfaces
         Close();
         string DefaultPort;
         try
         {
	         DefaultPort =
		         _systemManagement.QuerySystem("SELECT * FROM Win32_PnPEntity where name like '%Microcom Printer (COM%'",
			         "Caption");
	         if (DefaultPort.Contains("(COM"))
		         DefaultPort = DefaultPort.Substring(DefaultPort.LastIndexOf("(COM", StringComparison.Ordinal))
			         .Replace("(", string.Empty).Replace(")", string.Empty);

	         if (string.IsNullOrEmpty(DefaultPort)) DefaultPort = "COM3";


	         _serialPort =
		         new SerialPort(DefaultPort, 115200, Parity.None, 8, StopBits.One)
		         {
			         Handshake = Handshake.RequestToSend,
			         ReadTimeout = 1000,
			         WriteTimeout = 5000,
			         ReadBufferSize = 128
		         };

	         _serialPort.Open();

	         // send the initialization command
	         Write(CMD_PRINTER_INIT);
         }
         catch
         {
	         // ignored -- still outputting 'System.IO.IOException' in System.dll to the output window of IDE
         }
      }

      /// <summary>Close the printer.</summary>
      public void Close()
      {
         // check for non-null
         if (_serialPort != null)
         {
	         try
	         {
		         // close, dispose, and dereference the printer
		         _serialPort?.Close();
		         _serialPort?.Dispose();
	         }
	         catch
	         {
		         // ignore the exception since only trying to close
	         }

            _serialPort = null;
         }
      }

      /// <summary>Get the printer status.</summary>
      /// <returns>The printer status.</returns>
      public ExpressVoterPrinterStatusEnum GetStatus()
      {
         var wasOpen = true;
         // check for existing
         if (_serialPort == null)
            try
            {
               wasOpen = false;
               // open printer
               Open();
            }
            catch (IOException)
            {
               Close();
               return ExpressVoterPrinterStatusEnum.OFFLINE;
            }
            catch (Exception ex)
            {
               // do nothing besides logging the exception
               var logProps = new Dictionary<string, string> {{"Action", "Getting printer status"}};
               _logger.LogError(ex, logProps);
            }

         // return printer status
         var status = GetPrinterStatus();

         if (!wasOpen) Close();

         return status;
      }

      public ExpressVoterPrinterStatusEnum Print(VoterDto voterBallotInfo)
      {
         _configuration = SystemConfiguration.ElectionConfiguration;
         var data = ConvertToBytes(voterBallotInfo.PrecinctParty.BallotStyleCode.PadLeft(8, '0'));
         var serialId = string.Empty;
         if (_configuration.ExpressVotePrintSerialNumber)
         {
            var sb = new StringBuilder();
            sb.Append(" ")
               .Append(LoggedInPollplaceInfo.LoggedInPollPlace.PollingPlaceId.ToString().PadLeft(4, '0'))
               .Append(SystemDetails.SystemId.Substring(SystemDetails.SystemId.Length - 6))
               .Append(_pollPlaceFacade.GenerateSerialId());
            serialId = sb.ToString();
         }

         if (data == null)
         {
            //todo- handle this in error story in future
         }
         else
         {
            // initial printer
            Write(CMD_PRINTER_INIT);

            // print attributes
            Write(CMD_PAGE_MODE);
            Write(CMD_SET_FONT_A);
            Write(CMD_SET_BASIC_PITCH_CB_CB);

            // print region
            Write(CMD_SET_PRINT_REGION_PAGE_MODE_DEFAULT);
            Write(CMD_SET_PRINT_DIRECTION_PAGE_MODE_LEFT_TO_RIGHT);

            // print the bar code
            Write(CMD_SET_POSITION_ZERO);
            Write(CMD_SET_POSITION_VERTICAL_PAGE_MODE); // nL=53, nH=0
            Write(0x35); // nL=53, nH=0
            Write(0x00); // nL=53, nH=0
            Write(CMD_SET_CHARACTER_RIGHT_SPACE); // 1 unit
            Write(0x01); // 1 unit
            Write(CMD_SET_HRI_CHARACTER_POSITION_ZERO);
            Write(CMD_SET_BARCODE_WIDTH_3); // set the bar code width
            Write(CMD_SET_BARCODE_HEIGHT_52); // set the bar code height

            Write(CMD_BARCODE_CODE128); // barcode type
            Write(6); // 6 bytes in barcode
            Write(CMD_BARCODE_CODE128_TYPE_C); // 6 bytes in barcode
            Write(data); //Print ballot style code as barcode
            Write(CMD_SET_LINE_FEED);
            Write(0x30); // 48 units
            Write(CMD_LF);

            // print the id in human readable form
            Write(CMD_SET_EMPHASIZED);
            if (!_configuration.ExpressVotePrintSerialNumber)
            {
               Write(voterBallotInfo.PrecinctParty.BallotStyleCode.PadLeft(8, '0').PadLeft(10, ' '));
            }
            else
            {
               Write(voterBallotInfo.PrecinctParty.BallotStyleCode.PadLeft(8, '0'));
               Write(serialId);
            }

            // position, download character set and draw dashed line
            Write(CMD_SET_POSITION_ZERO);
            Write(CMD_SET_CHARACTER_SET_7F_7F_03);
            Write(CMD_SET_CHARACTER_SET);
            Write(CMD_SET_CHARACTER_RIGHT_SPACE);
            Write(0x05); // 5 units
            Write(CMD_CHARACTER_SET_7F);

            // reset character right space and line feed amount before printing text
            Write(CMD_SET_CHARACTER_RIGHT_SPACE);
            Write(0x01); // 1 unit
            Write(CMD_SET_LINE_FEED);
            Write(0x23); // 35 units
            Write(CMD_LF);
            Write(CMD_SET_CHARACTER_SET_CANCEL);

            // print the human readable information
            TextValidation(new[]
            {
               _configuration.JurisdictionName,
               _configuration.ElectionName,
               _configuration.ElectionDate.ToShortDateString(),
               $"{voterBallotInfo.PrecinctParty.PrecinctName}, {voterBallotInfo.PrecinctParty.BallotStyleShortDescriptionText}"
            });

            // download character set to print dashed line
            Write(CMD_SET_CHARACTER_SET_7F_7F_60);
            Write(CMD_SET_CHARACTER_SET);
            Write(CMD_SET_CHARACTER_RIGHT_SPACE);
            Write(0x05); // 5 units
            Write(CMD_CHARACTER_SET_7F);

            // reset line feed settings
            Write(CMD_SET_LINE_FEED);
            Write(0x16); // 22 units
            Write(CMD_LF);

            var boxesToPrint = new List<int>();

            if (SystemConfiguration.ElectionConfiguration.ExpressvoteInitial1)
               boxesToPrint.Add(INITIAL_BOX_ONE);

            if (SystemConfiguration.ElectionConfiguration.ExpressvoteInitial2)
               boxesToPrint.Add(INITIAL_BOX_TWO);

            if (SystemConfiguration.ElectionConfiguration.ExpressvoteReview)
               boxesToPrint.Add(REVIEW_BOX);

            if (boxesToPrint.Count > 0)
            {
               // print initial/review boxes
               PrintInitialReviewBoxes(boxesToPrint);

               // print initial/review box labels
               PrintInitialReviewBoxLabels(boxesToPrint);

               // print contents
               PrintInitialReviewBoxContent(boxesToPrint);
            }

            Write(CMD_PRINT_AND_RECOVER);
            Write(CMD_FORMFEED); // feed the paper out
         }

         return GetPrinterStatus();
      }

      public void TestSerialId()
      {
         _pollPlaceFacade.GenerateSerialId();
      }

      public static bool IsExpressVotePrintingEnabled()
      {
         if (SystemConfiguration.ElectionConfiguration == null)
            return false;

         bool isEnabled;

         if (LoggedInPollplaceInfo.LoggedInPollPlace == null)
         {
            isEnabled = (SystemConfiguration.ElectionConfiguration.EnableEDProvisionalExpressVotePrinting &&
                         SystemConfiguration.ElectionConfiguration.ElectionDayEnabled) ||
                        (SystemConfiguration.ElectionConfiguration.EnableEDStandardExpressVotePrinting &&
                         SystemConfiguration.ElectionConfiguration.ElectionDayEnabled) ||
                        (SystemConfiguration.ElectionConfiguration.EnableEVProvisionalExpressVotePrinting &&
                         SystemConfiguration.ElectionConfiguration.EarlyVotingEnabled) ||
                        (SystemConfiguration.ElectionConfiguration.EnableEVStandardExpressVotePrinting &&
                         SystemConfiguration.ElectionConfiguration.EarlyVotingEnabled);
         }
         else
         {
            if (!LoggedInPollplaceInfo.IsEarlyVotingPollPlace)
               isEnabled = (SystemConfiguration.ElectionConfiguration.EnableEDProvisionalExpressVotePrinting &&
                            SystemConfiguration.ElectionConfiguration.ElectionDayEnabled) ||
                           (SystemConfiguration.ElectionConfiguration.EnableEDStandardExpressVotePrinting &&
                            SystemConfiguration.ElectionConfiguration.ElectionDayEnabled);

            else
               isEnabled = (SystemConfiguration.ElectionConfiguration.EnableEVProvisionalExpressVotePrinting &&
                            SystemConfiguration.ElectionConfiguration.EarlyVotingEnabled) ||
                           (SystemConfiguration.ElectionConfiguration.EnableEVStandardExpressVotePrinting &&
                            SystemConfiguration.ElectionConfiguration.EarlyVotingEnabled);
         }

         return isEnabled;
      }

      #endregion

      #region Private Methods & Properties

      private void PrintInitialReviewBoxes(IEnumerable<int> boxesToPrint)
      {
         foreach (var box in boxesToPrint)
         {
            Write(CMD_SET_POSITION_VERTICAL_PAGE_MODE);
            Write(0x1A); // nL=26, nH=0
            Write(0x00); // nL=26, nH=0

            switch (box)
            {
               case INITIAL_BOX_ONE:
                  Write(CMD_CHARACTER_SET_60_6B);
                  Write(CMD_SET_CHARACTER_SET);
                  Write(CMD_SET_CHARACTER_RIGHT_SPACE_ZERO);

                  Write(CMD_SET_POSITION_INITIAL_BOX_ONE);
                  Write(CMD_INITIAL_BOX_ROW_ONE);
                  Write(CMD_LF);
                  Write(CMD_SET_POSITION_INITIAL_BOX_ONE);
                  Write(CMD_INITIAL_BOX_ROW_TWO);
                  Write(CMD_LF);
                  Write(CMD_SET_POSITION_INITIAL_BOX_ONE);
                  Write(CMD_INITIAL_BOX_ROW_THREE);
                  break;

               case INITIAL_BOX_TWO:
                  Write(CMD_CHARACTER_SET_60_6B);
                  Write(CMD_SET_CHARACTER_SET);
                  Write(CMD_SET_CHARACTER_RIGHT_SPACE_ZERO);

                  Write(CMD_SET_POSITION_INITIAL_BOX_TWO);
                  Write(CMD_INITIAL_BOX_ROW_ONE);
                  Write(CMD_LF);
                  Write(CMD_SET_POSITION_INITIAL_BOX_TWO);
                  Write(CMD_INITIAL_BOX_ROW_TWO);
                  Write(CMD_LF);
                  Write(CMD_SET_POSITION_INITIAL_BOX_TWO);
                  Write(CMD_INITIAL_BOX_ROW_THREE);
                  break;

               case REVIEW_BOX:
                  if (IsProvisional)
                     Write(CMD_CHARACTER_SET_60_6B_PROVISIONAL);
                  else
                     Write(CMD_CHARACTER_SET_60_6B);
                  Write(CMD_SET_CHARACTER_SET);
                  Write(CMD_SET_CHARACTER_RIGHT_SPACE_ZERO);

                  Write(CMD_SET_POSITION_REVIEW_BOX);
                  Write(CMD_REVIEW_BOX_ROW_ONE);
                  Write(CMD_LF);
                  Write(CMD_SET_POSITION_REVIEW_BOX);
                  Write(CMD_REVIEW_BOX_ROW_TWO);
                  Write(CMD_LF);
                  Write(CMD_SET_POSITION_REVIEW_BOX);
                  Write(CMD_REVIEW_BOX_ROW_THREE);

                  break;
            }
         }
      }

      private void PrintInitialReviewBoxLabels(IEnumerable<int> boxesToPrint)
      {
         Write(CMD_SET_CHARACTER_RIGHT_SPACE);
         Write(0x01); // 1 unit
         Write(CMD_SET_CHARACTER_SET_CANCEL);
         Write(CMD_SET_EMPHASIZED);

         foreach (var box in boxesToPrint)
         {
            Write(CMD_SET_POSITION_VERTICAL_PAGE_MODE);
            Write(0x5C); // nL=92, nH=0
            Write(0x00); // nL=92, nH=0

            switch (box)
            {
               case INITIAL_BOX_ONE:
                  if (!_configuration.ExpressVotePrintSerialNumber)
                  {
                     Write(CMD_SET_POSITION_INITIAL_LABEL_ONE);
                     Write(Convert.ToByte(SystemConfiguration.ElectionConfiguration.ExpressvoteLabelInitial1));
                  }

                  break;

               case INITIAL_BOX_TWO:
                  if (!_configuration.ExpressVotePrintSerialNumber)
                  {
                     Write(CMD_SET_POSITION_INITIAL_LABEL_TWO);
                     Write(Convert.ToByte(SystemConfiguration.ElectionConfiguration.ExpressvoteLabelInitial2));
                  }

                  break;

               case REVIEW_BOX:
                  Write(CMD_SET_POSITION_REVIEW_LABEL);
                  Write(Convert.ToByte(SystemConfiguration.ElectionConfiguration.ExpressvoteLabelReview));
                  break;
            }
         }
      }

      private void PrintInitialReviewBoxContent(IEnumerable<int> boxesToPrint)
      {
         var earlyVoting = LoggedInPollplaceInfo.IsEarlyVotingPollPlace;
         Write(CMD_SET_CHARACTER_RIGHT_SPACE);
         Write(0x01); // 1 unit
         Write(CMD_SET_CHARACTER_SET_CANCEL);
         Write(CMD_SET_EMPHASIZED);

         foreach (var box in boxesToPrint)
         {
            Write(CMD_SET_POSITION_VERTICAL_PAGE_MODE);
            Write(0x30); // nL=48, nH=0
            Write(0x00); // nL=48, nH=0

            switch (box)
            {
               case INITIAL_BOX_ONE:
                  Write(CMD_SET_POSITION_INITIAL_CONTENT_ONE);
                  if (earlyVoting)
                     Write(SystemConfiguration.ElectionConfiguration.EV_ExpressVoteContentInitial1);
                  else
                     Write(SystemConfiguration.ElectionConfiguration.ED_ExpressVoteContentInitial1);
                  break;

               case INITIAL_BOX_TWO:
                  Write(CMD_SET_POSITION_INITIAL_CONTENT_TWO);
                  if (earlyVoting)
                     Write(SystemConfiguration.ElectionConfiguration.EV_ExpressVoteContentInitial2);
                  else
                     Write(SystemConfiguration.ElectionConfiguration.ED_ExpressVoteContentInitial2);
                  break;

               case REVIEW_BOX:
                  Write(CMD_SET_POSITION_REVIEW_CONTENT);
                  if (earlyVoting)
                     Write(SystemConfiguration.ElectionConfiguration.EV_ExpressVoteContentReview);
                  else
                     Write(SystemConfiguration.ElectionConfiguration.ED_ExpressVoteContentReview);
                  break;
            }
         }
      }

      private void TextValidation(string[] text)
      {
         var maxLineLength = 36;

         for (int i = 0, lineCount = 0; i < text.Count() && lineCount < 5; i++)
         {
            var line = text[i].Trim();
            if (string.IsNullOrEmpty(line))
               continue;

            Write(FormatSingleLine(line.Length < maxLineLength ? line : line.Substring(0, maxLineLength)));
            Write(CMD_LF);
            lineCount++;
         }
      }

      private string FormatSingleLine(string inString)
      {
         // strip linefeeds and carriage returns
         var formatedString = inString.Replace("\n", " ").Replace("\r", "");
         return formatedString.ToUpper();
      }

      /// <summary>
      ///    Method that converts a string containing numbers into a byte array compatible with the 128C barcode format
      /// </summary>
      /// <param name="digits">String of digits to be converted</param>
      /// <returns>The converted byte array.</returns>
      private byte[] ConvertToBytes(string digits)
      {
         //Make sure the length of the string is a multiple of 2. Code 128C barcodes encode
         //two digits for every byte. So, make sure there are an even number of bytes.
         if (digits.Length % 2 != 0) digits = digits.PadLeft(digits.Length + 1, '0');

         if (!IsAllDigits(digits))
            return null;


         var bytes = new byte[digits.Length / 2];

         //Now look at every two digits and convert to a byte
         for (var i = 0; i < digits.Length; i += 2) bytes[i / 2] = Convert.ToByte(digits.Substring(i, 2));

         return bytes;
      }

      private bool IsAllDigits(string str)
      {
         var isDigits = !string.IsNullOrEmpty(str);
         if (isDigits)
            for (var i = 0; i < str.Length && isDigits; i++)
               isDigits = char.IsDigit(str[i]);
         return isDigits;
      }

      /// <summary>Write a byte array to the printer.</summary>
      /// <param name="bytes">The byte array to write.</param>
      private void Write(byte[] bytes)
      {
         //Make sure the port is open
         if (_serialPort == null) Open();

         // write bytes to serial printer
         _serialPort.Write(bytes, 0, bytes.Length);
      }

      private void Write(byte data)
      {
         Write(new[] {data});
      }

      private void Write(string text)
      {
         Write(Encoding.ASCII.GetBytes(text));
      }

      /// <summary>
      ///    Retrieves the current printer status.
      /// </summary>
      /// <returns>Printer status.</returns>
      private ExpressVoterPrinterStatusEnum GetPrinterStatus()
      {
         // return printer status
         ExpressVoterPrinterStatusEnum printerStatus;

         if (_serialPort == null)
            return ExpressVoterPrinterStatusEnum.OFFLINE;

         // make sure port is open
         if (_serialPort.IsOpen)
         {
            try
            {
               // get status byte
               var statusByte = GetStatus(CMD_PRINTER_STATUS);

               // check status byte
               if ((statusByte & COVER_OPEN) == COVER_OPEN)
                  printerStatus = ExpressVoterPrinterStatusEnum.COVER_OPEN;
               else if ((statusByte & MISFEED) == MISFEED)
                  printerStatus = ExpressVoterPrinterStatusEnum.PAPERJAM;
               else if ((statusByte & RIGHT_PAPER_COVERED) == RIGHT_PAPER_COVERED)
                  printerStatus = ExpressVoterPrinterStatusEnum.PAPER_UPSIDE_DOWN;
               else if ((GetStatus(CMD_PAPER_STATUS) & LEFT_PAPER_COVERED) != LEFT_PAPER_COVERED)
                  printerStatus = ExpressVoterPrinterStatusEnum.NO_PAPER;
               else
                  printerStatus = ExpressVoterPrinterStatusEnum.OK;

               _printerStatus = true;
            }
            catch (TimeoutException)
            {
               // read status byte timed out
               printerStatus = ExpressVoterPrinterStatusEnum.TIMEOUT;
               _printerStatus = false;
            }
            catch
            {
               //If an exception was thrown getting the status it means the printer is offline.
               //Set the serial port object to null...we'll re-initialize it later.
               printerStatus = ExpressVoterPrinterStatusEnum.OFFLINE;
               _printerStatus = false;

               try
               {
                  _serialPort?.Close();
               }
               catch
               {
                  // ignored
               }
               finally
               {
                  _serialPort = null;
               }
            }
         }
         else
         {
            // port closed - printer offline
            printerStatus = ExpressVoterPrinterStatusEnum.OFFLINE;

            _printerStatus = false;
         }

         return printerStatus;
      }

      private byte GetStatus(byte[] statusCmd)
      {
         // send the initialization command
         _serialPort.Write(statusCmd, 0, statusCmd.Length);

         // get status byte
         return (byte) _serialPort.ReadByte();
      }

      #endregion
   }

   public enum ExpressVoterPrinterStatusEnum : uint
   {
      /// <summary>No error.</summary>
      OK = 0,

      /// <summary>Offline</summary>
      OFFLINE,

      /// <summary>Out of paper.</summary>
      NO_PAPER,

      /// <summary>Device error.</summary>
      DEVICE_FAULT,

      /// <summary>Timeout.</summary>
      TIMEOUT,

      /// <summary>Paper jam.</summary>
      PAPERJAM,

      /// <summary>Print head up.</summary>
      HEAD_UP,

      /// <summary>Print head over heat.</summary>
      HEAD_TEMPERATURE_ERROR,

      /// <summary>Printer cover is open</summary>
      COVER_OPEN,

      /// <summary>Paper is upside down</summary>
      PAPER_UPSIDE_DOWN,

      /// <summary>Paper type is not supported. </summary>
      PAPER_TYPE_NOT_SUPPORTED
   }
}
