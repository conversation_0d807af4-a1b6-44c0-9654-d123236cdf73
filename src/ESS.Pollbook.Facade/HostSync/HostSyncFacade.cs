using ESS.ELLEGO.Rest.Stats;
using ESS.Pollbook.Components.Business.Host;
using ESS.Pollbook.Components.Business.IncrementalUpdates;
using ESS.Pollbook.Components.Business.PollbookTransaction;
using ESS.Pollbook.Components.Repository.ContentDeliveryNetwork;
using ESS.Pollbook.Components.Repository.Device;
using ESS.Pollbook.Components.Repository.PollbookTransaction;
using ESS.Pollbook.Components.Repository.SyncPointRepository;
using ESS.Pollbook.Components.Repository.VRSignatures;
using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.Messaging;
using ESS.Pollbook.Core.Model;
using ESS.Pollbook.Core.Model.Transactions;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.Device;
using ESS.Pollbook.Facade.Maintenance;
using ESS.Pollbook.Facade.PollbookTransaction;
using ESS.Pollbook.Hardware.Storage;
using ESS.Pollbook.MasterProcessor.Abstractions;
using ESS.Pollbook.MasterProcessor.Abstractions.Shared;
using GalaSoft.MvvmLight.Messaging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace ESS.Pollbook.Facade.HostSync
{
    public class HostSyncFacade : IHostSyncFacade
    {
        #region Member Variables

        private static bool IsHostSyncConnected =>
           SystemConfiguration.ElectionConfiguration.HostSync && SystemDetails.IsHostConnected;

        private readonly IEssLogger _essLogger;
        private readonly IMessenger _messenger;
        private readonly IDeviceFacade _deviceFacade;
        private readonly IMaintenanceFacade _maintenanceFacade;
        private readonly IPollbookTransactionFacade _pollbookTransactionFacade;
        private readonly IHostSyncFactory _hostSyncFactory;
        private readonly IIncrementalUpdatesFactory _incrementalUpdatesFactory;
        private readonly IPollbookTransactionFactory _pollbookTransactionFactory;
        private readonly ISyncPointRepository _syncPointRepository;
        private readonly IPollbookFailedTransactionRepository _pollbookFailedTransactionRepository;
        private readonly IDeviceRepository _deviceRepository;
        private readonly IPollbookTransactionRepository _pollbookTransactionRepository;
        private readonly IContentDeliveryNetworkRepository _contentDeliveryNetworkRepository;
        private readonly IVrSignaturesRepository _vrSignaturesRepository;
        private readonly IApiMonitor _apiMonitor;
        private readonly IPollbookQueue _pollbookQueue;

        private readonly string _className;

        #endregion

        public HostSyncFacade(
           IPollbookTransactionFacade pollbookTransactionFacade,
           IDeviceFacade deviceFacade,
           IMaintenanceFacade maintenanceFacade,
           IHostSyncFactory hostSyncFactory,
           IIncrementalUpdatesFactory incrementalUpdatesFactory,
           IPollbookTransactionFactory pollbookTransactionFactory,
           IEssLogger logger,
           IMessenger messenger,
           ISyncPointRepository syncPointRepository,
           IPollbookFailedTransactionRepository pollbookFailedTransactionRepository,
           IDeviceRepository deviceRepository,
           IPollbookTransactionRepository pollbookTransactionRepository,
           IContentDeliveryNetworkRepository contentDeliveryNetworkRepository,
           IVrSignaturesRepository vrSignaturesRepository,
           IApiMonitor apiMonitor,
           IPollbookQueue pollbookQueue)
        {
            _pollbookTransactionFacade = pollbookTransactionFacade;
            _deviceFacade = deviceFacade;
            _maintenanceFacade = maintenanceFacade;
            _hostSyncFactory = hostSyncFactory;
            _incrementalUpdatesFactory = incrementalUpdatesFactory;
            _pollbookTransactionFactory = pollbookTransactionFactory;
            _syncPointRepository = syncPointRepository;
            _pollbookFailedTransactionRepository = pollbookFailedTransactionRepository;
            _deviceRepository = deviceRepository;
            _pollbookTransactionRepository = pollbookTransactionRepository;
            _contentDeliveryNetworkRepository = contentDeliveryNetworkRepository;
            _vrSignaturesRepository = vrSignaturesRepository;
            _apiMonitor = apiMonitor;
            _essLogger = logger;
            _messenger = messenger;
            _pollbookQueue = pollbookQueue;
            _className = GetType().Name;
        }

        public async Task SetHostConnectedStatus(CancellationToken token = default)
        {
            if (token.IsCancellationRequested) return;

            SystemDetails.IsHostConnected =
               await _hostSyncFactory.GetSyncPointPingResponse(token).ConfigureAwait(false);

            if (!SystemDetails.IsHostConnected)
            {
                SystemDetails.DownloadInProgress = false;
                _messenger.Send(new DownloadInProgressMessage());
            }
        }

        public async Task<List<VoterDto>> GetVoterSearchResults(PollbookVoterSearchRequest req,
           CancellationToken token = default)
        {
            if (token.IsCancellationRequested || !IsHostSyncConnected) return null;

            var rest = _hostSyncFactory.CreateRESTHostVoterSearchRequest(req);
            return await _syncPointRepository
               .GetVoterSearchResultsFromHostAsync(rest)
               .ConfigureAwait(false);
        }

        public async Task<bool> SendFailedTransactionToHostAsync(
           IEnumerable<FailedTransactionDto> failed, CancellationToken token = default)
        {
            try
            {
                var tx = failed as FailedTransactionDto[] ?? failed.ToArray();
                if (tx.Length == 0) return true;

                foreach (var f in tx)
                {
                    if (token.IsCancellationRequested) break;

                    _essLogger.LogDebug(
                       $"SendFailedTransactionToHost() - Sending transaction: {f.TransactionGuid}");

                    var rest = _hostSyncFactory.CreateRESTHostFailedTransactionsSendToHost(f);
                    if (await _syncPointRepository.SubmitFailedTransactionAsync(rest)
                           .ConfigureAwait(false))
                    {
                        await Task.Run(() =>
                           _pollbookFailedTransactionRepository.UpdateSentToHostTransaction(
                              f.TransactionGuid), token).ConfigureAwait(false);
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                LogError(ex, nameof(SendFailedTransactionToHostAsync));
                return false;
            }
        }

        public async Task DownloadTransactionsAsync(CancellationToken token = default)
        {
            var swTotal = Stopwatch.StartNew();

            _essLogger.LogDebug($"###[DownloadTransactions] loop started at {DateTime.Now:hh:mm:ss t zz}.###");

            while (!IsValidState(token))
            {
                if (token.IsCancellationRequested) return;
                await Task.Delay(1000, token).ConfigureAwait(false);
            }

            var transactionCount = 0;

            do
            {
                while (!_pollbookQueue.Transactions.IsStandardQueueEmpty())
                {
                    if (token.IsCancellationRequested) return;
                    await Task.Delay(500, token).ConfigureAwait(false);
                }

                _essLogger.LogInformation("Attempting to download transactions from host...");

                try
                {
                    var batches = await FetchAndConvertHostTransactionsAsync()
                       .ConfigureAwait(false);

                    // Reset transaction count for this iteration
                    transactionCount = 0;

                    foreach (var b in batches)
                        transactionCount += b?.Count ?? 0;

                    if (transactionCount == 0)
                    {
                        DownloadNotification(0);
                        // Exit the loop if no transactions were found
                        break;
                    }

                    DownloadNotification(transactionCount);

                    if (!IsValidState(token)) continue;

                    await QueueSyncDataAndUpdateINIFile(batches, transactionCount)
                       .ConfigureAwait(false);
                }
                catch (Exception ex)
                {
                    LogError(ex, nameof(DownloadTransactionsAsync));
                    // On exception, exit the loop rather than continuing
                    break;
                }
            }
            while (transactionCount > 0 && !SystemDetails.InitialHostSyncDownloadCompleted);

            SystemDetails.InitialHostSyncDownloadCompleted = true;
            swTotal.Stop();
            _essLogger.LogDebug($"###[DownloadTransactions] loop completed. Total time: {swTotal.ElapsedMilliseconds}ms.###");
        }

        public async Task<bool> GetMissingZipFilesAsync()
        {
            try
            {
                var fileList = new List<string>();
                var filenames = await _vrSignaturesRepository.GetZipFileNameListAsync()
                   .ConfigureAwait(false);

                foreach (var filename in filenames)
                    if (!CheckFileExistsInStorageLocation(filename))
                        fileList.Add(Path.GetFileName(filename));

                if (fileList.Count == 0) return true;

                var uriList = await GetVRSignaturesUriAsync(fileList).ConfigureAwait(false);
                if (uriList.IsNullOrEmpty()) return true;

                using (var client = new WebClient())
                {
                    foreach (var uri in uriList)
                    {
                        var url = new Uri(uri);
                        var fileName = Path.GetFileName(url.AbsolutePath);
                        var dest = Path.Combine(StorageLocator.DefaultDbLocation, fileName);
                        await client.DownloadFileTaskAsync(url, dest).ConfigureAwait(false);
                    }
                }

                _essLogger.LogDebug("Finished retrieving zip files from CDN.");
                return true;
            }
            catch (Exception ex)
            {
                LogError(ex, nameof(GetMissingZipFilesAsync));
                return false;
            }
        }

        public Task<VoterDto> GetVoterDetailsResults(PollbookVoterDetailsRequest req) =>
           !IsHostSyncConnected
              ? Task.FromResult<VoterDto>(null)
              : _syncPointRepository.GetVoterDetailsFromHostAsync(
                 _hostSyncFactory.CreateRESTHostVoterDetailsRequest(req));

        public Task<TotalTransactionCountResponse> GetTransactionsCount(TotalTransactionRequest req) =>
           !SystemDetails.IsHostConnected
              ? Task.FromResult<TotalTransactionCountResponse>(null)
              : _syncPointRepository.GetTransactionCountFromHost(
                 _hostSyncFactory.CreateRESTHostTransactionTotalCountRequest(req));

        public Task<DevicePollPlaceResponse> GetPollPlaceAsync(PollingPlaceRequest req) =>
           !SystemDetails.IsHostConnected
              ? Task.FromResult<DevicePollPlaceResponse>(null)
              : _syncPointRepository.GetDevicePollPlaceFromHostAsync(
                 _hostSyncFactory.CreateRESTDevicePollPlaceRequest(req));

        public Task<List<TransactionModel>> GetVoterTransactions(PollbookVoterHistoryRequest req) =>
           (!IsHostSyncConnected || SystemDetails.IsHostSlow)
              ? Task.FromResult<List<TransactionModel>>(null)
              : _syncPointRepository.GetVoterTransactionsHistoryFromHostAsync(
                 _hostSyncFactory.CreateRESTHostVoterTransactionsRequest(req));

        public async Task<TransactionCdnModel> GetHostTransactionResponseAsync()
        {
            try
            {
                var transData = await GetTransactionData().ConfigureAwait(false);
                var restRequest = _hostSyncFactory.CreateRESTHostTransactionsRequest(
                   transData.MaxHostTransactionId,
                   transData.PollbookTransactionsCount,
                   transData.HostTransactionsCount,
                   transData.FailedTransactionsCount,
                   transData.UserIp);

                _essLogger.LogInformation(
                   $"GetHostTransactionsAsync() - Calling host sync service, last transaction = {transData.MaxHostTransactionId}");

                var response = await _syncPointRepository.GetVoterTransactionsFromHostAsync(restRequest)
                   .ConfigureAwait(false);

                _essLogger.LogInformation(
                   $"GetHostTransactionsAsync() - Got responses from host service, transactions count = {response?.Transactions?.Count ?? 0}, download a file? {(response?.DownloadUrl?.Any() == true ? "Yes" : "No")}.");

                return response ?? new TransactionCdnModel();
            }
            catch (Exception ex)
            {
                LogError(ex, nameof(GetHostTransactionResponseAsync));
                return new TransactionCdnModel();
            }
        }

        public async Task<bool> SendHeartbeatTransactionAsync(PollbookTransactionDto transaction,
           PowerStatusRequest powerStatusRequest)
        {
            transaction.TransactionType = nameof(TransactionType.Heartbeat);
            transaction.PollingPlaceId = LoggedInPollplaceInfo.LoggedInPollPlace.PollingPlaceId;

            var batch = _pollbookTransactionFactory.CreatePowerStatusTransaction(transaction, powerStatusRequest);
            return await SubmitHeartbeatTransactionRequest(batch.HostSyncTransactionRequest)
               .ConfigureAwait(false);
        }

        public async Task<bool> SendTestModeTransactionAsync(PollbookTransactionDto transaction,
           PowerStatusRequest powerStatusRequest)
        {
            var batch = await _pollbookTransactionFactory.CreateTestModeTransaction(transaction, powerStatusRequest)
               .ConfigureAwait(false);
            return await SubmitTestModeTransactionRequest(batch.HostSyncTransactionRequest)
               .ConfigureAwait(false);
        }

        public async Task<DownloadConfigurationsDto> GetConfigurationsUpdateAsync(DateTime lastUpdated)
        {
            if (!IsHostSyncConnected) return null;

            var rest = _hostSyncFactory.CreateRESTDownloadConfigurations(lastUpdated);
            return await _syncPointRepository.GetConfigurationsUpdateFromHostAsync(rest)
               .ConfigureAwait(false);
        }

        public async Task<bool> UpdateConfigDownloadedLogAsync()
        {
            if (!IsHostSyncConnected) return false;

            var last = await _pollbookTransactionRepository.GetLatestConfigurationAppliedDateTime()
               .ConfigureAwait(false);
            var rest = _hostSyncFactory.CreateRESTConfigLog(last);
            return await _syncPointRepository.UpdateConfigDownloadedLogToHostAsync(rest)
               .ConfigureAwait(false);
        }

        public async Task<bool> SubmitHeartbeatTransactionRequest(TransactionBatchRequest req,
           bool setInProcess = true)
        {
            if (!SystemConfiguration.ElectionConfiguration.HostSync) return false;

            try
            {
                var rest = _hostSyncFactory.CreateRESTRequest(req);
                var response = await _syncPointRepository.SubmitTransactionRequestAsync(rest)
                   .ConfigureAwait(false);

                return response?.Transactions?.FirstOrDefault()?.TransactionStatus ==
                       TransactionProcessingStatus.SuccessfullyProcessed;
            }
            catch (Exception ex)
            {
                LogError(ex, nameof(SubmitHeartbeatTransactionRequest));
                return false;
            }
        }


        public async Task<bool> SubmitTestModeTransactionRequest(TransactionBatchRequest req)
        {
            if (!SystemConfiguration.ElectionConfiguration.HostSync) return true;

            try
            {
                var rest = _hostSyncFactory.CreateRESTRequest(req);
                var response = await _syncPointRepository.SubmitTransactionRequestAsync(rest)
                   .ConfigureAwait(false);

                return response?.Transactions?.FirstOrDefault()?.TransactionStatus ==
                       TransactionProcessingStatus.SuccessfullyProcessed;
            }
            catch (Exception ex)
            {
                LogError(ex, nameof(SubmitTestModeTransactionRequest));
                return false;
            }
        }

        public async Task SubmitTransactionRequest(TransactionBatchRequest req, bool setInProcess = true)
        {
            _essLogger.LogDebug(
               $"*** SubmitTransactionRequest started - Transaction Count:{req?.Transactions?.Count() ?? 0}");
            if (!IsHostSyncConnected)
            {
                _essLogger.LogDebug("Host is not connected. Exiting SubmitTransactionRequest()");
                return;
            }

            try
            {
                var rest = _hostSyncFactory.CreateRESTRequest(req);

                var tranNum = 0;
                var tranGuid = new List<string>();

                foreach (var tran in req.Transactions)
                {
                    tranNum++;
                    tranGuid.Add(
                       JsonConvert.DeserializeObject<TransactionModel>(tran.TransactionJson)?.TransactionIdentifier);
                }

                var resp = await _syncPointRepository.SubmitTransactionRequestAsync(rest)
                   .ConfigureAwait(false);

                _essLogger.LogDebug(
                   $"SubmitTransactionRequest()::Submitted {tranNum} | Guids {string.Join(", ", tranGuid)} | HostResp {JsonConvert.SerializeObject(resp)}");

                foreach (var transaction in resp.Transactions)
                {
                    if (transaction.ServerTransactionId != 0)
                    {
                        await Task.Run(() => _pollbookTransactionFacade.UpdateHostTransactionId(transaction.TransactionIdentifier,
                            transaction.ServerTransactionId));
                    }
                    else
                    {
                        var errorMessage = string.Empty;
                        if (resp.StatusMessages.Count > 0)
                        {
                            // There is limitation in the server for the size of the error message
                            errorMessage = resp.StatusMessages[0].Length > 2048
                                ? resp.StatusMessages[0].Substring(0, 2048)
                                : resp.StatusMessages[0];
                        }

                        else if (transaction.StatusMessages.Count > 0)
                        {
                            var messageStringBuilder = new StringBuilder();
                            foreach (var message in transaction.StatusMessages)
                            {
                                messageStringBuilder.Append(" " + message);
                            }

                            var messages = messageStringBuilder.ToString();
                            // There is limitation in the server for the size of the error message
                            errorMessage = messages.Length > 2048 ? messages.Substring(0, 2048) : messages;
                        }

                        var logProps = new Dictionary<string, string>
                        {
                            { "Action", "Got an error while submitting transaction request to host;" },
                            { "Transaction Id", tranGuid[tranNum] }
                        };
                        _essLogger.LogError(errorMessage, logProps);
                    }
                }
            }
            catch (Exception ex)
            {
                LogError(ex, nameof(SubmitTransactionRequest));
            }

            _essLogger.LogDebug("SubmitTransactionRequest()::Done submitting transactions");
        }

        public async Task<List<string>> GetVRSignaturesUriAsync(List<string> fileList)
        {
            try
            {
                var ip = _deviceRepository.GetPublicIP();
                var rest = _hostSyncFactory.CreateRESTCDNFilesRequest(ip, fileList);
                return await _syncPointRepository.GetCdnUrisFromHostAsync(rest).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                LogError(ex, nameof(GetVRSignaturesUriAsync));
                return null;
            }
        }

        public Task<IEnumerable<FailedTransactionDto>> GetFailedTransactionsToUploadAsync(
           bool sendAll = false) =>
           _pollbookFailedTransactionRepository.GetFailedTransactionsForHostAsync(sendAll);

        public async Task UploadPendingTransactionsAsync(CancellationToken token = default)
        {
            if (token.IsCancellationRequested || !SystemDetails.IsHostConnected) return;

            _essLogger.LogInformation("UploadPendingTransactions");

            var local = await _pollbookTransactionFacade.GetPendingLocalTransactionsToUploadAsync()
               .ConfigureAwait(false);
            var list = local as PollbookTransactionDto[] ?? local.ToArray();
            if (list.Length == 0)
            {
                HostSyncDetails.HavePendingLocalTransactions = false;
                return;
            }

            HostSyncDetails.HavePendingLocalTransactions = true;

            try
            {
                var ok = await SendPendingTransactionToHost(list).ConfigureAwait(false);
                HostSyncDetails.HavePendingLocalTransactions = !ok;
            }
            catch (Exception ex)
            {
                LogError(ex, nameof(UploadPendingTransactionsAsync));
            }
        }

        public async Task<bool> UploadPendingTransactionsFromManageElectionAsync()
        {
            using (var src = new CancellationTokenSource())
            {
                await UploadPendingTransactionsAsync(src.Token).ConfigureAwait(false);
                await UploadFailedTransactionsAsync(src.Token, true).ConfigureAwait(false);
            }

            return !HostSyncDetails.HavePendingLocalTransactions;
        }

        public async Task UploadFailedTransactionsAsync(CancellationToken token = default,
           bool sendAll = false)
        {
            if (!SystemConfiguration.ElectionConfiguration.HostSync || !SystemDetails.IsHostConnected)
            {
                _essLogger.LogDebug("UploadFailedTransactions() skipped, host sync is off or host not connected.");
                return;
            }

            if (token.IsCancellationRequested) return;

            _essLogger.LogInformation("UploadFailedTransactions");

            try
            {
                var failed = await GetFailedTransactionsToUploadAsync(sendAll).ConfigureAwait(false);
                var ok = await SendFailedTransactionToHostAsync(failed, token).ConfigureAwait(false);
                HostSyncDetails.HavePendingLocalTransactions = !ok;
            }
            catch (Exception ex)
            {
                LogError(ex, nameof(UploadFailedTransactionsAsync));
            }
        }

        public async Task ProcessVoterSignaturesAsync(CancellationToken token = default)
        {
            if (!IsValidState(token)) return;

            _essLogger.LogInformation("ProcessVoterSignatures");

            try
            {
                var zipOk = await GetMissingZipFilesAsync().ConfigureAwait(false);
                if (zipOk)
                {
                    // additional signature processing omitted for brevity
                    await Task.Yield();
                }
            }
            catch (Exception ex)
            {
                LogError(ex, nameof(ProcessVoterSignaturesAsync));
            }
        }

        public async Task SendHeartbeatTransactionAsync(CancellationToken token = default)
        {
            if (!IsValidState(token)) return;

            _essLogger.LogInformation("Heartbeat Tick");

            try
            {
                var transaction = PollbookTransactionDto.Create();
                transaction.SystemIdentifier = _maintenanceFacade.GetSystemId();
                transaction.PollingPlaceId = LoggedInPollplaceInfo.LoggedInPollPlace.PollingPlaceId;
                transaction.DeviceName = SystemDetails.MachineName;
                transaction.SerialNumber = SystemDetails.DeviceSerialNumber;

                var batteryCharge = Convert.ToInt32(_deviceFacade.GetBatteryPercentRemaining());
                var isCharging = _deviceFacade.GetIsBatteryCharging();

                var powerStatus = new PowerStatusRequest
                {
                    BatteryCharge = batteryCharge,
                    ACOnline = isCharging,
                    Memory = string.Empty
                };

                SystemDetails.IsHostConnected =
                   await SendHeartbeatTransactionAsync(transaction, powerStatus).ConfigureAwait(false);

                if (!SystemDetails.IsHostConnected)
                {
                    SystemDetails.DownloadInProgress = false;
                    _messenger.Send(new DownloadInProgressMessage());
                }
            }
            catch (Exception ex)
            {
                LogError(ex, nameof(SendHeartbeatTransactionAsync));
            }
        }

        public async Task HostSyncConnectionRetryAsync(CancellationToken token = default)
        {
            if (SystemConfiguration.ElectionConfiguration.HostSync && !SystemDetails.IsHostConnected)
                await SetHostConnectedStatus(token).ConfigureAwait(false);
        }

        private async Task<List<List<TransactionModel>>> FetchAndConvertHostTransactionsAsync()
        {
            var response = await GetHostTransactionResponseAsync().ConfigureAwait(false);

            var urls = response.DownloadUrl;

            if (urls.IsNullOrEmpty())
            {
                return new List<List<TransactionModel>>(1)
            {
               response.Transactions ?? new List<TransactionModel>()
            };
            }

            var results = new List<List<TransactionModel>>();
            foreach (var url in urls)
            {
                try
                {
                    _essLogger.LogDebug($"Downloading transaction file: {url}");

                    var result = await _contentDeliveryNetworkRepository
                       .GetFileFromServerAsync(new CDNDownloadFileRequest(url, SystemDetails.PQCPassword))
                       .ConfigureAwait(false);
                    results.Add(result ?? new List<TransactionModel>());

                    _essLogger.LogDebug($"Completed download of transaction file with {results.Last().Count} transactions");
                }
                catch (Exception ex)
                {
                    _essLogger.LogError(ex, new Dictionary<string, string>(1) { { "Action", "FetchAndConvertHostTransactionsAsync" } });
                    results.Add(new List<TransactionModel>());
                }
            }

            return results;
        }

        private async Task QueueSyncDataAndUpdateINIFile(List<List<TransactionModel>> batches,
           int transactionCount)
        {
            _essLogger.LogDebug(
               $"DownloadTransactions - Got {batches.Count} batches with {transactionCount} total transactions.");

            if (transactionCount == 0 || batches.Count == 0)
            {
                _messenger.Send(new UpdateSystemStatsMessage());
                return;
            }

            // cache references for faster access
            var txQueue = _pollbookQueue.Transactions;
            var pdQueue = _pollbookQueue.Polldata;

            // Pre-allocate queue items list to avoid repeated allocations
            var queueItems = new List<(PollbookQueueItem txItem, PollbookQueueItem pdItem)>(batches.Count);

            // Process all batches first (CPU-bound work)
            for (int i = 0; i < batches.Count; i++)
            {
                var hostTransactions = batches[i];
                if (hostTransactions.IsNullOrEmpty())
                    continue;

                var transactions = _incrementalUpdatesFactory.SetupTransactionsFromHostSync(hostTransactions);

            // Create queue items with pre-sized dictionaries
            var txItem = new PollbookQueueItem
            {
               QueueParams = new Dictionary<string, object>(1) { [QueueParamKeyConstants.INCREMENTAL_UPDATE_TRANSACTIONS_KEY] = transactions },
               ItemQueueProcess = QueueProcess.HostSyncTransaction,
               ItemQueueType = QueueType.Transaction,
               ItemQueuePriority = QueuePriority.Standard
            };

            var pdItem = new PollbookQueueItem
            {
               QueueParams = new Dictionary<string, object>(1) { [QueueParamKeyConstants.INCREMENTAL_UPDATE_POLLDATA_TRANSACTIONS_KEY] = transactions },
               ItemQueueProcess = QueueProcess.HostSyncVoterPolldata,
               ItemQueueType = QueueType.Polldata,
               ItemQueuePriority = QueuePriority.Standard
            };

                queueItems.Add((txItem, pdItem));
            }

            // Batch enqueue operations
            for (int i = 0; i < queueItems.Count; i++)
            {
                var (txItem, pdItem) = queueItems[i];
                txQueue.Enqueue(txItem);
                pdQueue.Enqueue(pdItem);
            }

            // Single INI file update at the end instead of per batch
            if (queueItems.Count > 0)
            {
                await _incrementalUpdatesFactory.UpdateElectionSettingsIniFile().ConfigureAwait(false);
            }

            _messenger.Send(new UpdateSystemStatsMessage());
        }

        private void DownloadNotification(long count)
        {
            SystemDetails.DownloadInProgress =
               count >= SystemConfiguration.ElectionConfiguration.VoterTransactionsWaitSeconds;

            _messenger.Send(new DownloadInProgressMessage());
        }

        private async Task<TransactionData> GetTransactionData()
        {
            return new TransactionData
            {
                MaxHostTransactionId =
                  (await _pollbookTransactionFacade.GetMaxHostTransactionIdAsync(SystemDetails.SystemId)
                     .ConfigureAwait(false)).HostTransactionId,
                PollbookTransactionsCount =
                  await _pollbookTransactionFacade.GetPollbookTransactionsCountAsync().ConfigureAwait(false),
                HostTransactionsCount =
                  await _pollbookTransactionFacade.GetHostTransactionsCountAsync().ConfigureAwait(false),
                FailedTransactionsCount =
                  await _pollbookTransactionFacade.GetFailedTransactionsCountAsync().ConfigureAwait(false),
                UserIp = _deviceRepository.GetPublicIP()
            };
        }

        public void TurnOnApiMonitor()
        {
            if (SystemConfiguration.ElectionConfiguration?.HostSync != true) return;

            if (_apiMonitor.TurnOn(
                   TimeSpan.FromMilliseconds(
                      SystemConfiguration.ElectionConfiguration.ApiMonitorUpdateIntervalMilliseconds),
                   TimeSpan.FromMilliseconds(SystemConfiguration.ElectionConfiguration
                      .ApiMonitorCacheLifetimeMilliseconds)))
            {
                _essLogger.LogInformation("API Monitor has been turned on or is already enabled.");
            }
            else
            {
                _essLogger.LogError("Failed to turn on the API monitor.");
            }
        }

        private static bool CheckFileExistsInStorageLocation(string filename)
        {
            var fi = (!filename.StartsWith(StorageLocator.DefaultDbLocation) &&
                      string.IsNullOrEmpty(Path.GetDirectoryName(filename)))
               ? new FileInfo(Path.Combine(StorageLocator.DefaultDbLocation, filename))
               : new FileInfo(filename);

            return fi.Exists;
        }

        public async Task<bool> SendPendingTransactionToHost(IEnumerable<PollbookTransactionDto> transactionDtos)
        {
            try
            {
                foreach (var transaction in transactionDtos)
                {
                    if (!SystemDetails.IsHostConnected)
                    {
                        _essLogger.LogError("Not connected to host. Stopping upload.");
                        return false;
                    }

                    var request = PollbookTransactionRequest.Create(transaction);

                    _essLogger.LogDebug(
                       $"SendPendingTransactionToHost() - Sending transaction: {transaction.TransactionGuid}");
                    await SubmitTransactionRequest(request, setInProcess: false)
                       .ConfigureAwait(false);
                }

                return true;
            }
            catch (Exception ex)
            {
                LogError(ex, nameof(SendPendingTransactionToHost));
                return false;
            }
        }

        protected class TransactionData
        {
            public long MaxHostTransactionId { get; set; }
            public long PollbookTransactionsCount { get; set; }
            public long HostTransactionsCount { get; set; }
            public long FailedTransactionsCount { get; set; }
            public string UserIp { get; set; }
        }

        public Task<CheckListResponse> GetChecklistAsync() =>
           !IsHostSyncConnected
              ? Task.FromResult<CheckListResponse>(null)
              : _syncPointRepository.MakePostToHostAsync<CheckListResponse>(
                 _hostSyncFactory.CreateRESTChecklist());

        public Task<bool> CheckRegionalResultsFileAsync() =>
           !IsHostSyncConnected
              ? Task.FromResult(false)
              : _syncPointRepository.MakePostToHostAsync<bool>(
                 _hostSyncFactory.CreateRestRegionalResultsCheckFileRequest());

        public Task<byte[]> GetRegionalResultsFileAsync() =>
           !IsHostSyncConnected
              ? Task.FromResult<byte[]>(null)
              : _syncPointRepository.MakePostToHostAsync<byte[]>(
                 _hostSyncFactory.CreateRestRegionalResultsFileRequest());

        public Task<bool> CheckNewFileExists(DateTime? creationDate) =>
           !IsHostSyncConnected
              ? Task.FromResult(false)
              : _syncPointRepository.MakePostToHostAsync<bool>(
                 _hostSyncFactory.CreateRestRegionalResultsNewFileCheckRequest(creationDate));

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        private static bool IsValidState(CancellationToken token = default) =>
           SystemConfiguration.ElectionConfiguration?.HostSync == true &&
           SystemDetails.AppState == ApplicationState.SignIn &&
           SystemDetails.IsHostConnected &&
           !token.IsCancellationRequested;

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        private void LogError(Exception ex, string method) =>
           _essLogger.LogError(ex, new Dictionary<string, string>(1)
           {
               ["Action"] = $"{_className}.{method}"
           });
    }
}