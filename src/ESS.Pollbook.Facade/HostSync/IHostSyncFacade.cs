using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Model;
using ESS.Pollbook.Core.Model.Transactions;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace ESS.Pollbook.Facade.HostSync
{
   public interface IHostSyncFacade
   {
	   Task SetHostConnectedStatus(CancellationToken token = default);

	   Task SubmitTransactionRequest(TransactionBatchRequest request, bool setInProcess = true);

      Task<bool> SubmitHeartbeatTransactionRequest(TransactionBatchRequest request, bool setInProcess = true);

      Task<bool> SubmitTestModeTransactionRequest(TransactionBatchRequest request);

      Task<List<TransactionModel>> GetVoterTransactions(PollbookVoterHistoryRequest pollbookVoterHistoryRequest);

      Task<bool> SendPendingTransactionToHost(IEnumerable<PollbookTransactionDto> transactionDtos);

      Task<List<VoterDto>> GetVoterSearchResults(PollbookVoterSearchRequest pollbookVoterSearchRequest, CancellationToken cancellationToken = default);

      Task<VoterDto> GetVoterDetailsResults(PollbookVoterDetailsRequest pollbookVoterDetailsRequest);

      Task<TotalTransactionCountResponse> GetTransactionsCount(TotalTransactionRequest totalTransactionRequest);

      Task<List<string>> GetVRSignaturesUriAsync(List<string> fileList);

      Task<IEnumerable<FailedTransactionDto>> GetFailedTransactionsToUploadAsync(bool sendAllFailedTransactions = false);

      Task<bool> SendFailedTransactionToHostAsync(IEnumerable<FailedTransactionDto> failedTransactionDtos, CancellationToken token = default);

      Task<DownloadConfigurationsDto> GetConfigurationsUpdateAsync(DateTime lastUpdatedDateTime);

      Task<bool> UpdateConfigDownloadedLogAsync();

      Task SendHeartbeatTransactionAsync(CancellationToken token = default);

      Task<bool> SendHeartbeatTransactionAsync(PollbookTransactionDto transaction, PowerStatusRequest powerStatusRequest);

      Task<CheckListResponse> GetChecklistAsync();

      Task<bool> CheckRegionalResultsFileAsync();

      Task<byte[]> GetRegionalResultsFileAsync();

      Task<bool> CheckNewFileExists(DateTime? creationDate);

      Task<bool> SendTestModeTransactionAsync(PollbookTransactionDto transaction, PowerStatusRequest powerStatusRequest);

      Task HostSyncConnectionRetryAsync(CancellationToken token = default);

      Task UploadFailedTransactionsAsync(CancellationToken token = default, bool sendAllFailedTransactions = false);

      Task UploadPendingTransactionsAsync(CancellationToken token = default);

      Task DownloadTransactionsAsync(CancellationToken token = default);

      Task ProcessVoterSignaturesAsync(CancellationToken token = default);

      Task<bool> UploadPendingTransactionsFromManageElectionAsync();

      Task<bool> GetMissingZipFilesAsync();

      Task<DevicePollPlaceResponse> GetPollPlaceAsync(PollingPlaceRequest pollingPlaceRequest);

      void TurnOnApiMonitor();
   }
}
