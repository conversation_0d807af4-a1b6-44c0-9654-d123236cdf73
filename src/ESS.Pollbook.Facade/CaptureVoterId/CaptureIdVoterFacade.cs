using ESS.Pollbook.Components.Repository.CaptureVoterId;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Threading.Tasks;

namespace ESS.Pollbook.Facade.CaptureVoterId
{
	internal class CaptureIdVoterFacade : ICaptureIdVoterFacade, IDisposable
	{
		private readonly ICaptureIdVoterRepository _captureId;
		private readonly IEssLogger _logger;

		/// <summary>
		/// Stores the last update of the configuration.  If the configuration is updated,
		/// the cache will automagically flush and get reloaded in case the values are updated.
		/// </summary>
		private string _cacheConfigurationVersion = string.Empty;

		/// <summary>
		/// We utilize a standard list with a semaphore rather than utilizing a Concurrent structure
		/// as the semaphore is used as our blocking mechanism.  Also allows us to perfectly order the
		/// list for direct usage without ViewModel having to sort.  There is very little contention
		/// for this data which also is a reason of keeping it with a List.
		/// </summary>
		private readonly List<ElectionCaptureIdVoterTypeDbo> _cache = new List<ElectionCaptureIdVoterTypeDbo>();

		private SemaphoreSlim _semaphore = new SemaphoreSlim(1, 1);
		private bool _disposed;

		public CaptureIdVoterFacade(IEssLogger logger, ICaptureIdVoterRepository captureId)
		{
			_logger = logger;
			_captureId = captureId;
		}

		/// <summary>
		/// This will retrieve the list of capture types, presorted and cleaned of irregularities.
		/// The list is also cloned on return as to not create a direct reference to the cache.
		/// </summary>
		public async Task<IList<ElectionCaptureIdVoterTypeDbo>> RetrieveCaptureIdTypes(bool reloadCache = false)
		{
			var waitResult = await _semaphore.WaitAsync(5000);
			if (!waitResult)
			{
				// Few options here.  Means the Polldata.db3 is locked and can not retrieve values
				_logger.LogError("Failed to retrieve capture id types from database due to a lock on db.");
				return new List<ElectionCaptureIdVoterTypeDbo>(_cache);
			}

			try
			{
				if (HasConfigurationChanged() || reloadCache)
					CacheClear();

				if (_cache.Any())
					return new List<ElectionCaptureIdVoterTypeDbo>(_cache);

				CacheSet((List<ElectionCaptureIdVoterTypeDbo>)await _captureId.GetCaptureIdVoterTypes());
			}
			catch (Exception ex)
			{
				_logger.LogError(ex,
					new Dictionary<string, string>
						{ { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
			}
			finally
			{
				_semaphore.Release();
			}

			return new List<ElectionCaptureIdVoterTypeDbo>(_cache);
		}

		/// <summary>
		/// If configuration has been updated, clear the cache.  Subsequent code will then notice
		/// it is empty and attempt to populate it. Ensure wrapped in semaphore.
		/// </summary>
		[MethodImpl(MethodImplOptions.AggressiveInlining)]
		private bool HasConfigurationChanged()
		{
			return !_cacheConfigurationVersion.Equals(Core.StaticValues.ElectionSettings.ConfigurationVersion);
		}

		/// <summary>
		/// Ensure this is wrapped in a semaphore always.  This method also cooks up the
		/// values exactly how they are to be.  Any modifications that are required on the
		/// capture ID types should be made right here.
		/// </summary>
		[MethodImpl(MethodImplOptions.AggressiveInlining)]
		private void CacheSet(List<ElectionCaptureIdVoterTypeDbo> captureIds)
		{
			CacheClear();

			// Null or empty, clear the cache
			if (captureIds?.Any() != true)
			{
				_logger.LogInformation("Setting cache with an empty result.");
				return;
			}

			// Three calls for simplicity
			captureIds.RemoveAll(x => string.IsNullOrWhiteSpace(x.Capture_Voter_ID_Type_Name));
			captureIds.ForEach(x => x.Capture_Voter_ID_Type_Name = x.Capture_Voter_ID_Type_Name.Trim());
			captureIds.ForEach(x =>
				x.Capture_Voter_ID_Type_Form_Name = string.IsNullOrWhiteSpace(x.Capture_Voter_ID_Type_Form_Name)
					? string.Empty
					: x.Capture_Voter_ID_Type_Form_Name.Trim());

			// Ensure that FormName is null if white spaces and the list is sorted.
			// Note:  Sort Order is nullable and should they be null in db those will be at top of the list.
			_cache.AddRange(captureIds.OrderBy(z => z.Capture_Voter_ID_Type_Sort_Order));
			_cacheConfigurationVersion = Core.StaticValues.ElectionSettings.ConfigurationVersion;

			_logger.LogInformation($"Populating capture id type cache for version {_cacheConfigurationVersion}");
		}

		/// <summary>
		/// Ensure this is wrapped in a semaphore always.
		/// </summary>
		[MethodImpl(MethodImplOptions.AggressiveInlining)]
		private void CacheClear()
		{
			_logger.LogInformation("Clearing capture id type cache.");
			_cache.Clear(); // retrain capacity
			_cacheConfigurationVersion = string.Empty;
		}

		public void Dispose()
		{
			Dispose(true);
			GC.SuppressFinalize(this);
		}

		protected virtual void Dispose(bool disposing)
		{
			if (_disposed)
				return;

			if (disposing)
			{
				_semaphore?.Dispose();
				_semaphore = null;
			}

			_disposed = true;
		}
	}
}
