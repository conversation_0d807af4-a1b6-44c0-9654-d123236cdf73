using ESS.Pollbook.Components.Business.BallotCancellationStateTransition;
using System.Threading.Tasks;

namespace ESS.Pollbook.Facade.BallotCancellationStateTransition
{
    public class BallotCancellationSateTransitionFacade : IBallotCancellationStateTransitionFacade
    {
        private readonly IBallotCancellationStateTransitionFactory _factory;

        public BallotCancellationSateTransitionFacade(IBallotCancellationStateTransitionFactory factory)
        {
            _factory = factory;
        }

        public async Task<int> GetCancelledStateAsync(int currentState)
        {
            return await _factory.GetCancelledStateAsync(currentState);
        }
    }
}
