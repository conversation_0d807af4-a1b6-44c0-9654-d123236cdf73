
using ESS.Pollbook.Components.Business.Host;
using ESS.Pollbook.Components.Repository.SyncPointRepository;
using ESS.Pollbook.Components.Repository.Users;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace ESS.Pollbook.Facade.User
{
    public class UserFacade : IUserFacade
    {
        private readonly IEssLogger _essLogger;
        private readonly IHostSyncFactory _hostSyncFactory;
        private readonly IUserRepository _userRepository;
        private readonly ISyncPointRepository _syncPointRepository;

        public UserFacade(IEssLogger essLogger, IHostSyncFactory hostSyncFactory, IUserRepository userRepository, ISyncPointRepository syncPointRepository)
        {
            _hostSyncFactory = hostSyncFactory;
            _userRepository = userRepository;
            _syncPointRepository = syncPointRepository;
            _essLogger = essLogger;
        }

        public async Task GetUserUpdatesAsync()
        {
            if (!SystemConfiguration.ElectionConfiguration.HostSync || !SystemDetails.IsHostConnected)
                return;

            try
            {
                var maxDatetime = await _userRepository.GetMaxUserDateTimeAsync();

                var restRequest = _hostSyncFactory.CreateRESTHostUsersRequest(maxDatetime);

                var response = await _syncPointRepository.GetUsersFromHostAsync(restRequest);
                if (response != null && response.Users.Any())
                {
                    foreach (UserDto user in response.Users.ToList<UserDto>())
                    {
                        await Task.Run(() => _userRepository.UpsertUser(user));
                    }
                }
            }
            catch (Exception ex)
            {
                Dictionary<string, string> logProps = new Dictionary<string, string>();
                logProps.Add("Action", "GetUserUpdatesAsync()");
                _essLogger.LogError(ex, logProps);
            }
        }

        public async Task<bool> AreCurrentUsersValidAsync()
        {
            bool result = true;
            try
            {
	            var user1 = CurrentUserInfo.LoggedInUser;
	            if (user1 != null)
                {
                    var user = await _userRepository.GetUserByUserNameAsync(user1.Username);
                    if (user == null || user.UserDeletedIndicator)
                    {
                        result = false;
                    }
                }

	            var user2 = CurrentUserInfo.LoggedInUser2;
	            if (user2 != null)
                {
                    var user = await _userRepository.GetUserByUserNameAsync(user2.Username);
                    if (user == null || user.UserDeletedIndicator)
                    {
                        result = false;
                    }
                }
            }
            catch (Exception ex)
            {
                Dictionary<string, string> logProps = new Dictionary<string, string>();
                logProps.Add("Action", "AreCurrentUsersValidAsync()");
                _essLogger.LogError(ex, logProps);

                result = false;
            }

            return result;
        }
    }
}
