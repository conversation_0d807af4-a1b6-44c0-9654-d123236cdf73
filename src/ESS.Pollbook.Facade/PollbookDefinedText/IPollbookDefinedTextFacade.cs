using ESS.Pollbook.Core.DTO;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ESS.Pollbook.Facade.PollbookDefinedText
{
   public interface IPollbookDefinedTextFacade
   {
      Task<IEnumerable<ElectionPollbookDefinedTextDto>> LoadPollbookDefinedTextFromDb();

      ValueTask<List<string>> GetPollbookDefinedTextLanguagesAsync();

      string GetPollbookDefinedTextForLanguage(string textName, string language);

      string GetPollbookDefinedTextForLanguageWithDefault(string textName, string language, string textDefault);

      string GetPollbookDefinedTextForLanguageWithDefaultLanguage(string textName, string language,
         string defaultLanguage, string defaultText = null);

      void SetPollbookDefinedTextCurrentLanguage(string language);

      string GetPollbookDefinedTextCurrentLanguage();

      Task<ElectionPollbookDefinedTextDto> GetPollbookDefinedTextForControlIdAndLanguage(int controlId, string language);

      Task<string> GetPollbookDefinedTextForControlIdAndLanguageAsync(int controlId, string language);
   }
}