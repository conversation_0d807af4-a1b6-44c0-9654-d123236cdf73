using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Model;
using ESS.Pollbook.Core.Model.VoterEligibility;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ESS.Pollbook.Facade.VoterEligibility
{
    public interface IVoterEligibilityFacade
    {
        Task<EligibilityCalculationResult> Calculate(VoterDto voter,
            PollPlaceDto pollPlaceDto,
            SystemStatsDto systemStats,
            VoterP2PStatusModel voterStatus,
            bool forSearchResultsPage,
            List<ElectionJurisdictionEnumValueDto> electionJurisdictionEnumValueDtos, bool forceReload = false);

        bool MustVerifyId(System.DateTime? ballotRecordUpdateApplicationDatetime);

        Task<DateTime?> CalculateBallotRecordUpdateApplicationDatetimeFromLocalDb(string sourceKey);
    }


}
