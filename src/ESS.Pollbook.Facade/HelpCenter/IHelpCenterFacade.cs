using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Model.HelpCenter;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ESS.Pollbook.Facade.HelpCenter
{
    public interface IHelpCenterFacade
    {
        Task GetHelpUpdatesAsync(bool forceRefresh = false);
        string GetHelpCenterFolder();
        ValueTask<IEnumerable<HelpCenterItem>> GetHelpFileList(bool forceRefresh = false);
        Task<HelpFileCdnResponse> GetHelpFileUrisFromHost(List<HelpCenterManagementDto> records, string styleSheetName);
    }
}
