using ESS.Pollbook.Core.DTO;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ESS.Pollbook.Facade.VoterTransaction
{
    public interface IVoterTransactionFacade
    {
        Task<IEnumerable<VoterHistoryDto>> GetVoterHistoryAsync(string voterKey);

        Task<bool> IssueBallot(VoterDto voter, PollPlaceDto pollPlace);

        Task<bool> IssueEarlyVoteBallot(VoterDto voter, VoterDto previousVoter, PollPlaceDto pollPlace);
    }
}
