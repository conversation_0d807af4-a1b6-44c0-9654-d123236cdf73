using System;
using System.Security.Cryptography;

namespace ESS.Pollbook.Facade.Cryptography
{
    public class EssSalt : IEssSalt
    {
        public string GenerateSalt(int saltLength)
        {
            var salt = new byte[saltLength];
            using (var rng = RandomNumberGenerator.Create())
            {
                rng.GetBytes(salt);
            }

            return Convert.ToBase64String(salt);
        }
    }
}
