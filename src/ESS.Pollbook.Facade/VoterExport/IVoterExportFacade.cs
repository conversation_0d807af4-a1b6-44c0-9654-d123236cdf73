using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Model;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ESS.Pollbook.Facade.VoterExport
{
    public interface IVoterExportFacade
    {
        Task<bool> ExportVotedListAsync(List<VotedListItemDto> votedList, bool filteredList);

        Task<bool> ExportPollingPlaceVoterListAsync(List<PollingPlaceVoterListItemDto> voterList);

        Task<bool> ExportBallotReissueListAsync(BallotReissuedListCount ballotReissuedListCount);

        Task<bool> ExportSpoiledBallotReportAsync(Dictionary<string, int> counts);
    }
}
