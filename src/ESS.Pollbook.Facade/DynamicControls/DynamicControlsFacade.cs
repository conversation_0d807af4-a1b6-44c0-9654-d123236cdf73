using ESS.Pollbook.Components.Repository.DynamicControls;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Logging;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ESS.Pollbook.Facade.DynamicControls
{
    public class DynamicControlsFacade : IDynamicControlsFacade
    {
        private readonly IDynamicControlsRepository _dynamicControlsRepository;
        private readonly IEssLogger _essLogger;

        public DynamicControlsFacade(IDynamicControlsRepository dynamicControlsRepository, IEssLogger essLogger)
        {
            _dynamicControlsRepository = dynamicControlsRepository;
            _essLogger = essLogger;
        }

        public async Task<IEnumerable<DynamicControlsDto>> GetDynamicControlsByFormNameAsync(string formName)
        {
            _essLogger.LogInformation("Fetching dynamic controls by form name only (no voter ID).");
            return await _dynamicControlsRepository.GetDynamicControlsByFormNameAsync(formName);
        }
        public async Task<IEnumerable<DynamicControlsDto>> GetDynamicControlsByFormNameAndVoterIDAsync(string formName, long? voterId)
        {
            _essLogger.LogInformation("Fetching dynamic controls by form name and voter ID.");
            return await _dynamicControlsRepository.GetDynamicControlsByFormNameAndVoterIDAsync(formName, voterId);
        }

        public async Task<IEnumerable<DynamicControlsDto>> GetDynamicControlsWithAffidavitsDataByFormNameAsync(string formName)
        {
            return await _dynamicControlsRepository.GetDynamicControlsWithAffidavitsDataByFormNameAsync(formName);
        }
    }
}
