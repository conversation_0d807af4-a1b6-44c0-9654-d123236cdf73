using ESS.Pollbook.Components.Business.IncrementalUpdates;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Model.Transactions;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Hardware.Storage;
using System;
using System.IO;
using System.Threading.Tasks;

namespace ESS.Pollbook.Facade.IncrementalUpdates
{
    public class IncrementalUpdatesFacade : IIncrementalUpdatesFacade
    {
        private readonly IIncrementalUpdatesFactory _incrementalUpdatesFactory;

        public IncrementalUpdatesFacade(IIncrementalUpdatesFactory incrementalUpdatesFactory)
        {
            _incrementalUpdatesFactory = incrementalUpdatesFactory;
        }

        public async Task<LocalResponse> DownloadIncrementalUpdatesUsbAsync()
        {
            if (CurrentUserInfo.LoggedInUser == null)
                CurrentUserInfo.LoggedInUser = new UserDto { Username = StorageLocator.INCREMENTAL_UPDATE_USB_USERNAME };

            var localResponse = await _incrementalUpdatesFactory.ApplyIncrementalUpdatesUsbAsync();

            if (CurrentUserInfo.LoggedInUser.Username == StorageLocator.INCREMENTAL_UPDATE_USB_USERNAME)
                CurrentUserInfo.LoggedInUser = null;

            return localResponse;
        }

        public async Task<DateTime?> GetLastUpdateDateTimeAsync()
        {
            return await _incrementalUpdatesFactory.GetLastUpdatedDateTimeAsync();
        }

        public async Task<LocalResponse> ApplyAutoIncrementalUpdatesAsync(string filename)
        {
            if (CurrentUserInfo.LoggedInUser == null)
                CurrentUserInfo.LoggedInUser = new UserDto { Username = StorageLocator.INCREMENTAL_UPDATE_AUTO_USERNAME };

            var localResponse = await _incrementalUpdatesFactory.ApplyAutoIncrementalUpdatesAsync(filename);

            if (CurrentUserInfo.LoggedInUser.Username == StorageLocator.INCREMENTAL_UPDATE_AUTO_USERNAME)
                CurrentUserInfo.LoggedInUser = null;

            FileInfo fi = new FileInfo(filename);
            var resultTxtFile = Path.Combine(fi.DirectoryName, (localResponse.IsSuccess ? "Complete_" : "Error_") + fi.Name.Replace(".zip", ".txt"));
            var resultMessage = localResponse.IsSuccess ? "Auto Incremental Updates Completed successfully" : localResponse.LocalException.Message;
            File.Delete(filename);
            File.WriteAllText(resultTxtFile, resultMessage);
            return localResponse;
        }
    }
}