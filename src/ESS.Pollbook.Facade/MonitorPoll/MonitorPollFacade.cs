using ESS.Pollbook.Components.Repository.PollbookTransaction;
using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.HostSync;
using ESS.Pollbook.Facade.Maintenance;
using ESS.Pollbook.Facade.PollbookTransaction;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;

namespace ESS.Pollbook.Facade.MonitorPoll
{
   public class MonitorPollFacade : IMonitorPollFacade
   {
      private readonly IMaintenanceFacade _maintenanceFacade;
      private readonly IPollbookTransactionFacade _pollbookTransactionFacade;
      private readonly IHostSyncFacade _hostSyncFacade;
      private readonly IPollbookTransactionRepository _pollbookTransactionRepository;
      private readonly IEssLogger _essLogger;

      public MonitorPollFacade(IMaintenanceFacade maintenanceFacade,
         IPollbookTransactionFacade pollbookTransactionFacade, IHostSyncFacade hostSyncFacade,
         IPollbookTransactionRepository pollbookTransactionRepository, IEssLogger essLogger)
      {
         _maintenanceFacade = maintenanceFacade;
         _pollbookTransactionFacade = pollbookTransactionFacade;
         _hostSyncFacade = hostSyncFacade;
         _pollbookTransactionRepository = pollbookTransactionRepository;
         _essLogger = essLogger;
      }

      public async Task ClosePollAsync(int pollingPlaceId)
      {
         var sw = new Stopwatch();
         sw.Start();
         var transaction = PollbookTransactionDto.Create();
         transaction.SystemIdentifier = _maintenanceFacade.GetSystemId();
         transaction.PollingPlaceId = pollingPlaceId;
         transaction.DeviceName = SystemDetails.MachineName;
         transaction.SerialNumber = SystemDetails.DeviceSerialNumber;
         transaction.ProcessingStatus = nameof(ProcessingStatus.Ignored);
         transaction.RecordSource = nameof(ProcessingRecordSource.Pollbook);
         _essLogger.LogDebug($"ClosePollAsync - creating object [elapsed time:{sw.ElapsedMilliseconds}ms]");

         sw.Restart();
         var response = await _pollbookTransactionFacade.CreatePollsClosedTransaction(transaction, CurrentUserInfo.LoggedInUser.Username);
         _essLogger.LogDebug($"ClosePollAsync - creating transaction [elapsed time:{sw.ElapsedMilliseconds}ms]");

         if (response.TransactionInsertSuccess && !HostSyncDetails.HavePendingLocalTransactions)
         {
            sw.Restart();

            //Send the transaction to SyncPoint unless we have pending transactions
            await _hostSyncFacade.SubmitTransactionRequest(response.HostSyncTransactionRequest);
            _essLogger.LogDebug($"ClosePollAsync - sent to host [elapsed time:{sw.ElapsedMilliseconds}ms]");
         }

         sw.Stop();
      }

      public async Task OpenPollAsync(int pollingPlaceId)
      {
         var sw = new Stopwatch();
         sw.Start();

         var transaction = PollbookTransactionDto.Create();
         transaction.SystemIdentifier = _maintenanceFacade.GetSystemId();
         transaction.PollingPlaceId = pollingPlaceId;
         transaction.DeviceName = SystemDetails.MachineName;
         transaction.SerialNumber = SystemDetails.DeviceSerialNumber;
         transaction.ProcessingStatus = nameof(ProcessingStatus.Ignored);
         transaction.RecordSource = nameof(ProcessingRecordSource.Pollbook);
         _essLogger.LogDebug($"OpenPollAsync - created object [elapsed time:{sw.ElapsedMilliseconds}ms]");

         Task.Run(async () => { await OpenPollBackgroundAsync(transaction).ConfigureAwait(false); })
            .ConfigureAwait(false);

         _essLogger.LogDebug($"OpenPollAsync - created background task [elapsed time:{sw.ElapsedMilliseconds}ms]");
         sw.Stop();
      }

      private async Task OpenPollBackgroundAsync(PollbookTransactionDto transaction)
      {
         var sw = new Stopwatch();
         sw.Restart();
         var response = await _pollbookTransactionFacade.CreatePollsOpenTransaction(transaction);
         _essLogger.LogDebug($"OpenPollAsync - sending object to local [elapsed time:{sw.ElapsedMilliseconds}ms]");

         if (response.TransactionInsertSuccess && !HostSyncDetails.HavePendingLocalTransactions)
         {
            sw.Restart();
            //Send the transaction to SyncPoint unless we have pending transactions
            await _hostSyncFacade.SubmitTransactionRequest(response.HostSyncTransactionRequest);
            _essLogger.LogDebug($"OpenPollAsync - sending object to host [elapsed time:{sw.ElapsedMilliseconds}ms]");
         }

         sw.Stop();
      }

      public async Task<int> GetLocalAddedVoters()
      {
         var copyOfGuids = _pollbookTransactionRepository.GetAddTransactionGuids();
         var addedVoterTransactions = await _pollbookTransactionRepository.GetPollbookAddTransactions(copyOfGuids);
         return addedVoterTransactions.Count(p => p.SystemIdentifier == SystemDetails.MachineName);
      }

      public async Task<DeviceCountsDto> GetLocalBallotsIssued()
      {
         return await _pollbookTransactionRepository.GetDeviceBallotsIssuedCountsAsync(SystemDetails.MachineName);
      }

      public async Task<DeviceCountsDto> GetDeviceBallotsIssuedCountsAsync(string machineName)
      {
         return await _pollbookTransactionRepository.GetDeviceBallotsIssuedCountsAsync(machineName);
      }

      public async Task<IEnumerable<PollbookTransactionDto>> GetPendingLocalTransactionsToUploadAsync()
      {
         return await _pollbookTransactionRepository.GetPendingLocalTransactionsToUploadAsync();
      }

      public async Task<long> GetHostTransactionsCountAsync()
      {
         return await _pollbookTransactionRepository.GetHostTransactionsCountAsync();
      }

      public async Task<long> GetPollbookTransactionsCountAsync()
      {
         return await _pollbookTransactionRepository.GetPollbookTransactionsCountAsync();
      }

      public async Task<IEnumerable<TransactionProgressDTO>> GetTransactionProgress()
      {
         return await _pollbookTransactionRepository.GetTransactionDownloadProgress();
      }
   }
}
