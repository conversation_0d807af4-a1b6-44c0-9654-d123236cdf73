using ESS.Pollbook.Components.Repository.VoterBallot;
using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;

namespace ESS.Pollbook.Facade.VoterBallot
{
   public class VoterBallotFacade : IVoterBallotFacade
   {
      private readonly IEssLogger _essLogger;
      private readonly IVoterBallotRepository _voterBallotRepository;

      private int _pollingPlaceId;
      private List<VoterBallotDto> _voterBallotDtoByPollingPlaceIdCache = new List<VoterBallotDto>();
      private List<PartyAlternateBallotDto> _partyAlternateBallotCache = new List<PartyAlternateBallotDto>();

      public VoterBallotFacade(IVoterBallotRepository voterBallotRepository, IEssLogger essLogger)
      {
         _voterBallotRepository = voterBallotRepository;
         _essLogger = essLogger;
      }

      /// <summary>
      ///    Intended to be run when a user logs in.  If the Polling Place has changed the cache is reloaded. If the cache is
      ///    null the cache is loaded.
      /// </summary>
      /// <param name="pollingPlaceId"></param>
      /// <returns></returns>
      public async Task InitializeVoterBallotStyleCache(int pollingPlaceId)
      {
         if ((_voterBallotDtoByPollingPlaceIdCache != null || _voterBallotDtoByPollingPlaceIdCache?.Count != 0) &&
             pollingPlaceId == _pollingPlaceId)
            return;

         try
         {
            _pollingPlaceId = pollingPlaceId;
            await LoadCache();
         }
         catch (Exception ex)
         {
            var logProps = new Dictionary<string, string>
               {{"Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name}};
            _essLogger.LogError(ex, logProps);
         }
      }

      public async Task LoadCache()
      {
         _voterBallotDtoByPollingPlaceIdCache?.Clear();
         _partyAlternateBallotCache?.Clear();

         if (_pollingPlaceId == 0)
            return;

         _voterBallotDtoByPollingPlaceIdCache =
            (await _voterBallotRepository.GetVoterBallotStyleByPollPlaceId(_pollingPlaceId)).ToList();
         _partyAlternateBallotCache = (await _voterBallotRepository.GetPartyAlternateBallotsDtos()).ToList();
      }

      public List<PartyDto> GetParties(int precinctSplitId)
      {
         if (_voterBallotDtoByPollingPlaceIdCache == null || _voterBallotDtoByPollingPlaceIdCache.Count == 0)
            return new List<PartyDto>();

         return _voterBallotDtoByPollingPlaceIdCache
            .Where(x => x.PrecinctSplitId == precinctSplitId)
            .Select(p => new PartyDto
            {
               PartyId = p.PartyId,
               PartyName = p.PartyName,
               PartyDisplayName = p.PartyDisplayName,
               PartySourcePartyKey = p.PartySourcePartyKey,
               PartyDisplayOrderNumber = p.PartyDisplayOrderNumber
            })
            .GroupBy(g => g.PartyId)
            .Select(p => p.First())
            .OrderBy(p => p.PartyDisplayOrderNumber)
            .ToList();
      }

      public List<PartyDto> GetPartiesByVoter(VoterDto voter)
      {
         if (_voterBallotDtoByPollingPlaceIdCache == null
             || _voterBallotDtoByPollingPlaceIdCache.Count == 0)
            return new List<PartyDto>();

         var pabId = _partyAlternateBallotCache.Where(p => p.VoterPartyId == voter.PartyId)
            .Select(s => s.BallotPartyId).ToArray();

         var possibleParties = _voterBallotDtoByPollingPlaceIdCache
            .Where(p =>
               p.PartyId.IsIn(pabId) &&
               p.PrecinctSplitId == voter.PrecinctSplitId &&
               p.BallotStyleTypeJurisdictionEnumerationValueId == voter.VoterBallotStyleTypeJurisdictionEnumValueId)
            .Select(p => new PartyDto
            {
               PartyId = p.PartyId,
               PartyName = p.PartyName,
               PartyDisplayName = p.PartyDisplayName,
               PartySourcePartyKey = p.PartySourcePartyKey,
               PartyDisplayOrderNumber = p.PartyDisplayOrderNumber
            })
            .GroupBy(g => g.PartyId)
            .Select(p => p.First())
            .OrderBy(p => p.PartyDisplayOrderNumber);

         return possibleParties.ToList();
      }

      /// <summary>
      ///    Utilizes the IEqualityComparer"PrecinctSplitDto" for the distinct parameter which defines the equity algorithm.
      ///    This method replaced the PrecinctSplitFacade call to get the PrecinctSplitDto by leveraging the Cached
      ///    VoterBallotDtos
      /// </summary>
      /// <param name="searchTerm"></param>
      /// <returns></returns>
      public List<PrecinctSplitDto> GetPrecinctSplitNamesByPollingSearchTerm(string searchTerm)
      {
         var precinctSplitDtos = _voterBallotDtoByPollingPlaceIdCache
            .Where(x => x.PrecinctSplitDisplayName.StartsWith(searchTerm))
            .Select(o => new PrecinctSplitDto
            {
               PrecinctSplitId = o.PrecinctSplitId,
               PrecinctSplitName = o.PrecinctSplitDisplayName,
               PrecinctSplitPrecinctId = o.PrecinctSplitPrecinctId
            })
            .GroupBy(o => o.PrecinctSplitId)
            .Select(o => o.First());
         return precinctSplitDtos.ToList();
      }

      /// <summary>
      /// Using FirstOrDefault() is not the best means to do this.  Not saying that precinctSplitId will
      /// not generate the same PrecinctSplitDto, but generally not a good practice to use First() on a list
      /// which could possibly spawn different results.
      /// </summary>
      /// <param name="precinctSplitId"></param>
      /// <returns></returns>
      public PrecinctSplitDto GetPrecinctSplitNameByPrecinctSplitId(int precinctSplitId)
      {
	      return _voterBallotDtoByPollingPlaceIdCache
		      .Where(x => x.PrecinctSplitId == precinctSplitId)
		      .Select(o => new PrecinctSplitDto
		      {
			      PrecinctSplitId = o.PrecinctSplitId,
			      PrecinctSplitName = o.PrecinctSplitDisplayName,
			      PrecinctSplitPrecinctId = o.PrecinctSplitPrecinctId
		      })
		      .First();
      }

      public List<BallotStyleTypeDto> LoadBallotStyleTypes(int precinctSplitId, int partyId)
      {
         return _voterBallotDtoByPollingPlaceIdCache
            .Where(x => x.PrecinctSplitId == precinctSplitId && x.PartyId == partyId)
            .Select(o => new BallotStyleTypeDto
            {
               BallotStyleTypeId = o.BallotStyleTypeJurisdictionEnumerationValueId,
               BallotStyleTypeName = o.JurisdictionEnumerationValueName
            })
            .GroupBy(bt => bt.BallotStyleTypeId)
            .Select(gbst => gbst.First()).ToList();
      }


      // do NOT get results from cache. VoterBallotDto Cached records do NOT contain a voter id
      public async Task<VoterBallotDto> GetVoterBallotStyle(long? voterId, int ballotStylePartyId)
      {
	      if (voterId == null)
		      return null;
	      return await _voterBallotRepository.GetVoterBallotStyleByVoterId(voterId, ballotStylePartyId);
      }

      public VoterBallotDto GetDefaultVoterBallotStyleByVoter(VoterDto voter)
      {
         var alternateBallot = _partyAlternateBallotCache
                                  .Find(x => x.VoterPartyId == voter.PartyId
                                             && x.BallotPartyId == voter.PartyId) // exact match for voter's party
                               ?? _partyAlternateBallotCache
                                  .Find(x => x.VoterPartyId == voter.PartyId); // first match for alternate

         var cachedBallotResult = _voterBallotDtoByPollingPlaceIdCache
            .Find(x => alternateBallot != null
                       && x.PartyId == alternateBallot.BallotPartyId
                       && x.PrecinctSplitId == voter.PrecinctSplitId
                       && x.BallotStyleTypeJurisdictionEnumerationValueId ==
                       voter.VoterBallotStyleTypeJurisdictionEnumValueId);

         // last chance to find a ballot - maybe we are in general election with NIR voter
         if (cachedBallotResult == null)
	         cachedBallotResult = _voterBallotDtoByPollingPlaceIdCache
		         .Find(x => x.PrecinctSplitId == voter.PrecinctSplitId
		                    && x.PartyName == SystemConfiguration.ElectionConfiguration.DefaultParty
		                    && x.BallotStyleTypeJurisdictionEnumerationValueId
		                    == voter.VoterBallotStyleTypeJurisdictionEnumValueId);


         return cachedBallotResult ?? new VoterBallotDto();
      }

      public VoterBallotDto GetBallotStyleByPrecinctSplitIdAndPartyIdAndBallotStyleTypeId(int precinctSplitId,
         int partyId, int ballotStyleTypeId)
      {
         return _voterBallotDtoByPollingPlaceIdCache
            .Find(o => o.PrecinctSplitId == precinctSplitId
                       && o.PartyId == partyId
                       && o.BallotStyleTypeJurisdictionEnumerationValueId == ballotStyleTypeId);
      }

      public async Task<string> GetPdfFileNameAsync(VoterDto voter)
      {
         VoterBallotDto voterBallotDto;
         if (voter.VoterBallotDto.IsProvisional)
            voterBallotDto = GetVoterBallotDescription(voter.PrecinctParty.BallotStyleId);
         else
            voterBallotDto = await GetVoterBallotStyle(voter.VoterId, voter.PrecinctParty.PartyId);

         if (!string.IsNullOrWhiteSpace(voterBallotDto.BallotStylePDFFileName)) return voterBallotDto.BallotStylePDFFileName;
          
         _essLogger.LogError($"Could not find BallotStylePDFFileName for voter {voter.VoterId}.");
         return string.Empty;
      }

      public VoterBallotDto GetVoterBallotDescription(int ballotStyleId)
      {
         return _voterBallotDtoByPollingPlaceIdCache.Find(x => x.BallotStyleId == ballotStyleId);
      }

      public bool GetValuePartyAffiliationAffidavit(int partyId, int selectedPartyId)
      {
         try
         {
            return _partyAlternateBallotCache
               .Find(x => x.VoterPartyId == partyId && x.BallotPartyId == selectedPartyId)?
               .PartyAffiliationAffidavit == 1;
         }
         catch
         {
            return false;
         }
      }

      public bool ValidateAllBallotStylesHavePdfFilename()
      {
         return Task.Run(() => _voterBallotRepository.ValidateAllBallotStylesHavePdfFilename()).Result;
      }

      public bool ValidateAllBallotStyleCodes()
      {
         return Task.Run(() => _voterBallotRepository.ValidateAllBallotStyleCodes()).Result;
      }
   }
}