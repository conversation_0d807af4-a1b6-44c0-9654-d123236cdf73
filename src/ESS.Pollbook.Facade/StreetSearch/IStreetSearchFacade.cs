using ESS.Pollbook.Core.Model;
using System.Threading.Tasks;

namespace ESS.Pollbook.Facade.StreetSearch
{
    public interface IStreetSearchFacade
    {
        Task<StreetSearchResponse> GetStreetSearchResultsAsync(SteetSearchRequest param);
        Task<StreetSearchResponse> GetStreetsSearchResultsWithStateAsync(string address, string state);
        Task<bool> IsStreetsPopulatedAsync();
        bool CanSearch(string searchTerm);
    }
}
