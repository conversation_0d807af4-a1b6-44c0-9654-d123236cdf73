using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.Model;
using ESS.Pollbook.Hardware.Storage;
using Newtonsoft.Json;

namespace ESS.Pollbook.Facade.ElectionSettings
{
    public class ElectionSettingsFacade : IElectionSettingsFacade
    {
        private readonly IEssLogger _essLogger;
        private readonly IStorageLocator _storageLocator;

        public ElectionSettingsFacade(IEssLogger logger, IStorageLocator storageLocator)
        {
            _essLogger = logger;
            _storageLocator = storageLocator;
        }

        public async Task<ElectionSettingsModel> GetElectionInfo()
        {
            try
            {
                var electionSettingsFilePath = _storageLocator.GetElectionSettingsFilePath();

                if (string.IsNullOrEmpty(electionSettingsFilePath))
                   return null;

                var electionSettingsJson = await Task.Run(() =>
                   File.ReadAllText(electionSettingsFilePath)).ConfigureAwait(false);

                var defaultElectionSettings = await Task.Run(() =>
                   JsonConvert.DeserializeObject<ElectionSettingsModel>(electionSettingsJson));

                if (defaultElectionSettings == null)
                {
                    var electionSettingsBackupFilePath = _storageLocator.GetElectionSettingsBackupFilePath();
                    if (string.IsNullOrEmpty(electionSettingsBackupFilePath) &&
                        !File.Exists(electionSettingsBackupFilePath))
                    {
                        return null;
                    }
                    
                    var electionSettingsBackupJson = await Task.Run(() =>
                        File.ReadAllText(electionSettingsBackupFilePath)).ConfigureAwait(false);

                    defaultElectionSettings = await Task.Run(() =>
                        JsonConvert.DeserializeObject<ElectionSettingsModel>(electionSettingsBackupJson));

                    if (defaultElectionSettings != null)
                    {
                        File.Replace(electionSettingsBackupFilePath, electionSettingsFilePath, null);
                    }
                }

                if (defaultElectionSettings != null)
                {
                    Core.StaticValues.ElectionSettings.ElectionName = defaultElectionSettings.ElectionName;
                    Core.StaticValues.ElectionSettings.JurisdictionName = defaultElectionSettings.JurisdictionName;
                    Core.StaticValues.ElectionSettings.DatabaseVersion = defaultElectionSettings.DatabaseVersion;
                    Core.StaticValues.ElectionSettings.ConfigurationVersion =
                        defaultElectionSettings.ConfigurationVersion;
                    Core.StaticValues.ElectionSettings.ApiVersion = defaultElectionSettings.ApiVersion;
                    Core.StaticValues.ElectionSettings.LocalTransactions = defaultElectionSettings.LocalTransactions;
                    Core.StaticValues.ElectionSettings.SentToServer = defaultElectionSettings.SentToServer;
                    Core.StaticValues.ElectionSettings.LastSync = defaultElectionSettings.LastSync;

                    return defaultElectionSettings;
                }
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string> { { "Action", "Get Election Info" } };
                _essLogger.LogError(ex.Message, logProps);
            }

            return null;
        }
    }
}
