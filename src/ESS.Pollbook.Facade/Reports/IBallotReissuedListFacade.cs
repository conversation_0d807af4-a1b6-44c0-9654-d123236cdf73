using ESS.Pollbook.Core.Model;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ESS.Pollbook.Facade.Reports
{
    public interface IBallotReissuedListFacade
    {

        Task<List<string>> GetDevicesListTask(int pollingPlaceId);

        Task<BallotReissuedLimitResponse> GetBallotReissuedList(BallotReissuedListRequest ballotReissuedListRequest);

        PrintResponse PrintBallotReissuedList(BallotReissuedListCount ballotReissuedListCount);
    }
}
