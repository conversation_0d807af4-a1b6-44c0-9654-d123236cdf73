using ESS.Pollbook.Components.Business.Printing;
using ESS.Pollbook.Components.Repository.Reports;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Model;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace ESS.Pollbook.Facade.Reports
{
    public class BallotReissuedListFacade : IBallotReissuedListFacade
    {
        private readonly IBallotReissuedListRepository _ballotReissuedListRepository;
        private readonly IPrintBallotReissuedListFactory _printBallotReissuedListFactory;

        public BallotReissuedListFacade(IBallotReissuedListRepository ballotReissuedListRepository, IPrintBallotReissuedListFactory printBallotReissuedListFactory)
        {
            _ballotReissuedListRepository = ballotReissuedListRepository;
            _printBallotReissuedListFactory = printBallotReissuedListFactory;
        }

        public async Task<List<string>> GetDevicesListTask(int pollingPlaceId)
        {
            var result = await _ballotReissuedListRepository.GetDevicesListTask(pollingPlaceId);
            return result;
        }

        public async Task<BallotReissuedLimitResponse> GetBallotReissuedList(BallotReissuedListRequest ballotReissuedListRequest)
        {
            var results = await _ballotReissuedListRepository.GetBallotReissuedList(ballotReissuedListRequest);
            var ballotReissuedListItemDtos = results as IList<BallotReissuedListItemDto> ?? results.ToList();

            var status = ballotReissuedListItemDtos.Count > 0 ? BallotReissuedListStatus.Results : BallotReissuedListStatus.NoResults;

            return new BallotReissuedLimitResponse()
            {
                BallotReissuedListStatus = status,
                BallotReissuedListItemDtos = ballotReissuedListItemDtos.ToList()
            };

        }

        public PrintResponse PrintBallotReissuedList(BallotReissuedListCount ballotReissuedListCount)
        {
            return _printBallotReissuedListFactory.PrintData(ballotReissuedListCount);
        }
    }
}
