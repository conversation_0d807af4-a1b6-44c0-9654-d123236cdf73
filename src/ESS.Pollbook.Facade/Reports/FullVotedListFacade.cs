using ESS.Pollbook.Components.Business.Printing;
using ESS.Pollbook.Components.Repository.Reports;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Model;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ESS.Pollbook.Facade.CommonEnumerationValue;
using ESS.Pollbook.Core.StaticValues;

namespace ESS.Pollbook.Facade.Reports
{
   public class FullVotedListFacade : IFullVotedListFacade
   {
      private readonly ICommonEnumerationValueFacade _commonEnumerationValueFacade;
      private readonly IFullVotedListRepository _fullVotedListRepository;
      private readonly IPrintFullVotedListFactory _printFullVotedListFactory;

      private const string All = "All";

      private bool IsExpressVoteEnabled => SystemConfiguration.ElectionConfiguration.EnableEDProvisionalExpressVoteBallot
                                 || SystemConfiguration.ElectionConfiguration.EnableEVProvisionalExpressVoteBallot
                                 || SystemConfiguration.ElectionConfiguration.EnableEDProvisionalExpressVotePrinting
                                 || SystemConfiguration.ElectionConfiguration.EnableEVProvisionalExpressVotePrinting
                                 || SystemConfiguration.ElectionConfiguration.EnableEDStandardExpressVoteBallot
                                 || SystemConfiguration.ElectionConfiguration.EnableEVStandardExpressVoteBallot
                                 || SystemConfiguration.ElectionConfiguration.EnableEDStandardExpressVotePrinting
                                 || SystemConfiguration.ElectionConfiguration.EnableEVStandardExpressVotePrinting;
      private bool IsPaperBallotEnabled => SystemConfiguration.ElectionConfiguration.EnableEDProvisionalPaperBallot
                                 || SystemConfiguration.ElectionConfiguration.EnableEVProvisionalPaperBallot
                                 || SystemConfiguration.ElectionConfiguration.EnableEDProvisionalPaperBallotPrinting
                                 || SystemConfiguration.ElectionConfiguration.EnableEVProvisionalPaperBallotPrinting
                                 || SystemConfiguration.ElectionConfiguration.EnableEDStandardPaperBallot
                                 || SystemConfiguration.ElectionConfiguration.EnableEVStandardPaperBallot
                                 || SystemConfiguration.ElectionConfiguration.EnableEDStandardPaperBallotPrinting
                                 || SystemConfiguration.ElectionConfiguration.EnableEVStandardPaperBallotPrinting;

      public FullVotedListFacade(ICommonEnumerationValueFacade commonEnumerationValueFacade, IFullVotedListRepository fullVotedListRepository, IPrintFullVotedListFactory printFullVotedListFactory)
      {
         _commonEnumerationValueFacade = commonEnumerationValueFacade;
         _fullVotedListRepository = fullVotedListRepository;
         _printFullVotedListFactory = printFullVotedListFactory;
      }

      public Task<Dictionary<string, string>> GetDevicesWritingTransactions(int pollingPlaceId)
      {
         return _fullVotedListRepository.GetDevicesWritingTransactionsAsync(pollingPlaceId);
      }

      public async Task<FullVotedListResponse> GetFullVotedList(FullVotedListRequest fullVotedListRequest)
      {
         var results = await _fullVotedListRepository.GetFullVotedListAsync(fullVotedListRequest);

         var votedListItemDtos = results as List<VotedListItemDto> ?? results.ToList();
         if (votedListItemDtos.Any())
         {
            var sb = new StringBuilder();
            foreach (var v in votedListItemDtos)
            {
               if (string.IsNullOrEmpty(v.FullName) && !v.RecordInitialLoadIndicator)
               {
                  sb.Append($"'{v.VoterKey}',");
               }
            }

            if (!string.IsNullOrEmpty(sb.ToString()))
            {
               var sourceKeys = sb.ToString().Substring(0, sb.Length - 1);
               var newVoterInfo = await GetNewVoterInfo(sourceKeys);

               var newVoterInfos = newVoterInfo.ToList();
               if (newVoterInfos.Any())
               {
                  foreach (var v in newVoterInfos)
                  {
                     foreach (var x in votedListItemDtos)
                     {
                        if (x.VoterKey == v.SourceKey)
                        {
                           x.FullName = v.FullName;
                        }
                     }
                  }
               }
            }
         }

         var voterListItemFinalizedDtos = new List<VotedListItemDto>();
         foreach (var x in votedListItemDtos)
         {
            if (string.IsNullOrEmpty(x.FullName) && !x.RecordInitialLoadIndicator)
            {
               //P2P received voter not found ballot records
            }
            else
            {
               voterListItemFinalizedDtos.Add(x);
            }
         }

         var status = voterListItemFinalizedDtos.Count > 0 ? FullVotedListStatusEnum.Results : FullVotedListStatusEnum.NoResults;

         return new FullVotedListResponse()
         {
            FullVotedListStatus = status,
            VotedDtoResults = voterListItemFinalizedDtos
         };
      }

      public PrintResponse PrintFullVotedList(VotedList fullVotedListItemDtos, bool filteredList)
      {
         return _printFullVotedListFactory.PrintData(filteredList, fullVotedListItemDtos);
      }

      public async Task<List<CommonEnumerationValueDto>> GetMediaListAsync()
      {
	      var mediaTypeList = new List<CommonEnumerationValueDto>();

	      // Groom the list
         var tempMediaList = await _commonEnumerationValueFacade.GetMediaTypesListAsync();

         foreach (var listItem in tempMediaList
	                  .Where(i => (i.EnumerationValueName == nameof(BallotType.Paper) && IsPaperBallotEnabled)
	                              || (i.EnumerationValueName == nameof(BallotType.ExpressVote) && !SystemConfiguration.ElectionConfiguration.DacEnabled && IsExpressVoteEnabled)
	                              || ((i.EnumerationValueName == nameof(BallotType.DAC)) && SystemConfiguration.ElectionConfiguration.DacEnabled && IsExpressVoteEnabled)))
         {
	         listItem.EnumerationValueDescription = DefinedText.PollbookDefinedTextDtos
		         .FirstOrDefault(t => t.PollbookDefinedTextName.Equals(listItem.EnumerationValueName))
		         ?.PollbookDefinedTextValue ?? listItem.EnumerationValueDescription;

	         mediaTypeList.Add(listItem);
         }

         // Even if 0/1 Ballot types, ALL must be included to work with filter.
         mediaTypeList.Insert(0, new CommonEnumerationValueDto
	         {
		         EnumerationValueCode = All,
		         EnumerationValueDescription = All
	         }
         );
         return mediaTypeList;
      }

      private async Task<IEnumerable<NewVoterInfo>> GetNewVoterInfo(string sourceKeys)
      {
         return await _fullVotedListRepository.GetNewVotersInfoAsync(sourceKeys);
      }
   }
}