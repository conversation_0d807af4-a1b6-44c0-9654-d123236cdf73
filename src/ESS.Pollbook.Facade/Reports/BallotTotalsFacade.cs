using ESS.Pollbook.Components.Business.Printing;
using ESS.Pollbook.Core.Model;
using ESS.Pollbook.Core.NavigationParameters;

namespace ESS.Pollbook.Facade.Reports
{
    public class BallotTotalsFacade : IBallotTotalsFacade
    {
        private readonly IPrintBallotTotalsFactory _printBallotTotalsFactory;

        public BallotTotalsFacade(IPrintBallotTotalsFactory printBallotTotalsFactory)
        {
            _printBallotTotalsFactory = printBallotTotalsFactory;
        }

        public PrintResponse PrintBallotTotals(BallotTotalsReportParameter ballotTotalsReportData)
        {

            return _printBallotTotalsFactory.PrintData(ballotTotalsReportData);
        }
    }
}
