using ESS.Pollbook.Components.Business.Printing;
using ESS.Pollbook.Core.Model;
using ESS.Pollbook.Core.NavigationParameters;

namespace ESS.Pollbook.Facade.Reports
{
    public class SpoiledBallotFacade : ISpoiledBallotFacade
    {
        private readonly IPrintSpoiledBallotFactory _spoiledBallotTotalsFactory;

        public SpoiledBallotFacade(IPrintSpoiledBallotFactory printSpoiledBallotsFactory)
        {
            _spoiledBallotTotalsFactory = printSpoiledBallotsFactory;
        }

        public PrintResponse PrintBallotTotals(SpoiledBallotReportParameter spoiledBallotReportData)
        {

            return _spoiledBallotTotalsFactory.PrintData(spoiledBallotReportData);
        }
    }
}
