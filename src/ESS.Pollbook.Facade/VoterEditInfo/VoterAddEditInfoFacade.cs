using ESS.Pollbook.Components.Business.VoterEditInfo;
using ESS.Pollbook.Components.Repository.VoterEditInfo;
using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.EventsDto;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Core.Utilities;
using ESS.Pollbook.Facade.HostSync;
using ESS.Pollbook.Facade.PollbookTransaction;
using ESS.Pollbook.MasterProcessor.Abstractions.Shared;
using ESS.Pollbook.MasterProcessor.Abstractions;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ESS.Pollbook.Facade.VoterEditInfo
{
	public class VoterAddEditInfoFacade : IVoterAddEditInfoFacade
	{
      private readonly IVoterAddEditInfoFactory _voterEditInfoFactory;
      private readonly IVoterAddEditInfoRepository _voterAddEditInfoRepository;
      private readonly IHostSyncFacade _hostSyncFacade;
      private readonly IPollbookTransactionFacade _transactionFacade;
      private readonly IEssLogger _essLogger;
      private readonly IEssPollbookFileWatcher _essPollbookFileWatcher;


      public VoterAddEditInfoFacade(
	      IVoterAddEditInfoFactory voterEditInfoFactory,
	      IVoterAddEditInfoRepository voterAddEditInfoRepository,
	      IHostSyncFacade hostSyncFacade,
	      IPollbookTransactionFacade transactionFacade,
	      IEssLogger essLogger,
          IEssPollbookFileWatcher essPollbookFileWatcher)
      {
         _voterEditInfoFactory = voterEditInfoFactory;
         _voterAddEditInfoRepository = voterAddEditInfoRepository;
         _hostSyncFacade = hostSyncFacade;
         _transactionFacade = transactionFacade;
         _essLogger = essLogger;
         _essPollbookFileWatcher = essPollbookFileWatcher;
      }

        public async Task EditVoterAsync(VoterDto voter, VoterDto previousVoter)
        {
            var transaction = PollbookTransactionDto.Create();
            transaction.DeviceName = SystemDetails.MachineName;
            transaction.SerialNumber = SystemDetails.DeviceSerialNumber;
            transaction.SystemIdentifier = SystemDetails.SystemId;
            transaction.Voter = ValidateVoter(voter);
            transaction.SourceKey = voter.VoterKey;
            transaction.Signature = null;
            transaction.PollingPlaceId = LoggedInPollplaceInfo.LoggedInPollPlace.PollingPlaceId;
            transaction.ProcessingStatus = nameof(ProcessingStatus.Completed);
            transaction.RecordSource = nameof(ProcessingRecordSource.Pollbook);

         var transactionItem = new PollbookQueueItem
         {
             ClientId = Guid.NewGuid(),
             ElectionGuid = SystemConfiguration.ElectionConfiguration.ElectionDatabaseGuid,
             QueueParams = new Dictionary<string, object>
             { 
                 { QueueParamKeyConstants.VOTER_EDIT_TRANSACTION_KEY, transaction },
                 { QueueParamKeyConstants.VOTER_EDIT_PREVIOUS_VOTER_KEY, previousVoter },
                 { QueueParamKeyConstants.EDIT_VOTER_KEY, voter },
             },
             ItemQueueProcess = QueueProcess.VoterEditTransaction,
             ItemQueueType = QueueType.Transaction,
             ItemQueuePriority = QueuePriority.High
         };
         var polldataItem = new PollbookQueueItem
         {
             ClientId = Guid.NewGuid(),
             ElectionGuid = SystemConfiguration.ElectionConfiguration.ElectionDatabaseGuid,
             QueueParams = new Dictionary<string, object> { { QueueParamKeyConstants.EDIT_VOTER_KEY, voter }, },
             ItemQueueProcess = QueueProcess.VoterEditPolldata,
             ItemQueueType = QueueType.Polldata,
             ItemQueuePriority = QueuePriority.High
         };
         
         try
         {
             _essPollbookFileWatcher.AddFileToDirectory(transactionItem);
             _essPollbookFileWatcher.AddFileToDirectory(polldataItem);
         }
         catch (Exception ex)
         {
             var logProps = new Dictionary<string, string> { { "Action", "Error adding file to directory" } };
             _essLogger.LogError(ex, logProps);
         }
      }

        public async Task AddVoterAsync(VoterDto newVoter)
        {
            try
            {
                var transaction = PollbookTransactionDto.Create();
                transaction.DeviceName = SystemDetails.MachineName;
                transaction.PollingPlaceId = LoggedInPollplaceInfo.LoggedInPollPlace.PollingPlaceId;
                transaction.SerialNumber = SystemDetails.DeviceSerialNumber;
                transaction.Signature = null;
                transaction.SourceKey = newVoter.VoterKey;
                transaction.SystemIdentifier = SystemDetails.SystemId;
                transaction.Voter = ValidateVoter(newVoter);
                transaction.ProcessingStatus = nameof(ProcessingStatus.Completed);
                transaction.RecordSource = nameof(ProcessingRecordSource.Pollbook);

                var response = await _transactionFacade.CreateVoterAddTransaction(transaction);

                await Task.Run(() => _voterAddEditInfoRepository.InsertVoter(newVoter));
                await Task.Run(() => _voterAddEditInfoRepository.InsertVoterAddress(_voterEditInfoFactory.CreateVoterAddressDto(newVoter)));

                if (response.TransactionInsertSuccess && !HostSyncDetails.HavePendingLocalTransactions)
                {
                    response.HostSyncTransactionRequest.TransactionVoterSourceKey = newVoter.VoterKey;
                    await _hostSyncFacade.SubmitTransactionRequest(response.HostSyncTransactionRequest);
                }
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string> { { "Action", "Error adding newly created voter" } };
                _essLogger.LogError(ex, logProps);
            }
        }

        public void UpdateVoterAndVoterAddress(PollbookTransactionDto pollbookTransactionDto, QueueType? queueType = null)
        {
            try
            {
                if (queueType is null || queueType == QueueType.Polldata)
                {
                    var jsonObj = JsonConvert.DeserializeObject<VoterEditEventDto>(pollbookTransactionDto.JSON);

                    _voterAddEditInfoRepository.UpdateVoter(_voterEditInfoFactory.CreateEditVoterObject(jsonObj));
                    _voterAddEditInfoRepository.UpdateVoterAddress(jsonObj?.Address);
                    _voterAddEditInfoRepository.UpsertVoterStatus(new VoterAbsStatusUpdatesDto
                    {
                        VoterId = jsonObj.VoterId,
                        VoterStatusEnumId = jsonObj.VoterStatusEnumId ?? 0,
                        AbsenteeStatusEnumId = jsonObj.AbsenteeStatusEnumId ?? 0,
                        VoterDeleted = !jsonObj.VoterDeleted.HasValue ? 0 : jsonObj.VoterDeleted.Value ? 1 : 0
                    });
                }

                if (queueType is null || queueType == QueueType.Transaction)
                {
                    _transactionFacade.InsertTransaction(pollbookTransactionDto);
                }

            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string>{
               { "Action", nameof(UpdateVoterAndVoterAddress) },
               { "VoterId", pollbookTransactionDto.Voter.VoterId.ToString()},
               { "SourceKey", pollbookTransactionDto.SourceKey}};
                _essLogger.LogError(ex, logProps);
            }
        }

        public void UpdateVoterStatus(PollbookTransactionDto pollbookTransactionDto)
        {
            try
            {
                var jsonObj = JsonConvert.DeserializeObject<VoterStatusEventDto>(pollbookTransactionDto.JSON);

                Task.Run(() => _voterAddEditInfoRepository.UpdateVoterStatus(_voterEditInfoFactory.CreateVoterAbsStatusUpdatesDto(jsonObj)));
                _transactionFacade.InsertTransaction(pollbookTransactionDto);

            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string>{
               { "Action", nameof(UpdateVoterStatus) },
               { "SourceKey", pollbookTransactionDto.SourceKey}};
                _essLogger.LogError(ex, logProps);
            }
        }

        public async Task InsertVoterAndVoterAddressAsync(PollbookTransactionDto pollbookTransactionDto, QueueType? queueType = null)
        {
            try
            {
                if (queueType is null || queueType == QueueType.Transaction)
                {
                    var response = _transactionFacade.InsertTransaction(pollbookTransactionDto);
                    if (!response.IsSuccess) return;
                }

                if (queueType is null || queueType == QueueType.Polldata)
                {
                    var jsonObj = JsonConvert.DeserializeObject<VoterAddEventDto>(pollbookTransactionDto.JSON);
                    var insertVoter = await _voterEditInfoFactory.InsertVoterAddRecord(
                       _voterEditInfoFactory.CreateAddVoterObject(jsonObj, pollbookTransactionDto.SourceKey), null,
                       pollbookTransactionDto.DeviceName, pollbookTransactionDto.TransactionDate);

                    if (insertVoter.IsSuccess)
                    {
                        var addressDto = jsonObj.Address;
                        addressDto.VoterId = jsonObj.VoterId ?? -1;

                        if (addressDto.VoterId == -1) throw new Exception("VoterId is not valid.");

                        _voterAddEditInfoRepository.InsertVoterAddress(addressDto, pollbookTransactionDto.DeviceName,
                           pollbookTransactionDto.TransactionDate);
                    }
                }
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string>{
               { "Action", nameof(InsertVoterAndVoterAddressAsync) },
               { "VoterId", pollbookTransactionDto.Voter.VoterId.ToString()},
               { "SourceKey", pollbookTransactionDto.SourceKey}};
                _essLogger.LogError(ex, logProps);
            }
        }

        public void BulkUpdateEditVoterAndVoterAddress(List<List<object>> voterUpdateList, List<List<object>> voterAddressUpdateList)
        {
            _voterAddEditInfoRepository.BulkUpsertEditVoterTransactions(voterUpdateList);
            _voterAddEditInfoRepository.BulkUpsertEditVoterAddressTransactions(voterAddressUpdateList);
        }

        public List<object> ConvertEditVoterList(VoterEditEventDto voterEdit, string sourceKey, string deviceName, DateTime transactionDateTime)
        {
            return _voterAddEditInfoRepository.ConvertEditVoterList(voterEdit, sourceKey, deviceName, transactionDateTime);
        }

        public List<object> ConvertEditVoterAddressList(VoterEditEventDto voterEdit, string deviceName, DateTime transactionDateTime)
        {
            return _voterAddEditInfoRepository.ConvertEditVoterAddressList(voterEdit, deviceName, transactionDateTime);
        }

        public void BulkInsertAddVoterAndVoterAddress(List<List<object>> voterInsertList, List<List<object>> voterAddressInsertList)
        {
            var status = false;

            if (_voterAddEditInfoRepository.BulkInsertAddVoterTransactions(voterInsertList))
                status = _voterAddEditInfoRepository.BulkInsertAddVoterAddressTransactions(voterAddressInsertList);

            if (status) return;

            var prop = new Dictionary<string, string> { { "Action", "BulkInsertAddVoterAndVoterAddress" } };
            _essLogger.LogDebug("Failed to bulk insert records from reconciliation attempt.", prop);
        }

        public List<object> ConvertAddVoterList(VoterAddEventDto voterAdd, string sourceKey, string deviceName, DateTime transactionDateTime)
        {
            return _voterAddEditInfoRepository.ConvertAddVoterList(voterAdd, sourceKey, deviceName, transactionDateTime);
        }

        public List<object> ConvertAddVoterAddressList(VoterAddEventDto voterAdd, string deviceName, DateTime transactionDateTime)
        {
            return _voterAddEditInfoRepository.ConvertAddVoterAddressList(voterAdd, deviceName, transactionDateTime);
        }

        public string BuildFullAddress(VoterDto newVoter)
        {
            return (!string.IsNullOrEmpty(newVoter.HouseNumber) ? newVoter.HouseNumber + " " : "")
                                 + (!string.IsNullOrEmpty(newVoter.HouseNumberFractionValue) ? newVoter.HouseNumberFractionValue + " " : "")
                                 + (!string.IsNullOrEmpty(newVoter.StreetName) ? newVoter.StreetName : "")
                                 + (!string.IsNullOrEmpty(newVoter.UnitTypeName) ? " " + newVoter.UnitTypeName : "")
                                 + (!string.IsNullOrEmpty(newVoter.UnitValue) ? " " + newVoter.UnitValue : "")
                                 + ","
                                 + (!string.IsNullOrEmpty(newVoter.City) ? newVoter.City + "," : "")
                                 + (!string.IsNullOrEmpty(newVoter.State) ? newVoter.State + "," : "")
                                 + (!string.IsNullOrEmpty(newVoter.Zip) ? newVoter.Zip + " " : "")
                                 + (!string.IsNullOrEmpty(newVoter.AdditionalPostalCode) ? "-" + newVoter.AdditionalPostalCode : "");
        }

        private VoterDto ValidateVoter(VoterDto voter)
        {
            if (string.IsNullOrWhiteSpace(voter.LastNameSearch))
                voter.LastNameSearch = Helpers.GetAlphaNumericString(voter.LastName);

            if (string.IsNullOrWhiteSpace(voter.FirstNameSearch))
                voter.FirstNameSearch = Helpers.GetAlphaNumericString(voter.FirstName);

            voter.CompleteAddress = BuildFullAddress(voter);

            return voter;
        }

    }
}
