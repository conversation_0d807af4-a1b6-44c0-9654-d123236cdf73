using ESS.Pollbook.Core.DTO;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ESS.Pollbook.Facade.Authentication
{
    public interface IAuthenticationFacade
    {
        Task<bool> AuthenticateUser(string username, string password, int location);
        Task<bool> AuthenticateSupervisor(string password);
        Task<bool> AuthenticatePollworkerSupervisor(string password);
        Task<bool> AuthenticateManageDeviceUser(string password);
        Task CreateLoginTransaction(IEnumerable<UserDto> userData, int location);
    }
}
