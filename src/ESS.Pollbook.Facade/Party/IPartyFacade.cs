using ESS.Pollbook.Core.DTO;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ESS.Pollbook.Facade.Party
{
    public interface IPartyFacade
    {
        Task<IEnumerable<PartyDto>> GetAllParties();

        Task<IEnumerable<PrecinctPartyDto>> GetPrecinctParties(int precinctSplitId);

        Task<IEnumerable<PrecinctPartyDto>> GetPrecinctPartiesWithPartyIdAsync(int partyId, int precinctSplitId);

        Task<IEnumerable<PrecinctPartyDto>> GetAvailablePartiesByVoterIdAndPrecinctSplitId(long? voterId, int precinctSplitId);

        Task<PartyDto> GetPartyByPartyId(int id);

        Task<PartyDto> GetPartyByBallotStyleId(int ballotStyleId);
    }
}
