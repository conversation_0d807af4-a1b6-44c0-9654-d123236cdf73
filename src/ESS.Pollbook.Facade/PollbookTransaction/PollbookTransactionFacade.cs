using ESS.Pollbook.Components.Business.IncrementalUpdates;
using ESS.Pollbook.Components.Business.PollbookTransaction;
using ESS.Pollbook.Components.Repository.PollbookTransaction;
using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.Model;
using ESS.Pollbook.Core.Model.Transactions;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Core.Utilities;
using ESS.Pollbook.Facade.Maintenance;
using ESS.Pollbook.MasterProcessor.Abstractions;
using ESS.Pollbook.MasterProcessor.Abstractions.Shared;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using ESS.Pollbook.Components.Business.Cache;
using ESS.Pollbook.Core.Interface;

namespace ESS.Pollbook.Facade.PollbookTransaction
{
   public class PollbookTransactionFacade : IPollbookTransactionFacade
   {
	   #region Private Members

	   private readonly IPollbookTransactionRepository _pollbookTransactionRepository;
	   private readonly IPollbookTransactionFactory _pollbookTransactionFactory;
	   private readonly IMaintenanceFacade _maintenanceFacade;
	   private readonly IPollbookFailedTransactionRepository _pollbookFailedTransactionRepository;
	   private readonly IEssLogger _essLogger;
	   private readonly IIncrementalUpdatesFactory _incrementalUpdatesFactory;
       private readonly IEssPollbookFileWatcher _essPollbookFileWatcher;
       private readonly ITransactionGuidCache _transactionGuidCache;

	   #endregion

        public PollbookTransactionFacade(IPollbookTransactionRepository pollbookTransactionRepository,
           IPollbookTransactionFactory transactionFactory,
           IMaintenanceFacade maintenanceFacade,
           IPollbookFailedTransactionRepository pollbookFailedTransactionRepository,
           IIncrementalUpdatesFactory incrementalUpdatesFactory,
           IEssLogger essLogger,
           IEssPollbookFileWatcher essPollbookFileWatcher,
           ITransactionGuidCache transactionGuidCache)
        {
            _pollbookTransactionRepository = pollbookTransactionRepository;
            _pollbookTransactionFactory = transactionFactory;
            _maintenanceFacade = maintenanceFacade;
            _pollbookFailedTransactionRepository = pollbookFailedTransactionRepository;
            _incrementalUpdatesFactory = incrementalUpdatesFactory;
            _essLogger = essLogger;
            _essPollbookFileWatcher = essPollbookFileWatcher;
            _transactionGuidCache = transactionGuidCache;
        }

        #region Read Transactions

        public async Task<int> GetLastTransactionIdAsync(string guid)
	   {
		   return await _pollbookTransactionRepository.GetLastTransactionIdAsync(guid);
	   }


	   public async Task<PollbookTransactionDto> GetElectionBallotTransactionsByVoterKeyAsync(string voterKey)
	   {
		   var results = await _pollbookTransactionRepository.GetElectionBallotTransactionsByVoterKeyAsync(voterKey);
		   return results;
	   }

	   public async Task InitializeTransactionGuidsAsync()
	   {
		   if (SystemConfiguration.ElectionConfiguration.PeerSync)
			   await _pollbookTransactionRepository.InitializeTransactionGuids();
	   }

	   #endregion

	   #region Save Transactions

	   public LocalResponse InsertTransaction(PollbookTransactionDto transaction, bool retry = false)
	   {
		   var response = Task.Run(() => _pollbookTransactionRepository.InsertTransaction(transaction)).Result;
		   
		   if (response.IsSuccess && !string.IsNullOrEmpty(transaction.TransactionGuid))
		   {
			   if (Enum.TryParse<TransactionType>(transaction.TransactionType, out var transactionType))
			   {
				   _transactionGuidCache.AddTransactionGuid(transactionType, transaction.TransactionGuid);
			   }
			   else
			   {
				   // Log warning for unknown transaction type but don't fail
				   _essLogger?.LogWarning($"Unknown transaction type '{transaction.TransactionType}' for GUID '{transaction.TransactionGuid}'");
			   }
		   }

		   switch (response.IsSuccess)
		   {
			   case true:
			   {
				   if (SystemConfiguration.ElectionConfiguration.PeerSync
				       && Helpers.IsPeerSharableTransaction(transaction)
				       && !retry)
					   _pollbookTransactionRepository.ShareWithPeers(transaction);
				   break;
			   }
			   case false:
				   _ = _pollbookTransactionRepository.WriteInsertTransactionToQueue(response, transaction);
				   break;
		   }

		   return response;
	   }

	   public async Task<LocalResponse> UpdateHostTransactionId(string transactionGuid, long hostTransactionId, bool retry = false)
	   {
		   var response = await Task.Run(() => _pollbookTransactionRepository.UpdateHostTransactionId(transactionGuid, hostTransactionId));
		   if (response.IsSuccess || retry || !response.IsBusy) return response;

		   _essLogger.LogInformation($"Database is busy, enqueuing transaction update, id = {transactionGuid}");

			var transaction = new PollbookTransactionDto
			{
				TransactionGuid = transactionGuid,
				HostTransactionId = hostTransactionId
			};

			var transactionItem = new PollbookQueueItem
			{
				ClientId = Guid.NewGuid(),
				QueueParams = new Dictionary<string, object>
				{
					{ QueueParamKeyConstants.FAILED_TRANSACTIONS_KEY,  transaction }
				},
				ItemQueueProcess = QueueProcess.FailedUpdateHostTransaction,
				ItemQueueType = QueueType.Transaction,
				ItemQueuePriority = QueuePriority.Low
			};

			_essPollbookFileWatcher.AddFileToDirectory(transactionItem);

		   return response;
	   }

	   public void BulkInsertReconciliationTransactions(List<ResponseTransactionDto> transactionList)
	   {
		   var response = _pollbookTransactionRepository.BulkInsertReconciliationTransactions(transactionList);
		   if (!response.IsSuccess)
			   return;

		   foreach (var transaction in transactionList) // inserting the reconciliation transaction guids to the global hash set
		   {
			   if (Enum.TryParse<TransactionType>(transaction.TransactionType, out var transactionType))
			   {
				   // Use the cache for adding transaction GUIDs (preferred approach)
				   _transactionGuidCache.AddTransactionGuid(transactionType, transaction.TransactionGuid);
            
				   // Also add to repository cache using the parsed enum
				   _pollbookTransactionRepository.AddTransactionGuidToSet(transactionType, transaction.TransactionGuid);
			   }
			   else
			   {
				   // Log warning for unknown transaction type but don't fail
				   _essLogger?.LogWarning($"Unknown transaction type '{transaction.TransactionType}' for GUID '{transaction.TransactionGuid}'");
			   }
		   }


		   Task.Run(async () => await _incrementalUpdatesFactory.UpdateElectionSettingsIniFile());
	   }

	   public async Task<PollbookTransactionResponse> CreateVoterAddTransaction(PollbookTransactionDto transaction)
	   {
		   transaction.TransactionType = TransactionType.VoterAdd.ToString();
		   var value = await _pollbookTransactionFactory.CreateVoterAddTransaction(transaction);
		   if (value.TransactionInsertSuccess && !string.IsNullOrEmpty(transaction.TransactionGuid))
		   {
			   // Update cache immediately - use enum instead of string
			   _transactionGuidCache.AddTransactionGuid(TransactionType.VoterAdd, transaction.TransactionGuid);
		   }

		   return value;
	   }

	   public async Task<PollbookTransactionResponse> CreateVoterEditTransaction(PollbookTransactionDto transaction, VoterDto previousVoter, bool includeAffidavits = true)
	   {
		   transaction.TransactionType = TransactionType.VoterEdit.ToString();
		   var value = await _pollbookTransactionFactory.CreateVoterEditTransaction(transaction, previousVoter, includeAffidavits);
		   if (value.TransactionInsertSuccess && !string.IsNullOrEmpty(transaction.TransactionGuid))
		   {
			   // Update cache immediately - use enum instead of string
			   _transactionGuidCache.AddTransactionGuid(TransactionType.VoterEdit, transaction.TransactionGuid);
		   }
		   return value;
	   }

	   public async Task<PollbookTransactionResponse> CreateCancelBallotTransaction(PollbookTransactionDto transaction)
	   {
		   transaction.TransactionType = TransactionType.BallotCancel.ToString();
		   var value = await _pollbookTransactionFactory.CreateBallotCancelTransaction(transaction);
		   return value;
	   }

	   public async Task<PollbookTransactionResponse> CreatePollsOpenTransaction(PollbookTransactionDto transaction)
	   {
		   transaction.TransactionType = TransactionType.PollsOpen.ToString();
		   var value = await _pollbookTransactionFactory.CreatePollsOpenTransaction(transaction);
		   return value;
	   }

	   public async Task<PollbookTransactionResponse> CreatePollsClosedTransaction(PollbookTransactionDto transaction, string username)
	   {
		   transaction.TransactionType = TransactionType.PollsClose.ToString();
		   var value = await _pollbookTransactionFactory.CreatePollsCloseTransaction(transaction, username);
		   return value;
	   }

	   public async Task<PollbookTransactionResponse> CreateLoginTransaction(PollbookTransactionDto transaction, UserDto user)
	   {
		   transaction.TransactionType = TransactionType.SoftwareSignIn.ToString();
		   var value = await _pollbookTransactionFactory.CreateUserLoginTransaction(transaction, user);
		   return value;
	   }

	   public async Task<PollbookTransactionResponse> CreateLogoutTransaction(PollbookTransactionDto transaction, UserDto user)
	   {
		   transaction.TransactionType = TransactionType.SoftwareSignOut.ToString();
		   var value = await _pollbookTransactionFactory.CreateUserLogoutTransaction(transaction, user);
		   return value;
	   }

	   public async Task<VoterDto> GetVoterSignatureFromTransactionAsync(string transactionGuid)
	   {
		   var results = await _pollbookTransactionRepository.GetVoterSignatureFromTransactionAsync(transactionGuid);
		   return results;
	   }

	   public string GetProvisionalId()
	   {
		   // Example of getting a segment from ticks:
		   // November 21, 2019 12:25:46: 637099359460000000
		   // One second later:           637099359470000000
		   // Ten seconds later:          637099359570000000
		   // We are using tens of seconds up to 999,999 tens-of-seconds (about 115 days), so we start at index 4 and take the next 6 digits.
		   // This would give us "993595" out of the ticks string "637099359570000000".
		   string sequenceNumber = DateTime.UtcNow.Ticks.ToString().Substring(4, 6); // Tens of seconds, from 000,000 to 999,999.
		   return _maintenanceFacade.GetSystemId() + sequenceNumber;
	   }

	   public async Task<PollbookTransactionResponse> CreateUserShutdownTransaction(PollbookTransactionDto transaction)
	   {
		   transaction.TransactionType = TransactionType.SoftwareExit.ToString();
		   var value = await _pollbookTransactionFactory.CreateUserShutdownTransaction(transaction);
		   return value;
	   }

	   public async Task<PollbookTransactionResponse> CreateErrorTransaction(PollbookTransactionDto transaction, string errorMsg, string parentTransId,
		   bool isMaintenance = false)
	   {
		   transaction.TransactionType = TransactionType.SoftwareError.ToString();
		   var value = await _pollbookTransactionFactory.CreateErrorMessageTransaction(transaction, errorMsg, parentTransId, isMaintenance);
		   return value;
	   }

	   public async Task<PollbookTransactionResponse> CreateIncrementalUpdateTransaction(PollbookTransactionDto transaction, int progressIndicatorId,
		   bool isMaintenance = false)
	   {
		   transaction.TransactionType = TransactionType.IncrementalUpdate.ToString();
		   var value = await _pollbookTransactionFactory.CreateIncrementalUpdateTransaction(transaction, progressIndicatorId, isMaintenance);
		   return value;
	   }

	   #endregion

	   public async Task DetachBackupTransactionLog()
	   {
		   await _pollbookTransactionRepository.DetachBackupTransactionLog();
	   }

	   public HashSet<string> GetTransactionGuids()
	   {
		   return _pollbookTransactionRepository.GetTransactionGuids();
	   }

	   public HashSet<string> GetEditTransactionGuids()
	   {
		   return _pollbookTransactionRepository.GetEditTransactionGuids();
	   }

	   public HashSet<string> GetAddTransactionGuids()
	   {
		   return _pollbookTransactionRepository.GetAddTransactionGuids();
	   }

	   public HashSet<string> GetStatusTransactionGuids()
	   {
		   return _pollbookTransactionRepository.GetStatusTransactionGuids();
	   }

	   public string GetLastTransactionGuid()
	   {
		   return _pollbookTransactionRepository.GetLastTransactionGuid();
	   }

	   public async Task<bool> GetIsPollOpenAsync()
	   {
		   var value = await _pollbookTransactionRepository.GetIsPollOpenAsync();
		   return value;
	   }

	   public Task ProcessFailedTransactionAsync(CancellationToken token = default, bool isFromBackGroundProcess = false)
	   {
		   return _pollbookTransactionFactory.ProcessFailedTransactionAsync(isFromBackGroundProcess, token);
	   }

	   public Task<PollbookTransactionDto> GetMaxHostTransactionIdAsync(string systemId)
	   {
		   return _pollbookTransactionRepository.GetMaxHostTransactionIdAsync(systemId);
	   }

	   public async Task<IEnumerable<PollbookTransactionDto>> GetPendingLocalTransactionsToUploadAsync()
	   {
		   var result = await _pollbookTransactionRepository.GetPendingLocalTransactionsToUploadAsync();
		   return result;
	   }

	   public bool ProcessTransactionQueue(PollbookTransactionDto transaction, string processType)
		{
			LocalResponse response = null;

			switch (processType.ToLowerInvariant())
			{
				case "insert":
                    response = _pollbookTransactionRepository.InsertTransaction(transaction);
					break;
				case "update":
					response = _pollbookTransactionRepository.UpdateHostTransactionId(transaction.TransactionGuid, transaction.HostTransactionId);
                    break;
				default:
					_essLogger.LogError($"Process transaction queue was requested for an unknown process type of {processType}.");
					return false;
            }

            if (!response.IsSuccess)
				_essLogger.LogError($"Error encountered for {processType} failed transaction {transaction.TransactionGuid}.");

			return response.IsSuccess;
        }

	   public Task<long> GetPollbookTransactionsCountAsync()
	   {
		   return _pollbookTransactionRepository.GetPollbookTransactionsCountAsync();
	   }

	   public Task<long> GetHostTransactionsCountAsync()
	   {
		   return _pollbookTransactionRepository.GetHostTransactionsCountAsync();
	   }

	   public Task<long> GetFailedTransactionsCountAsync()
	   {
		   return _pollbookFailedTransactionRepository.GetCountAsync();
	   }

	   public async Task GetVoterTransactionsForProcessing()
	   {
		   await _incrementalUpdatesFactory.GetVoterTransactionsForProcessing();
	   }

	   public async Task<int> GetLastVoterBallotPollingPlaceIdAsync(string voterKey)
	   {
		   try
		   {
			   var transaction = await GetElectionBallotTransactionsByVoterKeyAsync(voterKey);
			   return transaction?.PollingPlaceId ?? 0;
		   }
		   catch (Exception ex)
		   {
			   var logProps = new Dictionary<string, string>
				   { { "Action", "PollbookTransactionFacade.GetLastVoterBallotPollingPlaceIdAsync" } };
			   _essLogger.LogError(ex, logProps);
		   }

		   return 0;
	   }
   }
}
