using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Model;
using ESS.Pollbook.Core.Model.Transactions;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace ESS.Pollbook.Facade.PollbookTransaction
{
    public interface IPollbookTransactionFacade
    {
        Task<VoterDto> GetVoterSignatureFromTransactionAsync(string transactionGuid);

        string GetProvisionalId();

        Task<PollbookTransactionResponse> CreateVoterAddTransaction(PollbookTransactionDto transaction);

        Task<PollbookTransactionResponse> CreateVoterEditTransaction(PollbookTransactionDto transaction, VoterDto previousVoter, bool includeAffidavits = true);

        Task<PollbookTransactionResponse> CreateCancelBallotTransaction(PollbookTransactionDto transaction);

        Task<PollbookTransactionResponse> CreatePollsOpenTransaction(PollbookTransactionDto transaction);

        Task<PollbookTransactionResponse> CreatePollsClosedTransaction(PollbookTransactionDto transaction, string username);

        Task<PollbookTransactionResponse> CreateLoginTransaction(PollbookTransactionDto transaction, UserDto user);

        Task<PollbookTransactionResponse> CreateLogoutTransaction(PollbookTransactionDto transaction, UserDto user);

        Task<PollbookTransactionResponse> CreateUserShutdownTransaction(PollbookTransactionDto transaction);

        Task<PollbookTransactionDto> GetElectionBallotTransactionsByVoterKeyAsync(string voterKey);

        Task<int> GetLastTransactionIdAsync(string guid);

        Task<bool> GetIsPollOpenAsync();

        Task<PollbookTransactionResponse> CreateErrorTransaction(PollbookTransactionDto transaction, string errorMsg, string parentTransId, bool isMaintenance = false);

        Task<PollbookTransactionResponse> CreateIncrementalUpdateTransaction(PollbookTransactionDto transaction, int progressIndicatorId, bool isMaintenance = false);

        LocalResponse InsertTransaction(PollbookTransactionDto transaction, bool retry = false);

        Task<LocalResponse> UpdateHostTransactionId(string transactionGuid, long hostTransactionId, bool retry = false);

        Task DetachBackupTransactionLog();

        void BulkInsertReconciliationTransactions(List<ResponseTransactionDto> transactionList);

        Task InitializeTransactionGuidsAsync();

        HashSet<string> GetTransactionGuids();

        HashSet<string> GetEditTransactionGuids();

        HashSet<string> GetAddTransactionGuids();

        HashSet<string> GetStatusTransactionGuids();

        string GetLastTransactionGuid();

        Task ProcessFailedTransactionAsync(CancellationToken token = default, bool isFromBackGroundProcess = false);

        Task<PollbookTransactionDto> GetMaxHostTransactionIdAsync(string systemId);
        Task<IEnumerable<PollbookTransactionDto>> GetPendingLocalTransactionsToUploadAsync();

        Task<long> GetPollbookTransactionsCountAsync();

        Task<long> GetHostTransactionsCountAsync();
        
        Task<long> GetFailedTransactionsCountAsync();

        bool ProcessTransactionQueue(PollbookTransactionDto transaction, string processType);

        Task GetVoterTransactionsForProcessing();

        Task<int> GetLastVoterBallotPollingPlaceIdAsync(string voterKey);
    }
}
