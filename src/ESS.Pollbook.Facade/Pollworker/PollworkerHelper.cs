using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.StaticValues;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace ESS.Pollbook.Facade.Pollworker
{
    public static class PollworkerHelper
    {
        private const int MAXPOLLPLACENAMEDISPLAYLENGTH = 100;

        /// <summary>
        ///     CurrentDate is the string representation of the local datetime
        ///     All datetime coming from the db is Utc
        /// </summary>
        public static string CurrentDate => DateTime.UtcNow.ToLocalTime().ToString("yyyy-MM-dd");

        public static TimeSpan SumTimeCards(List<TimeCardDto> timeCards)
        {
            var cards = timeCards.Select(c => c.TotalTimeValue).ToList();
            return cards.Aggregate(TimeSpan.Zero, (sumSoFar, nextMyObject) => sumSoFar + nextMyObject);
        }

        public static string GetPollPlaceDisplayText(string pollPlace)
        {
            if (pollPlace?.Length > MAXPOLLPLACENAMEDISPLAYLENGTH)
                return pollPlace.Substring(0, MAXPOLLPLACENAMEDISPLAYLENGTH);
            return pollPlace;
        }

        //borrowed from VoterView viewmodel
        public static string GetPollworkerFullName(PollworkerDto pollworker)
        {
            var temp = $"{pollworker.FirstName} {pollworker.MiddleName}";

            if (!string.IsNullOrEmpty(pollworker.LastName))
            {
                temp = $"{pollworker.LastName}, " + temp.Trim();

                if (!string.IsNullOrEmpty(pollworker.NameSuffix)) temp = pollworker.NameSuffix + " " + temp;
            }

            return temp.Trim();
        }

        public static string GetClockPromptLabel(PollworkerStatus status)
        {
            var isSingleCheckInEnabled = SystemConfiguration.PollworkerConfiguration.CheckInWorkflow.Equals("Single");
            switch (status)
            {
                case PollworkerStatus.ClockedIn:
                    return DefinedText.PollbookDefinedTextDtos?.Find(n =>
                            n.PollbookDefinedTextLanguage.Equals("English") && n.PollbookDefinedTextName.Equals(
                                isSingleCheckInEnabled ? "Pollworker_CheckIn_Button" : "Pollworker_CheckOut_Button"))
                        ?.PollbookDefinedTextValue ?? status.GetDisplayAttributePromptFrom(typeof(PollworkerStatus));
                case PollworkerStatus.ClockedOut:
                    return DefinedText.PollbookDefinedTextDtos?.Find(n =>
                        n.PollbookDefinedTextLanguage.Equals("English") &&
                        n.PollbookDefinedTextName.Equals("Pollworker_CheckIn_Button"))?.PollbookDefinedTextValue ?? status.GetDisplayAttributePromptFrom(typeof(PollworkerStatus));
                case PollworkerStatus.WrongPoll:
                default:
                    return status.GetDisplayAttributePromptFrom(typeof(PollworkerStatus));
            }
        }

        public static string GetStatusNameLabel(PollworkerStatus status)
        {
            switch (status)
            {
                case PollworkerStatus.ClockedIn:
                    return DefinedText.PollbookDefinedTextDtos?.Find(n =>
                        n.PollbookDefinedTextLanguage.Equals("English") &&
                        n.PollbookDefinedTextName.Equals("Pollworker_CheckIn_Status_Text"))?.PollbookDefinedTextValue.ToUpper() ?? status.GetDisplayAttributeNameFrom(typeof(PollworkerStatus));

                case PollworkerStatus.ClockedOut:
                    return DefinedText.PollbookDefinedTextDtos?.Find(n =>
                        n.PollbookDefinedTextLanguage.Equals("English") &&
                        n.PollbookDefinedTextName.Equals("Pollworker_CheckOut_Status_Text"))?.PollbookDefinedTextValue.ToUpper() ?? status.GetDisplayAttributeNameFrom(typeof(PollworkerStatus));
                case PollworkerStatus.WrongPoll:
                default:
                    return status.GetDisplayAttributeNameFrom(typeof(PollworkerStatus));
            }
        }

        public static void AddStatusDisplayNameToPollworkers(List<PollworkerDto> pollworkers)
        {
            foreach (var pollworker in pollworkers)
            {
                pollworker.StatusDisplayName = GetStatusNameLabel(pollworker.Status);
            }
        }
    }
}
