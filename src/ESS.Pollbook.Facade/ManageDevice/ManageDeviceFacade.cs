using ESS.Pollbook.Components.Repository.PollbookTransaction;
using System.Collections.Generic;
using System.Threading.Tasks;
using ESS.Pollbook.Core.DTO;

namespace ESS.Pollbook.Facade.ManageDevice
{
    public class ManageDeviceFacade : IManageDeviceFacade
    {
        private readonly IPollbookTransactionRepository _pollbookTransactionRepository;

        public ManageDeviceFacade(IPollbookTransactionRepository pollbookTransactionRepository)
        {
            _pollbookTransactionRepository = pollbookTransactionRepository;
        }
        public async Task<DeviceCountsDto> GetDeviceBallotsIssuedCountsAsync(string machineName)
        {
            return await _pollbookTransactionRepository.GetDeviceBallotsIssuedCountsAsync(machineName);
        }

        public async Task<long> GetPendingLocalTransactionCountAsync()
        {
            return await _pollbookTransactionRepository.GetPendingVoterTransactionCountsAsync();
        }

        public async Task<long> GetPollbookTransactionCountByTypeAsync(string transactionType)
        {
            return await _pollbookTransactionRepository.GetPollbookTransactionCountByTypeAsync(transactionType);
        }

        public async Task<IEnumerable<PollbookTransactionDto>> GetPendingLocalTransactionsToUploadAsync()
        {
            return await _pollbookTransactionRepository.GetPendingLocalTransactionsToUploadAsync();
        }
    }
}
