using System.Collections.Generic;
using System.Threading.Tasks;
using ESS.Pollbook.Core.DTO;

namespace ESS.Pollbook.Facade.ManageDevice
{
    public interface IManageDeviceFacade
    {
        Task<DeviceCountsDto> GetDeviceBallotsIssuedCountsAsync(string machineName);

        Task<long> GetPendingLocalTransactionCountAsync();

        Task<long> GetPollbookTransactionCountByTypeAsync(string transactionType);

        Task<IEnumerable<PollbookTransactionDto>> GetPendingLocalTransactionsToUploadAsync();
    }
}
