using ESS.Pollbook.Components.Repository.VoterSearch;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.Model;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.HostSync;
using ESS.Pollbook.Facade.VoterEligibility;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;

namespace ESS.Pollbook.Facade.VoterSearch
{
	public class VoterSearchFacade : IVoterSearchFacade
	{
		private readonly IVoterSearchRepository _repository;
		private readonly IVoterEligibilityFacade _eligibilityFacade;
		private readonly IHostSyncFacade _hostSyncFacade;
		private readonly IEssLogger _essLogger;

		public VoterSearchFacade(IVoterSearchRepository voterSearchRepository,
			IVoterEligibilityFacade eligibilityFacade,
			IHostSyncFacade hostSyncFacade, IEssLogger essLogger)
		{
			_eligibilityFacade = eligibilityFacade;
			_hostSyncFacade = hostSyncFacade;
			_repository = voterSearchRepository;
			_essLogger = essLogger;
		}

		public async Task<VoterSearchResponse> SearchVoterAsync(VoterNameSearchRequest param,
			List<ElectionJurisdictionEnumValueDto> enumerationList, CancellationToken cancellationToken = default)
		{
			if (param == null || cancellationToken.IsCancellationRequested)
				return new VoterSearchResponse();

			// Start the overall timer
			var stopwatch = Stopwatch.StartNew();
			_essLogger.LogInformation("Starting SearchVoterAsync...");

			param.HasHostResults = false;

			if (param.IsBarcodeSearch)
			{
				_essLogger.LogInformation("Performing barcode-based voter search...");
				var barcodeTimer = Stopwatch.StartNew();

				var response =
					await BarcodeScannerVoterSearchResponse(param, enumerationList,
						SystemConfiguration.ElectionConfiguration, cancellationToken);

				barcodeTimer.Stop();
				_essLogger.LogInformation($"Barcode search completed in {barcodeTimer.ElapsedMilliseconds} ms.");
				return response;
			}

			_essLogger.LogInformation("Performing manual voter search...");
			var manualSearchTimer = Stopwatch.StartNew();

			var result =
				await ManualVoterSearchResponse(param, enumerationList, SystemConfiguration.ElectionConfiguration,
					cancellationToken);

			manualSearchTimer.Stop();
			_essLogger.LogInformation($"Manual search completed in {manualSearchTimer.ElapsedMilliseconds} ms.");

			stopwatch.Stop();
			_essLogger.LogInformation($"Total search time: {stopwatch.ElapsedMilliseconds} ms.");
			return result;
		}

		public async Task<VoterDto> GetVoterByVoterSourceKey(string voterKey)
		{
			return await _repository.GetVoterByVoterSourceKey(voterKey);
		}

		private async Task<VoterSearchResponse> ManualVoterSearchResponse(VoterNameSearchRequest param,
			List<ElectionJurisdictionEnumValueDto> enumerationList, PollbookConfigurationDto config,
			CancellationToken cancellationToken)
		{
			var sw = Stopwatch.StartNew();
			var swTotal = Stopwatch.StartNew();

			_essLogger.LogInformation("Entered Local Manual Search Response");

			if (cancellationToken.IsCancellationRequested)
			{
				_essLogger.LogInformation("Operation canceled before starting.");
				return new VoterSearchResponse();
			}

			var enableFullDob = EnableFullDob(config.EnableFullDob);

			sw.Restart();
			_essLogger.LogInformation("Starting local voter database search...");

			// Perform local DB search or return empty list if the request is invalid
			var votersFromLocaldb = IsValidVoterSearchRequest(param)
				? (await _repository.DoVoterSearchAsync(param, enableFullDob, config.SourceKeySearchType, cancellationToken)
				   ?? new List<VoterDto>())
				: new List<VoterDto>();

			_essLogger.LogInformation($"Local voter database search completed in {sw.ElapsedMilliseconds}ms");

			if (cancellationToken.IsCancellationRequested)
			{
				_essLogger.LogInformation("Operation canceled after local database search.");
				return new VoterSearchResponse();
			}

			var voterCount = votersFromLocaldb.Count();
			if (voterCount > param.Configuration.MaxResultThreshold) //check if voter count > 25
			{
				_essLogger.LogInformation($"Search result exceeds threshold ({param.Configuration.MaxResultThreshold}). Found: {voterCount} voters.");

				if (cancellationToken.IsCancellationRequested)
				{
					_essLogger.LogInformation("Operation canceled before creating local response due to high threshold.");

					return new VoterSearchResponse();
				}

				sw.Restart();
				var response = await CreateVoterSearchResponse(votersFromLocaldb, param, enumerationList, cancellationToken);
				_essLogger.LogInformation($"Created local voter search response for over-threshold results in {sw.ElapsedMilliseconds}ms.");
				_essLogger.LogInformation($"Total search response creation time: {swTotal.ElapsedMilliseconds}ms.");
				return response;
			}

			// Host search logic
			if (SystemDetails.IsHostConnected && !SystemDetails.IsHostSlow)
			{
				_essLogger.LogInformation("Starting the Host Search.");
				sw.Restart();

				var pollbookSearchRequest = CreatePollbookVoterSearchRequest(param, config);
				_essLogger.LogInformation($"Host search request created: PollingPlaceId={pollbookSearchRequest.PollingPlaceId}.");

				if (cancellationToken.IsCancellationRequested)
				{
					_essLogger.LogInformation("Operation canceled before executing host search.");
					return new VoterSearchResponse();
				}

				var votersFromHost = await _hostSyncFacade.GetVoterSearchResults(pollbookSearchRequest, cancellationToken);
				_essLogger.LogInformation($"Host search completed in {sw.ElapsedMilliseconds}ms.");

				if (votersFromHost is null)
				{
					_essLogger.LogInformation("Host search returned null. Host marked as slow. Falling back to local database results.");

					sw.Restart();
					if (cancellationToken.IsCancellationRequested)
					{
						_essLogger.LogInformation("Operation canceled before fallback response.");
						return new VoterSearchResponse();
					}

					param.HasHostResults = false;
					_essLogger.LogInformation("Create Voter Search response using local results.");

					var response = await CreateVoterSearchResponse(votersFromLocaldb, param, enumerationList, cancellationToken);
					_essLogger.LogInformation($"Fallback local response creation completed in {sw.ElapsedMilliseconds}ms.");
					_essLogger.LogInformation($"Total search response creation time: {swTotal.ElapsedMilliseconds}ms.");
					return response;

				}

				// Use host results to create the response
				if (cancellationToken.IsCancellationRequested)
				{
					_essLogger.LogInformation("Operation canceled before creating response from host results.");
					return new VoterSearchResponse();
				}

				_essLogger.LogInformation("Creating Voter Search response using host results.");

				sw.Restart();
				param.HasHostResults = true;
				_essLogger.LogInformation("Create Voter Search response using host results");

				var responseFromHost = await CreateVoterSearchResponse(votersFromHost, param, enumerationList,
					cancellationToken);

				_essLogger.LogInformation($"Host result response creation completed in {sw.ElapsedMilliseconds}ms.");
				_essLogger.LogInformation($"Total search response creation time: {swTotal.ElapsedMilliseconds}ms.");
				return responseFromHost;
			}

			// Fallback to local results if host is unavailable or slow
			_essLogger.LogInformation("No host available or host marked as slow. Creating response using local database results.");

			sw.Restart();
			if (cancellationToken.IsCancellationRequested)
			{
				_essLogger.LogInformation("Operation canceled before creating local fallback response.");
				return new VoterSearchResponse();
			}

			_essLogger.LogInformation("Create Voter Search response using local results. No host");

			var localResponse = await CreateVoterSearchResponse(votersFromLocaldb, param, enumerationList, cancellationToken);
			_essLogger.LogInformation($"Local result response creation completed in {sw.ElapsedMilliseconds}ms.");
			_essLogger.LogInformation($"Total search response creation time: {swTotal.ElapsedMilliseconds}ms.");
			return localResponse;
		}

		private async Task<VoterSearchResponse> BarcodeScannerVoterSearchResponse(VoterNameSearchRequest param,
			List<ElectionJurisdictionEnumValueDto> enumerationList, PollbookConfigurationDto config,
			CancellationToken cancellationToken)
		{
			if (cancellationToken.IsCancellationRequested)
			{
				return new VoterSearchResponse();
			}

			VoterSearchResponse response;
			if (SystemDetails.IsHostConnected && !SystemDetails.IsHostSlow)
			{
				var pollbookSearchRequest = CreatePollbookVoterSearchRequest(param, config);

				if (cancellationToken.IsCancellationRequested)
				{
					return new VoterSearchResponse();
				}

				var votersFromHost =
					await _hostSyncFacade.GetVoterSearchResults(pollbookSearchRequest, cancellationToken);

				if (votersFromHost is null)
				{
					if (cancellationToken.IsCancellationRequested)
					{
						return new VoterSearchResponse();
					}

					response = await BarcodeScannerLocalVoterSearchResponse(param, enumerationList, config,
						cancellationToken);
				}
				else
				{
					param.HasHostResults = true;

					if (cancellationToken.IsCancellationRequested)
					{
						return new VoterSearchResponse();
					}

					response = await CreateVoterSearchResponse(votersFromHost, param, enumerationList,
						cancellationToken);
				}
			}
			else
			{
				if (cancellationToken.IsCancellationRequested)
				{
					return new VoterSearchResponse();
				}

				response = await BarcodeScannerLocalVoterSearchResponse(param, enumerationList, config,
					cancellationToken);
			}

			return response;
		}

		private bool IsValidVoterSearchRequest(VoterNameSearchRequest request)
		{
			return !(string.IsNullOrEmpty(request.FirstName)
			         && string.IsNullOrEmpty(request.LastName)
			         && !request.DateOfBirth.HasValue
			         && string.IsNullOrEmpty(request.DriversLicenseNumber)
			         && string.IsNullOrEmpty(request.VoterSourceKey)
			         && string.IsNullOrWhiteSpace(request.StreetNumber)
			         && string.IsNullOrEmpty(request.StreetName)
			         && request.Party == null);
		}

		private async Task<VoterSearchResponse> BarcodeScannerLocalVoterSearchResponse(VoterNameSearchRequest param,
			List<ElectionJurisdictionEnumValueDto> enumerationList, PollbookConfigurationDto config,
			CancellationToken cancellationToken)
		{
			if (cancellationToken.IsCancellationRequested)
			{
				return new VoterSearchResponse();
			}

			var votersFromLocaldb =
				(!IsValidVoterSearchRequest(param))
					? new List<VoterDto>()
					: await _repository.DoVoterSearchAsync(param, EnableFullDob(config.EnableFullDob),
						config.SourceKeySearchType, cancellationToken) ?? new List<VoterDto>();

			param.HasHostResults = false;
			var fromLocaldb = votersFromLocaldb.ToList();

			if (cancellationToken.IsCancellationRequested)
			{
				return new VoterSearchResponse();
			}

			return await CreateVoterSearchResponse(fromLocaldb, param, enumerationList, cancellationToken);
		}

		private PollbookVoterSearchRequest CreatePollbookVoterSearchRequest(VoterNameSearchRequest param,
			PollbookConfigurationDto config)
		{
			var result = new PollbookVoterSearchRequest
			{
				SearchFilter = param.SearchFilter.ToString(),
				SearchFilterValue = param.SearchFilterValue,
				PollingPlaceId = LoggedInPollplaceInfo.LoggedInPollPlace.PollingPlaceId
			};

			if (!string.IsNullOrEmpty(param.VoterSourceKey))
			{
				switch (config.SourceKeySearchType.ToLower())
				{
					case "sourcekey":
						result.VoterSourceKey = param.VoterSourceKey;
						break;
					case "affidavit":
						result.VoterAffidavitNumber = param.VoterSourceKey;
						break;
					default:
						result.VoterSourceKey = param.VoterSourceKey;
						result.VoterAffidavitNumber = param.VoterSourceKey;
						break;
				}
			}

			if (!string.IsNullOrEmpty(param.LastName))
				result.VoterLastName = param.LastName;

			if (!string.IsNullOrEmpty(param.FirstName))
				result.VoterFirstName = param.FirstName;

			result.VoterDateofBirth = param.DateOfBirth;

			if (!string.IsNullOrEmpty(param.DriversLicenseNumber))
				result.VoterDriversLicense = param.DriversLicenseNumber;

			if (!string.IsNullOrEmpty(param.StreetNumber))
				result.HouseNumber = param.StreetNumber;

			if (!string.IsNullOrEmpty(param.StreetName))
				result.StreetName = param.StreetName;

			if (param.Party != null)
				result.PartyId = param.Party.PartyId;

			return result;
		}

		private bool? EnableFullDob(string configValue)
		{
			if (string.IsNullOrEmpty(configValue) || configValue.Equals("none", StringComparison.OrdinalIgnoreCase))
				return null;

			if (configValue.Equals("only_year", StringComparison.OrdinalIgnoreCase))
				return false;

			if (configValue.Equals("full_dob", StringComparison.OrdinalIgnoreCase))
				return true;

			return null;
		}

		private async Task<VoterSearchResponse> CreateVoterSearchResponse(IEnumerable<VoterDto> voters,
			VoterNameSearchRequest param, List<ElectionJurisdictionEnumValueDto> enumerationList,
			CancellationToken cancellationToken)
		{
			var sw = Stopwatch.StartNew();
			var response = new VoterSearchResponse
			{
				SearchResultStatus = SearchResultStatusEnum.NoResults,
				VoterResults = new List<VoterSearchDto>()
			};

			if (cancellationToken.IsCancellationRequested)
			{
				return response;
			}

			int votersCount = voters.Count();
			_essLogger.LogInformation($"Entered voterSearch Response for {votersCount} voters");
			if (votersCount > 0 && votersCount <= param.Configuration.MaxResultThreshold)
			{
				if (cancellationToken.IsCancellationRequested)
				{
					return response;
				}

				response.SearchResultStatus =
					votersCount == 1 ? SearchResultStatusEnum.Result : SearchResultStatusEnum.Results;
				response.VoterResults = await CheckEligibility(voters, param.PollPlace, param.SystemStats,
					param.HasHostResults, enumerationList, cancellationToken);
			}

			// Show no results when there are more than the allowed threshold amount.  The UI will show a message instead.
			if (votersCount > param.Configuration.MaxResultThreshold)
			{
				response.SearchResultStatus = SearchResultStatusEnum.TooManyResults;
			}

			sw.Stop();
			_essLogger.LogInformation($"Time to create the Voter Search response: {sw.ElapsedMilliseconds}ms");

			return response;
		}

		private async Task<List<VoterSearchDto>> CheckEligibility(IEnumerable<VoterDto> voters, PollPlaceDto pollPlace,
			SystemStatsDto systemStats, bool HasHostResults,
			List<ElectionJurisdictionEnumValueDto> electionJurisdictionEnumValueDtos,
			CancellationToken cancellationToken)
		{
			List<VoterSearchDto> ret = new List<VoterSearchDto>();

			if (cancellationToken.IsCancellationRequested)
			{
				return ret;
			}

			if (pollPlace != null)
			{
				_essLogger.LogInformation("In VoterSearchFacade.CheckEligibility, checking eligibility for " + voters.Count() + " voters");
				foreach (var voter in voters)
				{
					try
					{
						VoterSearchDto search = new VoterSearchDto(voter);
						VoterP2PStatusModel voterStatus = null;
						if (HasHostResults)
						{
							voterStatus = new VoterP2PStatusModel()
							{
								VoterId = (long)voter.VoterId,
								VoterSourceKey = voter.VoterKey,
								VoterBallotCredit = (short?)(voter.IsPreviouslyVoted ? 1 : 0),
								VoterBallotStatusProvisionalBallotIndicator =
									voter.VoterBallotStatusProvisionalBallotIndicator,
								VoterBallotStatusEarlyVoteIndicator = voter.VoterBallotStatusEarlyVoteIndicator,
								BallotRecordUpdateApplicationDatetime = voter.BallotRecordUpdateApplicationDatetime,
								VoterStatusJurisdictionEnumId = voter.VoterStatusEnumId,
								AbsenteeStatusJurisdictionEnumId = voter.AbsenteeStatusEnumId,
								IdentificationRequirementStatusJurisdictionEnumId = voter.IdentificationStatusEnumId
							};
						}

						if (cancellationToken.IsCancellationRequested)
						{
							return ret;
						}

						search.EligibilityResult = await _eligibilityFacade.Calculate(voter, pollPlace, systemStats,
							voterStatus, true, electionJurisdictionEnumValueDtos);

						if (!HasHostResults)
						{
							if (search.EligibilityResult.AbsenteeStatusResponse != null)
							{
								voter.AbsenteeStatusEnumId =
									search.EligibilityResult.AbsenteeStatusResponse.EnumerationValueId;
							}

							if (search.EligibilityResult.VoterStatusResponse != null)
							{
								voter.VoterStatusEnumId =
									search.EligibilityResult.VoterStatusResponse.EnumerationValueId;
							}

							if (search.EligibilityResult.IdRequiredResponse != null)
							{
								voter.IdentificationStatusEnumId =
									search.EligibilityResult.IdRequiredResponse.EnumerationValueId;
							}
						}

						ret.Add(search);
					}
					catch (Exception ex)
					{
						_essLogger.LogError(ex,
							new Dictionary<string, string>
							{
								{ "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name },
								{ "Message", ex.Message }
							});
					}
				}
			}
			else
			{
				_essLogger.LogError(
					"In VoterSearchFacade.CheckEligibility, returning no results because Poll Place is null");
			}
			return ret;
		}
	}
}
