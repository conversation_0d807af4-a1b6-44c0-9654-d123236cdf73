using ESS.Pollbook.Core.DTO;
using System.Collections.Generic;
using System.Text.RegularExpressions;

namespace ESS.Pollbook.Facade.Barcode
{
    public class DriversLicenseParser : IDriversLicenseParser
    {
        private readonly Dictionary<string, string>
            _pdf417Codes = new Dictionary<string, string>
            {
                // TAB 1
                { "DCS", "Last Name" },
                { "DAB", "Family Name" }, // obsolete
                { "DBO", "Family Name" }, // obsolete
                { "DAC", "Given Name" },
                { "DCT", "Given Name" }, // obsolete
                { "DBP", "Given Name" }, // obsolete
                { "DAD", "Middle Name" },
                { "DAE", "Name Suffix" },
                { "DBR", "Suffix" },
                { "DBB", "Date of Birth" },
                { "DAQ", "License or ID Number" },
                // TAB 2
                { "DAL", "Residence Street Address1" },
                { "DAG", "Residence Street Address1" },
                { "DAM", "Residence Street Address2" },
                //{ "DAN", "Residence City" },
                { "DAI", "Residence City" },
                //{ "DAO", "Residence Jurisdiction Code" },
                { "DAJ", "Residence Jurisdiction Code" },
                { "DAP", "Residence Postal Code" },
                { "DAK", "Residence Postal Code" }
            };

        private readonly DriversLicenseDto _driverLicenseDto = new DriversLicenseDto();

        private const string SubPattern = "(.+)$";

        public DriversLicenseDto Parse2DBarcode(string licenseData)
        {
            foreach (var code in _pdf417Codes)
            {
                var pattern = "^" + code.Key + SubPattern;

                var match = Regex.Match(licenseData, pattern, RegexOptions.IgnoreCase | RegexOptions.Multiline);

                AddMatchToDto(code.Key, match.Groups[1].Value.Trim());
            }

            return _driverLicenseDto;
        }

        private void AddMatchToDto(string codeKey, string parsedData)
        {
	        switch (codeKey)
	        {
		        case "DCS":
			        _driverLicenseDto.LastName = parsedData;
			        break;
		        case "DBB":
			        _driverLicenseDto.DateOfBirth = parsedData;
			        break;
		        case "DAC":
			        _driverLicenseDto.FirstName = parsedData;
			        break;
		        // DAC has higher priority.  Should they both be set, the DAC will always win.
		        case "DCT":
			        if (string.IsNullOrEmpty(_driverLicenseDto.FirstName))
				        _driverLicenseDto.FirstName = parsedData;
			        break;
		        case "DAD":
			        _driverLicenseDto.MiddleName = parsedData;
			        break;
		        case "DAE":
			        if (string.IsNullOrEmpty(_driverLicenseDto
				            .NameSuffix)) //avoid overwriting if value has been found when we have multiple codes
				        _driverLicenseDto.NameSuffix = parsedData;
			        break;
		        case "DBR":
			        if (string.IsNullOrEmpty(_driverLicenseDto
				            .NameSuffix)) //avoid overwriting if value has been found when we have multiple codes
				        _driverLicenseDto.NameSuffix = parsedData;
			        break;
		        case "DAL":
			        if (string.IsNullOrEmpty(_driverLicenseDto
				            .ResidenceStreetAddress1)) //avoid overwriting if value has been found when we have multiple codes
				        _driverLicenseDto.ResidenceStreetAddress1 = parsedData;
			        break;
		        case "DAG":
			        if (string.IsNullOrEmpty(_driverLicenseDto
				            .ResidenceStreetAddress1)) //avoid overwriting if value has been found when we have multiple codes
				        _driverLicenseDto.ResidenceStreetAddress1 = parsedData;
			        break;
		        case "DAI":
			        _driverLicenseDto.ResidenceCity = parsedData;
			        break;
		        case "DAJ":
			        _driverLicenseDto.ResidenceJurisdictionCode = parsedData;
			        break;
		        case "DAP":
			        if (string.IsNullOrEmpty(_driverLicenseDto
				            .ResidencePostalCode)) //avoid overwriting if value has been found when we have multiple codes
				        _driverLicenseDto.ResidencePostalCode = parsedData;
			        break;
		        case "DAK":
			        if (string.IsNullOrEmpty(_driverLicenseDto
				            .ResidencePostalCode)) //avoid overwriting if value has been found when we have multiple codes
				        _driverLicenseDto.ResidencePostalCode = parsedData;
			        break;
	        }
        }
    }
}
