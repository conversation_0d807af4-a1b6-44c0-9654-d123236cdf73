using ESS.Pollbook.Components.Repository.VoterEditInfo;
using ESS.Pollbook.Components.Repository.VoterStatuses;
using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Core.Utilities;
using ESS.Pollbook.Facade.HostSync;
using ESS.Pollbook.Facade.PollbookTransaction;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ESS.Pollbook.Facade.ElectionJurisdictionEnum;

namespace ESS.Pollbook.Facade.ManageVoter
{
    public class ManageVoterFacade : IManageVoterFacade
    {
        private readonly IPollbookTransactionFacade _pollbookTransactionFacade;
        private readonly IHostSyncFacade _hostSyncFacade;
        private readonly IManageVoterRepository _manageVoterRepository;
        private readonly IVoterAddEditInfoRepository _voterAddEditInfoRepository;
        private readonly IVoterJurisdictionEnumFacade _voterEnumerationFacade;
        private readonly IEssLogger _essLogger;

        public ManageVoterFacade(IPollbookTransactionFacade pollbookTransactionFacade,
                                 IHostSyncFacade hostSyncFacade,
                                 IManageVoterRepository manageVoterRepository,
                                 IVoterAddEditInfoRepository voterAddEditInfoRepository,
                                 IVoterJurisdictionEnumFacade voterEnumerationFacade,
                                 IEssLogger essLogger)
        {
            _pollbookTransactionFacade = pollbookTransactionFacade;
            _hostSyncFacade = hostSyncFacade;
            _manageVoterRepository = manageVoterRepository;
            _voterAddEditInfoRepository = voterAddEditInfoRepository;
            _voterEnumerationFacade = voterEnumerationFacade;
            _essLogger = essLogger;
        }

        public async Task<IEnumerable<VoterStatusDto>> GetVoterStatuses()
      {
         var result = await _manageVoterRepository.GetVoterStatusesAsync((int)EssEnumeration.VoterStatus);
         return result;
      }

      public async Task ManageVoterAsync(VoterDto voter, VoterDto previousVoter)
      {
         try
         {
            var transaction = PollbookTransactionDto.Create();
            transaction.DeviceName = SystemDetails.MachineName;
            transaction.SerialNumber = SystemDetails.DeviceSerialNumber;
            transaction.SystemIdentifier = SystemDetails.SystemId;
            transaction.Voter = voter;
            transaction.SourceKey = voter.VoterKey;
            transaction.Signature = null;
            transaction.PollingPlaceId = LoggedInPollplaceInfo.LoggedInPollPlace.PollingPlaceId;
            transaction.ProcessingStatus = nameof(ProcessingStatus.Completed);
            transaction.RecordSource = nameof(ProcessingRecordSource.Pollbook);

            if (string.IsNullOrWhiteSpace(voter.FirstNameSearch))
               voter.FirstNameSearch = Helpers.GetAlphaNumericString(voter.FirstName);

            if (string.IsNullOrWhiteSpace(voter.LastNameSearch))
               voter.LastNameSearch = Helpers.GetAlphaNumericString(voter.LastName);

            var transactionTask = await _pollbookTransactionFacade.CreateVoterEditTransaction(transaction, previousVoter, false);

            await Task.Run(() => _voterAddEditInfoRepository.UpdateVoter(voter));
            await Task.Run(() => _voterAddEditInfoRepository.UpdateVoterStatus(new VoterAbsStatusUpdatesDto
            {
                VoterId = voter.VoterId,
                VoterStatusEnumId = voter.VoterStatusEnumId,
                AbsenteeStatusEnumId = voter.AbsenteeStatusEnumId,
                VoterDeleted = voter.Voter_Deleted
            }));

            if (!transactionTask.TransactionInsertSuccess) return;

            //Prepare to send the transaction to SyncPoint.
            if (!HostSyncDetails.HavePendingLocalTransactions)
            {
               transactionTask.HostSyncTransactionRequest.TransactionVoterSourceKey = voter.VoterKey;
               await _hostSyncFacade.SubmitTransactionRequest(transactionTask.HostSyncTransactionRequest);
            }

         }
         catch (Exception ex)
         {
            var logProps = new Dictionary<string, string> { { "Action", "Create status change transactions" } };
            _essLogger.LogError(ex, logProps);
         }
      }
      public async Task<int> GetEligibleAbsenteeStatusEnumId()
        {
            var absenteeStatuses = await _voterEnumerationFacade.GetAbsenteeStatuses();

            //find the first active Jurisdiction enumeration absentee status with an Enumeration Value Name of "Active" - that's our "Eligible" default
            var absenteeId = absenteeStatuses.FirstOrDefault(a =>
                    a.EnumerationValueName.Equals("Active", StringComparison.OrdinalIgnoreCase))?.JurisdictionEnumerationValueId;

            if (absenteeStatuses.Count == 0 || !absenteeId.HasValue || absenteeId == 0)
            {
                _essLogger.LogError("No absentee status enums or could not find Eligible absentee status enumeration.");
                return 0;
            }

            return absenteeId.Value;
        }
    }
}
