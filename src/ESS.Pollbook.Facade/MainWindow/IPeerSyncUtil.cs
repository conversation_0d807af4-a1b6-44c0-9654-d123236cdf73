using System.Threading;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.MasterProcessor.Abstractions.Shared;
using System.Threading.Tasks;

namespace ESS.Pollbook.Facade.MainWindow
{
    public interface IPeerSyncUtil
    {
        Task InitializePeerToPeerAsync(CancellationToken cancellationToken = default(CancellationToken));

        Task ReconcileWithPeer();

        void DisposeServiceBus();

        bool ServiceBusInitialized { get; set; }

        Task ProcessesMessageAsync(PollbookTransactionDto transaction, QueueType? queueType = null);
    }
}
