using ESS.ELLEGO.ServiceBus.Core.Interfaces;
using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Core.UICore;
using ESS.Pollbook.Facade.Configuration;
using ESS.Pollbook.Facade.HostSync;
using ESS.Pollbook.Facade.PollbookTransaction;
using ESS.Pollbook.Facade.Pollworker;
using ESS.Pollbook.MasterProcessor.Abstractions;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using ESS.Pollbook.Core.Messaging;
using GalaSoft.MvvmLight.Messaging;

namespace ESS.Pollbook.Facade.MainWindow
{
	public class MainWindowFacade : IMainWindowFacade, IDisposable
	{
		// Dependencies
		private readonly IPollbookTransactionFacade _pollbookTransactionFacade;
		private readonly IHostSyncFacade _hostSyncFacade;
		private readonly IPollworkerFacade _pollworkerFacade;
		private readonly IPollbookConfigurationFacade _pollbookConfigurationFacade;
		private readonly IPeerSyncUtil _peerSyncUtil;
		private readonly IEssServiceBus _essServiceBus;
		private readonly IEssLogger _essLogger;
		private readonly IEssPollbookQueueProcessor _queueProcessor;
		private readonly IEssPollbookFileWatcher _fileWatcher;
		private readonly IPollbookQueue _pollbookQueue;
		private readonly IMessenger _messenger;
		private const int MaxAttempts = 3;

		public MainWindowFacade(
			PollbookTransactionFacade pollbookTransactionFacade,
			IHostSyncFacade hostSyncFacade,
			IPollworkerFacade pollworkerFacade,
			IPollbookConfigurationFacade pollbookConfigurationFacade,
			IPeerSyncUtil peerSyncUtil,
			IEssServiceBus essServiceBus,
			IEssLogger essLogger,
			IEssPollbookQueueProcessor queueProcessor,
			IEssPollbookFileWatcher fileWatcher,
			IPollbookQueue pollbookQueue,
			IMessenger messenger)
		{
			_pollbookTransactionFacade = pollbookTransactionFacade;
			_hostSyncFacade = hostSyncFacade;
			_pollworkerFacade = pollworkerFacade;
			_pollbookConfigurationFacade = pollbookConfigurationFacade;
			_peerSyncUtil = peerSyncUtil;
			_essServiceBus = essServiceBus;
			_essLogger = essLogger;
			_queueProcessor = queueProcessor;
			_fileWatcher = fileWatcher;
			_pollbookQueue = pollbookQueue;
			_messenger = messenger;
		}

		private CancellationTokenSource _ctsMain;
		private CancellationTokenSource _ctsPollworkerTx;

		private sealed class Gate
		{
			private readonly SemaphoreSlim _sem = new SemaphoreSlim(1, 1);

			public async ValueTask<bool> EnterAsync(CancellationToken t)
				=> await _sem.WaitAsync(0, t).ConfigureAwait(false);

			public void Exit() => _sem.Release();
		}

		private readonly Gate _heartbeatGate = new Gate();
		private readonly Gate _downloadTxGate = new Gate();
		private readonly Gate _failedTxGate = new Gate();
		private readonly Gate _uploadTxGate = new Gate();
		private readonly Gate _peerReconcileGate = new Gate();
		private readonly Gate _voterSignatureProcessGate = new Gate();
		private readonly Gate _hostStatusGate = new Gate();

		public async Task StartAllBackgroundProcessing()
		{
			InitializeSettings();

			_ctsMain?.Cancel();
			_ctsMain = new CancellationTokenSource();
			var token = _ctsMain.Token;

			// queue processors
			RunSafeAsync(_queueProcessor.ProcessTransactionQueue, token,
				nameof(_queueProcessor.ProcessTransactionQueue));
			RunSafeAsync(_queueProcessor.ProcessPolldataQueue, token,
				nameof(_queueProcessor.ProcessPolldataQueue));

			//Process any existing files in the FileWatcher before starting the service
			_fileWatcher.QueueUnprocessedFiles();

			Task StartWatcherAsync(MainWindowFacade self, CancellationToken t)
			{
				return Task.Run(async () =>
				{
					TimeSpan delay = TimeSpan.FromSeconds(1);

					for (int attempt = 1; attempt <= MaxAttempts && !t.IsCancellationRequested; attempt++)
					{
						try
						{
							self._fileWatcher.StartFileWatchService();
							break; // success
						}
						catch (Exception ex) when (attempt < MaxAttempts)
						{
							self.LogErrorWithContext(ex,
								$"{nameof(self._fileWatcher.StartFileWatchService)} retry {attempt}");
							await Task.Delay(delay, t).ConfigureAwait(false);
							delay += delay; // simple exp. back-off
						}
						catch (Exception ex)
						{
							self.LogErrorWithContext(ex,
								nameof(self._fileWatcher.StartFileWatchService));
							throw; // re-throw to caller
						}
					}
				}, t);
			}

			await StartWatcherAsync(this, token).ConfigureAwait(false);
			var cfg = SystemConfiguration.ElectionConfiguration;

			// periodic jobs (already handle their own errors internally)
			var tasks = new List<Task>
			{
				PeriodicTask.RunPeriodicAsync(HeartbeatAsync,
					() => TimeSpan.FromSeconds(cfg.HeartbeatIntervalSeconds), token),
				PeriodicTask.RunPeriodicAsync(HostRetryAsync,
					() => TimeSpan.FromSeconds(cfg.HostConnectionRetryAttemptSeconds), token),
				PeriodicTask.RunPeriodicAsync(DownloadTxAsync,
					() => TimeSpan.FromSeconds(cfg.DownloadTransactionsSeconds), token),
				PeriodicTask.RunPeriodicAsync(ProcessFailedTxAsync,
					() => TimeSpan.FromSeconds(cfg.FailedTransactionRetryWaitSeconds), token),
				PeriodicTask.RunPeriodicAsync(UploadTxAsync,
					() => TimeSpan.FromSeconds(cfg.UploadPendingTransactionsSeconds), token),
				PeriodicTask.RunPeriodicAsync(VoterSignaturesAsync,
					() => TimeSpan.FromSeconds(cfg.DownloadTransactionsSeconds), token),
				PeriodicTask.RunPeriodicAsync(ReconcilePeersAsync,
					() => TimeSpan.FromSeconds(cfg.PeerReconciliationSeconds), token),
				PeriodicTask.RunPeriodicAsync(CheckHostStatusAsync,
					() => TimeSpan.FromSeconds(cfg.DeviceTransactionsRefreshStatsSeconds), token)
			};

			// poll-worker schedule (already wrapped internally)
			_ = StartPollworkerTransactionAsync(token);

			try
			{
				await Task.WhenAll(tasks).ConfigureAwait(false);
			}
			catch (OperationCanceledException) when (_ctsMain?.IsCancellationRequested == true)
			{
				/* normal shutdown */
			}
			catch (Exception ex)
			{
				LogErrorWithContext(ex, nameof(StartAllBackgroundProcessing));
				throw; // re-throw so callers can react if needed
			}
		}

		public void StopAllBackgroundProcessing() => _ctsMain?.Cancel();

		private async Task HeartbeatAsync(CancellationToken t)
		{
			if (!SystemDetails.IsHostConnected || !await _heartbeatGate.EnterAsync(t).ConfigureAwait(false)) return;

			try
			{
				await _hostSyncFacade.SendHeartbeatTransactionAsync(t).ConfigureAwait(false);
			}
			finally
			{
				_heartbeatGate.Exit();
			}
		}

		private async Task HostRetryAsync(CancellationToken t)
		{
			if (SystemDetails.IsHostConnected) return;
			await _hostSyncFacade.HostSyncConnectionRetryAsync(t).ConfigureAwait(false);
		}

		private async Task DownloadTxAsync(CancellationToken t)
		{
            if (!SystemConfiguration.ElectionConfiguration.HostSync)
            {
                SystemDetails.InitialHostSyncDownloadCompleted = true;
                return;
            }

            if (!SystemDetails.IsHostConnected || !await _downloadTxGate.EnterAsync(t).ConfigureAwait(false))
            {
                return;
            }

            try
			{
                await _hostSyncFacade.DownloadTransactionsAsync(t).ConfigureAwait(false);
			}
			finally
			{
                _downloadTxGate.Exit();
			}
		}

		private async Task ProcessFailedTxAsync(CancellationToken t)
		{
			
			if (!SystemDetails.InitialHostSyncDownloadCompleted || 
			    !_pollbookQueue.Transactions.IsLowPriorityQueueEmpty() ||
			    !await _failedTxGate.EnterAsync(t).ConfigureAwait(false)) 
				return;

            var sw = Stopwatch.StartNew();
            try
			{
				_essLogger.LogDebug("ProcessFailedTxAsync started.");
				await _pollbookTransactionFacade.ProcessFailedTransactionAsync(t, true);
				await _hostSyncFacade.UploadFailedTransactionsAsync(t);
				await _pollbookTransactionFacade.GetVoterTransactionsForProcessing();
			}
			finally
			{
				sw.Stop();
				_essLogger.LogDebug($"ProcessFailedTxAsync completed in {sw.ElapsedMilliseconds}ms.");
				_failedTxGate.Exit();
			}
		}

		private async Task UploadTxAsync(CancellationToken t)
		{
			if (!SystemDetails.IsHostConnected ||
				!SystemConfiguration.ElectionConfiguration.HostSync ||
				!await _uploadTxGate.EnterAsync(t)) return;
			try
			{
				await _hostSyncFacade.UploadPendingTransactionsAsync(t);
				await _pollbookConfigurationFacade.UpdateElectionSettingsIniFileTransactionCounts();
			}
			finally
			{
				_uploadTxGate.Exit();
			}
		}

		private async Task VoterSignaturesAsync(CancellationToken t)
		{
			if (!SystemDetails.IsHostConnected || !await _voterSignatureProcessGate.EnterAsync(t)) return;
			try
			{
				await _hostSyncFacade.ProcessVoterSignaturesAsync(t);
			}
			finally
			{
				_voterSignatureProcessGate.Exit();
			}
		}

		private async Task ReconcilePeersAsync(CancellationToken t)
		{
			if (!SystemConfiguration.ElectionConfiguration.PeerSync ||
			    SystemDetails.AppState != ApplicationState.SignIn ||
			    !_peerSyncUtil.ServiceBusInitialized ||
			    !_essServiceBus.Nodes.Any() ||
				!await _peerReconcileGate.EnterAsync(t)) return;

			try
			{
				await _peerSyncUtil.ReconcileWithPeer();
			}
			finally
			{
				_peerReconcileGate.Exit();
			}
		}
		
		private async Task CheckHostStatusAsync(CancellationToken t)
		{
			if (!SystemDetails.IsHostConnected ||
			    !SystemConfiguration.ElectionConfiguration.HostSync||
			    !await _hostStatusGate.EnterAsync(t).ConfigureAwait(false)) return;

			try
			{
				_messenger.Send(new HostStatusChangedMessage());
			}
			finally
			{
				_hostStatusGate.Exit();
			}
		}

		private async Task StartPollworkerTransactionAsync(CancellationToken parentToken)
		{
			if (!SystemConfiguration.ElectionConfiguration.EnablePollWorkerManagement) return;

			_ctsPollworkerTx = CancellationTokenSource.CreateLinkedTokenSource(parentToken);
			var token = _ctsPollworkerTx.Token;

			await Task.Run(async () =>
			{
				var nextUpload = DateTime.MinValue;
				var nextDownload = DateTime.MinValue;

				while (!token.IsCancellationRequested)
				{
					try
					{
						if (DateTime.UtcNow >= nextUpload)
							nextUpload = await UploadPendingPollworkerTransactionsAsync(token);

						if (DateTime.UtcNow >= nextDownload)
							nextDownload = await DownloadPollworkerTransactionsAsync(token);
					}
					catch (OperationCanceledException) when (token.IsCancellationRequested)
					{
						break;
					}
					catch (Exception ex)
					{
						LogErrorWithContext(ex, nameof(StartPollworkerTransactionAsync));
					}

					await Task.Delay(TimeSpan.FromSeconds(2), token).ConfigureAwait(false);
				}
			}, token).ConfigureAwait(false);
		}

		private async Task<DateTime> UploadPendingPollworkerTransactionsAsync(CancellationToken t)
		{
			if (!SystemDetails.IsHostConnected || !SystemConfiguration.ElectionConfiguration.EnablePollWorkerManagement) 
				return DateTime.UtcNow.AddSeconds(60);
			
			try
			{
				await _pollworkerFacade.UploadPendingPollworkerTransactionsAsync().ConfigureAwait(false);
				return DateTime.UtcNow.AddSeconds(SystemConfiguration.ElectionConfiguration
					.UploadPendingTransactionsSeconds);
			}
			catch (Exception ex)
			{
				LogErrorWithContext(ex, nameof(UploadPendingPollworkerTransactionsAsync));
			}

			return DateTime.UtcNow.AddSeconds(30); // fallback
		}

		private async Task<DateTime> DownloadPollworkerTransactionsAsync(CancellationToken t)
		{
			if (!SystemDetails.IsHostConnected || !SystemConfiguration.ElectionConfiguration.EnablePollWorkerManagement) 
				return DateTime.UtcNow.AddSeconds(60);
			
			try
			{
				await _pollworkerFacade.DownloadPollworkerTransactionsAsync(t).ConfigureAwait(false);
				return DateTime.UtcNow.AddSeconds(SystemConfiguration.ElectionConfiguration
					.DownloadTransactionsSeconds);
			}
			catch (Exception ex)
			{
				LogErrorWithContext(ex, nameof(DownloadPollworkerTransactionsAsync));
			}

			return DateTime.UtcNow.AddSeconds(30);
		}

		private void RunSafeAsync(Func<CancellationToken, Task> work, CancellationToken token, string ctx)
		{
			_ = Task.Run(() => work(token), token)
				.ContinueWith(t =>
				{
					if (t.IsFaulted && t.Exception != null)
						LogErrorWithContext(t.Exception.Flatten(), ctx);
				}, TaskContinuationOptions.ExecuteSynchronously);
		}

		private readonly Dictionary<string, string> _logProps = new Dictionary<string, string>(1);

		private void LogErrorWithContext(Exception ex, string ctx)
		{
			if (ex == null) return;
			_logProps.Clear();
			_logProps["Action"] = $"{nameof(MainWindowFacade)}.{ctx}";
			_essLogger.LogError(ex, _logProps);
		}

		private static void InitializeSettings()
		{
			var cfg = SystemConfiguration.ElectionConfiguration;
			if (cfg.VoterTransactionsWaitSeconds == 0 &&
			    int.TryParse(ConfigurationManager.AppSettings["VoterTransactionsWaitSeconds"], out var sec))
				cfg.VoterTransactionsWaitSeconds = sec;

			if (cfg.VoterTransactionsWaitSecondsMin == 0 &&
			    int.TryParse(ConfigurationManager.AppSettings["VoterTransactionsWaitSecondsMin"], out sec))
				cfg.VoterTransactionsWaitSecondsMin = sec;

			if (cfg.FailedTransactionRetryCount == 0 &&
			    int.TryParse(ConfigurationManager.AppSettings["FailedTransactionRetryCount"], out var cnt))
				cfg.FailedTransactionRetryCount = cnt;

			if (cfg.FailedTransactionRetryWaitSeconds == 0 &&
			    int.TryParse(ConfigurationManager.AppSettings["FailedTransactionRetryWaitSeconds"], out sec))
				cfg.FailedTransactionRetryWaitSeconds = sec;
			
			if (cfg.DeviceTransactionsRefreshStatsSeconds <= 0)
				cfg.DeviceTransactionsRefreshStatsSeconds = 30;
		}

		public void Dispose()
		{
			_ctsPollworkerTx?.Cancel();
			_ctsPollworkerTx?.Dispose();
			_ctsMain?.Cancel();
			_ctsMain?.Dispose();
		}
	}
}
