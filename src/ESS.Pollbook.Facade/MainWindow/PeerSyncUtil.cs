using ESS.ELLEGO.ServiceBus.Core.Interfaces;
using ESS.ELLEGO.ServiceBus.Core.Model;
using ESS.Pollbook.Components.Business.RequestResponse;
using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.Constants;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.Messaging;
using ESS.Pollbook.Core.Model;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Core.Utilities;
using ESS.Pollbook.Facade.PollbookTransaction;
using ESS.Pollbook.Facade.VoterEditInfo;
using ESS.Pollbook.MasterProcessor.Abstractions;
using ESS.Pollbook.MasterProcessor.Abstractions.Shared;
using GalaSoft.MvvmLight.Messaging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.NetworkInformation;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using ESS.Pollbook.Core.Interface;

namespace ESS.Pollbook.Facade.MainWindow
{
    public class PeerSyncResult
    {
        public EssServiceBusNode Node { get; set; }
        public int HighestCount { get; set; }
    }

    public class PeerSyncUtil : IPeerSyncUtil
    {
        private readonly IMessenger _messenger;
        private readonly IEssLogger _essLogger;
        private readonly IPollbookTransactionFacade _pollbookTransactionFacade;
        private readonly IEssServiceBus _essServiceBus;
        private readonly IRequestResponse _requestResponse;
        private readonly IVoterAddEditInfoFacade _voterAddEditInfoFacade;
        private CancellationTokenSource _cancelServiceBusToken;
        private CancellationTokenSource _reconciliationCancellationTokenSource;
        private Task _reconciliationTask;
        private readonly IPollbookQueue _pollbookQueue;
        private readonly ITransactionGuidCache _transactionGuidCache;

        public bool ServiceBusInitialized { get; set; }

        public PeerSyncUtil(IMessenger messengerService,
           IEssLogger logger,
           IPollbookTransactionFacade pollbookTransactionFacade,
           IEssServiceBus essServiceBus,
           IRequestResponse requestResponse,
           IVoterAddEditInfoFacade voterAddEditInfoFacade,
           IPollbookQueue pollbookQueue,
           ITransactionGuidCache transactionGuidCache)
        {
            _messenger = messengerService;
            _essLogger = logger;
            _pollbookTransactionFacade = pollbookTransactionFacade;
            _voterAddEditInfoFacade = voterAddEditInfoFacade;
            _essServiceBus = essServiceBus;
            _requestResponse = requestResponse;
            _pollbookQueue = pollbookQueue;
            _transactionGuidCache = transactionGuidCache;
        }

        public async Task InitializePeerToPeerAsync(CancellationToken cancellationToken = default(CancellationToken))
        {
            if (!NetworkInterface.GetIsNetworkAvailable() || ServiceBusInitialized)
            {
                return;
            }

            _essLogger.LogInformation("Inside peer to peer initialization method");

            try
            {
                // Initialize service bus
                await _essServiceBus.InitializeAsync(SystemDetails.MachineName,
                        SystemConfiguration.ElectionConfiguration.ElectionDatabaseGuid,
                        LoggedInPollplaceInfo.LoggedInPollPlace.PollingPlaceId,
                        SystemConfiguration.ElectionConfiguration.PassPhrase)
                    .ConfigureAwait(false);

                _messenger.Send(new PeerToPeerConnectedStatusMessage());

                _requestResponse.Initialize(SystemConfiguration.ElectionConfiguration.PassPhrase);
                
                // Initialize transaction GUIDs
                _essLogger.LogInformation("Initializing transaction GUIDs...");
                await _pollbookTransactionFacade.InitializeTransactionGuidsAsync()
                    .ConfigureAwait(false);

                // Initialize transaction GUID cache
                try
                {
                    _essLogger.LogInformation("Initializing transaction GUID cache...");

                    await _transactionGuidCache.RefreshFromDatabaseAsync(
                            () => _pollbookTransactionFacade.GetTransactionGuids(),
                            () => _pollbookTransactionFacade.GetAddTransactionGuids(),
                            () => _pollbookTransactionFacade.GetEditTransactionGuids(),
                            () => _pollbookTransactionFacade.GetStatusTransactionGuids())
                        .ConfigureAwait(false);

                    _transactionGuidCache.StartAutoRefresh();
                    _essLogger.LogInformation("Transaction GUID cache initialized for peer-to-peer operations");
                }
                catch (Exception ex)
                {
                    _essLogger.LogError(ex, new Dictionary<string, string>
                    {
                        { "Action", "Failed to initialize transaction GUID cache for peer sync" }
                    });
                    // Don't throw - cache failure shouldn't prevent peer sync initialization
                }

                // Check for cancellation before proceeding with service bus operations
                cancellationToken.ThrowIfCancellationRequested();

                // Initialize cancellation token for service bus
                _cancelServiceBusToken?.Cancel();
                _cancelServiceBusToken?.Dispose();
                _cancelServiceBusToken = new CancellationTokenSource();

                // Create linked token that respects both our token and the method's cancellation token
                using (var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(
                           cancellationToken, _cancelServiceBusToken.Token))
                {
                    _essServiceBus.StartReceiverAsync(linkedCts.Token);
                }

                _essLogger.LogInformation("Peer-to-peer is now listening for incoming pub/sub messages");

                // Check for cancellation before starting reconciliation
                cancellationToken.ThrowIfCancellationRequested();

                // Start reconciliation task
                _reconciliationCancellationTokenSource?.Cancel();
                _reconciliationCancellationTokenSource?.Dispose();
                _reconciliationCancellationTokenSource = new CancellationTokenSource();

                // Create linked token for reconciliation
                using (var reconciliationLinkedCts = CancellationTokenSource.CreateLinkedTokenSource(
                           cancellationToken, _reconciliationCancellationTokenSource.Token))
                {
                    _reconciliationTask = StartReconciliationTaskAsync(reconciliationLinkedCts.Token);
                }

                // Set flag only after successful initialization
                ServiceBusInitialized = true;
                _essLogger.LogInformation("Peer-to-peer initialization completed successfully");
            }
            catch (OperationCanceledException)
            {
                _essLogger.LogInformation("Peer-to-peer initialization was cancelled");
                await CleanupPartialInitializationAsync().ConfigureAwait(false);
                throw;
            }
            catch (Exception ex)
            {
                _essLogger.LogError(ex, new Dictionary<string, string>
                {
                    { "Action", "Failed to initialize peer-to-peer communication" }
                });

                // Cleanup partial initialization
                await CleanupPartialInitializationAsync().ConfigureAwait(false);
                throw;
            }
        }

        private async Task CleanupPartialInitializationAsync()
        {
            try
            {
                // Stop transaction cache auto-refresh
                _transactionGuidCache?.StopAutoRefresh();

                // Cancel and cleanup service bus token
                _cancelServiceBusToken?.Cancel();
                _cancelServiceBusToken?.Dispose();
                _cancelServiceBusToken = null;

                // Cancel and cleanup reconciliation
                _reconciliationCancellationTokenSource?.Cancel();
                if (_reconciliationTask != null)
                {
                    try
                    {
                        await _reconciliationTask.ConfigureAwait(false);
                    }
                    catch (OperationCanceledException)
                    {
                        // Expected when cancelling
                    }
                }

                _reconciliationCancellationTokenSource?.Dispose();
                _reconciliationCancellationTokenSource = null;
                _reconciliationTask = null;

                // Reset initialization flag
                ServiceBusInitialized = false;
            }
            catch (Exception ex)
            {
                _essLogger.LogError(ex, new Dictionary<string, string>
                {
                    { "Action", "Error during cleanup of partial peer-to-peer initialization" }
                });
            }
        }

        private async Task StartReconciliationTaskAsync(CancellationToken cancellationToken)
        {
            try
            {
                await Task.Delay(15 * 1000, cancellationToken);
                
                if (cancellationToken.IsCancellationRequested)
                    return;

                await ReconcileWithPeer();
                _essLogger.LogInformation("Peer to peer is now listening for reconciliation requests");
                await _requestResponse.StartReceivingReconciliationRequests(cancellationToken);
            }
            catch (OperationCanceledException)
            {
                _essLogger.LogInformation("Reconciliation task was cancelled");
            }
            catch (Exception ex)
            {
                _essLogger.LogError(ex, new Dictionary<string, string> 
                { 
                    { "Action", "Error in reconciliation task" } 
                });
            }
        }

        public async Task ProcessesMessageAsync(PollbookTransactionDto transaction, QueueType? queueType = null)
        {
            if (SystemDetails.IsHostConnected)
            {
                // if connected to host, then peer to peer should not be used for changes
                // and will use the host sync process instead
                return;
            }
            
            if (transaction == null)
            {
                _essLogger.LogWarning("Received null transaction in ProcessesMessageAsync");
                return;
            }

            // ensure HostTransactionId is zero when receiving via p2p
            transaction.HostTransactionId = 0;

            try
            {
                if (!Enum.TryParse<TransactionType>(transaction.TransactionType, out var transactionType))
                {
                    _essLogger.LogWarning($"Unknown transaction type: {transaction.TransactionType}");
                    transactionType = TransactionType.Unknown;
                }

                switch (transactionType)
                {
                    case TransactionType.VoterAdd:
                        await _voterAddEditInfoFacade.InsertVoterAndVoterAddressAsync(transaction, queueType);
                        break;
                    case TransactionType.VoterEdit:
                        _voterAddEditInfoFacade.UpdateVoterAndVoterAddress(transaction, queueType);
                        break;
                    case TransactionType.BallotCancel:
                    case TransactionType.BallotIssue:
                        _pollbookTransactionFacade.InsertTransaction(transaction);
                        break;
                    default:
                        _essLogger.LogDebug($"Unhandled transaction type: {transactionType}");
                        break;
                }
                
                // Update cache after successful transaction processing
                if (!string.IsNullOrEmpty(transaction.TransactionGuid))
                {
                    _transactionGuidCache?.AddTransactionGuid(transactionType, transaction.TransactionGuid);
                }
            }
            catch (Exception e)
            {
                _essLogger.LogError(e,
                    new Dictionary<string, string>
                    { 
                        { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name },
                        { "TransactionType", transaction.TransactionType ?? "Unknown" },
                        { "TransactionGuid", transaction.TransactionGuid ?? "Unknown" }
                    });
            }

            _messenger.Send(new UpdateBallotCountsMessage());
            _messenger.Send(new UpdateSystemStatsMessage());
        }

        public async Task ReconcileWithPeer()
        {
            if (SystemDetails.IsHostConnected)
            {
                // if connected to host, then peer to peer should not be used for changes
                // and will use the host sync process instead
                return;
            }
            
            var localTransactionGuids = _transactionGuidCache.GetTransactionGuids();
            var localEditGuids = _transactionGuidCache.GetEditTransactionGuids();
            var localAddGuids = _transactionGuidCache.GetAddTransactionGuids();
            var localStatusGuids = _transactionGuidCache.GetStatusTransactionGuids();
            
            var localChecksum = Helpers.GetMD5Checksum(localTransactionGuids);
            var localTransactionCount = localTransactionGuids.Count + localEditGuids.Count + localAddGuids.Count + localStatusGuids.Count;
            var result = await GetBestPeerToReconcileWith(localChecksum, localTransactionCount, _pollbookTransactionFacade.GetLastTransactionGuid());

            if (result.Node == null)
            {
                _essLogger.LogDebug("No peer found that has a higher count than our own.");
                return; // No peer found that has a higher count than our own count. No need to do anything more.
            }

            _essLogger.LogInformation($"This machine has {localTransactionCount} transactions, but {result.Node.DeviceName} has {result.HighestCount}, reconciling...");

            try
            {
                SystemDetails.ReconciliationInProgress = true;

                var timeoutSeconds = PeerToPeerConstants.syncResponseTimeoutSeconds;

                if (result.HighestCount > localTransactionCount + PeerToPeerConstants.maxTotalTransactionCount)
                {
                    timeoutSeconds = PeerToPeerConstants.largeSyncResponseTimeoutSeconds;
                    _messenger.Send(new DownloadInProgressMessage());
                }

                var reconciliationResult = await _requestResponse.SendRequestAsync(result.Node, new ReconciliationMessage
                {
                    // Use cached data for better performance
                    TransactionGuids = localTransactionGuids,
                    EditVoterTransactionGuids = localEditGuids,
                    AddVoterTransactionGuids = localAddGuids,
                    StatusVoterTransactionGuids = localStatusGuids,
                    ReconciliationEnvelopeHeader = new EssEnvelopeHeader
                    {
                        ElectionDatabaseGuid = SystemConfiguration.ElectionConfiguration.ElectionDatabaseGuid,
                        PollPlaceID = LoggedInPollplaceInfo.LoggedInPollPlace.PollingPlaceId
                    }
                }, timeoutSeconds);
                
                if (reconciliationResult == null)
                {
                    _essLogger.LogError($"Received null reconciliation result from {result.Node.DeviceName}");
                    return;
                }

                if (reconciliationResult.TransactionList.Count == 0
                    && reconciliationResult.EditTransactionList.Count == 0
                    && reconciliationResult.AddTransactionList.Count == 0
                    && reconciliationResult.StatusTransactionList.Count == 0)
                {
                    _essLogger.LogError($"No transactions received from {result.Node.DeviceName}");
                    return;
                }

                if (SystemDetails.AppState != ApplicationState.SignIn) 
                {
                    _essLogger.LogWarning("Application state changed during reconciliation, aborting");
                    return;
                }

                _essLogger.LogInformation($"Reconciliation received {reconciliationResult.TransactionList.Count} transactions from {result.Node.DeviceName}");
                _essLogger.LogInformation($"Reconciliation received {reconciliationResult.AddTransactionList.Count} Add transactions from {result.Node.DeviceName}");
                _essLogger.LogInformation($"Reconciliation received {reconciliationResult.EditTransactionList.Count} Edit transactions from {result.Node.DeviceName}");
                _essLogger.LogInformation($"Reconciliation received {reconciliationResult.StatusTransactionList.Count} Status transactions from {result.Node.DeviceName}");
                
                var transactions = reconciliationResult.AddTransactionList
                   .Concat(reconciliationResult.EditTransactionList)
                   .Concat(reconciliationResult.StatusTransactionList)
                   .Concat(reconciliationResult.TransactionList)
                   .ToList();

                // ensure HostTransactionId is zero when receiving via p2p
                transactions.ForEach(t => t.HostTransactionId = 0);
                
                // Validate queue is available before enqueueing
                if (_pollbookQueue?.Transactions == null)
                {
                    _essLogger.LogError("PollbookQueue is not available - cannot process reconciliation transactions");
                    return;
                }

                try
                {
                    // Queue transaction processing
                    _pollbookQueue.Transactions.Enqueue(new PollbookQueueItem
                    {
                        QueueParams = new Dictionary<string, object>() 
                        { 
                            { QueueParamKeyConstants.INCREMENTAL_UPDATE_TRANSACTIONS_KEY, transactions } 
                        },
                        ItemQueueProcess = QueueProcess.HostSyncTransaction,
                        ItemQueuePriority = QueuePriority.Standard
                    });

                    _essLogger.LogDebug($"Queued {transactions.Count} transactions for HostSyncTransaction processing");

                    // Queue voter poll data processing
                    _pollbookQueue.Transactions.Enqueue(new PollbookQueueItem
                    {
                        QueueParams = new Dictionary<string, object>() 
                        { 
                            { QueueParamKeyConstants.INCREMENTAL_UPDATE_POLLDATA_TRANSACTIONS_KEY, transactions } 
                        },
                        ItemQueueProcess = QueueProcess.HostSyncVoterPolldata,
                        ItemQueuePriority = QueuePriority.Standard
                    });

                    _essLogger.LogDebug($"Queued {transactions.Count} transactions for HostSyncVoterPolldata processing");
                }
                catch (Exception ex)
                {
                    _essLogger.LogError(ex, new Dictionary<string, string>
                    {
                        { "Action", "Failed to enqueue reconciliation transactions" },
                        { "TransactionCount", transactions.Count.ToString() }
                    });
            
                    // Still update UI even if queue fails
                    _messenger.Send(new UpdateBallotCountsMessage());
                    return;
                }

                // Send update messages once after successful queueing
                _messenger.Send(new UpdateBallotCountsMessage());
                _messenger.Send(new DownloadInProgressMessage());
                _messenger.Send(new UpdateSystemStatsMessage());
            }
            catch (Exception ex)
            {
                _essLogger.LogError(ex, new Dictionary<string, string>
                {
                    { "Action", "Failed during peer reconciliation process" },
                    { "PeerDevice", result.Node?.DeviceName ?? "Unknown" }
                });
        
                // Send error notification
                _messenger.Send(new UpdateBallotCountsMessage());
            }
            finally
            {
                SystemDetails.ReconciliationInProgress = false;
            }
        }

        private async Task<PeerSyncResult> GetBestPeerToReconcileWith(string localChecksum, int localCount,
            string localLastTransactionGuid)
        {
            var currentNodes = _essServiceBus.Nodes.Keys.ToList();
            var highestNodes = new List<EssServiceBusNode>();
            EssServiceBusNode highestNode = null;

            var highestNumberOfTransactions = localCount;

            foreach (EssServiceBusNode node in currentNodes)
            {
                try
                {
                    _essLogger.LogDebug($"Sending reconciliation request to {node}");
                    var numPeerTransactions = await _requestResponse.SendRequestAsync(node, new ReconciliationMessage
                    {
                        CountOnly = true,
                        ReconciliationEnvelopeHeader = new EssEnvelopeHeader
                        {
                            ElectionDatabaseGuid = SystemConfiguration.ElectionConfiguration.ElectionDatabaseGuid,
                            PollPlaceID = LoggedInPollplaceInfo.LoggedInPollPlace.PollingPlaceId
                        }
                    });
                    
                    if (numPeerTransactions == null)
                    {
                        _essLogger.LogWarning($"Received null response from {node}");
                        continue;
                    }

                    _essLogger.LogDebug(
                        $"{node} response had {numPeerTransactions.NumberOfVoterTransactions} voter transactions, {numPeerTransactions.NumberOfEditTransactions} edit transactions and {numPeerTransactions.NumberOfAddTransactions} add transactions... ");

                    // Include status transactions in the count
                    var totalPeerTransactions = numPeerTransactions.NumberOfVoterTransactions +
                                                numPeerTransactions.NumberOfEditTransactions +
                                                numPeerTransactions.NumberOfAddTransactions +
                                                numPeerTransactions.NumberOfStatusTransactions;
                    var peerChecksum = numPeerTransactions.TransactionsChecksum;

                    if (totalPeerTransactions < highestNumberOfTransactions)
                    {
                        _essLogger.LogDebug($"{node} not being selected to reconcile with");
                        continue;
                    }

                    if (totalPeerTransactions > highestNumberOfTransactions)
                    {
                        highestNodes.Clear();
                        highestNumberOfTransactions = totalPeerTransactions;
                        highestNodes.Add(node);
                        continue;
                    }

                    if (totalPeerTransactions == highestNumberOfTransactions
                        && highestNodes.Count == 0
                        && (numPeerTransactions.LastTransactionGuid != localLastTransactionGuid
                            || peerChecksum != localChecksum))
                    {
                        highestNodes.Add(node);
                    }
                }
                catch (Exception ex)
                {
                    _essLogger.LogError(ex,
                        new Dictionary<string, string>
                            { { "Message", $"Experienced a problem with reconciliation request with {node}" } });
                }
            }

            if (highestNodes.Any())
            {
                _essLogger.LogDebug($"There are {highestNodes.Count} peer(s) with more transactions than me.");
                highestNode = highestNodes[CryptoRandom.Instance.Next(highestNodes.Count)];
                _essLogger.LogDebug($"There were {highestNodes.Count} to select from {highestNode} has been selected");
            }

            return new PeerSyncResult
            {
                Node = highestNode,
                HighestCount = highestNumberOfTransactions
            };
        }

        public void DisposeServiceBus()
        {
            try
            {
                if (!SystemConfiguration.ElectionConfiguration.PeerSync || !ServiceBusInitialized)
                    return;

                // Stop the cache auto-refresh
                _transactionGuidCache?.StopAutoRefresh();

                // Cancel and dispose reconciliation task
                _reconciliationCancellationTokenSource?.Cancel();
                
                // Use try-catch for graceful shutdown with timeout
                try
                {
                    _reconciliationTask?.Wait(TimeSpan.FromSeconds(5)); // Wait up to 5 seconds for graceful shutdown
                }
                catch (AggregateException ex) when (ex.InnerExceptions.OfType<OperationCanceledException>().Any())
                {
                    _essLogger.LogDebug("Reconciliation task cancelled successfully");
                }
                catch (TimeoutException)
                {
                    _essLogger.LogWarning("Reconciliation task did not complete within timeout period");
                }

                _reconciliationCancellationTokenSource?.Dispose();

                _cancelServiceBusToken?.Cancel();
                _cancelServiceBusToken?.Dispose();
                ServiceBusInitialized = false;
                _essServiceBus?.Dispose();
                _requestResponse?.StopReceivingRequests();
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string> { { "Action", "DisposeServiceBus" } };
                _essLogger.LogError(ex, logProps);
            }
        }
    }
}