using ESS.Pollbook.Core.DTO;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ESS.Pollbook.Facade.PrecinctSplit
{
    public interface IPrecinctSplitFacade
    {
        Task<PrecinctSplitDto> GetPrecinctSplitNameByPrecinctSplitId(int precinctSplitId);

        Task<IEnumerable<PrecinctSplitDto>> GetPrecinctSplitsByNameSearch(string search);

        Task<IEnumerable<PrecinctSplitDto>> GetPrecinctSplitsNameByPollingPlaceAndSearchTerm(int? pollingPlaceId,
            string search);

        Task<PrecinctSplitBallotStylePartyDto> GetPrecinctSplitDisplayNameByBallotStyleIdAndPrecinctSplitBallotStyleId(
            int ballotStyleId, int precinctSplitBallotStyleId);

        Task<IEnumerable<PrecinctSplitDto>> GetPrecinctSplitsNameByPollingPlace(int? pollingPlaceId);
    }
}
