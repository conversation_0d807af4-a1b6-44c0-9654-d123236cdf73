using ESS.Pollbook.Core.DTO;
using System.Threading.Tasks;

namespace ESS.Pollbook.Facade.SystemStats
{
    public interface ISystemStatsFacade
    {
        Task<SystemStatsDto> GetBypollingPlaceCounty(int countyId, int pollingPlaceId);

        Task<BallotsIssuedCountDto> GetBallotCountsForThisPollingPlace();

        Task<LocalBallotsIssuedCountDto> GetLocalBallotCountsForThisPollingPlace();
        Task<bool> GetIsPollOpenAsync();
    }
}
