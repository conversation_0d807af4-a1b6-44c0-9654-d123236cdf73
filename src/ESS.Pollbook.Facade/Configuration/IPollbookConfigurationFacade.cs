using ESS.Pollbook.Core.DTO;
using System.Threading.Tasks;

namespace ESS.Pollbook.Facade.Configuration
{
   public interface IPollbookConfigurationFacade
   {
      PollbookConfigurationDto GetPollbookConfiguration();

      Task UpdateConfigAsync(string name, string value);

      Task SetSelectedExpressPollPrinter(int printerId);

      Task SetSelectedBODPrinter(int printerId);

      Task RefreshAsync(bool forceRefresh);

      Task<DownloadConfigurationsDto> RetrieveConfigurationUpdatesAsync();

      Task<int> CheckAndApplyConfigurationsUpdatesAsync();

      Task<int> CheckAndApplyPollbookConfigurationsUpdatesAsync(DownloadConfigurationsDto downloadConfigs = null);

      Task UpdateElectionSettingsIniFile();

      Task UpdateElectionSettingsIniFileTransactionCounts();
   }
}