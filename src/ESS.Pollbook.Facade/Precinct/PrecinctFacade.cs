using ESS.Pollbook.Components.Repository.Precinct;
using ESS.Pollbook.Core.DTO;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ESS.Pollbook.Facade.Precinct
{
    public class PrecinctFacade : IPrecinctFacade
    {
        private readonly IPrecinctRepository _precinctRepository;

        public PrecinctFacade(IPrecinctRepository precinctRepository)
        {
            _precinctRepository = precinctRepository;

        }

        public async Task<PrecinctDto> GetPrecinctByPrecinctSplitId(int id)
        {
            return await _precinctRepository.GetPrecinctByPrecinctSplitId(id);
        }

        public async Task<IEnumerable<PrecinctDto>> GetAllPrecincts()
        {
            return await _precinctRepository.GetAllPrecincts();
        }
    }
}
