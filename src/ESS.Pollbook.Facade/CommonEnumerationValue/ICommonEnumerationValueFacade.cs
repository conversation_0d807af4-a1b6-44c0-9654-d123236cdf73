using ESS.Pollbook.Core.DTO;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ESS.Pollbook.Facade.CommonEnumerationValue
{
    public interface ICommonEnumerationValueFacade
    {
        Task<IEnumerable<CommonEnumerationValueDto>> GetAllCommonEnumerationValuesAsync();

        Task<int> GetCommonEnumerationValueIdAsync(int enumValueNumber, int enumerationId);

        Task<IEnumerable<CommonEnumerationValueDto>> GetMediaTypesListAsync();
    }
}
