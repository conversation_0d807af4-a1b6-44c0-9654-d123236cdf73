using ESS.Pollbook.Components.Repository.CommonEnumerationValue;
using ESS.Pollbook.Core.DTO;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ESS.Pollbook.Core.Common;

namespace ESS.Pollbook.Facade.CommonEnumerationValue
{
    public class CommonEnumerationValueFacade : ICommonEnumerationValueFacade
    {
        #region Private Members
        private readonly ICommonEnumerationValueRepository _commonEnumerationValueRepository;
        #endregion

        public CommonEnumerationValueFacade(ICommonEnumerationValueRepository commonEnumerationValueRepository)
        {
            _commonEnumerationValueRepository = commonEnumerationValueRepository;
        }

        /// <summary>
        /// Gets all the common enumerations values from the repository
        /// </summary>
        /// <returns></returns>
        public async Task<IEnumerable<CommonEnumerationValueDto>> GetAllCommonEnumerationValuesAsync()
        {
            return await _commonEnumerationValueRepository.CommonEnumerationValuesAsync;
        }

        /// <summary>
        /// Gets a specific ID
        /// </summary>
        /// <param name="enumValueNumber"> can be coded as such: (int)PollType.EarlyVote </param>
        /// <param name="enumerationId">   can be coded as such: (int)EssEnumeration.PollType </param>
        /// <returns></returns>
        public async Task<int> GetCommonEnumerationValueIdAsync(int enumValueNumber, int enumerationId)
        {
            var enumDto = await _commonEnumerationValueRepository.GetCommonEnumerationValueAsync(enumValueNumber, enumerationId);
            return (enumDto?.EnumerationValueId) ?? 0;
        }

        public async Task<IEnumerable<CommonEnumerationValueDto>> GetMediaTypesListAsync()
        {
	        return (await GetAllCommonEnumerationValuesAsync()).Where(x =>
		        x.EnumerationId == (int)EssEnumeration.MediaType);
        }
    }
}
