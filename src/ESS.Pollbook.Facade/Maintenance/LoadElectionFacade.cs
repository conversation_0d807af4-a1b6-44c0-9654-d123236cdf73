using ESS.Pollbook.Components.Repository.Messages;
using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.CustomExceptions;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.Model.Transactions;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.Device;
using ESS.Pollbook.Facade.PQC;
using ESS.Pollbook.Hardware.Storage;
using ESS.Pollbook.MasterProcessor.Abstractions;
using Microsoft.Win32;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;

namespace ESS.Pollbook.Facade.Maintenance
{
    public class LoadElectionFacade : ILoadElectionFacade
    {
        private readonly IEssLogger _essLogger;
        private readonly IPQCFacade _pqcFacade;
        private readonly IDeviceFacade _deviceFacade;
        private readonly IMessageRepository _messageRepository;
        private readonly IEssPollbookFileWatcher _fileWatcher;

        #region Constructor(s)

        public LoadElectionFacade(IPQCFacade pqcFacade, IDeviceFacade deviceFacade, IEssLogger essLogger, IMessageRepository messageRepository, IEssPollbookFileWatcher fileWatcher)
        {
            _essLogger = essLogger;
            _pqcFacade = pqcFacade;
            _deviceFacade = deviceFacade;
            _messageRepository = messageRepository;
            _fileWatcher = fileWatcher;
        }

        #endregion

        public async Task<LocalResponse> LoadElectionFromUSBAsync(string key)
        {
            try
            {
                var expressPollZipFileName = StorageLocator.GetUsbExpressPollLoadFiles().Find(f => f.Exists).FileName;

                // getting the backup folder
                var destinationFolder = Path.Combine(StorageLocator.DefaultDbLocation, StorageLocator.BACKUP_FOLDER);

                // moving files from destinationFolder to temporary folder defined by StorageLocator
                _deviceFacade.CreateBackupFilesInFolder(destinationFolder);

                //copy usb zip file to backup
                var localCopyExpressPollLoadFileName = await _deviceFacade.CopyFileAsync(expressPollZipFileName, destinationFolder);

                // close the db connections to the files in the DefaultDbLocation
                if (!(await _pqcFacade.CloseConnectionsAsync()))
                    throw new LoadElectionException() { Code = LoadElectionCode.CNFC };

                // moving files from folder to temporary folder defined by StorageLocator
                _deviceFacade.CreateBackupFilesInFolder(StorageLocator.DefaultDbLocation);

                // validate password on zip by comparing zip entry size to memory buffer for first file in content
                // we are making the assumption that all the files used the same password when added to the archive
                await ValidateZipFile(key, localCopyExpressPollLoadFileName);

                // extract files from the zip file in the backup folder to the DefaultDbLocation
                await _deviceFacade.ExtractAllFilesFromZipToFolderAsync(localCopyExpressPollLoadFileName, StorageLocator.DefaultDbLocation, key);

                await ValidateFiles(localCopyExpressPollLoadFileName, key);

                CopyTransactionDbToSdCard();
                _ = LoadJpFile(StorageLocator.GetJpPasswordFile);

                _deviceFacade.ClearBackupFilesInFolder(StorageLocator.DefaultDbLocation);
                _deviceFacade.ClearBackupFilesInFolder(StorageLocator.GetBackupFolder());
                DeleteHelpCenter();

                SystemDetails.IsPQCVerified = false;
                return new LocalResponse() { IsSuccess = true };
            }
            catch (Exception ex)
            {
                _deviceFacade.RestoreFilesInFolder(StorageLocator.DefaultDbLocation);
                _deviceFacade.RestoreFilesInFolder(Path.Combine(StorageLocator.GetBackupFolder()));

                _essLogger.LogError(ex,
                    new Dictionary<string, string>
                        { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });

                return new LocalResponse() { IsSuccess = false, LocalException = ex };
            }
        }

        public async Task<LocalResponse> LoadElectionFromBackupAsync(string key)
        {
            try
            {
                // get election zip file from backup folder
                var backupExpressPollZipFileName = StorageLocator.GetBackupExpressPollZipFileName();

                // Check if found zip file in backup or not
                if (string.IsNullOrEmpty(backupExpressPollZipFileName))
                    throw new LoadElectionException() { Code = LoadElectionCode.DBFNF };

                // close the db connections to the files in the DefaultDbLocation
                if (!(await _pqcFacade.CloseConnectionsAsync()))
                    throw new LoadElectionException() { Code = LoadElectionCode.CNFC };

                // moving files from folder to temporary folder defined by StorageLocator
                _deviceFacade.CreateBackupFilesInFolder(StorageLocator.DefaultDbLocation);

                // validate password on zip by comparing zip entry size to memory buffer for first file in content
                await ValidateZipFile(key, backupExpressPollZipFileName);

                // extract files from the zip file in the backup folder to the DefaultDbLocation
                await _deviceFacade.ExtractAllFilesFromZipToFolderAsync(backupExpressPollZipFileName,
                    StorageLocator.DefaultDbLocation, key);

                await ValidateFiles(backupExpressPollZipFileName, key);

                CopyTransactionDbToSdCard();
                _ = LoadJpFile(StorageLocator.GetJpPasswordFile);

                _deviceFacade.ClearBackupFilesInFolder(StorageLocator.DefaultDbLocation);
                _deviceFacade.ClearBackupFilesInFolder(StorageLocator.GetBackupFolder());
                DeleteHelpCenter();

                SystemDetails.IsPQCVerified = false;
                return new LocalResponse() { IsSuccess = true };
            }
            catch (Exception ex)
            {
                _deviceFacade.RestoreFilesInFolder(StorageLocator.DefaultDbLocation);
                _deviceFacade.RestoreFilesInFolder(Path.Combine(StorageLocator.GetBackupFolder()));

                _essLogger.LogError(ex,
                    new Dictionary<string, string>
                        { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });

                return new LocalResponse() { IsSuccess = false, LocalException = ex };
            }
        }

        private void CopyTransactionDbToSdCard()
        {
            if (!_deviceFacade.IsSdCardInserted())
                return;

            try
            {
                var sdCardPath = _pqcFacade.GetStorageLocator().GetTransactionSDCardPath();
                if (File.Exists(sdCardPath))
                {
                    File.Move(sdCardPath, _pqcFacade.GetStorageLocator().GetBackUpTransactionSDCardPath(sdCardPath));
                }

                File.Copy(_pqcFacade.GetStorageLocator().GetTransactionDataPath(), sdCardPath, overwrite: true);

                _essLogger.LogDebug("Transaction database copied to SD Card.");
            }
            catch (Exception ex)
            {
                _essLogger.LogError(ex,
                    new Dictionary<string, string>
                        { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
            }
        }

        public bool IsElectionLoaded()
        {
            var storageLocator = _pqcFacade.GetStorageLocator();
            return File.Exists(storageLocator.GetPollDataPath()) &&
                   File.Exists(storageLocator.GetTransactionDataPath()) &&
                   File.Exists(storageLocator.GetAuditLogDataPath()) &&
                   File.Exists(storageLocator.GetElectionSettingsFilePath());
        }

        private void DeleteHelpCenter()
        {
            try
            {
                foreach (var file in Directory.GetFiles(
                             Path.Combine(StorageLocator.DefaultDbLocation, StorageLocator.HELP_CENTER_FOLDER), "*"))
                {
                    File.Delete(file);
                }
            }
            catch (Exception ex)
            {
                _essLogger.LogError(ex,
                    new Dictionary<string, string>
                        { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
            }
        }

        public void ClearLogs()
        {
            var files = Directory.GetFiles(StorageLocator.DefaultDbLocation);
            foreach (var file in files.Where(f => f.ToLower().Contains("pollbook.log")))
            {
                File.Delete(file);
            }
        }

        public void ClearQueues()
        {
            _messageRepository.CleanConversationQueues();
            _fileWatcher.EmptyQueues();
        }

        private async Task ValidateFiles(string zipFileName, string password)
        {
            List<string> content;
            using (var zipFile = new ZipHandler(zipFileName, password))
            {
                content = zipFile.Content;
            }

            foreach (var file in content)
            {
                var extension = file.Substring(file.Length - 3);
                switch (extension)
                {
                    case "db3":
                        await ValidateDatabase(password, file);
                        break;
                    case "zip":
                        await ValidateZipFile(password, Path.Combine(StorageLocator.DefaultDbLocation, file));
                        break;
                }
            }
        }

        private async Task ValidateDatabase(string password, string file)
        {
            var dbType = EnumExtensions.GetSqliteDatabaseTypeFromFileName(file);

            if (!dbType.HasValue)
                throw new LoadElectionException($"[{file}] Database not found.") { Code = LoadElectionCode.DBFNF };

            var response = Task.Run(() => _pqcFacade.CreateEssSqliteWriterConnection(dbType.Value, password)).Result;

            if (!response.Valid)
                throw new LoadElectionException($"[{file}] Database not valid.") { Code = LoadElectionCode.DBINV };

            await _pqcFacade.CloseDatabaseConnectionAsync(dbType.Value);
        }

        private async Task ValidateZipFile(string password, string file)
        {
            using (var zipFile = new ZipHandler(file, password))
            {
                var fileName = zipFile.Content.FirstOrDefault();
                var zipEntry = zipFile.GetEntryFromZip(fileName);
                var buffer = await zipFile.ExtractFileToMemoryAsync(fileName);

                if (zipEntry.Size != buffer.Length)
                    throw new LoadElectionException($"[{file} Zip file password is not valid.]") { Code = LoadElectionCode.ZPFINV };
            }
        }

        public int LoadJpFile(string fileName, bool forceDelete = true)
        {
            try
            {
                if (!File.Exists(fileName))
                {
                    _essLogger.LogDebug("No file found for registry value JP.");
                    return 0;
                }

                if (string.IsNullOrWhiteSpace(File.ReadAllText(fileName)))
                {
                    _essLogger.LogDebug("The jp file was found but it is empty.");
                    return -1;
                }

                // OpenSubKey returns null if not found rather than throwing an exception.
                var regKey = Registry.LocalMachine.OpenSubKey(SystemDetails.RegBasePath,
                    RegistryKeyPermissionCheck.ReadWriteSubTree);

                if (regKey == null)
                    _essLogger.LogDebug($"The registry key {SystemDetails.RegBasePath} could not be found or opened.");

                regKey?.SetValue("JP", File.ReadAllText(fileName));

                if (forceDelete)
                    File.Delete(fileName);

                return 0;
            }
            catch (Exception ex)
            {
                _essLogger.LogError(ex,
                    new Dictionary<string, string>
                        { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
                return ex.HResult;
            }
        }
    }
}

