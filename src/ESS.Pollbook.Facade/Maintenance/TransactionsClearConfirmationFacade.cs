using ESS.Pollbook.Components.Repository.Configuration;
using ESS.Pollbook.Components.Repository.PollbookTransaction;
using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.Model;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Core.UICore;
using ESS.Pollbook.Facade.Device;
using ESS.Pollbook.Facade.HostSync;
using ESS.Pollbook.Facade.PQC;
using ESS.Pollbook.Hardware.Storage;
using ESS.Pollbook.MasterProcessor.Abstractions;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ESS.Pollbook.Facade.Maintenance
{
    public class TransactionsClearConfirmationFacade : ITransactionsClearConfirmationFacade
    {
        private readonly IMaintenanceFacade _maintenanceFacade;
        private readonly IDeviceFacade _deviceFacade;
        private readonly IHostSyncFacade _hostSyncFacade;
        private readonly IPollbookConfigurationRepository _pollbookConfigurationRepository;
        private readonly IPollbookTransactionRepository _pollbookTransactionRepository;
        private readonly IEssLogger _essLogger;
        private readonly IPQCFacade _pqcFacade;
        private readonly IEssPollbookFileWatcher _fileWatcher;

        public TransactionsClearConfirmationFacade(
            IMaintenanceFacade maintenanceFacade,
            IDeviceFacade deviceFacade,
            IHostSyncFacade hostSyncFacade,
            IPollbookConfigurationRepository pollbookConfigurationRepository,
            IEssLogger essLogger,
            IPQCFacade pqcFacade,
            IPollbookTransactionRepository transaction,
            IEssPollbookFileWatcher fileWatcher)
        {
            _essLogger = essLogger;
            _maintenanceFacade = maintenanceFacade;
            _hostSyncFacade = hostSyncFacade;
            _deviceFacade = deviceFacade;
            _pollbookConfigurationRepository = pollbookConfigurationRepository;
            _pqcFacade = pqcFacade;
            _pollbookTransactionRepository = transaction;
            _fileWatcher = fileWatcher;
        }

        public async Task<bool> ClearPollbookTransactionsAsync()
        {
            try
            {
                var clearResult = await _maintenanceFacade.ClearPollbookTransactionsAsync();
                if (!clearResult)
                {
                    _essLogger.LogInformation("Failed to clear transactions from local database");
                    return false;
                }

                ClearTransactionQueues(); // we are "clearing", only transaction queues
                _essLogger.LogInformation("Cleared transactions from local database successfully");
                return true;
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string>() { { "Action", "TransactionsClearConfirmationFacade.ClearPollbookTransactionsAsync" } };
                _essLogger.LogError(ex, logProps);
                return false;
            }
        }

        private void ClearTransactionQueues()
        {
            _fileWatcher.EmptyQueues(StorageLocator.TRANSACTION_INSERT_QUEUE_FOLDER);
            _fileWatcher.EmptyQueues(StorageLocator.TRANSACTION_UPDATE_QUEUE_FOLDER);
        }

        public async Task<bool> EnableTestMode()
        {
            try
            {
                _ = await CreateTransaction();
                await SetTestModeConfigurationAsync();

                return true;
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string>()
                    {{"Action", "TransactionsClearConfirmationFacade.EnableTestMode"}};
                _essLogger.LogError(ex, logProps);
                return false;
            }
        }

        public bool DisableTestMode()
        {
            try
            {
                _deviceFacade.SaveTestMode(false);
                SystemDetails.IsTestMode = false;
                return true;
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string>() { { "Action", "TransactionsClearConfirmationFacade.DisableTestMode" } };
                _essLogger.LogError(ex, logProps);
                return false;
            }
        }

        public async Task RollBackTestModeConfiguration(bool enableTestMode)
        {
            await Task.Run(() => _pqcFacade.CreateEssSqliteWriterConnection(SqliteDatabaseType.Polldata, SystemDetails.PQCPassword));
            if (enableTestMode)
            {
                await EnableTestMode();
            }
            else
            {
                DisableTestMode();
            }
            await Task.Run(() => _pollbookConfigurationRepository.UpdateConfig(PollbookConfigurationDto.Test_Mode, enableTestMode ? "True" : "False"));
        }

        private async Task<bool> CreateTransaction()
        {
            try
            {
                var transaction = PollbookTransactionDto.Create();
                transaction.TransactionType = nameof(TransactionType.TestMode);
                transaction.SystemIdentifier = _maintenanceFacade.GetSystemId();
                transaction.DeviceName = SystemDetails.MachineName;
                transaction.SerialNumber = SystemDetails.DeviceSerialNumber;
                transaction.ProcessingStatus = nameof(ProcessingStatus.Ignored);
                transaction.RecordSource = nameof(ProcessingRecordSource.Pollbook);

                var batteryCharge = Convert.ToInt32(_deviceFacade.GetBatteryPercentRemaining());
                var isCharging = _deviceFacade.GetIsBatteryCharging();

                PowerStatusRequest powerStatusRequest = new PowerStatusRequest
                {
                    BatteryCharge = batteryCharge,
                    ACOnline = isCharging,
                    Memory = string.Empty
                };

                var value = await _hostSyncFacade.SendTestModeTransactionAsync(transaction, powerStatusRequest);
                return value;
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string>()
                    {{"Action", "TransactionsClearConfirmationFacade.CreateTransaction"}};
                _essLogger.LogError(ex, logProps);
                return false;
            }
        }

        private async Task SetTestModeConfigurationAsync()
        {
            _essLogger.LogDebug("Changing settings due to TestMode being active.");

            _deviceFacade.SaveTestMode(true);
            SystemDetails.IsTestMode = true;
            await Task.Run(() => _pollbookConfigurationRepository.UpdateConfig(PollbookConfigurationDto.Test_Mode, "True"));
            await Task.Run(() => _pollbookConfigurationRepository.UpdateConfig(PollbookConfigurationDto.host_synchronization, "False"));
            await Task.Run(() => _pollbookConfigurationRepository.UpdateConfig(PollbookConfigurationDto.peer_synchronization, "False"));
            await Task.Run(() => _pollbookConfigurationRepository.UpdateConfig(PollbookConfigurationDto.enable_messaging, "False"));
            SystemDetails.IsPQCVerified = false;
        }
    }
}
