using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Model;
using ManagedNativeWifi;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace ESS.Pollbook.Facade.Maintenance
{
    public interface IWifiMaintenanceFacade
    {
        #region Events
        event EventHandler NetworkRefreshed;
        event EventHandler<AvailabilityChangedEventArgs> AvailabilityChanged;
        event EventHandler<InterfaceChangedEventArgs> InterfaceChanged;
        event EventHandler<ProfileChangedEventArgs> ProfileChanged;
        event EventHandler<ConnectionChangedEventArgs> ConnectionChanged;

        #endregion

        #region Scan & Lists
        /// <summary>
        /// Scans for changes in radio and networks
        /// </summary>
        /// <param name="timeout"></param>
        /// <returns></returns>
        Task ScanNetworkAsync(TimeSpan timeout);
        /// <summary>
        /// Creates a list of ProfileItems
        /// </summary>
        /// <returns></returns>
        Task<IEnumerable<NativeWifiProfileItem>> GetProfilesAsync();
        /// <summary>
        /// Creates a list of RadioItems
        /// </summary>
        /// <returns></returns>
        Task<IEnumerable<RadioItem>> GetRadiosAsync();
        /// <summary>
        /// Retrieves the Wi-Fi interface ProfileItem if it exists
        /// </summary>
        /// <returns></returns>
        Task<ProfileItem> GetWiFiProfileAsync();
        /// <summary>
        /// Retrieves a ProfileItem from list
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        Task<NativeWifiProfileItem> GetProfileAsync(string name);
        #endregion

        #region Connect & Disconnect
        bool Disconnect(ProfileItem profileItem);
        bool Disconnect();

        Task<bool> ConnectAsync(ProfileItem profileItem, TimeSpan timeSpan, CancellationToken token);
        Task<bool> ConnectAsync(string name, BssType bssType, TimeSpan timeSpan, CancellationToken token);
        Task<bool> ConnectWithNewPasswordAsync(ProfileItem profile, string password, TimeSpan timeSpan, CancellationToken token);
        bool Connect(ProfileItem profileItem);
        #endregion

        #region Profile Management
        /// <summary>
        /// Deletes profile from profile list maintained by Windows
        /// </summary>
        /// <param name="profileItem"></param>
        /// <returns></returns>
        bool Delete(ProfileItem profileItem);
        /// <summary>
        /// Creates or updates profileitem
        /// </summary>
        /// <param name="profileItem"></param>
        /// <param name="networkName"></param>
        /// <param name="securityType"></param>
        /// <param name="encryptionType"></param>
        /// <param name="key"></param>
        /// <returns></returns>
        ProfileItem SetProfile(ProfileItem profileItem, string networkName = null, bool nonbroadcast = false, string securityType = null, string encryptionType = null, string key = null);
        /// <summary>
        /// Creates a profileitem from a radioitem
        /// </summary>
        /// <param name="radioItem"></param>
        /// <param name="networkName"></param>
        /// <param name="nonbroadcast"></param>
        /// <param name="securityType"></param>
        /// <param name="encryptionType"></param>
        /// <param name="key"></param>
        /// <returns></returns>
        ProfileItem SetProfileWithRadioItem(RadioItem radioItem, string networkName, bool nonbroadcast = false, string securityType = null, string encryptionType = null, string key = null);
        /// <summary>
        /// Creates a profileitem out of a radioitem
        /// </summary>
        /// <param name="radioItem"></param>
        /// <returns></returns>
        ProfileItem CreateProfileItemFromRadioItem(RadioItem radioItem);
        #endregion

        #region Misc
        /// <summary>
        /// Security types list of authentiction to encryption mappings
        /// </summary>
        /// <returns></returns>
        IEnumerable<string> SecurityTypes();
        /// <summary>
        /// Grabs the information relating to the interface identified
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        NetworkInterfaceDto GetInterfaceInformation(Guid id);
        #endregion
    }
}
