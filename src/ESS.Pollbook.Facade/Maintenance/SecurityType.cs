using ManagedNativeWifi;
using System.Collections.Generic;

namespace ESS.Pollbook.Facade.Maintenance
{
    public class SecurityType
    {
        public string Name { get; set; }
        public string Encryption { get; set; }
        public string DisplayName { get; set; }
        public AuthenticationAlgorithm auth { get; set; }
        public CipherAlgorithm cipher { get; set; }
    }

    public class SecurityMap
    {
        public List<SecurityType> SecurityTypes { get; set; }

        public SecurityMap()
        {
            SecurityTypes = new List<SecurityType>()
            {
                new SecurityType()
                {
                    DisplayName = "Open", Name = "open", Encryption = "none",
                    auth = AuthenticationAlgorithm.Open, cipher = CipherAlgorithm.None
                },
                new SecurityType()
                {
                    DisplayName = "WPAPSK", Name = "WPAPSK", Encryption = "TKIP",
                    auth = AuthenticationAlgorithm.WPA, cipher = CipherAlgorithm.TKIP
                },
                new SecurityType()
                {
                    DisplayName = "WPA2PSK w/AES", Name = "WPA2PSK", Encryption = "AES",
                    auth = AuthenticationAlgorithm.RSNA_PSK, cipher = CipherAlgorithm.CCMP
                },
                new SecurityType()
                {
                    DisplayName = "WPA/WPA2 Mixed", Name = "WPA2PSK", Encryption = "AES",
                    auth = AuthenticationAlgorithm.RSNA, cipher = CipherAlgorithm.CCMP
                },
                new SecurityType()
                {
                    DisplayName = "WPA3SAE w/AES", Name = "WPA3SAE", Encryption = "AES",
                    auth = AuthenticationAlgorithm.WPA3_SAE, cipher = CipherAlgorithm.CCMP
                },
                new SecurityType()
                {
                    DisplayName = "WPA3 Enterprise 192", Name = "WPA3SAE", Encryption = "AES",
                    auth = AuthenticationAlgorithm.WPA3_ENT_192, cipher = CipherAlgorithm.CCMP
                }
            };
        }
    }
}
