<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
	  <TargetFramework>net48</TargetFramework>
	  <Authors>Election Systems and Software</Authors>
	  <Company>Election Systems and Software</Company>
	  <Copyright>Election Systems and Software</Copyright>
	  <IsPackable>false</IsPackable>
	  <IsTestProject>true</IsTestProject>
	  <IsPublishable>False</IsPublishable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="MSTest.TestAdapter" Version="3.8.3" />
    <PackageReference Include="MSTest.TestFramework" Version="3.8.3" />
    <PackageReference Include="System.Collections.Immutable" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ESS.Integration.SmartCardService\ESS.Integration.SmartCardService.csproj" />
  </ItemGroup>

</Project>
