<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net48</TargetFramework>
	<Authors>Election Systems and Software</Authors>
	<Company>Election Systems and Software</Company>
	<Copyright>Election Systems and Software</Copyright>
    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
    <IsPublishable>False</IsPublishable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="ManagedNativeWifi" Version="2.7.1" />
    <PackageReference Include="MSTest.TestAdapter" Version="3.8.3" />
    <PackageReference Include="MSTest.TestFramework" Version="3.8.3" />
    <PackageReference Include="coverlet.collector" Version="6.0.4">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ESS.Pollbook.Core\ESS.Pollbook.Core.csproj" />
  </ItemGroup>

	<ItemGroup>
		<Reference Include="PresentationCore">
			<HintPath>..\..\..\..\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\PresentationCore.dll</HintPath>
		</Reference>
		<Reference Include="System.ComponentModel.DataAnnotations" />
		<Reference Include="System.Configuration" />
		<Reference Include="System.Management" />
		<Reference Include="WindowsBase" />
	</ItemGroup>
	
</Project>
