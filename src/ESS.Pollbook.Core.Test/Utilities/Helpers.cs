using System;
using System.Collections.Generic;
using System.Linq;
using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Core.Utilities;
using Microsoft.VisualStudio.TestTools.UnitTesting;

namespace ESS.Pollbook.Core.Test.Utilities
{
   [TestClass]
   public class Helpers
   {
      [TestMethod]
      public void IsPeerSharableTransaction_IsSharableBecauseSourceKey()
      {
         var transaction = new PollbookTransactionDto
         {
            Voter = new VoterDto{VoterKey = Guid.NewGuid().ToString() },
            TransactionType = nameof(TransactionType.BallotIssue)
         };

         const bool expected = true;
         var actual = Core.Utilities.Helpers.IsPeerSharableTransaction(transaction);

         Assert.IsNotNull(transaction.Voter);
         Assert.IsNotNull(transaction.Voter.VoterKey);
         Assert.AreEqual(expected,actual);
      }

      [TestMethod]
      public void IsPeerSharableTransaction_IsSharableBecauseOfCustomTransaction()
      {
         var transaction = new PollbookTransactionDto
         {
            TransactionType = nameof(TransactionType.Custom)
         };

         const bool expected = true;
         var actual = Core.Utilities.Helpers.IsPeerSharableTransaction(transaction);

         Assert.IsNull(transaction.Voter);
         Assert.IsNull(transaction.Voter?.VoterKey);
         Assert.AreEqual(expected, actual);
      }

      [TestMethod]
      public void IsPeerSharableTransaction_IsNotSharableBecauseOfEmptySourceKey()
      {
         var transaction = new PollbookTransactionDto
         {
            Voter = new VoterDto(),
            TransactionType = nameof(TransactionType.BallotIssue)
         };

         const bool expected = false;
         var actual = Core.Utilities.Helpers.IsPeerSharableTransaction(transaction);

         Assert.IsNotNull(transaction.Voter);
         Assert.IsNull(transaction.Voter.VoterKey);
         Assert.AreEqual(expected, actual);
      }

      [TestMethod]
      public void GetMD5Checksum_Validate()
      {
	      var testList = new HashSet<string>
	      {
		      "6d2c7af76de14f6999bb521ad896f061",
		      "391cb35437f04d57bdad0f006341cae7",
		      "9e22cf0afc544719b2561d61efb93a6d"
	      };

         var expected = "54846DB211D37B72D7BDB7EF030D6909";
         var actual = Core.Utilities.Helpers.GetMD5Checksum(testList);
     
         Assert.AreEqual(expected, actual);
      }

      [TestMethod]
      public void CryptRandom_Min_Max_BoundryChecks()
      {
	      List<int> result = new List<int>();

	      const int min = 10;
	      const int max = 100;

	      for (int i = 0; i < 100000; ++i)
		      result.Add(CryptoRandom.Instance.Next(min, max));

	      Assert.IsFalse(result.Any(x => x < min || x > max - 1));

	      result.Clear();
	      for (int i = 0; i < 100000; ++i)
		      result.Add(CryptoRandom.Instance.Next(max));
	      Assert.IsFalse(result.Any(x => x < 0 || x > max - 1));
      }

      [TestMethod]
      public void CryptRandom_Min_BoundryChecks()
      {
	      List<int> result = new List<int>();
	      const int max = 10000;

	      for (int i = 0; i < 100000; ++i)
		      result.Add(CryptoRandom.Instance.Next(max));
	      // Lib has 0 as lowest possible (want negative random, add a negative to it.)
	      Assert.IsFalse(result.Any(x => x < 0));

	      // Do we blow up?
	      Assert.ThrowsException<ArgumentOutOfRangeException>(() => CryptoRandom.Instance.Next(-1));
      }

      [TestMethod]
      public void CryptRandom_ArgumentOutOfRangeException_Checks()
      {
	      Assert.ThrowsException<ArgumentOutOfRangeException>(() => CryptoRandom.Instance.Next(-1));
      }
   }
}
