using Microsoft.VisualStudio.TestTools.UnitTesting;
using ESS.Pollbook.Core.Model;

namespace ESS.Pollbook.Core.Test.Model
{
   /// <summary>
   /// Summary description for CDNDownloadFileRequestTests
   /// </summary>
   [TestClass]
   public class CDNDownloadFileRequestTests
   {
      private const string cdnUrl =
         @"https://esspb.cachefly.net/Protected/expiretime=1691959037;ip=***************;badurl=aHR0cDovL3d3dy5lc3N2b3RlLmNvbQ==/e59c3fac3da4befb3fd0a6a05fa9a0fc8cdec565be3c3a24a399a1d60049d75d/ep/100/100/Transactions/Trans_8_17.zip";

      [TestMethod]
      public void InstantiatingCDNDownloadFileRequest_HappyPath()
      {
         const string expected = "Trans_8_17.zip";
         var request = new CDNDownloadFileRequest(cdnUrl, "passkey");
         var actual = request.DestinationPath;

         Assert.AreEqual(expected, actual);
      }

      [TestMethod]
      public void InstantiatingCDNDownloadFileRequest_NullPath()
      {
         var request = new CDNDownloadFileRequest(null, "passkey");

         Assert.IsTrue(string.IsNullOrWhiteSpace(request.DestinationPath));
      }
   }
}
