using ESS.ELLEGO.ServiceBus.Core.Configuration;
using ESS.ELLEGO.ServiceBus.Core.Extensions;
using ESS.ELLEGO.ServiceBus.Core.Interfaces;
using ESS.ELLEGO.ServiceBus.Core.Model;
using ESS.ELLEGO.ServiceBus.Core.Model.Enums;
using ESS.ELLEGO.ServiceBus.Core.Model.Messages;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.MasterProcessor.Abstractions;
using ESS.Pollbook.MasterProcessor.Abstractions.Shared;
using NetMQ;
using NetMQ.Sockets;
using Newtonsoft.Json;
using Org.BouncyCastle.Crypto;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Management;
using System.Net;
using System.Net.NetworkInformation;
using System.Net.Sockets;
using System.Runtime.InteropServices;
using System.Threading;
using System.Threading.Tasks;
using NetMQ.Monitoring;
using static ESS.ELLEGO.ServiceBus.Core.Model.EssServiceBusNode;

namespace ESS.ELLEGO.ServiceBus.Core
{
    public sealed class EssServiceBus : IEssServiceBus
    {
        public const string PublishCommand = "P";
        public const string GetHostAddressCommand = "GetHostAddress";
        public const string AddedNodeCommand = "AddedNode";
        public const string RemovedNodeCommand = "RemovedNode";
        public const string ReconciliationCommand = "Reconciliation";

        public event NodeAddedHandler NodeAdded;
        public event NodeRemovedHandler NodeRemoved;
        public event NodesClearedHandler NodesCleared;

        // Thread-safe backing store
        private readonly ConcurrentDictionary<EssServiceBusNode, DateTime> _nodes = new ConcurrentDictionary<EssServiceBusNode, DateTime>();
        private readonly ConcurrentDictionary<string, EssServiceBusNode> _deviceNameToNodeMap = new ConcurrentDictionary<string, EssServiceBusNode>();
        private readonly ConcurrentDictionary<string, EssServiceBusNode> _addressToNodeMap = new ConcurrentDictionary<string, EssServiceBusNode>();
        
        // Cached nodes property implementation
        private volatile Dictionary<EssServiceBusNode, DateTime> _cachedNodes;
        private DateTime _lastCacheUpdate = DateTime.MinValue;
        private readonly object _cacheLock = new object();
        private static readonly TimeSpan CacheValidityPeriod = TimeSpan.FromSeconds(1);

        // Property to maintain interface compatibility with optimized caching
        public Dictionary<EssServiceBusNode, DateTime> Nodes
        {
            get
            {
                var cached = _cachedNodes;
                var now = DateTime.UtcNow;
                
                if (cached == null || now - _lastCacheUpdate > CacheValidityPeriod)
                {
                    lock (_cacheLock)
                    {
                        // Double-check pattern
                        var lockTime = DateTime.UtcNow;
                        if (_cachedNodes == null || lockTime - _lastCacheUpdate > CacheValidityPeriod)
                        {
                            _cachedNodes = new Dictionary<EssServiceBusNode, DateTime>(_nodes);
                            _lastCacheUpdate = lockTime; // Use consistent time
                        }
                        return _cachedNodes;
                    }
                }
                
                return cached;
            }
        }

        public string DeviceName { get; private set; }
        public string HostAddress { get; private set; }
        public bool IsInitialized { get; private set; }
        public int SubscriberPort { get; set; }

        private readonly EssServiceBusConfiguration _configuration;
        private readonly IEssMessageFactory _messageFactory;
        private readonly IEssLogger _essLogger;
        private readonly IPollbookQueue _pollbookQueue;

        private int _broadcastPort;
        private NetMQActor _actor;
        private SubscriberSocket _subscriber;
        private PublisherSocket _publisher;
        private NetMQBeacon _beacon;
        private NetMQPoller _poller;
        private PairSocket _shim;

        // NetMQMonitor related fields
        private NetMQMonitor _publisherMonitor;
        private NetMQMonitor _subscriberMonitor;
        private readonly ConcurrentQueue<string> _immediateHealthCheckQueue = new ConcurrentQueue<string>();

        // Connection tracking for improved health checking
        private readonly ConcurrentDictionary<string, int> _nodeDisconnectCounts = new ConcurrentDictionary<string, int>();
        private readonly ConcurrentDictionary<string, DateTime> _nodeLastDisconnectTime = new ConcurrentDictionary<string, DateTime>();

        private string _electionGUID;
        private int _pollPlaceID;
        private string _passPhrase;
        private volatile bool _monitorsStarted = false;
        private volatile bool _isDisposed = false;

        // Configuration constants - moved from hardcoded values
        private int MaxDisconnectCount => _configuration?.MaxDisconnectCount ?? 3;
        private int DisconnectResetMinutes => _configuration?.DisconnectResetMinutes ?? 5;
        private int ImmediateHealthCheckTimeoutSeconds => _configuration?.ImmediateHealthCheckTimeoutSeconds ?? 30;
        private int MaxNodeCount => _configuration?.MaxNodeCount ?? 100;

        public ConcurrentQueue<EssServiceBusMessage> messageQueue { get; set; } = new ConcurrentQueue<EssServiceBusMessage>();

        public EssServiceBus(EssServiceBusConfiguration configuration,
            IEssLogger logger,
            IEssMessageFactory messageFactory,
            IPollbookQueue pollbookQueue)
        {
            _essLogger = logger ?? throw new ArgumentNullException(nameof(logger));
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            _messageFactory = messageFactory ?? throw new ArgumentNullException(nameof(messageFactory));
            _pollbookQueue = pollbookQueue ?? throw new ArgumentNullException(nameof(pollbookQueue));
        }

        public async Task InitializeAsync(string systemIdentifier,
                                        string electionDatabaseGuid,
                                        int pollPlaceId,
                                        string passPhrase,
                                        int? udpBroadcastPortOverride = null)
        {
            if (_isDisposed)
            {
                throw new ObjectDisposedException(nameof(EssServiceBus));
            }

            // Input validation
            if (string.IsNullOrWhiteSpace(systemIdentifier))
                throw new ArgumentException("System identifier cannot be null or empty", nameof(systemIdentifier));
            if (string.IsNullOrWhiteSpace(electionDatabaseGuid))
                throw new ArgumentException("Election database GUID cannot be null or empty", nameof(electionDatabaseGuid));
            if (string.IsNullOrWhiteSpace(passPhrase))
                throw new ArgumentException("Pass phrase cannot be null or empty", nameof(passPhrase));

            try
            {
                // Clear any existing nodes and reset cache
                _nodes.Clear();
                _deviceNameToNodeMap.Clear();
                _addressToNodeMap.Clear();
                _cachedNodes = null;

                _subscriber = new SubscriberSocket();
                _publisher = new PublisherSocket();
                _beacon = new NetMQBeacon();

                DeviceName = systemIdentifier;
                _electionGUID = electionDatabaseGuid;
                _pollPlaceID = pollPlaceId;
                _passPhrase = passPhrase;
                _broadcastPort = udpBroadcastPortOverride ?? _configuration.UdpBroadcastPort;

                if (!_broadcastPort.IsValidPortNumber(false))
                {
                    throw new ArgumentOutOfRangeException(nameof(EssServiceBusConfiguration.UdpBroadcastPort),
                        "The UDP Broadcast port must be between 0 and 65535");
                }

                // We must wait until we have a valid IP address in order to successfully create an actor.
                const int maxIpAddressTries = 50; // Maximum attempts
                const int millisecondsToWait = 200; // 200ms between attempts

                for (var i = 0; i < maxIpAddressTries; i++)
                {
                    var localIpAddress = LocalIPAddress();
                    if (!IPAddress.IsLoopback(localIpAddress) &&
                        !localIpAddress.Equals(IPAddress.None) &&
                        !localIpAddress.Equals(IPAddress.Any) &&
                        !localIpAddress.ToString().StartsWith("169.254."))
                    {
                        _actor = NetMQActor.Create(RunActor);
                        IsInitialized = true;
                        return;
                    }
                    await Task.Delay(millisecondsToWait);
                }
                _essLogger.LogError("Unable to obtain an IP address");
                IsInitialized = false;
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string> { { "Action", "InitializeAsync" } };
                _essLogger.LogError(ex, logProps);
                IsInitialized = false;
            }
        }

        public void SendMessage<TMessage>(TMessage message,
            EssEnvelopeHeader essEnvelopeHeader,
            EssServiceBusMessageType type = EssServiceBusMessageType.Unknown,
            string messageIdentifier = null) where TMessage : class, new()
        {
            // Input validation
            if (message == null) throw new ArgumentNullException(nameof(message));
            if (essEnvelopeHeader == null) throw new ArgumentNullException(nameof(essEnvelopeHeader));
            if (_isDisposed) throw new ObjectDisposedException(nameof(EssServiceBus));

            var wrapper = new EssServiceBusMessage()
            {
                MessageIdentifier = messageIdentifier ?? Guid.NewGuid().ToString(),
                Payload = JsonConvert.SerializeObject(message),
                BusEnvelopeHeader = essEnvelopeHeader,
                MessageType = type,
                HostAddress = HostAddress,
                DeviceName = DeviceName
            };

            SendMessage(wrapper);
        }

        public void SendMessage(string message,
            EssServiceBusMessageType type = EssServiceBusMessageType.Unknown,
            string messageIdentifier = null)
        {
            // Input validation
            if (string.IsNullOrEmpty(message)) throw new ArgumentException("Message cannot be null or empty", nameof(message));
            if (_isDisposed) throw new ObjectDisposedException(nameof(EssServiceBus));

            var wrapper = new EssServiceBusMessage
            {
                MessageIdentifier = messageIdentifier ?? Guid.NewGuid().ToString(),
                Payload = message,
                MessageType = type,
                HostAddress = HostAddress,
                DeviceName = DeviceName
            };

            SendMessage(wrapper);
        }

        public Task StartReceiverAsync(CancellationToken cancellationToken = default)
        {
            return Task.Run(async () =>
            {
                try
                {
                    while (!cancellationToken.IsCancellationRequested && !_isDisposed)
                    {
                        try
                        {
                            var message = ReceiveMessage();
                            if (message == null)
                            {
                                // Small delay to prevent tight loop on null messages
                                await Task.Delay(10, cancellationToken);
                                continue;
                            }

                            await ProcessReceivedMessage(message);
                        }
                        catch (OperationCanceledException)
                        {
                            // Expected when cancellation is requested
                            break;
                        }
                        catch (Exception ex)
                        {
                            var logProps = new Dictionary<string, string>
                                { { "Action", "StartReceiverAsync-MessageLoop" } };
                            _essLogger.LogError(ex, logProps);

                            // Brief delay to prevent error flooding
                            await Task.Delay(100, cancellationToken);
                        }
                    }
                }
                catch (OperationCanceledException)
                {
                    _essLogger.LogDebug("StartReceiverAsync was cancelled");
                }
                catch (Exception ex)
                {
                    var logProps = new Dictionary<string, string> { { "Action", "StartReceiverAsync-Outer" } };
                    _essLogger.LogError(ex, logProps);
                }
            }, cancellationToken);
        }

        private async Task ProcessReceivedMessage(string message)
        {
            switch (message)
            {
                case AddedNodeCommand:
                    var addedAddress = _actor.ReceiveFrameString();
                    NodeAdded?.Invoke(addedAddress);
                    _essLogger.LogDebug("Node Added", new Dictionary<string, string>()
                    {
                        { "Address", addedAddress }
                    });
                    break;

                case RemovedNodeCommand:
                    var removedAddress = _actor.ReceiveFrameString();
                    NodeRemoved?.Invoke(removedAddress);
                    _essLogger.LogDebug("Node Removed", new Dictionary<string, string>()
                    {
                        { "Address", removedAddress }
                    });
                    break;

                default:
                    var deserializedMessage = JsonConvert.DeserializeObject<EssServiceBusMessage>(message);
                    if (deserializedMessage != null)
                    {
                        await EnqueueMessage(deserializedMessage);
                    }

                    break;
            }
        }

        private async Task EnqueueMessage(EssServiceBusMessage deserializedMessage)
        {
            try
            {
                var transactionItem = new PollbookQueueItem
                {
                    QueueParams = new Dictionary<string, object>() { { "message", deserializedMessage } },
                    ItemQueueProcess = QueueProcess.EssServiceBusMessageTransaction,
                    ItemQueueType = QueueType.Transaction,
                    ItemQueuePriority = QueuePriority.Standard,
                };

                var polldataItem = new PollbookQueueItem
                {
                    QueueParams = new Dictionary<string, object>() { { "message", deserializedMessage } },
                    ItemQueueProcess = QueueProcess.EssServiceBusMessagePolldata,
                    ItemQueueType = QueueType.Polldata,
                    ItemQueuePriority = QueuePriority.Standard,
                };

                _pollbookQueue.Transactions.Enqueue(transactionItem);
                _pollbookQueue.Polldata.Enqueue(polldataItem);
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string> { { "Action", "EnqueueMessage" } };
                _essLogger.LogError(ex, logProps);
            }
        }

        private void RunActor(PairSocket shim)
        {
            try
            {
                _shim = shim;
                _shim.ReceiveReady += OnShimReady;
                _subscriber.Subscribe("");

                if (!TryBindToPort(_configuration.DataTransferPort))
                {
                    // Fallback to random port
                    SubscriberPort = _subscriber.BindRandomPort("tcp://*");
                }

                _essLogger.LogDebug($"Local IP Address is {LocalIPAddress()}");
                _essLogger.LogDebug($"Beacon [{DeviceName}] subscriber bound to {_subscriber.Options.LastEndpoint}");

                _subscriber.ReceiveReady += OnSubscriberReady;
                _essLogger.LogDebug($"Configuring beacon with port {_broadcastPort}");
                _beacon.Configure(_broadcastPort);
                _essLogger.LogDebug($"Beacon [{DeviceName}] udp broadcast port bound to {_broadcastPort}");

                SetBeaconMessage(new EssBeaconMessage()
                {
                    Port = SubscriberPort,
                    BeaconEnvelopeHeader = new EssEnvelopeHeader()
                    {
                        ElectionDatabaseGuid = _electionGUID,
                        PollPlaceID = _pollPlaceID
                    },
                    DeviceName = DeviceName
                }, _configuration.TickLength);
                
                _beacon.Subscribe("");
                HostAddress = $"{_beacon.BoundTo}:{SubscriberPort}";
                _beacon.ReceiveReady += OnBeaconReady;

                var timer = new NetMQTimer(_configuration.TickLength);
                timer.Elapsed += ClearDeadNodes;

                var immediateHealthCheckTimer = new NetMQTimer(TimeSpan.FromSeconds(5));
                immediateHealthCheckTimer.Elapsed += ProcessImmediateHealthChecks;

                SetupNetMQMonitors();

                _poller = new NetMQPoller
                {
                    _shim,
                    _subscriber,
                    _beacon,
                    timer,
                    immediateHealthCheckTimer
                };

                _shim.SignalOK();
                _poller.Run();
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string> { { "Action", "RunActor" } };
                _essLogger.LogError(ex, logProps);
            }
        }
        
        private void SetupNetMQMonitors()
        {
            try
            {
                if (_monitorsStarted || _isDisposed) return;

                // Stop and dispose existing monitors properly
                StopMonitorSafely(_publisherMonitor, "existing publisher");
                StopMonitorSafely(_subscriberMonitor, "existing subscriber");
                DisposeMonitorSafely(_publisherMonitor, "existing publisher");
                DisposeMonitorSafely(_subscriberMonitor, "existing subscriber");
                
                _publisherMonitor = null;
                _subscriberMonitor = null;

                // Monitor Publisher Socket
                _publisherMonitor = new NetMQMonitor(
                    _publisher, 
                    "inproc://publisher-monitor",
                    SocketEvents.Connected | SocketEvents.Disconnected | SocketEvents.ConnectRetried | SocketEvents.ConnectDelayed);
                _publisherMonitor.Connected += OnPublisherConnected;
                _publisherMonitor.Disconnected += OnPublisherDisconnected;
                _publisherMonitor.ConnectRetried += OnPublisherConnectRetried;
                _publisherMonitor.ConnectDelayed += OnPublisherConnectDelayed;

                // Monitor Subscriber Socket
                _subscriberMonitor = new NetMQMonitor(
                    _subscriber, 
                    "inproc://subscriber-monitor",
                    SocketEvents.Accepted | SocketEvents.Disconnected | SocketEvents.Closed);
                _subscriberMonitor.Accepted += OnSubscriberAccepted;
                _subscriberMonitor.Disconnected += OnSubscriberDisconnected;
                _subscriberMonitor.Closed += OnSubscriberClosed;

                // Start all monitors
                // Must use StartAsync(). Start() blocks and prevents other code from running after the Start()
                var publisherMonitorTask = _publisherMonitor.StartAsync();
                var subscriberMonitorTask = _subscriberMonitor.StartAsync();

                _monitorsStarted = true;
                _essLogger.LogDebug("NetMQMonitors initialized and started successfully");
            }
            catch (Exception ex)
            {
                StopMonitorSafely(_publisherMonitor, "publisher on setup failure");
                StopMonitorSafely(_subscriberMonitor, "subscriber on setup failure");
                DisposeMonitorSafely(_publisherMonitor, "publisher on setup failure");
                DisposeMonitorSafely(_subscriberMonitor, "subscriber on setup failure");
                _publisherMonitor = null;
                _subscriberMonitor = null;
                _monitorsStarted = false;
                
                var logProps = new Dictionary<string, string> { { "Action", "SetupNetMQMonitors" } };
                _essLogger.LogError(ex, logProps);
            }
        }

        #region NetMQMonitor Event Handlers

        private void OnPublisherConnected(object sender, NetMQMonitorSocketEventArgs e)
        {
            _essLogger.LogDebug($"Publisher connected to {e.Address}");
            
            var node = FindNodeByAddress(e.Address.ToString());
            if (node != null)
            {
                // Reset disconnect tracking on successful connection
                _nodeDisconnectCounts.TryRemove(node.DeviceName, out _);
                _nodeLastDisconnectTime.TryRemove(node.DeviceName, out _);
                _essLogger.LogDebug($"Reset disconnect tracking for {node.DeviceName} due to successful connection");
            }
        }

        private void OnPublisherDisconnected(object sender, NetMQMonitorSocketEventArgs e)
        {
            _essLogger.LogWarning($"Publisher disconnected from {e.Address}");

            var node = FindNodeByAddress(e.Address.ToString());
            if (node != null)
            {
                // Check if we should remove the node immediately based on disconnect count
                if (ShouldRemoveImmediately(node))
                {
                    _essLogger.LogWarning($"Removing {node.DeviceName} immediately due to repeated disconnects");
                    RemoveNode(node);
                }
                else
                {
                    // Queue for health check if not immediately removing
                    _essLogger.LogInformation($"Connection lost to {node.DeviceName}, queuing immediate health check");
                    _immediateHealthCheckQueue.Enqueue(node.DeviceName);
                }
            }
        }

        private void OnPublisherConnectRetried(object sender, NetMQMonitorIntervalEventArgs e)
        {
            _essLogger.LogWarning($"Publisher retrying connection to {e.Address} (interval: {e.Interval}ms)");
        }

        private void OnPublisherConnectDelayed(object sender, NetMQMonitorErrorEventArgs e)
        {
            _essLogger.LogWarning($"Publisher connection delayed to {e.Address} (error: {e.ErrorCode})");
        }

        private void OnSubscriberAccepted(object sender, NetMQMonitorSocketEventArgs e)
        {
            _essLogger.LogDebug($"Subscriber accepted connection from {e.Address}");
        }

        private void OnSubscriberDisconnected(object sender, NetMQMonitorSocketEventArgs e)
        {
            _essLogger.LogDebug($"Subscriber disconnected from {e.Address}");
        }

        private void OnSubscriberClosed(object sender, NetMQMonitorSocketEventArgs e)
        {
            _essLogger.LogDebug($"Subscriber socket closed for {e.Address}");
        }

        #endregion

        #region Health Check Logic

        private bool ShouldRemoveImmediately(EssServiceBusNode node)
        {
            if (node == null) return false;

            var deviceName = node.DeviceName;
            var now = DateTime.UtcNow;

            // Get current disconnect count, defaulting to 0 if not present
            var currentCount = _nodeDisconnectCounts.GetOrAdd(deviceName, 0);

            // Check if we should reset the count due to time elapsed
            if (_nodeLastDisconnectTime.TryGetValue(deviceName, out DateTime lastDisconnectTime))
            {
                var timeSinceLastDisconnect = now - lastDisconnectTime;
                if (timeSinceLastDisconnect.TotalMinutes > DisconnectResetMinutes)
                {
                    // Reset the disconnect count and update the timestamp
                    _nodeDisconnectCounts.TryUpdate(deviceName, 1, currentCount);
                    _nodeLastDisconnectTime.TryUpdate(deviceName, now, lastDisconnectTime);
                    
                    _essLogger.LogDebug($"Reset disconnect count for {deviceName} due to time elapsed ({timeSinceLastDisconnect.TotalMinutes:F1} minutes)");
                    return false; // Don't remove immediately after reset
                }
            }

            // Increment the disconnect count and update timestamp
            var newCount = _nodeDisconnectCounts.AddOrUpdate(deviceName, 1, (key, oldValue) => oldValue + 1);
            _nodeLastDisconnectTime.AddOrUpdate(deviceName, now, (key, oldValue) => now);

            _essLogger.LogDebug($"Disconnect count for {deviceName}: {newCount}/{MaxDisconnectCount}");

            // Remove immediately if we've reached the threshold
            if (newCount >= MaxDisconnectCount)
            {
                _essLogger.LogWarning($"Node {deviceName} has disconnected {newCount} times, will be removed immediately");
                return true;
            }

            return false;
        }

        private EssServiceBusNode FindNodeByAddress(string address)
        {
            if (string.IsNullOrEmpty(address)) return null;
            
            // Use the existing mapping first (O(1) lookup)
            if (_addressToNodeMap.TryGetValue(address, out var node))
                return node;
                
            // Fallback to exact match search
            return _deviceNameToNodeMap.Values.FirstOrDefault(n => 
                string.Equals(n.Address, address, StringComparison.OrdinalIgnoreCase));
        }

        private EssServiceBusNode FindNodeByDeviceName(string deviceName)
        {
            if (string.IsNullOrEmpty(deviceName)) return null;
            
            _deviceNameToNodeMap.TryGetValue(deviceName, out var node);
            return node;
        }

        private void ProcessImmediateHealthChecks(object sender, NetMQTimerEventArgs e)
        {
            try
            {
                var processedNodes = new HashSet<string>();
                
                while (_immediateHealthCheckQueue.TryDequeue(out string deviceName) && !processedNodes.Contains(deviceName))
                {
                    processedNodes.Add(deviceName);
                    
                    var node = FindNodeByDeviceName(deviceName);
                    if (node != null && _nodes.TryGetValue(node, out DateTime lastSeen))
                    {
                        _essLogger.LogInformation($"Processing immediate health check for {deviceName}");
                        
                        var immediateTimeout = TimeSpan.FromSeconds(ImmediateHealthCheckTimeoutSeconds);
                        
                        if (DateTime.UtcNow > lastSeen.Add(immediateTimeout))
                        {
                            _essLogger.LogWarning($"Immediate health check failed for {deviceName} (beacon timeout), removing node");
                            RemoveNode(node);

                            if (_nodes.Count == 0)
                            {
                                RenewIPConnection();
                            }
                        }
                        else
                        {
                            _essLogger.LogInformation($"Immediate health check passed for {deviceName}, node still active");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string> { { "Action", "ProcessImmediateHealthChecks" } };
                _essLogger.LogError(ex, logProps);
            }
        }

        #endregion

        private void OnShimReady(object sender, NetMQSocketEventArgs e)
        {
            try
            {
               var command = _shim.ReceiveFrameString();

               switch (command)
               {
                  case NetMQActor.EndShimMessage:
                     _poller.Stop();
                     break;
                  case PublishCommand:
                  {
                     var message = _shim.ReceiveMultipartMessage();
                     _publisher.SendMultipartMessage(message);
                     break;
                  }
                  case GetHostAddressCommand:
                     _shim.SendFrame(HostAddress);
                     break;
               }
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string> { { "Action", "OnShimReady" } };
                _essLogger.LogError(ex, logProps);
            }
        }

        private void OnSubscriberReady(object sender, NetMQSocketEventArgs e)
        {
            try
            {
                var message = _subscriber.ReceiveMultipartMessage();
                _shim.SendMultipartMessage(message);
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string> { { "Action", "OnSubscriberReady" } };
                _essLogger.LogError(ex, logProps);
            }
        }

        private void OnBeaconReady(object sender, NetMQBeaconEventArgs e)
        {
            try
            {
                var message = _beacon.Receive();

                if (string.IsNullOrEmpty(message.String)) return;
                _essLogger.LogDebug("UDP message received - validating...");

                var decryptedMessage = _messageFactory.Decrypt(message.String, _passPhrase);
                if (string.IsNullOrEmpty(decryptedMessage)) return;
                
                var payload = JsonConvert.DeserializeObject<EssBeaconMessage>(decryptedMessage);

                if (payload?.BeaconEnvelopeHeader?.ElectionDatabaseGuid != _electionGUID ||
                    payload?.BeaconEnvelopeHeader?.PollPlaceID != _pollPlaceID ||
                    string.IsNullOrWhiteSpace(payload?.DeviceName) ||
                    payload.Port <= 0 || payload.Port > 65535)
                    return;
                
                if (string.IsNullOrWhiteSpace(message.PeerHost))
                {
                    _essLogger.LogWarning("Received beacon with invalid peer host");
                    return;
                }

                var node = new EssServiceBusNode(payload.DeviceName, message.PeerHost, payload.Port);
                if (node.DeviceName == DeviceName) return;
                
                _essLogger.LogDebug($"UDP message received from [{node.DeviceName}:{node.Port}]");
                
                // Check node count limit
                if (_nodes.Count >= MaxNodeCount && !_nodes.ContainsKey(node))
                {
                    _essLogger.LogWarning($"Maximum node count ({MaxNodeCount}) reached, rejecting new node {node.DeviceName}");
                    return;
                }
                
                if (_nodes.TryAdd(node, DateTime.UtcNow))
                {
                    // Check for existing node with same device name
                    var existingNode = _deviceNameToNodeMap.TryGetValue(node.DeviceName, out var existing) ? existing : null;
                    
                    // Add to lookup maps
                    _deviceNameToNodeMap.AddOrUpdate(node.DeviceName, node, (key, oldValue) => node);
                    _addressToNodeMap.TryAdd(node.Address, node);

                    // Remove old node if it exists and is different
                    if (existingNode != null && !existingNode.Equals(node))
                    {
                        _essLogger.LogInformation($"Replacing existing node {existingNode.DeviceName} at {existingNode.Address} with new address {node.Address}");
                        RemoveNode(existingNode);
                    }
                    
                    _publisher.Connect(node.Address);

                    var encryptedCommand = _messageFactory.Encrypt(AddedNodeCommand, _passPhrase);
                    if (string.IsNullOrEmpty(encryptedCommand)) 
                    {
                        _essLogger.LogError("Failed to encrypt AddedNodeCommand");
                        return;
                    }
                    _shim.SendMoreFrame(encryptedCommand).SendFrame(node.DeviceName + " " + node.Address);

                    _essLogger.LogInformation($"Registering node [{payload.DeviceName}] at [{node.Address}]");
                    
                    // Invalidate cache
                    _cachedNodes = null;
                }
                else
                {
                    // Update last seen time for existing node
                    _nodes.TryUpdate(node, DateTime.UtcNow, _nodes[node]);
                    
                    // Reset disconnect tracking on beacon received (node is healthy)
                    if (_nodeDisconnectCounts.ContainsKey(node.DeviceName))
                    {
                        _nodeDisconnectCounts.TryRemove(node.DeviceName, out _);
                        _nodeLastDisconnectTime.TryRemove(node.DeviceName, out _);
                        _essLogger.LogDebug($"Reset disconnect tracking for {node.DeviceName} due to beacon received");
                    }
                }
            }
            catch (InvalidCipherTextException)
            {
                // Intentionally suppress - different passphrase/election
            }
            catch (JsonException ex)
            {
                _essLogger.LogWarning($"Failed to deserialize beacon message: {ex.Message}");
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string> { { "Action", "OnBeaconReady" } };
                _essLogger.LogError(ex, logProps);
            }
        }

        private void ClearDeadNodes(object sender, NetMQTimerEventArgs e)
        {
            try
            {
                var now = DateTime.UtcNow;
                var deadNodes = _nodes
                    .Where(n => now > n.Value.Add(_configuration.NodeInactivityTimeout))
                    .Select(n => n.Key)
                    .ToArray();

                foreach (var node in deadNodes)
                {
                    _essLogger.LogInformation($"Node {node.DeviceName} appears inactive based on beacon timeout, removing node");
                    RemoveNode(node);
                }

                if (_nodes.Count == 0 && deadNodes.Length > 0)
                {
                    RenewIPConnection();
                }
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string> { { "Action", "ClearDeadNodes" } };
                _essLogger.LogError(ex, logProps);
            }
        }

        private void RemoveNode(EssServiceBusNode node)
        {
            try
            {
                if (node == null) return;

                var removed = _nodes.TryRemove(node, out _);
                _deviceNameToNodeMap.TryRemove(node.DeviceName, out _);
                _addressToNodeMap.TryRemove(node.Address, out _);
                
                if (removed)
                {
                    // Invalidate cache
                    _cachedNodes = null;
                }
                
                try
                {
                    _publisher.Disconnect(node.Address);
                }
                catch (Exception ex)
                {
                    _essLogger.LogDebug($"Error disconnecting from {node.Address}: {ex.Message}");
                }

                // Clean up disconnect tracking
                _nodeDisconnectCounts.TryRemove(node.DeviceName, out _);
                _nodeLastDisconnectTime.TryRemove(node.DeviceName, out _);

                _essLogger.LogInformation($"Removing node for inactivity [{node.DeviceName}] at [{node.Address}]");

                try
                {
                    var encryptedCommand = _messageFactory.Encrypt(RemovedNodeCommand, _passPhrase);
                    if (string.IsNullOrEmpty(encryptedCommand))
                    {
                        _essLogger.LogError("Failed to encrypt RemovedNodeCommand");
                        return;
                    }
                    _shim.SendMoreFrame(encryptedCommand).SendFrame(node.DeviceName + " " + node.Address);
                }
                catch (Exception ex)
                {
                    _essLogger.LogDebug($"Error sending remove node command: {ex.Message}");
                }
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string> { { "Action", "RemoveNode" } };
                _essLogger.LogError(ex, logProps);
            }
        }

        private void SetBeaconMessage(EssBeaconMessage message, TimeSpan interval)
        {
            try
            {
                _essLogger.LogInformation($"Beacon Publish [{message.DeviceName}] at [{message.Port}]");
                var payload = _messageFactory.Encrypt(JsonConvert.SerializeObject(message), _passPhrase);
                if (string.IsNullOrEmpty(payload))
                {
                    _essLogger.LogError("Failed to encrypt beacon message");
                    return;
                }
                _beacon.Publish(payload, interval);
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string> { { "Action", "SetBeaconMessage" } };
                _essLogger.LogError(ex, logProps);
            }
        }

        private void SendMessage(EssServiceBusMessage message)
        {
            if (!IsInitialized || _isDisposed)
            {
                _essLogger.LogWarning("Attempted to send message while service bus not initialized or disposed");
                return;
            }
            
            if (message == null)
            {
                _essLogger.LogWarning("Attempted to send null message");
                return;
            }
            
            try
            {
                var cipherText = _messageFactory.Encrypt(JsonConvert.SerializeObject(message), _passPhrase);
                if (string.IsNullOrEmpty(cipherText))
                {
                    _essLogger.LogError("Failed to encrypt message for sending");
                    return;
                }
                _actor.SendMoreFrame(PublishCommand).SendFrame(cipherText);
            }
            catch (SocketException se)
            {
                if (se.SocketErrorCode != SocketError.WouldBlock)
                {
                    var logProps = new Dictionary<string, string> { { "Action", "SendMessage" } };
                    _essLogger.LogError(se, logProps);
                }
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string> { { "Action", "SendMessage" } };
                _essLogger.LogError(ex, logProps);
            }
        }

        private string ReceiveMessage()
        {
            try
            {
                if (!IsInitialized || _actor == null || _isDisposed)
                {
                    return null;
                }
                var cipher = _actor.ReceiveFrameString();
                if (string.IsNullOrEmpty(cipher)) return null;
                
                var decrypted = _messageFactory.Decrypt(cipher, _passPhrase);
                if (string.IsNullOrEmpty(decrypted))
                {
                    _essLogger.LogWarning("Failed to decrypt received message");
                }
                return decrypted;
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string> { { "Action", "ReceiveMessage" } };
                _essLogger.LogError(ex, logProps);
                return null;
            }
        }

        private IPAddress LocalIPAddress()
        {
            if (!NetworkInterface.GetIsNetworkAvailable())
            {
                return IPAddress.None;
            }

            var host = Dns.GetHostEntry(Dns.GetHostName());
            var address = host.AddressList.FirstOrDefault(ip => ip.AddressFamily == AddressFamily.InterNetwork);
            return address ?? IPAddress.None;
        }

        private void RenewIPConnection()
        {
            try
            {
                if (!RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    _essLogger.LogWarning("IP renewal is only supported on Windows");
                    return;
                }

                using (var objMc = new ManagementClass("Win32_NetworkAdapterConfiguration"))
                using (var objMoc = objMc.GetInstances())
                {
                    foreach (var managementObject in objMoc.Cast<ManagementObject>())
                    {
                        using (managementObject)
                        {
                            var description = managementObject.Properties["Description"]?.Value?.ToString() ?? "";
                            if (description.Contains("Wireless"))
                            {
                                _essLogger.LogInformation("Forcing the network to release & renew IP.");
                                managementObject.InvokeMethod("ReleaseDHCPLease", null, null);
                                managementObject.InvokeMethod("RenewDHCPLease", null, null);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                var dict = new Dictionary<string, string> { { "Action", "RenewIPConnection" } };
                _essLogger.LogError(ex, dict);
            }
        }
        
        private bool TryBindToPort(int port)
        {
            try 
            {
                _subscriber.Bind($"tcp://*:{port}");
                SubscriberPort = port;
                return true;
            }
            catch (AddressAlreadyInUseException)
            {
                _essLogger.LogWarning($"Port {port} is already in use, will try random port");
                return false;
            }
            catch (Exception ex)
            {
                _essLogger.LogError(ex, new Dictionary<string, string> { { "Action", "TryBindToPort" } });
                return false;
            }
        }

        public void Dispose()
        {
            if (_isDisposed) return;

            _isDisposed = true;
            IsInitialized = false;

            try
            {
                // Stop poller first to prevent new events
                try
                {
                    _poller?.Stop();
                }
                catch (Exception ex)
                {
                    _essLogger.LogDebug($"Error stopping poller: {ex.Message}");
                }

                // Clear queues
                ClearQueues();

                // Clear collections and cache
                ClearCollections();

                NodesCleared?.Invoke();

                // Stop and dispose monitors
                DisposeMonitors();

                // Dispose actor
                DisposeActor();

                // Dispose sockets
                DisposeSockets();

                // Dispose poller
                try
                {
                    _poller?.Dispose();
                }
                catch (Exception ex)
                {
                    _essLogger.LogDebug($"Error disposing poller: {ex.Message}");
                }

                _monitorsStarted = false;
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string> { { "Action", "Dispose" } };
                _essLogger.LogError(ex, logProps);
            }
        }

        private void ClearQueues()
        {
            try
            {
                while (messageQueue.TryDequeue(out _))
                {
                }

                while (_immediateHealthCheckQueue.TryDequeue(out _))
                {
                }
            }
            catch (Exception ex)
            {
                _essLogger.LogDebug($"Error clearing queues: {ex.Message}");
            }
        }

        private void ClearCollections()
        {
            try
            {
                _nodes.Clear();
                _deviceNameToNodeMap.Clear();
                _addressToNodeMap.Clear();
                _nodeDisconnectCounts.Clear();
                _nodeLastDisconnectTime.Clear();
                _cachedNodes = null;
            }
            catch (Exception ex)
            {
                _essLogger.LogDebug($"Error clearing collections: {ex.Message}");
            }
        }

        private void DisposeMonitors()
        {
            StopMonitorSafely(_publisherMonitor, "publisher");
            StopMonitorSafely(_subscriberMonitor, "subscriber");
            DisposeMonitorSafely(_publisherMonitor, "publisher");
            DisposeMonitorSafely(_subscriberMonitor, "subscriber");
            _publisherMonitor = null;
            _subscriberMonitor = null;
        }

        private void DisposeActor()
        {
            try
            {
                _actor?.Dispose();
                _actor = null;
            }
            catch (Exception ex)
            {
                _essLogger.LogDebug($"Error disposing actor: {ex.Message}");
            }
        }
        
        private void StopMonitorSafely(NetMQMonitor monitor, string name)
        {
            try
            {
                if (monitor != null && _monitorsStarted)
                {
                    monitor.Stop();
                }
            }
            catch (InvalidOperationException ex)
            {
                _essLogger.LogDebug($"Monitor {name} was already stopped: {ex.Message}");
            }
            catch (Exception ex)
            {
                _essLogger.LogWarning($"Unexpected error stopping {name} monitor: {ex.Message}");
            }
        }

        private void DisposeMonitorSafely(NetMQMonitor monitor, string name)
        {
            try
            {
                monitor?.Dispose();
            }
            catch (Exception ex)
            {
                _essLogger.LogDebug($"Error disposing {name} monitor: {ex.Message}");
            }
        }

        private void DisposeSockets()
        {
            try
            {
                _subscriber?.Unbind($"tcp://*:{SubscriberPort}");
                _subscriber?.Dispose();
            }
            catch (Exception ex)
            {
                _essLogger.LogDebug($"Error disposing subscriber: {ex.Message}");
            }

            try
            {
                _publisher?.Dispose();
            }
            catch (Exception ex)
            {
                _essLogger.LogDebug($"Error disposing publisher: {ex.Message}");
            }

            try
            {
                _beacon?.Unsubscribe();
                _beacon?.Dispose();
            }
            catch (Exception ex)
            {
                _essLogger.LogDebug($"Error disposing beacon: {ex.Message}");
            }
        }

        // Performance metrics method
        public Dictionary<string, object> GetMetrics()
        {
            return new Dictionary<string, object>
            {
                { "ActiveNodes", _nodes.Count },
                { "IsInitialized", IsInitialized },
                { "DeviceName", DeviceName },
                { "HostAddress", HostAddress },
                { "MonitorsStarted", _monitorsStarted },
                { "DisconnectCounts", new Dictionary<string, int>(_nodeDisconnectCounts) }
            };
        }
    }
}
