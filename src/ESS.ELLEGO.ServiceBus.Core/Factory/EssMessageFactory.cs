using ESS.ELLEGO.ServiceBus.Core.Extensions;
using ESS.ELLEGO.ServiceBus.Core.Interfaces;
using ESS.Pollbook.Core.Utilities;
using Org.BouncyCastle.Crypto.Engines;
using Org.BouncyCastle.Crypto.Modes;
using Org.BouncyCastle.Crypto.Parameters;
using System;
using System.IO;
using System.Runtime.CompilerServices;
using System.Text;

namespace ESS.ELLEGO.ServiceBus.Core.Factory
{
	public class EssMessageFactory : IEssMessageFactory
	{
	    // Default Sizes
	    private const int DefaultKeyBitSize = 256;
        private const int DefaultMacBitSize = 128;
        private const int DefaultNonceBitSize = 128;

        public string Encrypt(string toEncrypt, string securityKey)
        {
	        return Convert.ToBase64String(
		        EncryptWithKey(Encoding.UTF8.GetBytes(toEncrypt),
			        Encoding.UTF8.GetBytes(securityKey.Length < 32 ? securityKey.PadRight(32) : securityKey))
	        );
        }

        private byte[] EncryptWithKey(byte[] messageToEncrypt, byte[] key)
        {
            //User Error Checks
            CheckKey(key);

            //Using random nonce large enough not to repeat
            var nonce = new byte[DefaultNonceBitSize / 8];
            CryptoRandom.Instance.NextBytes(nonce);

            var cipher = new GcmBlockCipher(new AesEngine());
            var parameters = new AeadParameters(new KeyParameter(key), DefaultMacBitSize, nonce);
            cipher.Init(true, parameters);

            //Generate Cipher Text With Auth Tag
            var cipherText = new byte[cipher.GetOutputSize(messageToEncrypt.Length)];
            var len = cipher.ProcessBytes(messageToEncrypt, 0, messageToEncrypt.Length, cipherText, 0);
            cipher.DoFinal(cipherText, len);

            //Assemble Message
            using (var combinedStream = new MemoryStream())
            {
                using (var binaryWriter = new BinaryWriter(combinedStream))
                {
                    //Prepend Nonce
                    binaryWriter.Write(nonce);
                    //Write Cipher Text
                    binaryWriter.Write(cipherText);
                }
                return combinedStream.ToArray();
            }
        }

        public string Decrypt(string toDecrypt, string securityKey)
        {
            if (string.IsNullOrEmpty(toDecrypt))
                throw new ArgumentException("Encrypted Message Required!", nameof(toDecrypt));

            byte[] toDecryptArray = Convert.FromBase64String(toDecrypt);

            return Encoding.UTF8.GetString(DecryptWithKey(toDecryptArray,
	            Encoding.UTF8.GetBytes(securityKey.Length < 32 ? securityKey.PadRight(32) : securityKey)));
        }

        private byte[] DecryptWithKey(byte[] encryptedMessage, byte[] key, int nonSecretPayloadLength = 0)
        {
            //User Error Checks
            CheckKey(key);

            if (encryptedMessage.IsNullOrEmpty())
                throw new ArgumentException("Encrypted Message Required!", nameof(encryptedMessage));

            using (var cipherStream = new MemoryStream(encryptedMessage))
            using (var cipherReader = new BinaryReader(cipherStream))
            {
	            //Grab Nonce
	            var nonce = cipherReader.ReadBytes(DefaultNonceBitSize / 8);

	            var cipher = new GcmBlockCipher(new AesEngine());
	            var parameters = new AeadParameters(new KeyParameter(key), DefaultMacBitSize, nonce);
	            cipher.Init(false, parameters);

                //Decrypt Cipher Text
                var cipherText = cipherReader.ReadBytes(encryptedMessage.Length - nonSecretPayloadLength - nonce.Length);
                var plainText = new byte[cipher.GetOutputSize(cipherText.Length)];

                var len = cipher.ProcessBytes(cipherText, 0, cipherText.Length, plainText, 0);
                cipher.DoFinal(plainText, len);

                return plainText;
            }
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        private void CheckKey(byte[] key)
        {
	        if (key == null || key.Length != DefaultKeyBitSize / 8)
		        throw new ArgumentException($"Key needs to be {DefaultKeyBitSize} bit! Actual:{key?.Length * 8}",
			        nameof(key));
        }
    }
}
