<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
	  <TargetFramework>net48</TargetFramework>
	  <Authors>Election Systems and Software</Authors>
	  <Company>Election Systems and Software</Company>
	  <Copyright>Election Systems and Software</Copyright>
	  <AssemblyVersion>7.2.9.0</AssemblyVersion>
	  <FileVersion>7.2.9.0</FileVersion>
	  <CodeAnalysisRuleSet>..\.sonarlint\express-poll-branchescsharp.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AsyncIO" Version="0.1.69" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ESS.Pollbook.Core\ESS.Pollbook.Core.csproj" />
    <ProjectReference Include="..\ESS.Pollbook.MasterProcessor.Abstractions\ESS.Pollbook.MasterProcessor.Abstractions.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="NetMQ">
      <HintPath>NetMQ\NetMQ.dll</HintPath>
    </Reference>
    <Reference Include="System.Management" />
  </ItemGroup>

</Project>
