using System;

namespace ESS.ELLEGO.ServiceBus.Core.Model
{
    public class EssServiceBusNode
    {
        public string DeviceName { get; }

        public string Domain { get; }

        public string Address => $"tcp://{Domain}:{Port}";

        public int Port { get; }

        public string HostName { get; }

        public delegate void NodeAdded<PERSON><PERSON>ler(string address);

        public delegate void NodeRemoved<PERSON><PERSON><PERSON>(string address);

        public delegate void NodesClearedHandler();

        public delegate void Reconciliation<PERSON><PERSON>ler(string message);

        public EssServiceBusNode(string systemIdentifier, string domain, int port)
        {
            DeviceName = systemIdentifier;
            Domain = domain;
            Port = port;
        }

        protected bool Equals(EssServiceBusNode item)
        {
            return string.Equals(DeviceName, item.DeviceName, StringComparison.OrdinalIgnoreCase)
                   && string.Equals(Domain, item.Domain, StringComparison.OrdinalIgnoreCase)
                   && Port == item.Port;
        }

        public override bool Equals(object obj)
        {
            if (obj == null)
            {
                return false;
            }

            if (ReferenceEquals(this, obj))
            {
                return true;
            }

            return obj is EssServiceBusNode identifier && Equals(identifier);
        }

        public override int GetHashCode()
        {
            return $"{DeviceName}.{Domain}.{Port}".GetHashCode();
        }

        public override string ToString()
        {
            return Address;
        }
    }
}
