using System;
using System.Collections.ObjectModel;
using System.Reflection;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Facade.Pollworker;
using ESS.Pollbook.ViewModel.Pollworker.TimeCard;
using GalaSoft.MvvmLight.Messaging;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;

namespace ESS.Pollbook.ViewModel.Test.Pollworker.TimeCard
{
    [TestClass]
    public class PollworkerManageTimeViewModelTests
    {
        private PollworkerManageTimeViewModel _viewModel;

        private Mock<IFrameNavigationService> _navigation;
        private Mock<IMessenger> _messenger;
        private Mock<IEssLogger> _essLogger;
        private Mock<IAuditLogFacade> _auditLogFacade;
        private Mock<IPollworkerFacade> _pollworkerFacade;

        [TestInitialize]
        public void Initialize()
        {
            _navigation = new Mock<IFrameNavigationService>();
            _messenger = new Mock<IMessenger>();
            _essLogger = new Mock<IEssLogger>();
            _auditLogFacade = new Mock<IAuditLogFacade>();
            _pollworkerFacade = new Mock<IPollworkerFacade>();

            _viewModel = new PollworkerManageTimeViewModel(
                _navigation.Object,
                _messenger.Object,
                _essLogger.Object,
                _auditLogFacade.Object,
                _pollworkerFacade.Object);
        }

        private void SetDefaultConfig(bool isSingleCheckInEnabled = false, int judgeApprovalStatus = 0)
        {
            SystemConfiguration.PollworkerConfiguration = new PollworkerConfigurationDto()
            {
                CheckInWorkflow = isSingleCheckInEnabled ? "Single" : "ClockInOut"
            };

            _viewModel.GetType().GetField("_pollingPlaceJudgeApprovalStatus", BindingFlags.NonPublic | BindingFlags.Instance)?.SetValue(_viewModel, judgeApprovalStatus);
        }

        /// <summary>
        /// Verifies that adding a new time entry is not allowed in the 'Single Check In' workflow
        /// if poll worker is already checked in.
        /// </summary>
        [TestMethod()]
        public void AddTimeEnabled_ShouldReturnFalse_WhenSingleCheckInEnabledAndTimeCardExistsForToday()
        {
            // Assemble
            SetDefaultConfig(isSingleCheckInEnabled: true);

            _viewModel.TimeCardsList = new ObservableCollection<TimeCardDto>
                { new TimeCardDto { StartTime = DateTime.Now }};

            // Act & Assert
            Assert.IsFalse(_viewModel.AddTimeEnabled);
        }

        /// <summary>
        /// Verifies that adding a new time entry is allowed in the 'Single Check In' workflow
        /// if poll worker is not already check in.
        /// </summary>
        [TestMethod()]
        public void AddTimeEnabled_ShouldReturnTrue_WhenSingleCheckInEnabledAndNoExistingTimeCard()
        {
            // Assemble
            SetDefaultConfig(isSingleCheckInEnabled: true);

            _viewModel.TimeCardsList = new ObservableCollection<TimeCardDto>();

            // Act & Assert
            Assert.IsTrue(_viewModel.AddTimeEnabled);
        }

        /// <summary>
        /// Verifies that adding a new time card entry is allowed in the 'Clock In/Out' workflow
        /// when judge approval status is zero.
        /// </summary>
        [TestMethod()]
        public void AddTimeEnabled_ShouldReturnTrue_WhenClockInOutEnabledAndApprovalStatusZero()
        {
            // Assemble
            SetDefaultConfig(isSingleCheckInEnabled: false, judgeApprovalStatus: 0);

            // Act & Assert
            Assert.IsTrue(_viewModel.AddTimeEnabled);
        }

        /// <summary>
        /// Verifies that adding a new time entry is not allowed in the 'Clock In/Out' workflow,
        /// when the judge approval is not zero.
        /// </summary>
        [TestMethod()]
        public void AddTimeEnabled_ShouldReturnFalse_WhenClockInOutEnabledAndApprovalStatusNotZero()
        {
            // Assemble
            SetDefaultConfig(isSingleCheckInEnabled: false, judgeApprovalStatus: 1);

            // Act & Assert
            Assert.IsFalse(_viewModel.AddTimeEnabled);
        }
    }
}
