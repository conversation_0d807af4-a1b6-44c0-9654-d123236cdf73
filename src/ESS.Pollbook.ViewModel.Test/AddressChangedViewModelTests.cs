using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Core.Messaging;
using ESS.Pollbook.ViewModel.Voter;
using GalaSoft.MvvmLight.Messaging;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using System.Collections.Generic;

namespace ESS.Pollbook.ViewModel.Test.AddressChanged
{
    [TestClass]
    public class AddressChangedViewModelTests
    {
        private Mock<IFrameNavigationService> _navigation;
        private Mock<IMessenger> _messenger;
        private Mock<IEssLogger> _essLogger;
        private Mock<IAuditLogFacade> _auditLogFacade;

        private VoterDto _voter;

        private AddressChangedViewModel _viewModel;

        [TestInitialize]
        public void Initialize()
        {
            _navigation = new Mock<IFrameNavigationService>();
            _messenger = new Mock<IMessenger>();
            _essLogger = new Mock<IEssLogger>();
            _auditLogFacade = new Mock<IAuditLogFacade>();

            SystemConfiguration.ElectionConfiguration = new PollbookConfigurationDto
            {
                AddressOnlyChangeType = AddressOnlyChangeType.Addr_Only,
            };

            DefinedText.PollbookDefinedTextDtos = new List<ElectionPollbookDefinedTextDto>();
            DefinedText.PollbookDefinedTextDtos.Add(new ElectionPollbookDefinedTextDto
            {
                PollbookDefinedTextLanguage = "English",
                PollbookDefinedTextName = "AddressUnchangedButton",
                PollbookDefinedTextValue = "Address Unchanged"
            });

            _navigation.Setup(x => x.PageName(It.IsAny<string>())).Returns("AddressChanged");

            _viewModel = new AddressChangedViewModel(
               _navigation.Object,
               _messenger.Object,
               _essLogger.Object,
               _auditLogFacade.Object);

            _voter = new VoterDto();
            _viewModel.Voter = _voter;
        }

        [TestMethod]
        public void No_NavigatesToVoterView_WhenConfigured()
        {
            //Assemble
            _auditLogFacade.Setup(x => x.AddToAuditLog(It.IsAny<string>(), It.IsAny<string>()));
            _messenger.Setup(x => x.Send(It.IsAny<AddressChangedMessage>()));

            _navigation.Setup(x => x.NavigateTo<VoterViewModel>(
                    It.IsAny<NavigationFrameEnum>(),
                    It.IsAny<bool>(), //registerHistory
                    It.IsAny<bool>(), //isRootPage
                    It.IsAny<bool>(), //withVerticalEffect
                    It.IsAny<bool>(), //suppressHorizontalEffect
                    It.IsAny<bool>()) //useThreadSleep
            );

            //Act
            _viewModel.No();

            //Assert
            _auditLogFacade.Verify(x => x.AddToAuditLog(It.IsAny<string>(), It.IsAny<string>()), Times.Exactly(1));
            _messenger.Verify(x => x.Send(It.IsAny<AddressChangedMessage>()), Times.Exactly(1));

            _navigation.Verify(x => x
                .NavigateTo<VoterViewModel>(NavigationFrameEnum.ContextFrame,
                It.IsAny<bool>(), It.IsAny<bool>(), It.IsAny<bool>(), It.IsAny<bool>(), It.IsAny<bool>()),
                Times.Exactly(1));
        }

        [TestMethod]
        public void No_ClosesModal_WhenNotConfigured()
        {
            //Assemble
            SystemConfiguration.ElectionConfiguration.AddressOnlyChangeType = AddressOnlyChangeType.None;

            _auditLogFacade.Setup(x => x.AddToAuditLog(It.IsAny<string>(), It.IsAny<string>()));
            _messenger.Setup(x => x.Send(It.IsAny<AddressChangedMessage>()));
            _navigation.Setup(x => x.CloseModalWindow(It.IsAny<bool>()));

            //Act
            _viewModel.No();

            //Assert
            _auditLogFacade.Verify(x => x.AddToAuditLog(It.IsAny<string>(), It.IsAny<string>()), Times.Exactly(1));
            _messenger.Verify(x => x.Send(It.IsAny<AddressChangedMessage>()), Times.Exactly(1));
            _navigation.Verify(x => x.CloseModalWindow(true), Times.Exactly(1));
        }

        [TestMethod]
        public void Yes_NavigatesToVoterEditView_WhenConfigured()
        {
            //Assemble
            _auditLogFacade.Setup(x => x.AddToAuditLog(It.IsAny<string>(), It.IsAny<string>()));
            _messenger.Setup(x => x.Send(It.IsAny<AddressChangedMessage>()));
            _messenger.Setup(x => x.Send(It.IsAny<VoterEditMessage>()));

            _navigation.Setup(x => x.NavigateTo<VoterViewModel>(
                    It.IsAny<bool>(),
                    It.IsAny<NavigationFrameEnum>(),
                    It.IsAny<bool>(), //registerHistory
                    It.IsAny<bool>(), //isRootPage
                    It.IsAny<bool>(), //withVerticalEffect
                    It.IsAny<bool>(), //suppressHorizontalEffect
                    It.IsAny<bool>()) //useThreadSleep
            );

            //Act
            _viewModel.Yes();

            //Assert
            _auditLogFacade.Verify(x => x.AddToAuditLog(It.IsAny<string>(), It.IsAny<string>()), Times.Exactly(1));
            _messenger.Verify(x => x.Send(It.IsAny<AddressChangedMessage>()), Times.Exactly(1));
            _messenger.Verify(x => x.Send(It.IsAny<VoterEditMessage>()), Times.Exactly(1));

            _navigation.Verify(x => x
                .NavigateTo<VoterEditViewModel>(It.IsAny<bool>(), NavigationFrameEnum.ContextFrame,
                It.IsAny<bool>(), It.IsAny<bool>(), It.IsAny<bool>(), It.IsAny<bool>(), It.IsAny<bool>()),
                Times.Exactly(1));
        }

        [TestMethod]
        public void Yes_ClosesModal_WhenNotConfigured()
        {
            //Assemble
            SystemConfiguration.ElectionConfiguration.AddressOnlyChangeType = AddressOnlyChangeType.None;

            _auditLogFacade.Setup(x => x.AddToAuditLog(It.IsAny<string>(), It.IsAny<string>()));
            _messenger.Setup(x => x.Send(It.IsAny<AddressChangedMessage>()));
            _messenger.Setup(x => x.Send(It.IsAny<VoterEditMessage>()));

            //Act
            _viewModel.Yes();

            //Assert
            _auditLogFacade.Verify(x => x.AddToAuditLog(It.IsAny<string>(), It.IsAny<string>()), Times.Exactly(1));
            _messenger.Verify(x => x.Send(It.IsAny<AddressChangedMessage>()), Times.Exactly(1));
            _navigation.Verify(x => x.CloseModalWindow(true), Times.Exactly(1));
        }
    }
}