using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Facade.Configuration;
using ESS.Pollbook.Facade.Maintenance;
using ESS.Pollbook.Facade.PagesTitle;
using ESS.Pollbook.Facade.PollbookDefinedText;
using ESS.Pollbook.ViewModel.SelectBallotType;
using ESS.Pollbook.ViewModel.Utils;
using GalaSoft.MvvmLight.Messaging;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace ESS.Pollbook.ViewModel.Test.SelectBallotType
{
   [TestClass]
   public class SelectBallotTypeViewModelTests
   {
      private Mock<IFrameNavigationService> _navigationMock;
      private Mock<IMessenger> _messengerMock;
      private Mock<IPollbookConfigurationFacade> _pollbookConfigurationFacadeMock;
      private Mock<IPagesTitleFacade> _pagesTitleFacadeMock;
      private Mock<IMaintenanceFacade> _maintenanceFacadeMock;
      private Mock<IEssLogger> _essLoggerMock;
      private Mock<IPollbookDefinedTextFacade> _pollbookDefinedTextFacadeMock;
      private Mock<IAuditLogFacade> _auditLogFacadeMock;
      private Mock<IPrintBallotUtil> _printBallotUtilMock;
      private SelectBallotTypeViewModel _viewModel;

      private List<string> _paperList;
      private List<string> _expressVoteList;

      [TestInitialize]
      public void Initialize()
      {
         _navigationMock = new Mock<IFrameNavigationService>();
         _messengerMock = new Mock<IMessenger>();
         _pollbookConfigurationFacadeMock = new Mock<IPollbookConfigurationFacade>();
         _pagesTitleFacadeMock = new Mock<IPagesTitleFacade>();
         _maintenanceFacadeMock = new Mock<IMaintenanceFacade>();
         _essLoggerMock = new Mock<IEssLogger>();
         _pollbookDefinedTextFacadeMock = new Mock<IPollbookDefinedTextFacade>();
         _auditLogFacadeMock = new Mock<IAuditLogFacade>();
         _printBallotUtilMock = new Mock<IPrintBallotUtil>();

         _paperList = new List<string> {{"Paper"}};
         _expressVoteList = new List<string>{{"ExpressVote"}, {"Activation Card"}};

         _pollbookDefinedTextFacadeMock
            .Setup(x => x.GetPollbookDefinedTextForLanguage("ExpressVote_Button_Label", It.IsAny<string>()))
            .Returns("ExpressVote Activation Card");

         _pollbookDefinedTextFacadeMock
            .Setup(x => x.GetPollbookDefinedTextForLanguage("Paper_Ballot_Button_Label", It.IsAny<string>()))
            .Returns("Paper");

            _auditLogFacadeMock
               .Setup(x => x.AddToAuditLog(It.IsAny<string>(), It.IsAny<string>()));

         _navigationMock
            .Setup(x => x.PageName(It.IsAny<string>()))
            .Returns("SelectBallotType");

         _viewModel = new SelectBallotTypeViewModel(_navigationMock.Object,
	         _messengerMock.Object,
	         _pagesTitleFacadeMock.Object,
	         _essLoggerMock.Object,
	         _pollbookDefinedTextFacadeMock.Object,
	         _auditLogFacadeMock.Object,
	         _printBallotUtilMock.Object);
      }

      [TestMethod]
      public void PageIsLoaded_WithSingleItemList_Success()
      {
         var expectedPaper = _paperList;

         _viewModel.PageIsLoaded();

         var actualPaper = _viewModel.PaperLabelText;

         var comparePaper = expectedPaper.SequenceEqual(actualPaper);
         Assert.IsTrue(comparePaper);
      }

      [TestMethod]
      public void PageIsLoaded_WithSingleItemLists_Failure()
      {
         var expectedExpressVote = new List<string>
         {
            {"Activation Card"}
         };

         var expectedPaper = new List<string> { "Zig Paper" };

         _viewModel.PageIsLoaded();

         var actualExpressVote = _viewModel.ExpressVoteLabelText;
         var actualPaper = _viewModel.PaperLabelText;

         var compareExpressVote = expectedExpressVote.SequenceEqual(actualExpressVote);
         Assert.AreNotEqual(actualExpressVote.Count, expectedExpressVote.Count);
         Assert.IsFalse(compareExpressVote);

         var comparePaper = expectedPaper.SequenceEqual(actualPaper);
         Assert.AreEqual(actualPaper.Count, expectedPaper.Count);
         Assert.IsFalse(comparePaper);
      }

      [TestMethod]
      public void PageIsLoaded_WithMultipleItemLists_Success()
      {
         var expectedExpressVote = _expressVoteList;

         _viewModel.PageIsLoaded();

         var actualExpressVote = _viewModel.ExpressVoteLabelText;

         var compareExpressVote = expectedExpressVote.SequenceEqual(actualExpressVote);
         Assert.IsTrue(compareExpressVote);
      }

      [TestMethod]
      public async Task IssueBallotAsync_Paper_Selected()
      {
         SystemDetails.IsPQCVerified = true;
         _viewModel.ExpressVoteActivationIsChecked = false;
         _viewModel.BODIsChecked = false;
         _viewModel.PaperBallotIsChecked = true;

         SystemConfiguration.ElectionConfiguration = new PollbookConfigurationDto
         {
            EnableEDProvisionalExpressVoteBallot = true,
            EnableEVProvisionalExpressVoteBallot = true,
            EnableEDStandardExpressVoteBallot = true,
            EnableEVStandardExpressVoteBallot = true,
            EnableEDProvisionalExpressVotePrinting = false,
            EnableEVProvisionalExpressVotePrinting = false,
            EnableEDStandardExpressVotePrinting = false,
            EnableEVStandardExpressVotePrinting = false,
            DacEnabled = false
         };
         LoggedInPollplaceInfo.LoggedInPollPlace = new PollPlaceDto
            {PollingPlacePollTypeNumber = (int) PollType.EarlyVote};
         IssueBallotHelper.Voter = new VoterDto {VoterBallotDto = new VoterBallotDto {IsProvisional = false}};

         var expected = BallotType.Paper;

         await _viewModel.IssueBallotAsync();

         var actual = IssueBallotHelper.SelectedBallotType;

         Assert.AreEqual(expected, actual);
      }

      [TestMethod]
      public async Task IssueBallotAsync_ExpressVote_Selected()
      {
         SystemDetails.IsPQCVerified = true;
         _viewModel.ExpressVoteActivationIsChecked = true;
         _viewModel.BODIsChecked = false;

         IssueBallotHelper.Voter = new VoterDto();

         SystemConfiguration.ElectionConfiguration = new PollbookConfigurationDto{DacEnabled = false};

         var expected = BallotType.ExpressVote;

         await _viewModel.IssueBallotAsync();

         var actual = IssueBallotHelper.SelectedBallotType;

         Assert.AreEqual(expected, actual);
      }

      [TestMethod]
      public async Task IssueBallotAsync_DAC_Selected()
      {
         SystemDetails.IsPQCVerified = true;
         _viewModel.ExpressVoteActivationIsChecked = true;
         _viewModel.BODIsChecked = false;

         IssueBallotHelper.Voter = new VoterDto();

         SystemConfiguration.ElectionConfiguration = new PollbookConfigurationDto { DacEnabled = true };

         var expected = BallotType.DAC;

         await _viewModel.IssueBallotAsync();

         var actual = IssueBallotHelper.SelectedBallotType;

         Assert.AreEqual(expected, actual);
      }

      [TestMethod]
      public async Task IssueBallotAsync_AuditLog_Verify()
      {
         SystemDetails.IsPQCVerified = true;
         _viewModel.ExpressVoteActivationIsChecked = false;
         _viewModel.BODIsChecked = false;

         await _viewModel.IssueBallotAsync();

         var label = _viewModel.IsReissuingBallot ? UIText.ReissueBallot : UIText.IssueBallot;
         _auditLogFacadeMock.Verify(x => x.AddToAuditLogAsync("SelectBallotType", $"'{label}' button was activated."), Times.AtLeast(1));
      }

      [TestMethod]
      [DataRow("ABCDEFG", new[] { "ABCDEFG" })]
      [DataRow("ABCDEFGHIJKLMNOPQRSTUVWXYZ", new[]{"ABCDEFGHIJKLM", "NOPQRSTUVWXYZ"})]
      [DataRow("ABCD EFGHIJKLMNOPQRSTUVWXYZ", new[] { "ABCD", "EFGHIJKLMNOPQRSTUVWXYZ" })]
      [DataRow("ABCDEFGHIJKLM NOPQRSTUVWXYZ", new[] { "ABCDEFGHIJKLM", "NOPQRSTUVWXYZ" })]
      [DataRow("ABCDEFGHIJKLMNOPQRST UVWXYZ", new[] { "ABCDEFGHIJKLMNOPQRST", "UVWXYZ" })]
      [DataRow("ABCDEFGHIJKLM NOPQRST UVWXYZ", new[] { "ABCDEFGHIJKLM", "NOPQRST UVWXYZ" })]
      [DataRow("ABCD EFGHIJKLM NOPQRST UVWXYZ", new[] { "ABCD EFGHIJKLM", "NOPQRST UVWXYZ" })]
      [DataRow("ABCD EFGHIJK LM NOPQRST UVWXYZ", new[] { "ABCD EFGHIJK LM", "NOPQRST UVWXYZ" })]
      [DataRow("ABCD EFGHIJKLM N OPQRST UVWXYZ", new[] { "ABCD EFGHIJKLM N", "OPQRST UVWXYZ" })]
      public void TwoLineButtonLabel_WithData_Success(string input, string[] array)
      {
         var expected = array.ToList();

         var actual = _viewModel.CreateButtonTextLabelLayout(input, 20);

         var compare = expected.SequenceEqual(actual);

         Assert.IsTrue(compare, $"Tested: {input}");
      }

   }
}
