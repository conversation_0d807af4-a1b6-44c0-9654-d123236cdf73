using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.Model;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Facade.ElectionJurisdictionEnum;
using ESS.Pollbook.Facade.PagesTitle;
using ESS.Pollbook.Facade.Party;
using ESS.Pollbook.Facade.PollbookDefinedText;
using ESS.Pollbook.Facade.PollbookTransaction;
using ESS.Pollbook.Core.Messaging;
using ESS.Pollbook.ViewModel.Utils;
using ESS.Pollbook.ViewModel.VoterSignature;
using GalaSoft.MvvmLight.Messaging;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ESS.Pollbook.ViewModel.Test.VoterSignature
{
    [TestClass()]
    public class SignatureComparisonViewModelTests
    {
        private Mock<IMessenger> _messengerMock;
        private Mock<IFrameNavigationService> _navigationMock;
        private Mock<IPagesTitleFacade> _pagesTitleMock;
        private Mock<IPartyFacade> _partyServiceMock;
        private Mock<IPollbookDefinedTextFacade> _definedTextMock;
        private Mock<IVoterJurisdictionEnumFacade> _jurisdictionEnumMock;
        private Mock<IPollbookTransactionFacade> _transactionMock;
        private Mock<IEssLogger> _essLoggerMock;
        private Mock<IAuditLogFacade> _auditLogFacadeMock;
        private Mock<IPrintBallotUtil> _printBallotMock;

        private SignatureComparisonViewModel _viewModel;

        [TestInitialize]
        public void Initialize()
        {
            _messengerMock = new Mock<IMessenger>();
            _navigationMock = new Mock<IFrameNavigationService>();
            _partyServiceMock = new Mock<IPartyFacade>();
            _essLoggerMock = new Mock<IEssLogger>();
            _auditLogFacadeMock = new Mock<IAuditLogFacade>();
            _jurisdictionEnumMock = new Mock<IVoterJurisdictionEnumFacade>();
            _printBallotMock = new Mock<IPrintBallotUtil>();
            _pagesTitleMock = new Mock<IPagesTitleFacade>();
            _definedTextMock = new Mock<IPollbookDefinedTextFacade>();
            _transactionMock = new Mock<IPollbookTransactionFacade>();
        }

        [TestMethod()]
        public void UnableToSignPlaceholder_ShouldBeVisible_WhenEnabledAndSignatureSkipped()
        {
            //Assemble
            SetupConfig(ReasonsDisplayType.JurValuesAndOtherText);
            SetupGenericMocks();
            BuildViewModel();
            IssueBallotHelper.Voter = BuildVoter();

            //Act
            SystemConfiguration.ElectionConfiguration.EnableSkipSignature = true;
            SystemConfiguration.ElectionConfiguration.UnableSignType = ReasonsDisplayType.JurValues;
            IssueBallotHelper.Voter.SignatureSkipReasonEnumId = 1;
            IssueBallotHelper.Voter.VoterTransactionSignature = null;

            //Assert
            Assert.IsTrue(_viewModel.ShowUnableToSignPlaceholder);
            Assert.IsFalse(_viewModel.ShowSignatureCapturedOnPaperPlaceholder);
        }

        [TestMethod()]
        public void SignatureCapturedOnPaperPlaceholder_ShouldBeVisible_WhenEnabledAndSignatureSkipped()
        {
            //Assemble
            SetupConfig(ReasonsDisplayType.JurValuesAndOtherText);
            SetupGenericMocks();
            BuildViewModel();
            IssueBallotHelper.Voter = BuildVoter();

            //Act
            SystemConfiguration.ElectionConfiguration.CapturePaperSignature = true;
            SystemConfiguration.ElectionConfiguration.UnableSignType = ReasonsDisplayType.JurValues;
            IssueBallotHelper.Voter.SignatureSkipReasonEnumId = 1;
            IssueBallotHelper.Voter.VoterTransactionSignature = null;

            //Assert
            Assert.IsFalse(_viewModel.ShowUnableToSignPlaceholder);
            Assert.IsTrue(_viewModel.ShowSignatureCapturedOnPaperPlaceholder);
        }

        [TestMethod()]
        public void SkipSignaturePlaceholder_ShouldBeHidden_WhenSignatureCaptured()
        {
            //Assemble
            SetupConfig(ReasonsDisplayType.JurValuesAndOtherText);
            SetupGenericMocks();
            BuildViewModel();
            IssueBallotHelper.Voter = BuildVoter();

            //Act
            SystemConfiguration.ElectionConfiguration.CapturePaperSignature = true;
            SystemConfiguration.ElectionConfiguration.UnableSignType = ReasonsDisplayType.JurValues;
            IssueBallotHelper.Voter.VoterTransactionSignature = new byte[1000];

            //Assert
            Assert.IsFalse(_viewModel.ShowUnableToSignPlaceholder);
            Assert.IsFalse(_viewModel.ShowSignatureCapturedOnPaperPlaceholder);
        }

        private void SetupGenericMocks()
        {
            _messengerMock
                .Setup(x => x.Send(It.IsAny<StatusBarMessage>()));
        }

        private void SetupConfig(ReasonsDisplayType unableSign)
        {
            var electionConfiguration = new PollbookConfigurationDto
            {
                UnableSignType = unableSign
            };
            SystemConfiguration.ElectionConfiguration = electionConfiguration;
        }

        private void BuildViewModel()
        {
            _viewModel = new SignatureComparisonViewModel(
                _navigationMock.Object,
                _messengerMock.Object,
                _partyServiceMock.Object,
                _pagesTitleMock.Object,
                _definedTextMock.Object,
                _jurisdictionEnumMock.Object,
                _transactionMock.Object,
                _essLoggerMock.Object,
                _auditLogFacadeMock.Object,
                _printBallotMock.Object
            );
        }

        private VoterDto BuildVoter()
        {
            return new VoterDto
            {
                SignatureSkipReasonEnumId = 0,
                VoterTransactionSignature = new byte[5]
            };
        }
    }
}
