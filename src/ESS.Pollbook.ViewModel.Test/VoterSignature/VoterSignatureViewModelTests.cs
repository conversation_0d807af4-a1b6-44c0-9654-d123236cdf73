using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Facade.Party;
using ESS.Pollbook.Facade.PollbookDefinedText;
using ESS.Pollbook.Facade.VRSignatures;
using ESS.Pollbook.ViewModel.IssueBallotHelperInterfaced;
using ESS.Pollbook.Core.Messaging;
using ESS.Pollbook.ViewModel.Utils;
using ESS.Pollbook.ViewModel.VoterSignature;
using GalaSoft.MvvmLight.Messaging;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;

namespace ESS.Pollbook.ViewModel.Test.VoterSignature
{
    [TestClass()]
    public class VoterSignatureViewModelTests
    {
        private Mock<IFrameNavigationService> _navigationServiceMock;
        private Mock<IMessenger> _messengerServiceMock;
        private Mock<IPartyFacade> _partyServiceMock;
        private Mock<ISignatureComparisonFacade> _vrSignaturesFacadeMock;
        private Mock<IEssLogger> _essLoggerMock;
        private Mock<IPollbookDefinedTextFacade> _pollbookDefinedTextFacadeMock;
        private Mock<IAuditLogFacade> _auditLogFacadeMock;
        private Mock<IIssueBallotHelper> _issueBallotHelperMock;
        private Mock<IPrintBallotUtil> _printBallotUtilMock;

        private VoterSignatureViewModel _viewModel;

        [TestInitialize]
        public void Initialize()
        {
            _navigationServiceMock = new Mock<IFrameNavigationService>();
            _messengerServiceMock = new Mock<IMessenger>();
            _partyServiceMock = new Mock<IPartyFacade>();
            _vrSignaturesFacadeMock = new Mock<ISignatureComparisonFacade>();
            _essLoggerMock = new Mock<IEssLogger>();
            _pollbookDefinedTextFacadeMock = new Mock<IPollbookDefinedTextFacade>();
            _auditLogFacadeMock = new Mock<IAuditLogFacade>();
            _issueBallotHelperMock = new Mock<IIssueBallotHelper>();
            _printBallotUtilMock = new Mock<IPrintBallotUtil>();
        }

        [TestMethod()]
        public void SkipSignatureAsync_NavigatesWithoutRequiredReason_WhenUnableSignNone()
        {
            //Arrange
            SetupConfig(unableSign: ReasonsDisplayType.None);
            SetupGenericMocks();
            IssueBallotHelper.Voter = BuildVoter();

            // Precondition asserts
            Assert.IsNotNull(IssueBallotHelper.Voter.SignatureSkipReasonEnumId);
            Assert.IsNotNull(IssueBallotHelper.Voter.VoterTransactionSignature);

            BuildViewModel();

            _navigationServiceMock
                .Setup(x => x.NavigateTo<DashboardViewModel>(
                    NavigationFrameEnum.MainFrame,
                    false,
                    It.IsAny<bool>(),
                    It.IsAny<bool>(),
                    It.IsAny<bool>(),
                    It.IsAny<bool>()));
            
            //Act
            var skipCommand = _viewModel.SkipCommand;
            skipCommand.Execute(this);

            //Assert
            Assert.IsNull(IssueBallotHelper.Voter.SignatureSkipReasonEnumId);
            Assert.IsNull(IssueBallotHelper.Voter.VoterTransactionSignature);

            _navigationServiceMock
                .Verify(x => x.NavigateTo<DashboardViewModel>(
                    NavigationFrameEnum.MainFrame,
                false,
                It.IsAny<bool>(), It.IsAny<bool>(),
                It.IsAny<bool>(),
                It.IsAny<bool>()),
                Times.Exactly(1));

            _issueBallotHelperMock
                .Verify(x => x.NavigateToNextPage(
                It.IsAny<IFrameNavigationService>(),
                It.IsAny<IPartyFacade>(),
                It.IsAny<IMessenger>(),
                It.IsAny<IPrintBallotUtil>()),
                Times.Exactly(1));
        }

        [TestMethod()]
        public void SkipSignatureAsync_NavigatesWithoutRequiredReason_WhenUnableSignNotNone()
        {
            //Arrange
            SetupConfig(unableSign: ReasonsDisplayType.JurValues);
            SetupGenericMocks();
            IssueBallotHelper.Voter = BuildVoter();

            // Precondition asserts
            Assert.IsNotNull(IssueBallotHelper.Voter.VoterTransactionSignature);

            BuildViewModel();

            _navigationServiceMock
                .Setup(x => x.NavigateTo<SelectSkipSignatureReasonViewModel>(
                    NavigationFrameEnum.ContextFrame,
                    It.IsAny<bool>(),
                    It.IsAny<bool>(),
                    It.IsAny<bool>(),
                    It.IsAny<bool>(),
                    It.IsAny<bool>()));

            //Act
            var skipCommand = _viewModel.SkipCommand;
            skipCommand.Execute(this);

            //Assert
            Assert.IsNull(IssueBallotHelper.Voter.VoterTransactionSignature);

            _navigationServiceMock
                .Verify(x => x.NavigateTo<SelectSkipSignatureReasonViewModel>(
                        NavigationFrameEnum.ContextFrame,
                        It.IsAny<bool>(),
                        It.IsAny<bool>(),
                        It.IsAny<bool>(),
                        It.IsAny<bool>(),
                        It.IsAny<bool>()),
                    Times.Exactly(1));
        }

        [TestMethod()]
        public void SignatureCapturedOnPaperActivatedAsync_NavigatesWithoutRequiredReason_WhenUnableSignNone()
        {
            //Arrange
            SetupConfig(unableSign: ReasonsDisplayType.None);
            SetupGenericMocks();
            IssueBallotHelper.Voter = BuildVoter();

            // Precondition asserts
            Assert.IsNotNull(IssueBallotHelper.Voter.SignatureSkipReasonEnumId);
            Assert.IsNotNull(IssueBallotHelper.Voter.VoterTransactionSignature);

            BuildViewModel();

            _navigationServiceMock
                .Setup(x => x.NavigateTo<DashboardViewModel>(
                    NavigationFrameEnum.MainFrame,
                    false,
                    It.IsAny<bool>(),
                    It.IsAny<bool>(),
                    It.IsAny<bool>(),
                    It.IsAny<bool>()));

            //Act
            var command = _viewModel.SignatureCapturedOnPaperCommand;
            command.Execute(this);

            //Assert
            Assert.IsNull(IssueBallotHelper.Voter.SignatureSkipReasonEnumId);
            Assert.IsNull(IssueBallotHelper.Voter.VoterTransactionSignature);

            _navigationServiceMock
                .Verify(x => x.NavigateTo<DashboardViewModel>(
                    NavigationFrameEnum.MainFrame,
                false,
                It.IsAny<bool>(), It.IsAny<bool>(),
                It.IsAny<bool>(),
                It.IsAny<bool>()),
                Times.Exactly(1));

            _navigationServiceMock
                .Verify(x => x.NavigateTo<SignatureComparisonViewModel>(
                    NavigationFrameEnum.ContextFrame,
                It.IsAny<bool>(),
                It.IsAny<bool>(), It.IsAny<bool>(),
                It.IsAny<bool>(),
                It.IsAny<bool>()),
                Times.Exactly(1));
        }

        [TestMethod()]
        public void SignatureCapturedOnPaperActivatedAsync_NavigatesWithRequiredReason_WhenUnableSignNotNone()
        {
            //Arrange
            SetupConfig(unableSign: ReasonsDisplayType.JurValues);
            SetupGenericMocks();
            IssueBallotHelper.Voter = BuildVoter();

            // Precondition asserts
            Assert.IsNotNull(IssueBallotHelper.Voter.SignatureSkipReasonEnumId);
            Assert.IsNotNull(IssueBallotHelper.Voter.VoterTransactionSignature);

            BuildViewModel();

            _navigationServiceMock
                .Setup(x => x.NavigateTo<DashboardViewModel>(
                    NavigationFrameEnum.MainFrame,
                    false,
                    It.IsAny<bool>(),
                    It.IsAny<bool>(),
                    It.IsAny<bool>(),
                    It.IsAny<bool>()));

            //Act
            var command = _viewModel.SignatureCapturedOnPaperCommand;
            command.Execute(this);

            //Assert
            Assert.IsNotNull(IssueBallotHelper.Voter.SignatureSkipReasonEnumId);
            Assert.IsNull(IssueBallotHelper.Voter.VoterTransactionSignature);

            _navigationServiceMock
                .Verify(x => x.NavigateTo<DashboardViewModel>(
                    NavigationFrameEnum.MainFrame,
                false,
                It.IsAny<bool>(), It.IsAny<bool>(),
                It.IsAny<bool>(),
                It.IsAny<bool>()),
                Times.Exactly(1));

            _navigationServiceMock
                .Verify(x => x.NavigateTo<SelectSkipSignatureReasonViewModel>(
                    NavigationFrameEnum.ContextFrame,
                It.IsAny<bool>(),
                It.IsAny<bool>(), It.IsAny<bool>(),
                It.IsAny<bool>(),
                It.IsAny<bool>()),
                Times.Exactly(1));
        }

        private void SetupGenericMocks()
        {
            _messengerServiceMock
                .Setup(x => x.Send(It.IsAny<StatusBarMessage>()));

            _issueBallotHelperMock
                .Setup(x => x.NavigateToNextPage(
                    It.IsAny<IFrameNavigationService>(),
                    It.IsAny<IPartyFacade>(),
                    It.IsAny<IMessenger>(),
                    It.IsAny<IPrintBallotUtil>()
                ));
        }

        private void SetupConfig(ReasonsDisplayType unableSign)
        {
            var electionConfiguration = new PollbookConfigurationDto
            {
                UnableSignType = unableSign
            };
            SystemConfiguration.ElectionConfiguration = electionConfiguration;
        }

        private void BuildViewModel()
        {
            _viewModel = new VoterSignatureViewModel(
                _navigationServiceMock.Object,
                _messengerServiceMock.Object,
                _partyServiceMock.Object,
                _vrSignaturesFacadeMock.Object,
                _essLoggerMock.Object,
                _pollbookDefinedTextFacadeMock.Object,
                _auditLogFacadeMock.Object,
                _issueBallotHelperMock.Object,
                _printBallotUtilMock.Object
            );
        }

        private VoterDto BuildVoter()
        {
            return new VoterDto
            {
                SignatureSkipReasonEnumId = 0,
                VoterTransactionSignature = new byte[5]
            };
        }
    }
}