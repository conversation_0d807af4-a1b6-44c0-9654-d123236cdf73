using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.Model;
using ESS.Pollbook.Core.Model.VoterEligibility;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Facade.DynamicControls;
using ESS.Pollbook.Facade.ElectionJurisdictionEnum;
using ESS.Pollbook.Facade.Party;
using ESS.Pollbook.Facade.PollbookDefinedText;
using ESS.Pollbook.Facade.PollbookTransaction;
using ESS.Pollbook.Facade.PollPlace;
using ESS.Pollbook.Facade.VoterBallot;
using ESS.Pollbook.Facade.VoterDetails;
using ESS.Pollbook.Facade.VoterEligibility;
using ESS.Pollbook.Facade.VoterTransaction;
using ESS.Pollbook.Core.Messaging;
using ESS.Pollbook.ViewModel.Utils;
using ESS.Pollbook.ViewModel.Utils.NavRouter;
using ESS.Pollbook.ViewModel.Voter;
using GalaSoft.MvvmLight.Messaging;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ESS.Pollbook.ViewModel.Test.Voter
{
    [TestClass]
    public class VoterViewModelTests
    {
        private Mock<IFrameNavigationService> _navigation;
        private Mock<IEssLogger> _essLogger;
        private Mock<IVoterDetailsFacade> _voterDetailsFacade;
        private Mock<IPollPlaceFacade> _pollPlaceFacade;
        private Mock<IVoterEligibilityFacade> _voterEligibilityFacade;
        private Mock<IMessenger> _messenger;
        private Mock<IPollbookTransactionFacade> _transactionFacade;
        private Mock<IPrintPollPlaceFacade> _printPollPlaceFacade;
        private Mock<IPartyFacade> _partyService;
        private Mock<IDynamicControlsFacade> _dynamicControlsFacade;
        private Mock<IPollbookDefinedTextFacade> _pollbookDefinedTextFacade;
        private Mock<IVoterJurisdictionEnumFacade> _voterJurisdictionEnumFacade;
        private Mock<IAuditLogFacade> _auditLogFacade;
        private Mock<IVoterTransactionFacade> _voterTransactionFacade;
        private Mock<IPrintBallotUtil> _printBallotUtil;
        private Mock<IVoterBallotFacade> _voterBallotFacade;
        private Mock<INavRouter> _navRouter;

        private VoterDto _voter;
        private EligibilityCalculationResult _eligibility;
        private bool _provisional;

        private VoterViewModel _viewModel;

        public VoterViewModelTests()
        {
            _eligibility = new EligibilityCalculationResult
            {
                StatusColor = "blue",
                DisplayMessage = "Eligible",
                ErrorMessage = string.Empty,
                WrongPollingLocation = false
            };

            _voter = new VoterDto
            {
                VoterStatusEnumId = 1,
                IdentificationStatusEnumId = 1,
                AbsenteeStatusEnumId = 1,
                IsPreviouslyVoted = false,
                HasHostResults = true,
                PrecinctSplitName = "TestPrecinct",
                PartyName = "TestParty",
                BallotStyleDescription = "Frankford Township, District 1",
                HasMultipleBallots = false,
                RecordInitialLoadIndicator = true
            };

            SetupPollbookDefinedText();
            SetupDefaultMocks();
        }

        [TestInitialize]
        public async Task Initialize()
        {
            SetupConfig();
            await SetupViewModel();
        }

        private void SetupPollbookDefinedText()
        {
            DefinedText.PollbookDefinedTextDtos = new List<ElectionPollbookDefinedTextDto>();
            DefinedText.PollbookDefinedTextDtos.Add(new ElectionPollbookDefinedTextDto
            {
                PollbookDefinedTextLanguage = "English",
                PollbookDefinedTextName = "Party",
                PollbookDefinedTextValue = "Party Label"
            });
            DefinedText.PollbookDefinedTextDtos.Add(new ElectionPollbookDefinedTextDto
            {
                PollbookDefinedTextLanguage = "English",
                PollbookDefinedTextName = "Precinct",
                PollbookDefinedTextValue = "Precinct Label"
            });
            DefinedText.PollbookDefinedTextDtos.Add(new ElectionPollbookDefinedTextDto
            {
                PollbookDefinedTextLanguage = "English",
                PollbookDefinedTextName = "Voter_Registration_Number",
                PollbookDefinedTextValue = "VoterRegistrationNumber"
            });
        }

        private void SetupConfig()
        {
            LoggedInPollplaceInfo.IsPollOpen = true;
            LoggedInPollplaceInfo.LoggedInPollPlace = new PollPlaceDto
            {
                PollingPlacePollTypeNumber = (int)PollType.EarlyVote,
                PollingPlaceVoteCenterIndicator = 0
            };

            SystemConfiguration.ElectionConfiguration = new PollbookConfigurationDto
            {
                EnableEVStandardPaperBallot = true,
                ElectionType = ElectionTypeEnum.General.ToString(),
                PrintingEnabled = false,
                EnableEditVoter = true,
                AddressOnlyChangeType = AddressOnlyChangeType.Addr_Only,
                EnableEVProvisionalPaperBallot = false,
                EnableSmsMessages = false,
                DisplayRegistrationDate = true,
                DisplayVoterRegisteredParty = true,
                EnableFullDob = DobFormatEnum.full_dob.ToString(),
                EnableDobValidation = false
            };

            _provisional = SystemConfiguration.ElectionConfiguration.ProvisionalBallotEnabled(LoggedInPollplaceInfo.IsEarlyVotingPollPlace);
        }

        private void SetupDefaultMocks()
        {
            _navigation = new Mock<IFrameNavigationService>();
            _messenger = new Mock<IMessenger>();
            _pollPlaceFacade = new Mock<IPollPlaceFacade>();
            _voterEligibilityFacade = new Mock<IVoterEligibilityFacade>();
            _printPollPlaceFacade = new Mock<IPrintPollPlaceFacade>();
            _transactionFacade = new Mock<IPollbookTransactionFacade>();
            _partyService = new Mock<IPartyFacade>();
            _essLogger = new Mock<IEssLogger>();
            _voterDetailsFacade = new Mock<IVoterDetailsFacade>();
            _dynamicControlsFacade = new Mock<IDynamicControlsFacade>();
            _pollbookDefinedTextFacade = new Mock<IPollbookDefinedTextFacade>();
            _voterJurisdictionEnumFacade = new Mock<IVoterJurisdictionEnumFacade>();
            _auditLogFacade = new Mock<IAuditLogFacade>();
            _voterTransactionFacade = new Mock<IVoterTransactionFacade>();
            _printBallotUtil = new Mock<IPrintBallotUtil>();
            _voterBallotFacade = new Mock<IVoterBallotFacade>();
            _navRouter = new Mock<INavRouter>();

            _voterEligibilityFacade.Setup(x => x
                .Calculate(
                    It.IsAny<VoterDto>(),
                    It.IsAny<PollPlaceDto>(),
                    It.IsAny<SystemStatsDto>(),
                    It.IsAny<VoterP2PStatusModel>(),
                    false,
                    It.IsAny<List<ElectionJurisdictionEnumValueDto>>(),
                    false
                ))
                .Returns(Task.FromResult(_eligibility));
        }

        private async Task SetupViewModel()
        {
	        _viewModel = new VoterViewModel(
		        _navigation.Object,
		        _messenger.Object,
		        _pollPlaceFacade.Object,
		        _voterEligibilityFacade.Object,
		        _printPollPlaceFacade.Object,
		        _transactionFacade.Object,
		        _essLogger.Object,
		        _voterDetailsFacade.Object,
		        _dynamicControlsFacade.Object,
		        _pollbookDefinedTextFacade.Object,
		        _voterJurisdictionEnumFacade.Object,
		        _auditLogFacade.Object,
		        _voterTransactionFacade.Object,
		        _voterBallotFacade.Object,
		        _navRouter.Object);

            var message = new VoterSelectedMessage(_voter);

            await _viewModel.OnVoterSelectedAsync(message);
        }

        [TestMethod]
        //in roster (PASS)
        [DataRow("Test1", 24, 165, true, true, 300, false, true, false, "1/1/2000", false, false, false, true)]

        // NIR, conditional enabled, early voting (PASS)
        [DataRow("Test2", 24, 165, false, true, 300, false, true, false, "1/1/2000", false, false, false, true)]

        // NIR, conditional not enabled, early voting (PASS)
        [DataRow("Test3", 24, 165, false, true, 300, false, false, false, "1/1/2000", false, false, false, true)]

        // NIR, conditional enabled, early ballot issued, early voting (FAIL)
        [DataRow("Test4", 24, 204, false, true, 300, false, true, false, "1/1/2000", false, false, false, false)]

        // NIR, conditional NOT enabled, early ballot issued, early voting (PASS) to RE-ISSUE a standard with an early ballot issue absentee status
        [DataRow("Test5", 24, 204, false, true, 300, false, false, false, "1/1/2000", false, false, false, true)]

        //NIR, conditional enabled, election day (FAIL)
        [DataRow("Test6", 24, 165, false, true, 200, false, true, false, "1/1/2000", false, false, false, false)]

        //NIR, conditional disabled, election day (PASS)
        [DataRow("Test7", 24, 165, false, true, 200, false, false, false, "1/1/2000", false, false, false, true)]

        //NIR, conditional disabled, config set to fail, election day (FAIL)
        [DataRow("Test8", 23, 165, false, true, 200, false, false, false, "1/1/2000", false, false, false, false)]

        //NIR, TX Workflow (pass)
        [DataRow("Test9", 23, 165, false, true, 200, true, false, false, "1/1/2000", false, false, false, true)]

        //DOB Fail
        [DataRow("Test10", 24, 165, true, true, 200, true, false, true, "1/1/2015", false, false, false, false)]

        //DOB Pass
        [DataRow("Test11", 24, 165, true, true, 200, true, false, true, "1/1/1975", false, false, false, true)]
        public void IsNextEnabled_MatrixTest(
           string testname, //1
           int voterStatusEnumId, //2
           int absenteeStatusEnumId, //3
           bool recordInitialLoadIndicator, //4
           bool isPollOpen, //5
           int pollTypeNumber, //6
           bool enableTexasWorkflow, //7
           bool enableConditionalRegistration, //8
           bool enableDobValidation, //9
           string birthDate, //10
           bool standardBallotDisabled, //11
           bool isProvisional, //12
           bool isReissueLimitReached, //13
           bool expectedResult //14
        )
        {
            _viewModel.Voter = new VoterDto
            {
                VoterStatusEnumId = 24,
                AbsenteeStatusEnumId = 165,
                RecordInitialLoadIndicator = recordInitialLoadIndicator,
                DateOfBirth = birthDate
            };

            _voterJurisdictionEnumFacade
               .Setup(x => x.GetCommonEnumValueIdFromJurisdictionEnumId(_viewModel.Voter.VoterStatusEnumId))
               .Returns(voterStatusEnumId);
            _voterJurisdictionEnumFacade
               .Setup(x => x.GetCommonEnumValueIdFromJurisdictionEnumId(_viewModel.Voter.AbsenteeStatusEnumId))
               .Returns(absenteeStatusEnumId);

            LoggedInPollplaceInfo.IsPollOpen = isPollOpen;
            LoggedInPollplaceInfo.LoggedInPollPlace = new PollPlaceDto { PollingPlacePollTypeNumber = pollTypeNumber };

            SystemConfiguration.ElectionConfiguration = new PollbookConfigurationDto
            {
                EnableTexasWorkflow = enableTexasWorkflow,
                EnableConditionalRegistration = enableConditionalRegistration,
                EnableDobValidation = enableDobValidation,
                DobValidationDate = DateTime.Parse("11/2/2023")
            };

            _viewModel.CalculationResult = new EligibilityCalculationResult
            { StandardBallotDisabled = standardBallotDisabled };

            _viewModel.IsProvisional = isProvisional;
            _viewModel.IsReissueLimitReached = isReissueLimitReached;

            Assert.AreEqual(_viewModel.IsNextEnabled, expectedResult);
        }


        [TestMethod]
        public void IsNextEnabled()
        {
            _viewModel.Voter = new VoterDto
            {
                VoterStatusEnumId = 24,
                AbsenteeStatusEnumId = 165,
                RecordInitialLoadIndicator = false
            };

            _voterJurisdictionEnumFacade
               .Setup(x => x.GetCommonEnumValueIdFromJurisdictionEnumId(_viewModel.Voter.VoterStatusEnumId))
               .Returns(24);
            _voterJurisdictionEnumFacade
               .Setup(x => x.GetCommonEnumValueIdFromJurisdictionEnumId(_viewModel.Voter.AbsenteeStatusEnumId))
               .Returns(165);

            SystemConfiguration.ElectionConfiguration = new PollbookConfigurationDto
            {
                EnableTexasWorkflow = false,
                EnableConditionalRegistration = true
            };

            LoggedInPollplaceInfo.IsPollOpen = true;
            LoggedInPollplaceInfo.LoggedInPollPlace = new PollPlaceDto { PollingPlacePollTypeNumber = 300 };

            _viewModel.CalculationResult = new EligibilityCalculationResult
            {
                StandardBallotDisabled = false
            };
            _viewModel.IsProvisional = false;
            _viewModel.IsReissueLimitReached = false;

            Assert.IsTrue(_viewModel.IsNextEnabled);
        }

        [TestMethod]
        public void IsNextEnabled_NotEarlyPollPlaceDBConfigGood()
        {
            _viewModel.Voter = new VoterDto
            {
                VoterStatusEnumId = 24,
                AbsenteeStatusEnumId = 165,
                RecordInitialLoadIndicator = false
            };

            _voterJurisdictionEnumFacade
               .Setup(x => x.GetCommonEnumValueIdFromJurisdictionEnumId(_viewModel.Voter.VoterStatusEnumId))
               .Returns(24);
            _voterJurisdictionEnumFacade
               .Setup(x => x.GetCommonEnumValueIdFromJurisdictionEnumId(_viewModel.Voter.AbsenteeStatusEnumId))
               .Returns(165);

            LoggedInPollplaceInfo.IsPollOpen = true;
            LoggedInPollplaceInfo.LoggedInPollPlace = new PollPlaceDto { PollingPlacePollTypeNumber = 200 };

            SystemConfiguration.ElectionConfiguration = new PollbookConfigurationDto
            {
                EnableTexasWorkflow = false,
                EnableConditionalRegistration = true
            };

            _viewModel.CalculationResult = new EligibilityCalculationResult
            {
                StandardBallotDisabled = false
            };

            _viewModel.IsProvisional = false;
            _viewModel.IsReissueLimitReached = false;

            Assert.IsFalse(_viewModel.IsNextEnabled);
        }

        [TestMethod]
        public void IsNextEnabled_NotEarlyPollPlaceDBConfigNotGood()
        {
            _viewModel.Voter = new VoterDto
            {
                VoterStatusEnumId = 24,
                AbsenteeStatusEnumId = 165,
                RecordInitialLoadIndicator = false
            };

            _voterJurisdictionEnumFacade
               .Setup(x => x.GetCommonEnumValueIdFromJurisdictionEnumId(_viewModel.Voter.VoterStatusEnumId)).Returns(23);
            _voterJurisdictionEnumFacade
               .Setup(x => x.GetCommonEnumValueIdFromJurisdictionEnumId(_viewModel.Voter.AbsenteeStatusEnumId))
               .Returns(165);

            LoggedInPollplaceInfo.IsPollOpen = true;
            LoggedInPollplaceInfo.LoggedInPollPlace = new PollPlaceDto { PollingPlacePollTypeNumber = 200 };

            SystemConfiguration.ElectionConfiguration = new PollbookConfigurationDto();
            SystemConfiguration.ElectionConfiguration.EnableTexasWorkflow = false;
            SystemConfiguration.ElectionConfiguration.EnableConditionalRegistration = true;

            _viewModel.CalculationResult = new EligibilityCalculationResult();
            _viewModel.CalculationResult.StandardBallotDisabled = false;

            _viewModel.IsProvisional = false;
            _viewModel.IsReissueLimitReached = false;

            Assert.IsFalse(_viewModel.IsNextEnabled);
        }

        [TestMethod]
        public void IsNextEnabled_ConditionalRegistrationNotEnabled()
        {
            _viewModel.Voter = new VoterDto
            {
                VoterStatusEnumId = 24,
                AbsenteeStatusEnumId = 165,
                RecordInitialLoadIndicator = false
            };

            _voterJurisdictionEnumFacade
               .Setup(x => x.GetCommonEnumValueIdFromJurisdictionEnumId(_viewModel.Voter.VoterStatusEnumId))
               .Returns(24);
            _voterJurisdictionEnumFacade
               .Setup(x => x.GetCommonEnumValueIdFromJurisdictionEnumId(_viewModel.Voter.AbsenteeStatusEnumId))
               .Returns(165);

            LoggedInPollplaceInfo.IsPollOpen = true;
            LoggedInPollplaceInfo.LoggedInPollPlace = new PollPlaceDto { PollingPlacePollTypeNumber = 300 };

            SystemConfiguration.ElectionConfiguration = new PollbookConfigurationDto
            {
                EnableTexasWorkflow = false,
                EnableConditionalRegistration = false,
                EnableDobValidation = false
            };

            _viewModel.CalculationResult = new EligibilityCalculationResult
            {
                StandardBallotDisabled = false
            };

            _viewModel.IsProvisional = false;
            _viewModel.IsReissueLimitReached = false;

            Assert.IsTrue(_viewModel.IsNextEnabled);
        }

        [TestMethod]
        public void IsNextEnabled_ConditionalRegistrationEnabledEarlyVoting()
        {
            _viewModel.Voter = new VoterDto
            {
                VoterStatusEnumId = 24,
                AbsenteeStatusEnumId = 165,
                RecordInitialLoadIndicator = false
            };

            _voterJurisdictionEnumFacade
               .Setup(x => x.GetCommonEnumValueIdFromJurisdictionEnumId(_viewModel.Voter.VoterStatusEnumId)).Returns(24);
            _voterJurisdictionEnumFacade
               .Setup(x => x.GetCommonEnumValueIdFromJurisdictionEnumId(_viewModel.Voter.AbsenteeStatusEnumId))
               .Returns(165);

            LoggedInPollplaceInfo.IsPollOpen = true;
            LoggedInPollplaceInfo.LoggedInPollPlace = new PollPlaceDto { PollingPlacePollTypeNumber = 300 };

            SystemConfiguration.ElectionConfiguration = new PollbookConfigurationDto
            {
                EnableTexasWorkflow = false,
                EnableConditionalRegistration = true,
                EnableDobValidation = false
            };

            _viewModel.CalculationResult = new EligibilityCalculationResult
            {
                StandardBallotDisabled = false
            };

            _viewModel.IsProvisional = false;
            _viewModel.IsReissueLimitReached = false;

            Assert.IsTrue(_viewModel.IsNextEnabled);
        }

        [TestMethod]
        public void IsNextEnabled_ConditionalRegistrationEnabledEarlyVoting_FailedDBConfig()
        {
            _viewModel.Voter = new VoterDto
            {
                VoterStatusEnumId = 24,
                AbsenteeStatusEnumId = 165,
                RecordInitialLoadIndicator = false
            };

            _voterJurisdictionEnumFacade
               .Setup(x => x.GetCommonEnumValueIdFromJurisdictionEnumId(_viewModel.Voter.VoterStatusEnumId)).Returns(23);
            _voterJurisdictionEnumFacade
               .Setup(x => x.GetCommonEnumValueIdFromJurisdictionEnumId(_viewModel.Voter.AbsenteeStatusEnumId))
               .Returns(165);

            LoggedInPollplaceInfo.IsPollOpen = true;
            LoggedInPollplaceInfo.LoggedInPollPlace = new PollPlaceDto { PollingPlacePollTypeNumber = 300 };

            SystemConfiguration.ElectionConfiguration = new PollbookConfigurationDto
            {
                EnableTexasWorkflow = false,
                EnableConditionalRegistration = true,
                EnableDobValidation = false
            };

            _viewModel.CalculationResult = new EligibilityCalculationResult
            {
                StandardBallotDisabled = false
            };

            _viewModel.IsProvisional = false;
            _viewModel.IsReissueLimitReached = false;

            Assert.IsFalse(_viewModel.IsNextEnabled);
        }

        [TestMethod]
        public void IsNextEnabled_DOBValidationNotEnabled()
        {
            _viewModel.Voter = new VoterDto
            {
                RecordInitialLoadIndicator = true,
                BirthDate = DateTime.Parse("11/11/2009")
            };

            LoggedInPollplaceInfo.IsPollOpen = true;
            LoggedInPollplaceInfo.LoggedInPollPlace = new PollPlaceDto { PollingPlacePollTypeNumber = 200 };

            SystemConfiguration.ElectionConfiguration = new PollbookConfigurationDto
            {
                EnableTexasWorkflow = false,
                EnableConditionalRegistration = true,
                EnableDobValidation = false
            };

            _viewModel.CalculationResult = new EligibilityCalculationResult
            {
                StandardBallotDisabled = false
            };

            _viewModel.IsProvisional = false;
            _viewModel.IsReissueLimitReached = false;

            Assert.IsTrue(_viewModel.IsNextEnabled);
        }

        [TestMethod]
        public void IsNextEnabled_DOBValidationEnabled_NotOldEnough()
        {
            _viewModel.Voter = new VoterDto
            {
                RecordInitialLoadIndicator = true,
                BirthDate = DateTime.Parse("11/11/2009")
            };

            LoggedInPollplaceInfo.IsPollOpen = true;
            LoggedInPollplaceInfo.LoggedInPollPlace = new PollPlaceDto { PollingPlacePollTypeNumber = 200 };

            SystemConfiguration.ElectionConfiguration = new PollbookConfigurationDto
            {
                EnableTexasWorkflow = false,
                EnableConditionalRegistration = true,
                EnableDobValidation = true,
                DobValidationDate = DateTime.Parse("11/2/2023")
            };

            _viewModel.CalculationResult = new EligibilityCalculationResult
            {
                StandardBallotDisabled = false
            };

            _viewModel.IsProvisional = false;
            _viewModel.IsReissueLimitReached = false;

            Assert.IsFalse(_viewModel.IsNextEnabled);
        }

        [TestMethod]
        public void IsNextEnabled_DOBValidationEnabled_OldEnough()
        {
            _viewModel.Voter = new VoterDto
            {
                RecordInitialLoadIndicator = true,
                DateOfBirth = "11-11-1959"
            };

            LoggedInPollplaceInfo.IsPollOpen = true;
            LoggedInPollplaceInfo.LoggedInPollPlace = new PollPlaceDto { PollingPlacePollTypeNumber = 200 };

            SystemConfiguration.ElectionConfiguration = new PollbookConfigurationDto
            {
                EnableTexasWorkflow = false,
                EnableConditionalRegistration = true,
                EnableDobValidation = true,
                DobValidationDate = DateTime.Parse("11/2/2023")
            };

            _viewModel.CalculationResult = new EligibilityCalculationResult
            {
                StandardBallotDisabled = false
            };

            _viewModel.IsProvisional = false;
            _viewModel.IsReissueLimitReached = false;

            Assert.IsTrue(_viewModel.IsNextEnabled);
        }

        [TestMethod]
        public async Task FormButtons_ShouldBeCorrect_WhenEnableVoterAddressChangeAndWrongPollingLocation()
        {
            //Assemble
            SystemConfiguration.ElectionConfiguration.AddressOnlyChangeType = AddressOnlyChangeType.Addr_Affidavit;
            _eligibility.WrongPollingLocation = true;

            //Act
            SetupDefaultMocks();
            await SetupViewModel();

            //Assert
            Assert.AreEqual(true, _viewModel.IsPollOpen);
            Assert.AreEqual(true, _viewModel.EnableVoterAddressChange);
            Assert.AreEqual(false, _provisional);

            Assert.AreEqual(true, _viewModel.IsProvisionalEnabled);
            Assert.AreEqual(true, _viewModel.IsProvisionalBtnVisible);

            Assert.AreEqual(true, _viewModel.IsWrongPollPlace);

            Assert.AreEqual(false, _viewModel.IsNextBtnVisible);
            Assert.AreEqual(false, _viewModel.IsNextEnabled);
        }

        [TestMethod]
        public async Task FormButtons_ShouldBeCorrect_WhenEnableVoterAddressChangeAndCorrectPollingLocation()
        {
            //Assemble
            SystemConfiguration.ElectionConfiguration.AddressOnlyChangeType = AddressOnlyChangeType.Addr_Only;
            _eligibility.WrongPollingLocation = false;

            //Act
            SetupDefaultMocks();
            await SetupViewModel();

            //Assert
            Assert.AreEqual(true, _viewModel.IsPollOpen);
            Assert.AreEqual(true, _viewModel.EnableVoterAddressChange);
            Assert.AreEqual(false, _provisional);

            Assert.AreEqual(false, _viewModel.IsProvisionalEnabled);
            Assert.AreEqual(false, _viewModel.IsProvisionalBtnVisible);

            Assert.AreEqual(false, _viewModel.IsWrongPollPlace);

            Assert.AreEqual(true, _viewModel.IsNextBtnVisible);
            Assert.AreEqual(true, _viewModel.IsNextEnabled);
        }

        [TestMethod]
        public async Task FormButtons_ShouldBeCorrect_WhenDisableVoterAddressChangeAndWrongPollingLocation()
        {
            //Assemble
            SystemConfiguration.ElectionConfiguration.AddressOnlyChangeType = AddressOnlyChangeType.None;
            _eligibility.WrongPollingLocation = true;

            //Act
            SetupDefaultMocks();
            await SetupViewModel();

            //Assert
            Assert.AreEqual(true, _viewModel.IsPollOpen);
            Assert.AreEqual(false, _viewModel.EnableVoterAddressChange);
            Assert.AreEqual(false, _provisional);

            Assert.AreEqual(false, _viewModel.IsProvisionalEnabled);
            Assert.AreEqual(false, _viewModel.IsProvisionalBtnVisible);

            Assert.AreEqual(true, _viewModel.IsWrongPollPlace);

            Assert.AreEqual(false, _viewModel.IsNextBtnVisible);
            Assert.AreEqual(false, _viewModel.IsNextEnabled);
        }

        [TestMethod]
        public async Task FormButtons_ShouldBeCorrect_WhenDisableVoterAddressChangeAndCorrectPollingLocation()
        {
            //Assemble
            SystemConfiguration.ElectionConfiguration.AddressOnlyChangeType = AddressOnlyChangeType.None;
            _eligibility.WrongPollingLocation = false;

            //Act
            SetupDefaultMocks();
            await SetupViewModel();

            //Assert
            Assert.AreEqual(true, _viewModel.IsPollOpen);
            Assert.AreEqual(false, _viewModel.EnableVoterAddressChange);
            Assert.AreEqual(false, _provisional);

            Assert.AreEqual(false, _viewModel.IsProvisionalEnabled);
            Assert.AreEqual(false, _viewModel.IsProvisionalBtnVisible);

            Assert.AreEqual(false, _viewModel.IsWrongPollPlace);

            Assert.AreEqual(true, _viewModel.IsNextBtnVisible);
            Assert.AreEqual(true, _viewModel.IsNextEnabled);
        }
    }
}