using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Facade.ElectionJurisdictionEnum;
using ESS.Pollbook.Facade.Party;
using ESS.Pollbook.Facade.PollPlace;
using ESS.Pollbook.Facade.PrecinctSplit;
using ESS.Pollbook.Facade.StreetSearch;
using ESS.Pollbook.Facade.VoterEditInfo;
using ESS.Pollbook.ViewModel.Maintenance;
using ESS.Pollbook.ViewModel.Voter;
using GalaSoft.MvvmLight.Messaging;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using System;
using System.Collections.Generic;

namespace ESS.Pollbook.ViewModel.Test.Voter
{
    [TestClass()]
    public class VoterAddViewModelTests
    {
        private Mock<IFrameNavigationService> _navigation;
        private Mock<IEssLogger> _essLogger;
        private Mock<IPollPlaceFacade> _pollPlaceFacade;
        private Mock<IMessenger> _messenger;
        private Mock<IVoterJurisdictionEnumFacade> _voterJurisdictionEnumFacade;
        private Mock<IAuditLogFacade> _auditLogFacade;
        private VoterAddViewModel _viewModel;
        private Mock<IPrecinctSplitFacade> _precinctSplitFacade;
        private Mock<IVoterAddEditInfoFacade> _voterAddEditInfoFacade;
        private Mock<IStreetSearchFacade> _streetSearchFacade;
        private Mock<IPartyFacade> _partyFacade;

        [TestInitialize]
        public void Initialize()
        {
            _navigation = new Mock<IFrameNavigationService>();
            _messenger = new Mock<IMessenger>();
            _pollPlaceFacade = new Mock<IPollPlaceFacade>();
            _essLogger = new Mock<IEssLogger>();
            _voterJurisdictionEnumFacade = new Mock<IVoterJurisdictionEnumFacade>();
            _auditLogFacade = new Mock<IAuditLogFacade>();
            _precinctSplitFacade = new Mock<IPrecinctSplitFacade>();
            _voterAddEditInfoFacade = new Mock<IVoterAddEditInfoFacade>();
            _streetSearchFacade = new Mock<IStreetSearchFacade>();
            _partyFacade = new Mock<IPartyFacade>();
        }

        private void SetDefaultConfig()
        {
            //either full_dob, only_year, none or null
            SystemConfiguration.ElectionConfiguration = new PollbookConfigurationDto()
            {
                EnableEmailEdit = false,
                EnableFullDob = "full_dob",
                CaptureCitizenNotInRoster = true,
                CaptureGenderNotInRoster = true,
                CapturePartyNotInRoster = true,
                EnableDobValidation = true,
                DefaultParty = "Unaffiliated",
                DobValidationDate = new DateTime(2024, 11, 2)
            };

            SystemDetails.DeviceSerialNumber = "Test Pollbook Go2";

            LoggedInPollplaceInfo.IsPollOpen = true;
            LoggedInPollplaceInfo.LoggedInPollPlace = new PollPlaceDto { PollingPlacePollTypeNumber = 200 };
        }

        private void CreateViewmodel()
        {
            var ballotStyleTypeList = new List<ElectionJurisdictionEnumValueDto>
            {
                new ElectionJurisdictionEnumValueDto
                {
                    EnumerationValueCode = "Reg",
                    EnumerationValueDescription = "Regular",
                    JurisdictionEnumerationValueName = "Regular",
                    JurisdictionEnumerationValueId = 1
                }
            };

            _voterJurisdictionEnumFacade
                .Setup(x => x.GetBallotStyleTypesAsync())
                .ReturnsAsync(ballotStyleTypeList);

            _voterJurisdictionEnumFacade
                .Setup(x => 
                    x.GetBallotStyleTypesByPrecinctAndPartyAsync(It.IsAny<int>(), It.IsAny<int>()))
                .ReturnsAsync(ballotStyleTypeList);

            _viewModel = new VoterAddViewModel(
                _essLogger.Object,
                _messenger.Object,
                _navigation.Object,
                _precinctSplitFacade.Object,
                _voterAddEditInfoFacade.Object,
                _pollPlaceFacade.Object,
                _streetSearchFacade.Object,
                _voterJurisdictionEnumFacade.Object,
                _partyFacade.Object,
                _auditLogFacade.Object)
            {
                IsInitialized = true,
                EmailAddress = "<EMAIL>",
                FirstName = "TestFirst2",
                LastName = "TestLast2",
                HouseNumber = "1234",
                StreetName = "Main St",
                City = "Omaha",
                State = "NE",
                Zip = "68122",
                SearchTerm = "Test Search",
                SelectedPrecinctSplitItem = new PrecinctSplitDto { PrecinctSplitName = "Test Precinct" },
                SelectedMonth = new TimeUnit(10, "Month"),
                SelectedDay = new TimeUnit(3, "Day"),
                SelectedYear = new TimeUnit(2003, "Year"),
                BallotStyleTypeList = ballotStyleTypeList,
                SelectedBallotStyleType = ballotStyleTypeList[0],
                SelectedCitizen = UIText.Yes,
                SelectedParty = new PartyDto(),
                SelectedGender = new ElectionJurisdictionEnumValueDto
                {
                    EnumerationValueCode = "M",
                    EnumerationValueDescription = "Male"
                }
            };
        }

        [DataRow(true)]
        [DataRow(false)]
        [TestMethod()]
        public void EnableEmailEdit_ShouldMatchConfig_WhenViewmodelLoads(bool enableEmailEdit)
        {
            SetDefaultConfig();
            SystemConfiguration.ElectionConfiguration.EnableEmailEdit = enableEmailEdit;

            CreateViewmodel();

            Assert.AreEqual(enableEmailEdit, _viewModel.EnableEmailEdit);
        }

        [TestMethod()]
        public void EnableSave_ShouldBeTrue_WhenFormIsValid()
        {
            SetDefaultConfig();
            CreateViewmodel();

            _viewModel.EnableSave();

            Assert.IsTrue(_viewModel.IsSaveEnabled);
        }

        [TestMethod()]
        public void EnableSave_ShouldBeFalse_WhenEmailIsNotValid()
        {
            SetDefaultConfig();
            CreateViewmodel();
            _viewModel.EmailAddress = "BAD";

            _viewModel.EnableSave();

            Assert.IsFalse(_viewModel.IsSaveEnabled);
        }

        [TestMethod()]
        public void VoterAddViewModel_Validate_True()
        {
            SetDefaultConfig();
            CreateViewmodel();

            Assert.IsTrue(_viewModel.Validate());
        }

        [TestMethod()]
        public void VoterAddViewModel_Validate_False_NotACitizen()
        {
            Initialize();

            //either full_dob, only_year, none or null
            SystemConfiguration.ElectionConfiguration = new PollbookConfigurationDto()
            {
                EnableFullDob = "full_dob",
                CaptureCitizenNotInRoster = true,
                CaptureGenderNotInRoster = true,
                CapturePartyNotInRoster = true,
                EnableDobValidation = true,
                DobValidationDate = new DateTime(2024, 11, 2)
            };

            SystemDetails.DeviceSerialNumber = "Test Pollbook Go2";

            LoggedInPollplaceInfo.IsPollOpen = true;
            LoggedInPollplaceInfo.LoggedInPollPlace = new PollPlaceDto { PollingPlacePollTypeNumber = 200 };

            CreateViewmodel();

            _viewModel.IsInitialized = true;
            _viewModel.FirstName = "TestFirst2";
            _viewModel.LastName = "TestLast2";
            _viewModel.HouseNumber = "1234";
            _viewModel.StreetName = "Main St";
            _viewModel.City = "Omaha";
            _viewModel.State = "NE";
            _viewModel.Zip = "68122";

            _viewModel.SearchTerm = "Test Search";
            _viewModel.SelectedPrecinctSplitItem = new PrecinctSplitDto() { PrecinctSplitName = "Test Precinct" };

            _viewModel.SelectedYear = new TimeUnit(2003, "Year");
            _viewModel.SelectedMonth = new TimeUnit(10, "Month");
            _viewModel.SelectedDay = new TimeUnit(3, "Day");

            _viewModel.SelectedCitizen = UIText.No;
            _viewModel.SelectedParty = new PartyDto();
            _viewModel.SelectedGender = new ElectionJurisdictionEnumValueDto();

            Assert.IsFalse(_viewModel.Validate());
        }

        [DataRow(11, 10, 2005, true)]
        [DataRow(11, 11, 2005, true)]
        [TestMethod()]
        public void VoterAddViewModel_Validate(int month, int day, int year, bool expectedResult)
        {
            SetDefaultConfig();
            SystemConfiguration.ElectionConfiguration.DobValidationDate = new DateTime(2025, 11, 11);

            CreateViewmodel();
            _viewModel.SelectedYear = new TimeUnit(year, "Year");
            _viewModel.SelectedMonth = new TimeUnit(month, "Month");
            _viewModel.SelectedDay = new TimeUnit(day, "Day");

            var actual = _viewModel.ValidateDob();

            Assert.AreEqual(expectedResult, actual);
        }

        [DataRow(12, 33, 2005)]
        [TestMethod()]
        public void VoterAddViewModel_ValidateDob_Failure(int month, int day, int year)
        {
            SetDefaultConfig();
            SystemConfiguration.ElectionConfiguration.DobValidationDate = new DateTime(2025, 11, 11);

            CreateViewmodel();
            _viewModel.SelectedYear = new TimeUnit(year, "Year");
            _viewModel.SelectedMonth = new TimeUnit(month, "Month");
            _viewModel.SelectedDay = new TimeUnit(day, "Day");

            Assert.ThrowsException<ArgumentOutOfRangeException>(() => _viewModel.ValidateDob());
        }
    }
}