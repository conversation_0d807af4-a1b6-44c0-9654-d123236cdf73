using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Facade.Device;
using ESS.Pollbook.Facade.ElectionJurisdictionEnum;
using ESS.Pollbook.Facade.PollPlace;
using ESS.Pollbook.Facade.PrecinctSplit;
using ESS.Pollbook.Facade.StreetSearch;
using ESS.Pollbook.Facade.VoterDetails;
using ESS.Pollbook.Facade.VoterEditInfo;
using ESS.Pollbook.ViewModel.Affidavit;
using ESS.Pollbook.Core.Messaging;
using ESS.Pollbook.ViewModel.Voter;
using GalaSoft.MvvmLight.Messaging;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ESS.Pollbook.ViewModel.Test.Voter
{
    [TestClass()]
    public class VoterEditViewModelTests
    {
        private Mock<IEssLogger> _essLogger;
        private Mock<IMessenger> _messenger;
        private Mock<IFrameNavigationService> _navigation;
        private Mock<IPrecinctSplitFacade> _precinctSplitFacade;
        private Mock<IVoterAddEditInfoFacade> _voterAddEditInfoFacade;
        private Mock<IPollPlaceFacade> _pollPlaceFacade;
        private Mock<IStreetSearchFacade> _streetSearchFacade;
        private Mock<IAuditLogFacade> _auditLogFacade;
        private Mock<IVoterDetailsFacade> _voterDetailsFacade;
        private Mock<IVoterJurisdictionEnumFacade> _voterJurisdictionEnumFacade;
        private Mock<IPrinterFacade> _printerFacade;

        private VoterDto _voter;
        private VoterEditViewModel _viewModel;

        [TestInitialize]
        public void Initialize()
        {
            _essLogger = new Mock<IEssLogger>();
            _messenger = new Mock<IMessenger>();
            _navigation = new Mock<IFrameNavigationService>();
            _precinctSplitFacade = new Mock<IPrecinctSplitFacade>();
            _voterAddEditInfoFacade = new Mock<IVoterAddEditInfoFacade>();
            _pollPlaceFacade = new Mock<IPollPlaceFacade>();
            _streetSearchFacade = new Mock<IStreetSearchFacade>();
            _auditLogFacade = new Mock<IAuditLogFacade>();
            _voterDetailsFacade = new Mock<IVoterDetailsFacade>();
            _voterJurisdictionEnumFacade = new Mock<IVoterJurisdictionEnumFacade>();
            _printerFacade = new Mock<IPrinterFacade>();
        }

        private void SetDefaultConfig()
        {
            //either full_dob, only_year, none or null
            SystemConfiguration.ElectionConfiguration = new PollbookConfigurationDto()
            {
                EnableEmailEdit = false,
                EnableFullDob = "full_dob",
                CaptureCitizenNotInRoster = true,
                CaptureGenderNotInRoster = true,
                CapturePartyNotInRoster = true,
                EnableDobValidation = true,
                DobValidationDate = new DateTime(2024, 11, 2),
                AddressOnlyChangeType = AddressOnlyChangeType.None
            };

            SystemDetails.DeviceSerialNumber = "Test Pollbook Go2";

            LoggedInPollplaceInfo.IsPollOpen = true;
            LoggedInPollplaceInfo.LoggedInPollPlace = new PollPlaceDto { PollingPlacePollTypeNumber = 200 };
        }

        private void CreateViewmodel()
        {
            var ballotStyleTypeList = new List<ElectionJurisdictionEnumValueDto>
            {
                new ElectionJurisdictionEnumValueDto
                {
                    EnumerationValueCode = "Reg",
                    EnumerationValueDescription = "Regular"
                }
            };

            _voterJurisdictionEnumFacade
                .Setup(x => x.GetBallotStyleTypesAsync())
                .Returns(Task.FromResult(ballotStyleTypeList));

            var precinct = new PrecinctSplitDto { PrecinctSplitName = "Test Precinct" };

            _precinctSplitFacade
                .Setup(x => x
                    .GetPrecinctSplitNameByPrecinctSplitId(It.IsAny<int>()))
                .Returns(Task.FromResult(precinct));

            _voter = new VoterDto()
            {
                EmailAddress = "<EMAIL>",
                FirstName = "TestFirst2",
                LastName = "TestLast2",
                HouseNumber = "1234",
                StreetName = "Main St",
                City = "Omaha",
                State = "NE",
                Zip = "68122",
            };

            _viewModel = new VoterEditViewModel(
                _essLogger.Object,
                _messenger.Object,
                _navigation.Object,
                _precinctSplitFacade.Object,
                _voterAddEditInfoFacade.Object,
                _pollPlaceFacade.Object,
                _streetSearchFacade.Object,
                _auditLogFacade.Object,
                _voterDetailsFacade.Object,
                _voterJurisdictionEnumFacade.Object,
                _printerFacade.Object);

            _viewModel.VoterEditMessageReceived(new VoterEditMessage(_voter));
        }

        [DataRow(true)]
        [DataRow(false)]
        [TestMethod()]
        public void EnableEmailEdit_ShouldMatchConfig_WhenViewmodelLoads(bool enableEmailEdit)
        {
            SetDefaultConfig();
            SystemConfiguration.ElectionConfiguration.EnableEmailEdit = enableEmailEdit;

            CreateViewmodel();

            Assert.AreEqual(enableEmailEdit, _viewModel.EnableEmailEdit);
        }

        [TestMethod()]
        public void EnableSave_ShouldBeFalse_WhenNoEdits()
        {
            SetDefaultConfig();
            CreateViewmodel();

            Assert.IsFalse(_viewModel.IsSaveEnabled);
        }

        [TestMethod()]
        public void EnableSave_ShouldBeTrue_WhenFormIsValid()
        {
            SetDefaultConfig();
            CreateViewmodel();
            _viewModel.FirstName = "UpdatedName";

            Assert.IsTrue(_viewModel.IsSaveEnabled);
        }

        [TestMethod()]
        public void EnableSave_ShouldBeFalse_WhenEmailIsNotValid()
        {
            SetDefaultConfig();
            CreateViewmodel();
            _viewModel.EmailAddress = "BAD";

            Assert.IsFalse(_viewModel.IsSaveEnabled);
            Assert.IsFalse(_viewModel.IsEmailAddressValid);
        }

        [TestMethod]
        public void Next_NavigateToAffidavitView_WhenConfigured()
        {
            //Assemble
            SetDefaultConfig();
            SystemConfiguration.ElectionConfiguration.AddressOnlyChangeType = AddressOnlyChangeType.Addr_Affidavit;
            CreateViewmodel();

            //Act
            _viewModel.IsModal = true;
            _viewModel.SaveCommand.ExecuteAsync();

            //Assert
            _messenger.Verify(x => x.Send(It.IsAny<AffirmationOfResidenceAffidavitMessage>()), Times.Exactly(1));
            _navigation.Verify(x => x
                .NavigateTo<AffirmationOfResidenceAffidavitViewModel>(It.IsAny<bool>(), NavigationFrameEnum.ContextFrame,
                It.IsAny<bool>(), It.IsAny<bool>(), It.IsAny<bool>(), It.IsAny<bool>(), It.IsAny<bool>()),
                Times.Exactly(1));
        }

        [TestMethod]
        public void Next_NavigateToVoterView_WhenConfiguredAddrOnly()
        {
            //Assemble
            SetDefaultConfig();
            SystemConfiguration.ElectionConfiguration.AddressOnlyChangeType = AddressOnlyChangeType.Addr_Only;
            CreateViewmodel();

            //Act
            _viewModel.SaveCommand.ExecuteAsync();

            //Assert
            _messenger.Verify(x => x.Send(It.IsAny<VoterSelectedMessage>()), Times.Exactly(1));
            _navigation.Verify(x => x
                .NavigateTo<VoterViewModel>(NavigationFrameEnum.ContextFrame,
                It.IsAny<bool>(), It.IsAny<bool>(), It.IsAny<bool>(), It.IsAny<bool>(), It.IsAny<bool>()),
                Times.Exactly(1));
        }

        [TestMethod]
        public void Next_NavigateToVoterView_WhenConfiguredDefault()
        {
            //Assemble
            SetDefaultConfig();
            CreateViewmodel();

            //Act
            _viewModel.SaveCommand.ExecuteAsync();

            //Assert
            _messenger.Verify(x => x.Send(It.IsAny<VoterSelectedMessage>()), Times.Exactly(1));
            _navigation.Verify(x => x
                .NavigateTo<VoterViewModel>(NavigationFrameEnum.ContextFrame,
                It.IsAny<bool>(), It.IsAny<bool>(), It.IsAny<bool>(), It.IsAny<bool>(), It.IsAny<bool>()),
                Times.Exactly(1));
        }
    }
}