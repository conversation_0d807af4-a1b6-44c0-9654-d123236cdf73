using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Facade.PollbookDefinedText;
using ESS.Pollbook.ViewModel.Affidavit;
using ESS.Pollbook.Core.Messaging;
using GalaSoft.MvvmLight.Messaging;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ESS.Pollbook.ViewModel.Test.Affidavit
{
    [TestClass()]
    public class VoterNameVerificationViewModelTests
    {
        private Mock<IAuditLogFacade> _auditLogFacadeMock;
        private Mock<IEssLogger> _loggerMock;
        private Mock<IMessenger> _messengerMock;
        private Mock<IFrameNavigationService> _navigationServiceMock;
        private Mock<IPollbookDefinedTextFacade> _pollbookDefinedTextFacadeMock;

        private VoterNameVerificationViewModel _targetViewModel;
        private VoterDto _voter;
        private DynamicControlsDto _affidavit;

        [ClassInitialize]
        public static void ClassInitialize(TestContext testContext)
        {
            // context comment
        }

        [TestInitialize]
        public async Task Initialize()
        {
            _navigationServiceMock = new Mock<IFrameNavigationService>();
            _loggerMock = new Mock<IEssLogger>();
            _messengerMock = new Mock<IMessenger>();
            _pollbookDefinedTextFacadeMock = new Mock<IPollbookDefinedTextFacade>();
            _auditLogFacadeMock = new Mock<IAuditLogFacade>();

            _targetViewModel = new VoterNameVerificationViewModel(
                _auditLogFacadeMock.Object,
                _loggerMock.Object,
                _messengerMock.Object,
                _navigationServiceMock.Object,
                _pollbookDefinedTextFacadeMock.Object
            );

            _voter = new VoterDto()
            {
                EmailAddress = "<EMAIL>",
                FirstName = "TestFirst2",
                LastName = "TestLast2",
                HouseNumber = "1234",
                StreetName = "Main St",
                City = "Omaha",
                State = "NE",
                Zip = "68122",
                DateOfBirth = "01-01-2000",
            };

            _affidavit = new DynamicControlsDto()
            {
                Affidavit_Type_Name = "Similar Name",
                Affidavit_Type_Code = "SNA",
                Affidavit_Type_Enable_Confirmation = true,
                Affidavit_Type_Enable_Printing = false,
                Affidavit_Type_Enable_Name_Capture = true,
                Affidavit_Type_Enable_Relationship_Capture = true,
                Affidavit_Type_Enable_Address_Capture = true,
                Affidavit_Type_Enable_Compensation_Capture = true 
            };

            _auditLogFacadeMock.Setup(x => x.AddToAuditLog(It.IsAny<string>(), It.IsAny<string>()));
            _loggerMock.Setup(x => x.LogError(
	            It.IsAny<Exception>(),
	            It.IsAny<Dictionary<string, string>>(), It.IsAny<string>(), It.IsAny<string>()
            ));

            _targetViewModel.SelectedVoterHandler(new VoterSelectedMessage(_voter));

            var msg = new AffidavitCaptureDataMessage(_affidavit);
            _targetViewModel.CaptureDataHandler(msg);
        }

        [TestMethod()]
        public async Task VoterLabels_ShouldBeUpdated_OnSelectedVoterMessageReceived()
        {
            _targetViewModel.SelectedVoterHandler(new VoterSelectedMessage(_voter));

            Assert.AreEqual(_voter.FullName, _targetViewModel.VoterFullName);
            Assert.AreEqual(Convert.ToDateTime(_voter.DateOfBirth).ToString("MM/dd/yyyy"), _targetViewModel.VoterDateOfBirth);
            Assert.AreEqual(_voter.GetAddress(), _targetViewModel.VoterAddress);

            var nextVoter = new VoterDto()
            {
                EmailAddress = "<EMAIL>",
                FirstName = "Testy",
                LastName = "McUnitTest",
                HouseNumber = "567",
                StreetName = "Secondary St",
                City = "Omaha",
                State = "NE",
                Zip = "68120",
                DateOfBirth = "04-16-1987",
            };

            _targetViewModel.SelectedVoterHandler(new VoterSelectedMessage(nextVoter));

            Assert.AreEqual(nextVoter.FullName, _targetViewModel.VoterFullName);
            Assert.AreEqual(Convert.ToDateTime(nextVoter.DateOfBirth).ToString("MM/dd/yyyy"), _targetViewModel.VoterDateOfBirth);
            Assert.AreEqual(nextVoter.GetAddress(), _targetViewModel.VoterAddress);
        }

        [TestMethod()]
        public async Task OkButton_ShouldCloseModal_OnClick()
        {
            _targetViewModel.OkCommand.Execute(null);

            _messengerMock.Verify(x => x.Send(It.IsAny<AffidavitDataMessage>()), Times.Once);
            _navigationServiceMock.Verify(x => x
                    .CloseModalWindow(true), Times.Exactly(1));
        }
    }
}
