using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Facade.Party;
using ESS.Pollbook.Facade.PollbookDefinedText;
using ESS.Pollbook.ViewModel.Affidavit;
using ESS.Pollbook.Core.Messaging;
using ESS.Pollbook.ViewModel.SelectProvisionalReasonAndId;
using ESS.Pollbook.ViewModel.Utils;
using ESS.Pollbook.ViewModel.Utils.NavRouter;
using ESS.Pollbook.ViewModel.VoterDisabilityOath;
using ESS.Pollbook.ViewModel.VoterVerification;
using GalaSoft.MvvmLight.Messaging;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using System.Collections.Generic;

namespace ESS.Pollbook.ViewModel.Test.Voter
{
    [TestClass]
    public class VoterVerificationViewModelTests
    {
        private Mock<IFrameNavigationService> _navigation;
        private Mock<IMessenger> _messenger;
        private Mock<IEssLogger> _essLogger;
        private Mock<IPartyFacade> _partyService;
        private Mock<IAuditLogFacade> _auditLogFacade;
        private Mock<IPollbookDefinedTextFacade> _pollbookDefinedTextFacade;
        private Mock<IPrintBallotUtil> _printBallotUtil;
        private Mock<INavRouter> _navRouter;

        private VoterVerificationViewModel _viewModel;

        [TestInitialize]
        public void Initialize()
        {
            _navigation = new Mock<IFrameNavigationService>();
            _messenger = new Mock<IMessenger>();
            _partyService = new Mock<IPartyFacade>();
            _essLogger = new Mock<IEssLogger>();
            _pollbookDefinedTextFacade = new Mock<IPollbookDefinedTextFacade>();
            _auditLogFacade = new Mock<IAuditLogFacade>();
            _printBallotUtil = new Mock<IPrintBallotUtil>();
            _navRouter = new Mock<INavRouter>();

            SystemConfiguration.ElectionConfiguration = new PollbookConfigurationDto
            {
                VerifyNotInRoster = false
            };

            VoterDisabilityOathControls.Controls = new List<DynamicControlsDto>();
        }

        private void ViewModelSetup(bool withAffidavits = true)
        {
            VoterVerificationControls.Controls = new List<DynamicControlsDto>();

            _viewModel = new VoterVerificationViewModel(
	            _navigation.Object,
	            _messenger.Object,
	            _essLogger.Object,
	            _auditLogFacade.Object,
	            _pollbookDefinedTextFacade.Object,
	            _navRouter.Object);

            _viewModel.Voter = BuildVoter();

            _viewModel.VoterVerifications = new List<VoterVerificationDto>();

            if (withAffidavits)
            {
                _viewModel.VoterVerifications.Add(new VoterVerificationDto
                {
                    Control_ID = 1,
                    IsChecked = true
                });

                //if (_selectedDefaultAffidavits.Count > 0) -> needs to be true
                VoterVerificationControls.Controls.Add(new DynamicControlsDto
                {
                    Control_ID = 1,
                    Affidavit_Type_Code = "T",
                    Affidavit_Template_Name = "Default",
                    Affidavit_Type_Name = "VoterAssistance",
                    Container_Name = "VoterVerificationGrid"
                });
            }

            // sets _viewModel._dynamicFormControls = VoterVerificationControls.Controls;
            _viewModel.VoterVerifications_Reset();

            // sets _viewModel._selectedDefaultAffidavits
            _viewModel.BuildAffidavitsList();
        }

        private VoterDto BuildVoter()
        {
            var voter = new VoterDto
            {
                IsPreviouslyVoted = false
            };
            return voter;
        }

        [TestMethod]
        public void CapturedBallotNumberVisibility_ShouldBeTrue_WhenIssuingStandardBallot()
        {
            //Assemble
            ViewModelSetup(withAffidavits: false);
            IssueBallotHelper.Voter = _viewModel.Voter;
            IssueBallotHelper.Voter.VoterBallotDto = new VoterBallotDto();
            IssueBallotHelper.Voter.VoterBallotDto.IsProvisional = false;

            //Act
            var captureBallotNumberEnabled = true;
            _viewModel.CalculateCapturedBallotNumberView(captureBallotNumberEnabled);

            Assert.IsTrue(_viewModel.CapturedBallotNumberVisibility);
        }

        [TestMethod]
        public void CapturedBallotNumberVisibility_ShouldBeFalse_WhenIssuingProvisionalBallot()
        {
            //Assemble
            ViewModelSetup(withAffidavits: false);
            IssueBallotHelper.Voter = _viewModel.Voter;
            IssueBallotHelper.Voter.VoterBallotDto = new VoterBallotDto();
            IssueBallotHelper.Voter.VoterBallotDto.IsProvisional = true;

            //Act
            var captureBallotNumberEnabled = false;
            _viewModel.CalculateCapturedBallotNumberView(captureBallotNumberEnabled);

            Assert.IsFalse(_viewModel.CapturedBallotNumberVisibility);
        }
    }
}