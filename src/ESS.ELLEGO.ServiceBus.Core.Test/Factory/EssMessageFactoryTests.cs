using System;
using ESS.ELLEGO.ServiceBus.Core.Factory;
using ESS.ELLEGO.ServiceBus.Core.Extensions;
using Microsoft.VisualStudio.TestTools.UnitTesting;

namespace ESS.ELLEGO.ServiceBus.Core.Test.Factory
{
    [TestClass()]
    public class EssMessageFactoryTests
    {
        [TestMethod]
        public void EncryptionTest()
        {
            //pass phrases to test
            string passPhrase = "KentAndSameera";

            //test encryption and decryption
            string originalText = "This is a test. This is only a test. Had this been an actual emergency, you would receive further instructions.";
            var encrypter = new EssMessageFactory();

            var encryptedValue = encrypter.Encrypt(originalText, passPhrase);
            Assert.AreNotEqual(originalText, encryptedValue);

            var decryptedValue = encrypter.Decrypt(encryptedValue, passPhrase);
            Assert.AreEqual(originalText, decryptedValue);
        }

        [TestMethod]
        public void Encrypt()
        {
            //pass phrases to test
            const string passPhrase = "KentAndSameera";
            const string originalText = "This is a test. This is only a test. Had this been an actual emergency, you would receive further instructions.";

            var essMessageFactory = new EssMessageFactory();

            // ACT
            var actual = essMessageFactory.Encrypt(originalText, passPhrase);

            //ASSERT
            Assert.AreNotEqual(originalText, actual);
        }

        [TestMethod]
        public void Decrypt()
        {
            //pass phrases to test
            const string passPhrase = "KentAndSameera";
            const string originalText = "This is a test. This is only a test. Had this been an actual emergency, you would receive further instructions.";
            const string encryptedText = "1kN/d9oHhgeNPMskQbxYeLPiAd0c6tc+70RXDjCXHBeRv27NUkhtwLZSgIbf49/+/4f9QSA6QLeos2TXsiSaGaPgLmnSN062IIB4Z1KGeg+NyODsKyElkz2kBBQ2UD69utof89wOeUlJkw6LJL4JLPqnuvGUj7E+Z9BYiiMbTLGWmlkgVSBejSThCg8TicM=";

            //ACT
            var essMessageFactory = new EssMessageFactory();
            var encryptedResult = essMessageFactory.Decrypt(encryptedText, passPhrase);

            //ASSERT
            Assert.AreEqual(encryptedResult, originalText);
        }

        [TestMethod]
        public void TestByteIsNullOrEmpty()
        {
            byte[] nullByteArray = null;
            Assert.IsTrue(nullByteArray.IsNullOrEmpty());

            byte[] emptyArray = new byte[0];
            Assert.IsTrue(emptyArray.IsNullOrEmpty());

            byte[] somethingInMe = new byte[] { 1, 2, 3, 4, 5 };
            Assert.IsFalse(somethingInMe.IsNullOrEmpty());
        }
    }
}