//using System.Collections.Generic;
//using ESS.Pollbook.Core.DTO;
//using Microsoft.VisualStudio.TestTools.UnitTesting;
//using ESS.Pollbook.Components.Business.IncrementalUpdates.CanProcessTransactionRules.Rules;

//namespace ESS.Pollbook.Components.Business.Test.IncrementalUpdates.CanProcessTransactionRules.Rules
//{
//    [TestClass()]
//    public class VoterEditRuleTests
//    {
//        [TestMethod()]
//        [Ignore]
//        public void CanProcessTransactionRule_BallotIssuedWithVoterEdit_PollPlaces_DeviceNames_Match()
//        {
//            var ballotsIssued = new Dictionary<string, PollbookTransactionDto>();

//            #region Arrange
//            var earlyVoteRule = new VoterEditRule();

//            // create issue ballot transaction
//            const string pbTxBallotIssueJson = "{ 'TransactionId' : 60, 'SessionGuid' : '', 'TransactionGuid' : '0d58852d5e824734aa6dc1b6b91f9d2f', 'ParentTransactionGuid' : '', 'SessionSequenceNumber' : '', 'SystemIdentifier' : 'OMA-40322', 'DeviceName' : 'OMA-40322', 'SerialNumber' : '6NW60J3', 'SourceKey' : 'e96c7599-7888-4257-b0d9-b9973a2264ed', 'PollingPlaceId' : 433, 'TransactionType' : 'BallotIssue', 'JSON' : {'TransactionType':3000,'PrecinctId':8,'BallotStyleId':93,'PrecinctSplitBallotStyleId':1273,'MediaTypeEnumId':144,'CardTypeEnumId':null,'ReissueReasonEnumId':null,'ProvisionalReasonEnumId':11,'LanguageEnumId':null,'IsProvisional':true,'IsEarlyVote':true,'ProvisionalBallotIdentifier':'OMA-40322105644','IsReissue':false,'HasSignature':true,'SignatureSkipReasonEnumId':null,'BallotNumber':'Clark Ward 4 District 1','IsAlternateStyle':null,'IsBallotCancellation':false,'DynamicData':{},'AcknowledgementSimilarNameInitials':'','AcknowledgementNotOnRosterInitials':'','Affidavits':null,'SurrenderAbsentee':null,'OtherTransactionType':null,'TransactionIdentifier':'0d58852d5e824734aa6dc1b6b91f9d2f','ParentTransactionIdentifier':'','SessionId':'e030aab9b2df4182802d0ba1f63f39e1','SessionSequenceNumber':0,'CreatedOnUtc':'2023-02-03T21:27:41.7608159Z','CreatedBy':'P1','Attributes':{'address_changed':'0'}}, 'Signature' : null, 'AltSignature1' : , 'AltSignature2' : , 'TransactionDate' : 638110564617600000, 'HostTransactionId' : 79, 'UploadQueueRetryAttempts' : 0, 'TransactionRecievedDate' : '2023-03-14 18:46:25' }";
//            var txBallotIssued = Converters.GetDtoFromJson(pbTxBallotIssueJson);
//            ballotsIssued.Add(txBallotIssued.SourceKey, txBallotIssued);

//            // create early vote transaction
//            const string pbTxEarlyVoteJson = "{ 'TransactionId' : 59, 'SessionGuid' : '', 'TransactionGuid' : '69ba6842af044ecca99bbca25e3e1784', 'ParentTransactionGuid' : '', 'SessionSequenceNumber' : '', 'SystemIdentifier' : 'OMA-40322', 'DeviceName' : 'OMA-40322', 'SerialNumber' : '6NW60J3', 'SourceKey' : 'e96c7599-7888-4257-b0d9-b9973a2264ed', 'PollingPlaceId' : 433, 'TransactionType' : 'VoterEdit', 'JSON' : {'TransactionType':4010,'AbsenteeStatusEnumId':38,'Address':{'AddressId':337188333632880,'HouseNumber':'123','HouseNumberFraction':null,'StreetName':'WESTFIELD AVE','City':'CLARK','IsoStateProvinceCode':'NJ','PostalCode':'07066','AdditionalPostalCode':null,'IsoCountryCode':null,'AddressFullText':'123 WESTFIELD AVE CLARK,NJ,07066 ','AddressTypeEnumId':36,'UnitTypeEnumId':null,'UnitValue':'','UnitTypeCode':null,'TypeCode':0},'AffidavitNumber':null,'BallotStyleId':null,'CountyId':1,'Comments':null,'DateOfBirth':'2000-01-01T00:00:00','DriversLicenseNumber':null,'EmailAddress':null,'FirstName':'SHLIEFERT','IdentificationRequirementStatusEnumId':22,'LanguageEnumId':50,'LastName':'DEB','MiddleName':'A','NamePrefix':null,'NameSuffix':'','PartyId':10,'PrecinctSplitId':320,'SignatureImageFileName':null,'SsnLast4':null,'VoterDeleted':false,'VoterId':337188333632880,'VoterStatusEnumId':37,'FirstNameSearch':'SHLIEFERT','LastNameSearch':'DEB','OtherTransactionType':null,'TransactionIdentifier':'69ba6842af044ecca99bbca25e3e1784','ParentTransactionIdentifier':'','SessionId':'da42f0a2a35541c0b4af25ef0ad76d1e','SessionSequenceNumber':0,'CreatedOnUtc':'2023-02-03T21:27:23.99371Z','CreatedBy':'P1','Attributes':{'MiddleName':''}}, 'Signature' : null, 'AltSignature1' : , 'AltSignature2' : , 'TransactionDate' : 638110564439930000, 'HostTransactionId' : 78, 'UploadQueueRetryAttempts' : 0, 'TransactionRecievedDate' : '2023-03-14 18:46:25' }";
//            var txEarlyVote = Converters.GetDtoFromJson(pbTxEarlyVoteJson);
//            #endregion Arrange

//            #region Act
//            var actual = earlyVoteRule.CanProcessTransactionRule(txEarlyVote, ballotsIssued);
//            #endregion Act

//            #region Assert
//            Assert.AreEqual(true, actual.IsSuccess);
//            #endregion Assert
//        }

//        [TestMethod()]
//        public void CanProcessTransactionRule_BallotIssuedWithVoterEdit_PollPlaces_MisMatch()
//        {
//            var ballotsIssued = new Dictionary<string, PollbookTransactionDto>();

//            #region Arrange
//            var earlyVoteRule = new VoterEditRule();

//            // create issue ballot transaction
//            const string pbTxBallotIssueJson = "{ 'TransactionId' : 60, 'SessionGuid' : '', 'TransactionGuid' : '0d58852d5e824734aa6dc1b6b91f9d2f', 'ParentTransactionGuid' : '', 'SessionSequenceNumber' : '', 'SystemIdentifier' : 'OMA-40322', 'DeviceName' : 'OMA-40322', 'SerialNumber' : '6NW60J3', 'SourceKey' : 'e96c7599-7888-4257-b0d9-b9973a2264ed', 'PollingPlaceId' : 433, 'TransactionType' : 'BallotIssue', 'JSON' : {'TransactionType':3000,'PrecinctId':8,'BallotStyleId':93,'PrecinctSplitBallotStyleId':1273,'MediaTypeEnumId':144,'CardTypeEnumId':null,'ReissueReasonEnumId':null,'ProvisionalReasonEnumId':11,'LanguageEnumId':null,'IsProvisional':true,'IsEarlyVote':true,'ProvisionalBallotIdentifier':'OMA-40322105644','IsReissue':false,'HasSignature':true,'SignatureSkipReasonEnumId':null,'BallotNumber':'Clark Ward 4 District 1','IsAlternateStyle':null,'IsBallotCancellation':false,'DynamicData':{},'AcknowledgementSimilarNameInitials':'','AcknowledgementNotOnRosterInitials':'','Affidavits':null,'SurrenderAbsentee':null,'OtherTransactionType':null,'TransactionIdentifier':'0d58852d5e824734aa6dc1b6b91f9d2f','ParentTransactionIdentifier':'','SessionId':'e030aab9b2df4182802d0ba1f63f39e1','SessionSequenceNumber':0,'CreatedOnUtc':'2023-02-03T21:27:41.7608159Z','CreatedBy':'P1','Attributes':{'address_changed':'0'}}, 'Signature' : null, 'AltSignature1' : , 'AltSignature2' : , 'TransactionDate' : 638110564617600000, 'HostTransactionId' : 79, 'UploadQueueRetryAttempts' : 0, 'TransactionRecievedDate' : '2023-03-14 18:46:25' }";
//            var txBallotIssued = Converters.GetDtoFromJson(pbTxBallotIssueJson);
//            ballotsIssued.Add(txBallotIssued.SourceKey, txBallotIssued);

//            // create early vote transaction
//            const string pbTxEarlyVoteJson = "{ 'TransactionId' : 59, 'SessionGuid' : '', 'TransactionGuid' : '69ba6842af044ecca99bbca25e3e1784', 'ParentTransactionGuid' : '', 'SessionSequenceNumber' : '', 'SystemIdentifier' : 'OMA-40322', 'DeviceName' : 'OMA-40322', 'SerialNumber' : '6NW60J3', 'SourceKey' : 'e96c7599-7888-4257-b0d9-b9973a2264ed', 'PollingPlaceId' : 434, 'TransactionType' : 'VoterEdit', 'JSON' : {'TransactionType':4010,'AbsenteeStatusEnumId':38,'Address':{'AddressId':337188333632880,'HouseNumber':'123','HouseNumberFraction':null,'StreetName':'WESTFIELD AVE','City':'CLARK','IsoStateProvinceCode':'NJ','PostalCode':'07066','AdditionalPostalCode':null,'IsoCountryCode':null,'AddressFullText':'123 WESTFIELD AVE CLARK,NJ,07066 ','AddressTypeEnumId':36,'UnitTypeEnumId':null,'UnitValue':'','UnitTypeCode':null,'TypeCode':0},'AffidavitNumber':null,'BallotStyleId':null,'CountyId':1,'Comments':null,'DateOfBirth':'2000-01-01T00:00:00','DriversLicenseNumber':null,'EmailAddress':null,'FirstName':'SHLIEFERT','IdentificationRequirementStatusEnumId':22,'LanguageEnumId':50,'LastName':'DEB','MiddleName':'A','NamePrefix':null,'NameSuffix':'','PartyId':10,'PrecinctSplitId':320,'SignatureImageFileName':null,'SsnLast4':null,'VoterDeleted':false,'VoterId':337188333632880,'VoterStatusEnumId':37,'FirstNameSearch':'SHLIEFERT','LastNameSearch':'DEB','OtherTransactionType':null,'TransactionIdentifier':'69ba6842af044ecca99bbca25e3e1784','ParentTransactionIdentifier':'','SessionId':'da42f0a2a35541c0b4af25ef0ad76d1e','SessionSequenceNumber':0,'CreatedOnUtc':'2023-02-03T21:27:23.99371Z','CreatedBy':'P1','Attributes':{'MiddleName':''}}, 'Signature' : null, 'AltSignature1' : , 'AltSignature2' : , 'TransactionDate' : 638110564439930000, 'HostTransactionId' : 78, 'UploadQueueRetryAttempts' : 0, 'TransactionRecievedDate' : '2023-03-14 18:46:25' }";
//            var txEarlyVote = Converters.GetDtoFromJson(pbTxEarlyVoteJson);
//            #endregion Arrange

//            #region Act
//            var actual = earlyVoteRule.CanProcessTransactionRule(txEarlyVote, ballotsIssued);
//            #endregion Act

//            #region Assert
//            Assert.AreEqual(false, actual.IsSuccess);
//            Assert.AreEqual("Vote Edit after ballot issue and Device Names OR Polling Places don't match.", actual.LocalException.Message);
//            #endregion Assert
//        }

//        [TestMethod()]
//        public void CanProcessTransactionRule_BallotIssuedWithVoterEdit_DeviceNames_MisMatch()
//        {
//            var ballotsIssued = new Dictionary<string, PollbookTransactionDto>();

//            #region Arrange
//            var earlyVoteRule = new VoterEditRule();

//            // create issue ballot transaction
//            const string pbTxBallotIssueJson = "{ 'TransactionId' : 60, 'SessionGuid' : '', 'TransactionGuid' : '0d58852d5e824734aa6dc1b6b91f9d2f', 'ParentTransactionGuid' : '', 'SessionSequenceNumber' : '', 'SystemIdentifier' : 'OMA-40322', 'DeviceName' : 'OMA-40323', 'SerialNumber' : '6NW60J3', 'SourceKey' : 'e96c7599-7888-4257-b0d9-b9973a2264ed', 'PollingPlaceId' : 433, 'TransactionType' : 'BallotIssue', 'JSON' : {'TransactionType':3000,'PrecinctId':8,'BallotStyleId':93,'PrecinctSplitBallotStyleId':1273,'MediaTypeEnumId':144,'CardTypeEnumId':null,'ReissueReasonEnumId':null,'ProvisionalReasonEnumId':11,'LanguageEnumId':null,'IsProvisional':true,'IsEarlyVote':true,'ProvisionalBallotIdentifier':'OMA-40322105644','IsReissue':false,'HasSignature':true,'SignatureSkipReasonEnumId':null,'BallotNumber':'Clark Ward 4 District 1','IsAlternateStyle':null,'IsBallotCancellation':false,'DynamicData':{},'AcknowledgementSimilarNameInitials':'','AcknowledgementNotOnRosterInitials':'','Affidavits':null,'SurrenderAbsentee':null,'OtherTransactionType':null,'TransactionIdentifier':'0d58852d5e824734aa6dc1b6b91f9d2f','ParentTransactionIdentifier':'','SessionId':'e030aab9b2df4182802d0ba1f63f39e1','SessionSequenceNumber':0,'CreatedOnUtc':'2023-02-03T21:27:41.7608159Z','CreatedBy':'P1','Attributes':{'address_changed':'0'}}, 'Signature' : null, 'AltSignature1' : , 'AltSignature2' : , 'TransactionDate' : 638110564617600000, 'HostTransactionId' : 79, 'UploadQueueRetryAttempts' : 0, 'TransactionRecievedDate' : '2023-03-14 18:46:25' }";
//            var txBallotIssued = Converters.GetDtoFromJson(pbTxBallotIssueJson);
//            ballotsIssued.Add(txBallotIssued.SourceKey, txBallotIssued);

//            // create early vote transaction
//            const string pbTxEarlyVoteJson = "{ 'TransactionId' : 59, 'SessionGuid' : '', 'TransactionGuid' : '69ba6842af044ecca99bbca25e3e1784', 'ParentTransactionGuid' : '', 'SessionSequenceNumber' : '', 'SystemIdentifier' : 'OMA-40322', 'DeviceName' : 'OMA-40322', 'SerialNumber' : '6NW60J3', 'SourceKey' : 'e96c7599-7888-4257-b0d9-b9973a2264ed', 'PollingPlaceId' : 433, 'TransactionType' : 'VoterEdit', 'JSON' : {'TransactionType':4010,'AbsenteeStatusEnumId':38,'Address':{'AddressId':337188333632880,'HouseNumber':'123','HouseNumberFraction':null,'StreetName':'WESTFIELD AVE','City':'CLARK','IsoStateProvinceCode':'NJ','PostalCode':'07066','AdditionalPostalCode':null,'IsoCountryCode':null,'AddressFullText':'123 WESTFIELD AVE CLARK,NJ,07066 ','AddressTypeEnumId':36,'UnitTypeEnumId':null,'UnitValue':'','UnitTypeCode':null,'TypeCode':0},'AffidavitNumber':null,'BallotStyleId':null,'CountyId':1,'Comments':null,'DateOfBirth':'2000-01-01T00:00:00','DriversLicenseNumber':null,'EmailAddress':null,'FirstName':'SHLIEFERT','IdentificationRequirementStatusEnumId':22,'LanguageEnumId':50,'LastName':'DEB','MiddleName':'A','NamePrefix':null,'NameSuffix':'','PartyId':10,'PrecinctSplitId':320,'SignatureImageFileName':null,'SsnLast4':null,'VoterDeleted':false,'VoterId':337188333632880,'VoterStatusEnumId':37,'FirstNameSearch':'SHLIEFERT','LastNameSearch':'DEB','OtherTransactionType':null,'TransactionIdentifier':'69ba6842af044ecca99bbca25e3e1784','ParentTransactionIdentifier':'','SessionId':'da42f0a2a35541c0b4af25ef0ad76d1e','SessionSequenceNumber':0,'CreatedOnUtc':'2023-02-03T21:27:23.99371Z','CreatedBy':'P1','Attributes':{'MiddleName':''}}, 'Signature' : null, 'AltSignature1' : , 'AltSignature2' : , 'TransactionDate' : 638110564439930000, 'HostTransactionId' : 78, 'UploadQueueRetryAttempts' : 0, 'TransactionRecievedDate' : '2023-03-14 18:46:25' }";
//            var txEarlyVote = Converters.GetDtoFromJson(pbTxEarlyVoteJson);
//            #endregion Arrange

//            #region Act
//            var actual = earlyVoteRule.CanProcessTransactionRule(txEarlyVote, ballotsIssued);
//            #endregion Act

//            #region Assert
//            Assert.AreEqual(false, actual.IsSuccess);
//            Assert.AreEqual("Vote Edit after ballot issue and Device Names OR Polling Places don't match.", actual.LocalException.Message);
//            #endregion Assert
//        }

//        [TestMethod()]
//        public void CanProcessTransactionRule_NoBallotIssued_VoterEdit()
//        {
//            var ballotsIssued = new Dictionary<string, PollbookTransactionDto>();

//            #region Arrange
//            var earlyVoteRule = new VoterEditRule();

//            // create early vote transaction
//            const string pbTxEarlyVoteJson = "{ 'TransactionId' : 59, 'SessionGuid' : '', 'TransactionGuid' : '69ba6842af044ecca99bbca25e3e1784', 'ParentTransactionGuid' : '', 'SessionSequenceNumber' : '', 'SystemIdentifier' : 'OMA-40322', 'DeviceName' : 'OMA-40322', 'SerialNumber' : '6NW60J3', 'SourceKey' : 'e96c7599-7888-4257-b0d9-b9973a2264ed', 'PollingPlaceId' : 433, 'TransactionType' : 'VoterEdit', 'JSON' : {'TransactionType':4010,'AbsenteeStatusEnumId':38,'Address':{'AddressId':337188333632880,'HouseNumber':'123','HouseNumberFraction':null,'StreetName':'WESTFIELD AVE','City':'CLARK','IsoStateProvinceCode':'NJ','PostalCode':'07066','AdditionalPostalCode':null,'IsoCountryCode':null,'AddressFullText':'123 WESTFIELD AVE CLARK,NJ,07066 ','AddressTypeEnumId':36,'UnitTypeEnumId':null,'UnitValue':'','UnitTypeCode':null,'TypeCode':0},'AffidavitNumber':null,'BallotStyleId':null,'CountyId':1,'Comments':null,'DateOfBirth':'2000-01-01T00:00:00','DriversLicenseNumber':null,'EmailAddress':null,'FirstName':'SHLIEFERT','IdentificationRequirementStatusEnumId':22,'LanguageEnumId':50,'LastName':'DEB','MiddleName':'A','NamePrefix':null,'NameSuffix':'','PartyId':10,'PrecinctSplitId':320,'SignatureImageFileName':null,'SsnLast4':null,'VoterDeleted':false,'VoterId':337188333632880,'VoterStatusEnumId':37,'FirstNameSearch':'SHLIEFERT','LastNameSearch':'DEB','OtherTransactionType':null,'TransactionIdentifier':'69ba6842af044ecca99bbca25e3e1784','ParentTransactionIdentifier':'','SessionId':'da42f0a2a35541c0b4af25ef0ad76d1e','SessionSequenceNumber':0,'CreatedOnUtc':'2023-02-03T21:27:23.99371Z','CreatedBy':'P1','Attributes':{'MiddleName':''}}, 'Signature' : null, 'AltSignature1' : , 'AltSignature2' : , 'TransactionDate' : 638110564439930000, 'HostTransactionId' : 78, 'UploadQueueRetryAttempts' : 0, 'TransactionRecievedDate' : '2023-03-14 18:46:25' }";
//            var txEarlyVote = Converters.GetDtoFromJson(pbTxEarlyVoteJson);
//            #endregion Arrange

//            #region Act
//            var actual = earlyVoteRule.CanProcessTransactionRule(txEarlyVote, ballotsIssued);
//            #endregion Act

//            #region Assert
//            Assert.AreEqual(true, actual.IsSuccess);
//            #endregion Assert
//        }

//        [TestMethod()]
//        public void CanProcessTransactionRule_BallotIssued_EarlyVote_VoterEdit_DifferentDeviceName()
//        {
//            var ballotsIssued = new Dictionary<string, PollbookTransactionDto>();

//            #region Arrange
//            var earlyVoteRule = new VoterEditRule();

//            // create issue ballot transaction
//            const string pbTxBallotIssueJson = "{ 'TransactionId' : 60, 'SessionGuid' : '', 'TransactionGuid' : '0d58852d5e824734aa6dc1b6b91f9d2f', 'ParentTransactionGuid' : '', 'SessionSequenceNumber' : '', 'SystemIdentifier' : 'OMA-40322', 'DeviceName' : 'OMA-40322', 'SerialNumber' : '6NW60J3', 'SourceKey' : 'e96c7599-7888-4257-b0d9-b9973a2264ed', 'PollingPlaceId' : 433, 'TransactionType' : 'BallotIssue', 'JSON' : {'TransactionType':3000,'PrecinctId':8,'BallotStyleId':93,'PrecinctSplitBallotStyleId':1273,'MediaTypeEnumId':144,'CardTypeEnumId':null,'ReissueReasonEnumId':null,'ProvisionalReasonEnumId':11,'LanguageEnumId':null,'IsProvisional':true,'IsEarlyVote':true,'ProvisionalBallotIdentifier':'OMA-40322105644','IsReissue':false,'HasSignature':true,'SignatureSkipReasonEnumId':null,'BallotNumber':'Clark Ward 4 District 1','IsAlternateStyle':null,'IsBallotCancellation':false,'DynamicData':{},'AcknowledgementSimilarNameInitials':'','AcknowledgementNotOnRosterInitials':'','Affidavits':null,'SurrenderAbsentee':null,'OtherTransactionType':null,'TransactionIdentifier':'0d58852d5e824734aa6dc1b6b91f9d2f','ParentTransactionIdentifier':'','SessionId':'e030aab9b2df4182802d0ba1f63f39e1','SessionSequenceNumber':0,'CreatedOnUtc':'2023-02-03T21:27:41.7608159Z','CreatedBy':'P1','Attributes':{'address_changed':'0'}}, 'Signature' : null, 'AltSignature1' : , 'AltSignature2' : , 'TransactionDate' : 638110564617600000, 'HostTransactionId' : 79, 'UploadQueueRetryAttempts' : 0, 'TransactionRecievedDate' : '2023-03-14 18:46:25' }";
//            var txBallotIssued = Converters.GetDtoFromJson(pbTxBallotIssueJson);
//            ballotsIssued.Add(txBallotIssued.SourceKey, txBallotIssued);

//            // create early vote transaction
//            const string pbTxEarlyVoteJson = "{ 'TransactionId' : 59, 'SessionGuid' : '', 'TransactionGuid' : '69ba6842af044ecca99bbca25e3e1784', 'ParentTransactionGuid' : '', 'SessionSequenceNumber' : '', 'SystemIdentifier' : 'OMA-40322', 'DeviceName' : 'OMA-99999', 'SerialNumber' : '6NW60J3', 'SourceKey' : 'e96c7599-7888-4257-b0d9-b9973a2264ed', 'PollingPlaceId' : 433, 'TransactionType' : 'VoterEdit', 'JSON' : {'TransactionType':4010,'AbsenteeStatusEnumId':38,'Address':{'AddressId':337188333632880,'HouseNumber':'123','HouseNumberFraction':null,'StreetName':'WESTFIELD AVE','City':'CLARK','IsoStateProvinceCode':'NJ','PostalCode':'07066','AdditionalPostalCode':null,'IsoCountryCode':null,'AddressFullText':'123 WESTFIELD AVE CLARK,NJ,07066 ','AddressTypeEnumId':36,'UnitTypeEnumId':null,'UnitValue':'','UnitTypeCode':null,'TypeCode':0},'AffidavitNumber':null,'BallotStyleId':null,'CountyId':1,'Comments':null,'DateOfBirth':'2000-01-01T00:00:00','DriversLicenseNumber':null,'EmailAddress':null,'FirstName':'SHLIEFERT','IdentificationRequirementStatusEnumId':22,'LanguageEnumId':50,'LastName':'DEB','MiddleName':'A','NamePrefix':null,'NameSuffix':'','PartyId':10,'PrecinctSplitId':320,'SignatureImageFileName':null,'SsnLast4':null,'VoterDeleted':false,'VoterId':337188333632880,'VoterStatusEnumId':37,'FirstNameSearch':'SHLIEFERT','LastNameSearch':'DEB','OtherTransactionType':null,'TransactionIdentifier':'69ba6842af044ecca99bbca25e3e1784','ParentTransactionIdentifier':'','SessionId':'da42f0a2a35541c0b4af25ef0ad76d1e','SessionSequenceNumber':0,'CreatedOnUtc':'2023-02-03T21:27:23.99371Z','CreatedBy':'P1','Attributes':{'MiddleName':''}}, 'Signature' : null, 'AltSignature1' : , 'AltSignature2' : , 'TransactionDate' : 638110564439930000, 'HostTransactionId' : 78, 'UploadQueueRetryAttempts' : 0, 'TransactionRecievedDate' : '2023-03-14 18:46:25' }";
//            var txEarlyVote = Converters.GetDtoFromJson(pbTxEarlyVoteJson);
//            #endregion Arrange

//            #region Act
//            var actual = earlyVoteRule.CanProcessTransactionRule(txEarlyVote, ballotsIssued);
//            #endregion Act

//            #region Assert
//            Assert.AreEqual(false, actual.IsSuccess);
//            Assert.AreEqual("Vote Edit after ballot issue and Device Names OR Polling Places don't match.", actual.LocalException.Message);
//            #endregion Assert
//        }

//        [TestMethod()]
//        public void CanProcessTransactionRule_BallotIssued_EarlyVote_VoterEdit_BadJson()
//        {
//            var ballotsIssued = new Dictionary<string, PollbookTransactionDto>();

//            #region Arrange
//            var earlyVoteRule = new VoterEditRule();

//            // create issue ballot transaction
//            const string pbTxBallotIssueJson = "{ 'TransactionId' : 60, 'SessionGuid' : '', 'TransactionGuid' : '0d58852d5e824734aa6dc1b6b91f9d2f', 'ParentTransactionGuid' : '', 'SessionSequenceNumber' : '', 'SystemIdentifier' : 'OMA-40322', 'DeviceName' : 'OMA-40322', 'SerialNumber' : '6NW60J3', 'SourceKey' : 'e96c7599-7888-4257-b0d9-b9973a2264ed', 'PollingPlaceId' : 433, 'TransactionType' : 'BallotIssue', 'JSON' : {}, 'Signature' : null, 'AltSignature1' : , 'AltSignature2' : , 'TransactionDate' : 638110564617600000, 'HostTransactionId' : 79, 'UploadQueueRetryAttempts' : 0, 'TransactionRecievedDate' : '2023-03-14 18:46:25' }";
//            var txBallotIssued = Converters.GetDtoFromJson(pbTxBallotIssueJson);
//            txBallotIssued.JSON = "#$%";
//            ballotsIssued.Add(txBallotIssued.SourceKey, txBallotIssued);

//            // create early vote transaction
//            const string pbTxEarlyVoteJson = "{ 'TransactionId' : 59, 'SessionGuid' : '', 'TransactionGuid' : '69ba6842af044ecca99bbca25e3e1784', 'ParentTransactionGuid' : '', 'SessionSequenceNumber' : '', 'SystemIdentifier' : 'OMA-40322', 'DeviceName' : 'OMA-40322', 'SerialNumber' : '6NW60J3', 'SourceKey' : 'e96c7599-7888-4257-b0d9-b9973a2264ed', 'PollingPlaceId' : 433, 'TransactionType' : 'VoterEdit', 'JSON' : {'TransactionType':4010,'AbsenteeStatusEnumId':38,'Address':{'AddressId':337188333632880,'HouseNumber':'123','HouseNumberFraction':null,'StreetName':'WESTFIELD AVE','City':'CLARK','IsoStateProvinceCode':'NJ','PostalCode':'07066','AdditionalPostalCode':null,'IsoCountryCode':null,'AddressFullText':'123 WESTFIELD AVE CLARK,NJ,07066 ','AddressTypeEnumId':36,'UnitTypeEnumId':null,'UnitValue':'','UnitTypeCode':null,'TypeCode':0},'AffidavitNumber':null,'BallotStyleId':null,'CountyId':1,'Comments':null,'DateOfBirth':'2000-01-01T00:00:00','DriversLicenseNumber':null,'EmailAddress':null,'FirstName':'SHLIEFERT','IdentificationRequirementStatusEnumId':22,'LanguageEnumId':50,'LastName':'DEB','MiddleName':'A','NamePrefix':null,'NameSuffix':'','PartyId':10,'PrecinctSplitId':320,'SignatureImageFileName':null,'SsnLast4':null,'VoterDeleted':false,'VoterId':337188333632880,'VoterStatusEnumId':37,'FirstNameSearch':'SHLIEFERT','LastNameSearch':'DEB','OtherTransactionType':null,'TransactionIdentifier':'69ba6842af044ecca99bbca25e3e1784','ParentTransactionIdentifier':'','SessionId':'da42f0a2a35541c0b4af25ef0ad76d1e','SessionSequenceNumber':0,'CreatedOnUtc':'2023-02-03T21:27:23.99371Z','CreatedBy':'P1','Attributes':{'MiddleName':''}}, 'Signature' : null, 'AltSignature1' : , 'AltSignature2' : , 'TransactionDate' : 638110564439930000, 'HostTransactionId' : 78, 'UploadQueueRetryAttempts' : 0, 'TransactionRecievedDate' : '2023-03-14 18:46:25' }";
//            var txEarlyVote = Converters.GetDtoFromJson(pbTxEarlyVoteJson);
//            #endregion Arrange

//            #region Act
//            var actual = earlyVoteRule.CanProcessTransactionRule(txEarlyVote, ballotsIssued);
//            #endregion Act

//            #region Assert
//            Assert.AreEqual(false, actual.IsSuccess);
//            #endregion Assert
//        }

//        [TestMethod()]
//        public void CanProcessTransactionRule_BallotIssued_EarlyVote_VoterEdit_NullJson()
//        {
//            var ballotsIssued = new Dictionary<string, PollbookTransactionDto>();

//            #region Arrange
//            var earlyVoteRule = new VoterEditRule();

//            // create issue ballot transaction
//            const string pbTxBallotIssueJson = "{ 'TransactionId' : 60, 'SessionGuid' : '', 'TransactionGuid' : '0d58852d5e824734aa6dc1b6b91f9d2f', 'ParentTransactionGuid' : '', 'SessionSequenceNumber' : '', 'SystemIdentifier' : 'OMA-40322', 'DeviceName' : 'OMA-40322', 'SerialNumber' : '6NW60J3', 'SourceKey' : 'e96c7599-7888-4257-b0d9-b9973a2264ed', 'PollingPlaceId' : 433, 'TransactionType' : 'BallotIssue', 'JSON' : {}, 'Signature' : null, 'AltSignature1' : , 'AltSignature2' : , 'TransactionDate' : 638110564617600000, 'HostTransactionId' : 79, 'UploadQueueRetryAttempts' : 0, 'TransactionRecievedDate' : '2023-03-14 18:46:25' }";
//            var txBallotIssued = Converters.GetDtoFromJson(pbTxBallotIssueJson);
//            txBallotIssued.JSON = "null";
//            ballotsIssued.Add(txBallotIssued.SourceKey, txBallotIssued);

//            // create early vote transaction
//            const string pbTxEarlyVoteJson = "{ 'TransactionId' : 59, 'SessionGuid' : '', 'TransactionGuid' : '69ba6842af044ecca99bbca25e3e1784', 'ParentTransactionGuid' : '', 'SessionSequenceNumber' : '', 'SystemIdentifier' : 'OMA-40322', 'DeviceName' : 'OMA-40322', 'SerialNumber' : '6NW60J3', 'SourceKey' : 'e96c7599-7888-4257-b0d9-b9973a2264ed', 'PollingPlaceId' : 433, 'TransactionType' : 'VoterEdit', 'JSON' : {'TransactionType':4010,'AbsenteeStatusEnumId':38,'Address':{'AddressId':337188333632880,'HouseNumber':'123','HouseNumberFraction':null,'StreetName':'WESTFIELD AVE','City':'CLARK','IsoStateProvinceCode':'NJ','PostalCode':'07066','AdditionalPostalCode':null,'IsoCountryCode':null,'AddressFullText':'123 WESTFIELD AVE CLARK,NJ,07066 ','AddressTypeEnumId':36,'UnitTypeEnumId':null,'UnitValue':'','UnitTypeCode':null,'TypeCode':0},'AffidavitNumber':null,'BallotStyleId':null,'CountyId':1,'Comments':null,'DateOfBirth':'2000-01-01T00:00:00','DriversLicenseNumber':null,'EmailAddress':null,'FirstName':'SHLIEFERT','IdentificationRequirementStatusEnumId':22,'LanguageEnumId':50,'LastName':'DEB','MiddleName':'A','NamePrefix':null,'NameSuffix':'','PartyId':10,'PrecinctSplitId':320,'SignatureImageFileName':null,'SsnLast4':null,'VoterDeleted':false,'VoterId':337188333632880,'VoterStatusEnumId':37,'FirstNameSearch':'SHLIEFERT','LastNameSearch':'DEB','OtherTransactionType':null,'TransactionIdentifier':'69ba6842af044ecca99bbca25e3e1784','ParentTransactionIdentifier':'','SessionId':'da42f0a2a35541c0b4af25ef0ad76d1e','SessionSequenceNumber':0,'CreatedOnUtc':'2023-02-03T21:27:23.99371Z','CreatedBy':'P1','Attributes':{'MiddleName':''}}, 'Signature' : null, 'AltSignature1' : , 'AltSignature2' : , 'TransactionDate' : 638110564439930000, 'HostTransactionId' : 78, 'UploadQueueRetryAttempts' : 0, 'TransactionRecievedDate' : '2023-03-14 18:46:25' }";
//            var txEarlyVote = Converters.GetDtoFromJson(pbTxEarlyVoteJson);
//            #endregion Arrange

//            #region Act
//            var actual = earlyVoteRule.CanProcessTransactionRule(txEarlyVote, ballotsIssued);
//            #endregion Act

//            #region Assert
//            Assert.AreEqual(false, actual.IsSuccess);
//            #endregion Assert
//        }

//      [TestMethod()]
//      public void CanProcessTransactionRule_BallotIssued_VoterEdit_IsNotEarlyVote_DeviceNames_PollingPlaces_Match()
//      {
//         var ballotsIssued = new Dictionary<string, PollbookTransactionDto>();

//         #region Arrange
//         var earlyVoteRule = new VoterEditRule();

//         // create issue ballot transaction
//         const string pbTxBallotIssueJson = "{ 'TransactionId' : 60, 'SessionGuid' : '', 'TransactionGuid' : '0d58852d5e824734aa6dc1b6b91f9d2f', 'ParentTransactionGuid' : '', 'SessionSequenceNumber' : '', 'SystemIdentifier' : 'OMA-40322', 'DeviceName' : 'OMA-40322', 'SerialNumber' : '6NW60J3', 'SourceKey' : 'e96c7599-7888-4257-b0d9-b9973a2264ed', 'PollingPlaceId' : 433, 'TransactionType' : 'BallotIssue', 'JSON' : {'TransactionType':3000,'PrecinctId':8,'BallotStyleId':93,'PrecinctSplitBallotStyleId':1273,'MediaTypeEnumId':144,'CardTypeEnumId':null,'ReissueReasonEnumId':null,'ProvisionalReasonEnumId':11,'LanguageEnumId':null,'IsProvisional':true,'IsEarlyVote':false,'ProvisionalBallotIdentifier':'OMA-40322105644','IsReissue':false,'HasSignature':true,'SignatureSkipReasonEnumId':null,'BallotNumber':'Clark Ward 4 District 1','IsAlternateStyle':null,'IsBallotCancellation':false,'DynamicData':{},'AcknowledgementSimilarNameInitials':'','AcknowledgementNotOnRosterInitials':'','Affidavits':null,'SurrenderAbsentee':null,'OtherTransactionType':null,'TransactionIdentifier':'0d58852d5e824734aa6dc1b6b91f9d2f','ParentTransactionIdentifier':'','SessionId':'e030aab9b2df4182802d0ba1f63f39e1','SessionSequenceNumber':0,'CreatedOnUtc':'2023-02-03T21:27:41.7608159Z','CreatedBy':'P1','Attributes':{'address_changed':'0'}}, 'Signature' : null, 'AltSignature1' : , 'AltSignature2' : , 'TransactionDate' : 638110564617600000, 'HostTransactionId' : 79, 'UploadQueueRetryAttempts' : 0, 'TransactionRecievedDate' : '2023-03-14 18:46:25' }";
//         var txBallotIssued = Converters.GetDtoFromJson(pbTxBallotIssueJson);
//         ballotsIssued.Add(txBallotIssued.SourceKey, txBallotIssued);

//         // create early vote transaction
//         const string pbTxEarlyVoteJson = "{ 'TransactionId' : 59, 'SessionGuid' : '', 'TransactionGuid' : '69ba6842af044ecca99bbca25e3e1784', 'ParentTransactionGuid' : '', 'SessionSequenceNumber' : '', 'SystemIdentifier' : 'OMA-40322', 'DeviceName' : 'OMA-99999', 'SerialNumber' : '6NW60J3', 'SourceKey' : 'e96c7599-7888-4257-b0d9-b9973a2264ed', 'PollingPlaceId' : 433, 'TransactionType' : 'VoterEdit', 'JSON' : {'TransactionType':4010,'AbsenteeStatusEnumId':38,'Address':{'AddressId':337188333632880,'HouseNumber':'123','HouseNumberFraction':null,'StreetName':'WESTFIELD AVE','City':'CLARK','IsoStateProvinceCode':'NJ','PostalCode':'07066','AdditionalPostalCode':null,'IsoCountryCode':null,'AddressFullText':'123 WESTFIELD AVE CLARK,NJ,07066 ','AddressTypeEnumId':36,'UnitTypeEnumId':null,'UnitValue':'','UnitTypeCode':null,'TypeCode':0},'AffidavitNumber':null,'BallotStyleId':null,'CountyId':1,'Comments':null,'DateOfBirth':'2000-01-01T00:00:00','DriversLicenseNumber':null,'EmailAddress':null,'FirstName':'SHLIEFERT','IdentificationRequirementStatusEnumId':22,'LanguageEnumId':50,'LastName':'DEB','MiddleName':'A','NamePrefix':null,'NameSuffix':'','PartyId':10,'PrecinctSplitId':320,'SignatureImageFileName':null,'SsnLast4':null,'VoterDeleted':false,'VoterId':337188333632880,'VoterStatusEnumId':37,'FirstNameSearch':'SHLIEFERT','LastNameSearch':'DEB','OtherTransactionType':null,'TransactionIdentifier':'69ba6842af044ecca99bbca25e3e1784','ParentTransactionIdentifier':'','SessionId':'da42f0a2a35541c0b4af25ef0ad76d1e','SessionSequenceNumber':0,'CreatedOnUtc':'2023-02-03T21:27:23.99371Z','CreatedBy':'P1','Attributes':{'MiddleName':''}}, 'Signature' : null, 'AltSignature1' : , 'AltSignature2' : , 'TransactionDate' : 638110564439930000, 'HostTransactionId' : 78, 'UploadQueueRetryAttempts' : 0, 'TransactionRecievedDate' : '2023-03-14 18:46:25' }";
//         var txEarlyVote = Converters.GetDtoFromJson(pbTxEarlyVoteJson);
//         #endregion Arrange

//         #region Act
//         var actual = earlyVoteRule.CanProcessTransactionRule(txEarlyVote, ballotsIssued);
//         #endregion Act

//         #region Assert
//         Assert.AreEqual(false, actual.IsSuccess);
//         #endregion Assert
//      }
//   }
//}