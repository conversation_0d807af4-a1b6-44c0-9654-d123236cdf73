using ESS.Pollbook.Core.Model;
using ESS.Pollbook.Core.Model.Transactions;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using ESS.Pollbook.Core.CustomExceptions;
using ESS.Pollbook.Core.Logging;
using ESS.ELLEGO.Rest;

namespace ESS.Pollbook.Components.Repository.ContentDeliveryNetwork
{
   /// <summary>
   /// The Context for CDN Strategy
   /// </summary>
   public class CdnContext
   {
      private ICdnStrategy _cdnStrategy;

      public CdnContext(IEssRestExecutor essRestResponseFactory, CDNDownloadFileRequest cdnFileRequest, IEssLogger essLogger)
      {
          essLogger.LogDebug($"CdnContext - request values: Url:[{cdnFileRequest.Url}] Path:[{cdnFileRequest.DestinationPath}]");

         if (string.IsNullOrWhiteSpace(cdnFileRequest.DestinationPath)) return;

         var cdnFile = new FileInfo(cdnFileRequest.DestinationPath);
         switch (cdnFile.Extension)
         {
            case ".json":
                essLogger.LogDebug("Extension = .json");

               SetStrategy(new CdnJsonStrategy(essRestResponseFactory, cdnFileRequest));
               break;
            case ".zip":
                essLogger.LogDebug("Extension = .zip");

               SetStrategy(new CdnZipStrategy(cdnFileRequest));
               break;

            default:
               throw new CDNException("Extension not usable");
         }
      }

      private void SetStrategy(ICdnStrategy cdnStrategy)
      {
         _cdnStrategy = cdnStrategy;
      }

      public async Task<List<TransactionModel>> GetTransactionsAsync()
      {
         return await _cdnStrategy.GetCdnTransactionsAsync<List<TransactionModel>>();
      }
   }
}
