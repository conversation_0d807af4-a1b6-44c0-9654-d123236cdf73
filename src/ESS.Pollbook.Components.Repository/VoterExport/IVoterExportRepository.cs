using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Model;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ESS.Pollbook.Components.Repository.VoterExport
{
    public interface IVoterExportRepository
    {
        Task<bool> ExportVotedListAsync(string systemId, List<VotedListItemDto> votedList, bool filteredList);

        Task<bool> ExportPollingPlaceVoterListAsync(string systemId, List<PollingPlaceVoterListItemDto> voterList);

        Task<bool> ExportBallotReissueListAsync(string systemId, BallotReissuedListCount ballotReissuedListCount);

        Task<bool> ExportSpoiledBallotReportAsync(string systemId, Dictionary<string, int> counts);
    }
}
