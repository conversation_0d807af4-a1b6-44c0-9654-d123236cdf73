using Autofac.Features.Indexed;
using ESS.Pollbook.DataAccess.Interfaces;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.UICore;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ESS.Pollbook.Components.Repository.BallotCancellationStateTransition
{
    public class BallotCancellationStateTransitionRepository : IBallotCancellationStateTransitionRepository
    {
        private readonly IEssSqliteReaderAdapter _essSqliteReaderPollDataAdapter;

        public BallotCancellationStateTransitionRepository(IIndex<SqliteDatabaseType, IEssSqliteReaderAdapter> adapters)
        {
            _essSqliteReaderPollDataAdapter = adapters[SqliteDatabaseType.Polldata];
        }

        public async Task<IEnumerable<ElectionEnumerationStateChange>> GetEnumerationStateChangesAsync()
        {
            string sql = @"SELECT State_Change_ID AS MappingId,
                                  Pre_Jurisdiction_Enumeration_Value_ID as PreVoterJurisdictionEnumerationValueId,
                                  Post_Jurisdiction_Enumeration_Value_ID as PostVoterJurisdictionEnumerationValueId
                          FROM Election_Enumeration_State_Change;";

            var results = await _essSqliteReaderPollDataAdapter.QueryAsync<ElectionEnumerationStateChange>(sql);
            return results;
        }
    }
}
