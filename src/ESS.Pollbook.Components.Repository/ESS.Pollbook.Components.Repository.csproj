<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
	  <TargetFramework>net48</TargetFramework>
	  <Authors>Election Systems and Software</Authors>
	  <Company>Election Systems and Software</Company>
	  <Copyright>Election Systems and Software</Copyright>
	  <AssemblyVersion>7.2.9.0</AssemblyVersion>
	  <FileVersion>7.2.9.0</FileVersion>
	  <CodeAnalysisRuleSet>..\.sonarlint\express-poll-branchescsharp.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="RequestResponse\**" />
    <Compile Remove="SmartCard\**" />
    <EmbeddedResource Remove="RequestResponse\**" />
    <EmbeddedResource Remove="SmartCard\**" />
    <None Remove="RequestResponse\**" />
    <None Remove="SmartCard\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Autofac" Version="5.2.0" />
    <PackageReference Include="ManagedNativeWifi" Version="2.7.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ESS.ELLEGO.Rest\ESS.ELLEGO.Rest.csproj" />
    <ProjectReference Include="..\ESS.ELLEGO.ServiceBus.Core\ESS.ELLEGO.ServiceBus.Core.csproj" />
    <ProjectReference Include="..\ESS.Integration.SmartCardService\ESS.Integration.SmartCardService.csproj" />
    <ProjectReference Include="..\ESS.Pollbook.Core\ESS.Pollbook.Core.csproj" />
    <ProjectReference Include="..\ESS.Pollbook.DataAccess\ESS.Pollbook.DataAccess.csproj" />
    <ProjectReference Include="..\ESS.Pollbook.Hardware\ESS.Pollbook.Hardware.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="System.Configuration" />
    <Reference Include="System.Runtime.Caching" />
  </ItemGroup>
</Project>