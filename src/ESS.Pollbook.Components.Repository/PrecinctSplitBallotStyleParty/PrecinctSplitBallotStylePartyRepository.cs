using Autofac.Features.Indexed;
using ESS.Pollbook.DataAccess.Interfaces;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.UICore;
using System.Threading.Tasks;

namespace ESS.Pollbook.Components.Repository.PrecinctSplitBallotStyleParty
{
    public class PrecinctSplitBallotStylePartyRepository : IPrecinctSplitBallotStylePartyRepository
    {
        private readonly IEssSqliteReaderAdapter _essSqliteReaderPolldataAdapter;

        public PrecinctSplitBallotStylePartyRepository(IIndex<SqliteDatabaseType, IEssSqliteReaderAdapter> adapters)
        {
            _essSqliteReaderPolldataAdapter = adapters[SqliteDatabaseType.Polldata];
        }

        public async Task<PrecinctSplitBallotStylePartyDto> GetPrecinctSplitBallotStylePartyDetails(int voterPrecinctSplitId, int voterPartyId)
        {
            const string sql = @"SELECT  eps.Precinct_Split_ID as PrecinctSplitId,
                                eps.Precinct_Split_Source_Precinct_Split_Key as PrecinctSplitSourcePrecinctSplitKey,
                                eps.Precinct_Split_Name as PrecinctSplitName,
                                ebs.Ballot_Style_ID as BallotStyleId,
                                ebs.Ballot_Style_Source_Ballot_Style_Key as BallotStyleSourceBallotStyleKey,
                                ebs.Ballot_Style_Long_Description_Text as BallotStyleLongDescriptionText,
                                ebs.Ballot_Style_Short_Description_text as BallotStyleShortDescriptionText,
                                ebs.Ballot_Style_Code as BallotStyleCode,
                                ep.Party_ID as PartyId,
                                ep.Party_Name as PartyName,
                                ep.Party_Source_Party_key as PartySourcePartykey,
                                ep.Party_Display_Name as PartyDisplayName,
                                epsbs.Precinct_Split_Ballot_Style_ID as PrecinctSplitBallotStyleID
                        FROM Election_Precinct_Split eps
                        INNER JOIN Election_Precinct_Split_Ballot_Style epsbs ON eps.Precinct_Split_ID = epsbs.Precinct_Split_ID
                        INNER JOIN Election_Ballot_Style ebs ON ebs.Ballot_Style_ID = epsbs.Ballot_Style_ID
                        INNER JOIN Election_Party ep ON ep.Party_ID = ebs.Ballot_Style_Party_ID
                        WHERE eps.Precinct_Split_ID = @precinctSplitId and ep.Party_ID = @partyId ";

            return await _essSqliteReaderPolldataAdapter.QueryFirstOrDefaultAsync<PrecinctSplitBallotStylePartyDto>(sql, new
            {
                precinctSplitId = voterPrecinctSplitId,
                partyId = voterPartyId
            });
        }

        public async Task<PrecinctSplitBallotStylePartyDto> GetPrecinctSplitDisplayNameByBallotStyleIdAndPrecinctSplitBallotStyleId(int ballotStyleId, int precinctSplitBallotStyleId)
        {
            const string sql = @"SELECT  eps.Precinct_Split_Display_Name as PrecinctSplitName
                        FROM Election_Precinct_Split eps
                        INNER JOIN Election_Precinct_Split_Ballot_Style epsbs ON eps.Precinct_Split_ID = epsbs.Precinct_Split_ID
                        INNER JOIN Election_Ballot_Style ebs ON ebs.Ballot_Style_ID = epsbs.Ballot_Style_ID
                        WHERE epsbs.Ballot_Style_ID = @BallotStyleId and epsbs.Precinct_Split_Ballot_Style_ID = @PrecinctSplitBallotStyleId ";

            return await _essSqliteReaderPolldataAdapter.QueryFirstOrDefaultAsync<PrecinctSplitBallotStylePartyDto>(sql, new
            {
                BallotStyleId = ballotStyleId,
                PrecinctSplitBallotStyleId = precinctSplitBallotStyleId
            });
        }
    }
}
