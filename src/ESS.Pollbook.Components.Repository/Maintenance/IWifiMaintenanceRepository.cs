using ESS.Pollbook.Core.Model;
using ManagedNativeWifi;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace ESS.Pollbook.Components.Repository.Maintenance
{
    public interface IWifiMaintenanceRepository
    {
        event EventHandler NetworkRefreshed;
        event EventHandler<AvailabilityChangedEventArgs> AvailabilityChanged;
        event EventHandler<InterfaceChangedEventArgs> InterfaceChanged;
        event EventHandler<ProfileChangedEventArgs> ProfileChanged;
        event EventHandler<ConnectionChangedEventArgs> ConnectionChanged;


        List<InterfaceInfo> GetInterfaces();

        Task ScanNetworkAsync(TimeSpan timeout);
        Task<IEnumerable<NativeWifiProfileItem>> GetProfilesAsync();
        Task<IEnumerable<RadioItem>> GetRadiosAsync();

        bool SetProfile(ProfileItem profileItem);
        bool DeleteProfile(ProfileItem profileItem);
        Task<bool> ConnectNetworkAsync(ProfileItem profileItem, TimeSpan timeSpan, CancellationToken token);
        Task<bool> ConnectNetworkAsync(Guid interfaceId, string name, BssType bssType, TimeSpan timeSpan, CancellationToken token);
        bool ConnectNetwork(ProfileItem profileItem);
        bool DisconnectNetwork(ProfileItem profileItem);
        bool DisconnectNetwork(Guid interfaceId);
    }
}
