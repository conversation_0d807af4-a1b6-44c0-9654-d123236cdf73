using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Model.Transactions;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ESS.Pollbook.Components.Repository.SystemStats
{
    public interface ISystemStatsRepository
    {
        Task<IncrementalUpdateRequest> GetLastUpdatedDateTimeByTableName(string tableName);
        Task<IncrementalUpdateRequest> GetLastUpdatedDateTimeBySignaturesTableName(string tableName);
        Task<IEnumerable<EnumertaionCountsDto>> GetEnumerationCounts(int? pollingPlaceId);
        Task<int> GetRegisteredCountyVoters(string countyId);
        Task<int> GetRegisteredVoters(string pollingPlaceId);
        Task<int> GetNewVoters(string pollingPlaceId);
        Task<IEnumerable<JurisdictionEnumDto>> GetEnumInfo();
    }
}
