using ESS.Pollbook.Core.DTO;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ESS.Pollbook.Components.Repository.ElectionJurisdictionEnum
{
    public interface IVoterJurisdictionEnumRepository
    {
        Task<IEnumerable<ElectionJurisdictionEnumValueDto>> GetVoterAbsenteeOrVoterStatusResolvedJurisEnumValueIdAsync(int enumerationId, int enumerationValueNumber);

        Task<IEnumerable<ElectionJurisdictionEnumValueDto>> GetEnumValueIdAsync(int enumerationId, int enumerationValueNumber);

        Task<int> GetEnumValueFromCodeAsync(int EnumId, string Code);

        Task<IEnumerable<ElectionJurisdictionEnumValueDto>> GetEnumerationDetailsByStatusesAsync(int enumerationId);

        Task<IEnumerable<ElectionJurisdictionEnumValueDto>> GetEnumValueNameAsync(int enumerationId, int enumerationValueId);

        Task<IEnumerable<ElectionJurisdictionEnumValueDto>> GetCommonEnumsAsync(int enumerationId);

        Task<IEnumerable<ElectionJurisdictionEnumValueDto>> GetElectionJurisdictionEnumValueAsync();

        Task<int> GetEarlyBallotIssuedIdAsync(int enumerationId, int enumerationValueNumber);

        Task<IEnumerable<ElectionJurisdictionEnumValueDto>> JurisdictionToCommonEnumLookupAsync(int enumerationId, int enumerationValueNumber, int jurisdictionEnumerationValueID);

        Task <IEnumerable<ElectionJurisdictionEnumValueDto>> GetAddressUnitTypesAsync();
        Task<IEnumerable<ElectionJurisdictionEnumValueDto>> GetGendersAsync();

        Task<IEnumerable<ElectionJurisdictionEnumValueDto>> GetBallotStyleTypesAsync();
        Task<IEnumerable<ElectionJurisdictionEnumValueDto>> GetBallotStyleTypesByPrecinctAndPartyAsync(int precinctSplitId, int voterPartyId);
    }
}
