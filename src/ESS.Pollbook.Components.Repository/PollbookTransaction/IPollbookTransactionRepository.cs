using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Model.Transactions;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ESS.Pollbook.Components.Repository.PollbookTransaction
{
    public interface IPollbookTransactionRepository : IDisposable
    {
        LocalResponse InsertTransaction(PollbookTransactionDto transaction);

        Task<VoterDto> GetVoterSignatureFromTransactionAsync(string transactionGuid);

        Task<bool> BackupDatabaseIsAttachedAsync();

        Task<int> GetBallotsIssuedCountByPollPlaceAsync(int pollingPlaceId);

        Task<int> GetLocalBallotsIssuedCountByPollPlaceAsync(int pollingPlaceId);

        Task<int> GetBallotsReIssuedCountAsync(int pollingPlaceId);
        Task<IDictionary<int, int>> GetBallotReIssuedCountBreakdownAsync(int pollingPlaceId);

        Task<int> GetLocalBallotsReIssuedCountAsync(int pollingPlaceId);

        Task<PollbookTransactionDto> GetElectionBallotTransactionsByVoterKeyAsync(string voterKey);

        Task<int> GetBallotsCanceledCountAsync(int pollingPlaceId);

        Task<int> GetLocalBallotsCanceledCountAsync(int pollingPlaceId);

        Task<IEnumerable<PollbookTransactionDto>> GetLogInTransactions();

        Task<bool> GetIsPollOpenAsync();

        Task<IEnumerable<PollbookTransactionDto>> GetEarlyVoteBallotTransactionsByCurrentPollPlace();

        Task<IEnumerable<PollbookTransactionDto>> GetVoterBallotsByPollPlaceAndTransactionType(int pollPlaceId);

        Task<int> GetLastTransactionIdAsync(string guid);

        string GetLastTransactionGuid();

        LocalResponse UpdateHostTransactionId(string transactionGuid, long hostTransactionId);

        Task<UserDto> GetFirstLogInUserNameFromTransactions();

        Task<IEnumerable<PollbookTransactionDto>> GetAllTransactionsForVoterAsync(string voterKey);

        Task<IEnumerable<PollbookTransactionDto>> GetCalculationTransactionsByVoterKeyAsync(string voterKey);

        Task<PollbookTransactionDto> GetMaxHostTransactionIdAsync(string systemId);

        LocalResponse BulkUpsertHostTransactions(List<PollbookTransactionDto> hostTransactionDtos);

        Task<IEnumerable<PollbookTransactionDto>> GetPendingLocalTransactionsToUploadAsync();

        Task EnsureSecondaryTransactionLogMatches(string encryptionKey);

        Task<bool> AttachBackupTransactionLog(string encryptionKey);

        Task DetachBackupTransactionLog();

        LocalResponse BulkInsertReconciliationTransactions(List<ResponseTransactionDto> transactionList);

        Task<List<PollbookTransactionDto>> GetPollbookTransactions(IEnumerable<string> guids);

        Task<List<PollbookTransactionDto>> GetPollbookEditTransactions(IEnumerable<string> guids);

        Task<List<PollbookTransactionDto>> GetPollbookAddTransactions(IEnumerable<string> guids);

        Task<List<PollbookTransactionDto>> GetPollbookStatusTransactions(IEnumerable<string> guids);

        Task<WaitTimeDto> GetLastWaitTimeTransaction();

        Task<int?> GetLastWaitTimeMinutes();

        Task InitializeTransactionGuids();

        HashSet<string> GetTransactionGuids();

        HashSet<string> GetEditTransactionGuids();

        HashSet<string> GetAddTransactionGuids();

        HashSet<string> GetStatusTransactionGuids();

        Task ForceManualWalCommandAsync();

        Task<DeviceCountsDto> GetDeviceBallotsIssuedCountsAsync(string deviceName);

        Task<IEnumerable<ExpressVoteSerialNumberDto>> GetSerialNumbers();

        void AddTransactionGuidToSet(string transactionType, string transactionGuid);

        bool IsWriterTransactionConnectionAvailable();

        void InsertConfigurationUpdateTransaction(string json, DateTime updateDateTime);

        Task<DateTime> GetLatestConfigurationAppliedDateTime();

        void ShareWithPeers(PollbookTransactionDto transaction);

        Task<long> GetPollbookTransactionsCountAsync();

        Task<long> GetHostTransactionsCountAsync();

        bool WriteInsertTransactionToQueue(LocalResponse response, PollbookTransactionDto transaction);

        Task<IEnumerable<TransactionProgressDTO>> GetTransactionDownloadProgress();

        Task<long> GetPollbookTransactionCountByTypeAsync(string transactionType);

        Task<long> GetPendingVoterTransactionCountsAsync();

        Task<IEnumerable<PollbookTransactionDto>> GetVoterTransactionsForProcessing();

        void BulkUpdateStatusForTransactionsById(IEnumerable<long> ids, ProcessingStatus status);

        void BulkUpdateStatusForTransactionsByGuid(IEnumerable<string> guids, ProcessingStatus status);

        void UpdateStatusForTransaction(string guid, ProcessingStatus status);

        Task<IEnumerable<PollbookTransactionDto>> GetTransactionGuidHostTransactionIdList();

        //void UpdateRecords();

        Task BuildVoterBallotList();

        Dictionary<string, BallotDto> VotersWithBallots { get; set; }
    }
}