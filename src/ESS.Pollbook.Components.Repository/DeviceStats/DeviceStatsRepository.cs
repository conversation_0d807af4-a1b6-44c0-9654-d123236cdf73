using ESS.ELLEGO.Rest;
using ESS.Pollbook.Core.Interface;
using System.Net.Http;
using System.Threading.Tasks;

namespace ESS.Pollbook.Components.Repository.DeviceStats
{
    public class DeviceStatsRepository : IDeviceStatsRepository
    {
	    private readonly ISystemManagement _systemManagement;
	    private readonly IEssRestExecutor _essRestHelper;

	    #region Constructor(s)

	    public DeviceStatsRepository(ISystemManagement systemManagement, IEssRestExecutor essRestHelper)
	    {
            _systemManagement = systemManagement;
            _essRestHelper = essRestHelper;
        }

        #endregion

        #region Public Methods
        /// <summary>
        /// Getting battery life information from System.Management.dll wrapper.
        /// </summary>
        /// <returns></returns>
        public float GetBatteryPercentRemaining()
        {
            return _systemManagement.GetBatteryPercentRemaining();
        }
        
        public string GetBatteryRemainingTime()
        {
            return _systemManagement.GetBatteryRemainingTime();
        }

        public async Task<HttpResponseMessage> GetHostConnectedStatus(EssRestRequest request)
        {
	        return await _essRestHelper.PostAsync<HttpResponseMessage>(request);
        }

        public bool GetIsBatteryCharging()
        {
            BatteryStatusEnum status = _systemManagement.GetBatteryStatus();

            return (status & BatteryStatusEnum.AnyCharging) > 0;
        }
        #endregion

        #region Private Methods


        #endregion
    }
}
