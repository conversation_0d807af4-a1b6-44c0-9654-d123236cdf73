using Autofac.Features.Indexed;
using ESS.Pollbook.DataAccess.Interfaces;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.UICore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace ESS.Pollbook.Components.Repository.Languages
{
    public class ConfigUpdateLanguagesRepository : IConfigUpdateLanguagesRepository
    {
        private readonly IEssSqliteReaderAdapter _essSqliteReaderPolldataAdapter;
        private readonly IEssLogger _essLogger;

        public ConfigUpdateLanguagesRepository(IIndex<SqliteDatabaseType, IEssSqliteReaderAdapter> readerAdapters, IEssLogger essLogger)
        {
            _essSqliteReaderPolldataAdapter = readerAdapters[SqliteDatabaseType.Polldata];
            _essLogger = essLogger;
        }

        public async Task<IEnumerable<ConfigUpdateLanguageDto>> GetAllLanguagesAsync()
        {
            try
            {
                const string sql = @"SELECT Pollbook_Defined_Text_Language as PollbookDefinedTextLanguage
                                   FROM ConfigUpdate_Languages;";

                return await _essSqliteReaderPolldataAdapter.QueryAsync<ConfigUpdateLanguageDto>(sql);
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string> { { "Action", "ConfigUpdateLanguagesRepository.GetAllLanguagesAsync" } };
                _essLogger.LogError(ex, logProps);
            }

            return Enumerable.Empty<ConfigUpdateLanguageDto>();
        }
    }
}
