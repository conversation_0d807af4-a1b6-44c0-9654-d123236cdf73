using Autofac.Features.Indexed;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.UICore;
using ESS.Pollbook.DataAccess.Interfaces;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ESS.Pollbook.Components.Repository.VoterBallot
{
    public class VoterBallotRepository : IVoterBallotRepository
    {
        #region Private Members

        private readonly IEssSqliteReaderAdapter _essSqliteReaderPolldataAdapter;

        #endregion

        #region Constructor(s)

        public VoterBallotRepository(IIndex<SqliteDatabaseType, IEssSqliteReaderAdapter> readerAdapters)
        {
            _essSqliteReaderPolldataAdapter = readerAdapters[SqliteDatabaseType.Polldata];
        }

        #endregion

        #region Public Methods

        public async Task<IEnumerable<PartyAlternateBallotDto>> GetPartyAlternateBallotsDtos()
        {
           const string sql = @"SELECT Party_Alternate_Ballot_ID AS PartyAlternateBallotId,
                                        Voter_Party_ID AS VoterPartyId,
                                        Ballot_Party_ID AS BallotPartyId,
                                        Party_Affiliation_Affidavit AS PartyAffiliationAffidavit
                                 FROM   Election_Party_Alternate_Ballot epab
                                 JOIN   Election_Party ep1 ON  epab.Voter_Party_ID=ep1.Party_ID
                                 JOIN   Election_Party ep2 ON epab.Ballot_Party_ID=ep2.Party_ID
                                 ORDER BY ep1.Party_Display_Order_Number, ep2.Party_Display_Order_Number";
           var result = await _essSqliteReaderPolldataAdapter.QueryAsync<PartyAlternateBallotDto>(sql);

            return result;
        }

        /// <summary>
        ///     This method returns a collection to populate the cache
        /// </summary>
        /// <param name="pollPlaceId"></param>
        /// <returns></returns>
        public async Task<IEnumerable<VoterBallotDto>> GetVoterBallotStyleByPollPlaceId(int pollPlaceId)
        {
            //const string
            const string sql = @"SELECT ebs.Ballot_Style_ID AS BallotStyleID,
	                                      p.Precinct_ID AS PrecinctId,
                                          p.Precinct_Name As PrecinctName,
	                                     ps.Precinct_Split_ID AS PrecinctSplitId,
                                            ps.Precinct_Split_Name AS PrecinctSplitName,
                                            ps.Precinct_Split_Precinct_Id As PrecinctSplitPrecinctId,
                                            ps.Precinct_Split_Display_Name AS PrecinctSplitDisplayName,
	                                   esbs.Precinct_Split_Ballot_Style_ID AS PrecinctSplitBallotStyleId,
	                                    ebs.Ballot_Style_Source_Ballot_Style_KEY AS BallotStyleKey,
	                                    ebs.Ballot_Style_Code AS BallotStyleCode,
	                                    ebs.Ballot_Style_Short_Description_Text AS BallotStyleDescription,
	                                    ebs.Ballot_Style_PDF_FileName AS BallotStylePDFFileName,
	                                    ebs.Ballot_Style_Type_Jurisdiction_Enumeration_Value_Id AS BallotStyleTypeJurisdictionEnumerationValueId,
	                                    jev.Jurisdiction_Enumeration_Value_Name AS JurisdictionEnumerationValueName,
                                         ep.Party_Id AS PartyId,

                                         --EP.Party_ID AS PartyId, 
                                         EP.Party_Source_Party_Key AS PartySourcePartyKey, 
                                         EP.Party_Name AS PartyName, 
                                         EP.Party_Display_Name AS PartyDisplayName, 
                                         EP.Party_Display_Order_Number AS PartyDisplayOrderNumber


                                FROM 		Election_Polling_Place_Precinct PPP
                                INNER JOIN 	Election_Polling_Place PP
		                                ON  PPP.Polling_Place_ID = PP.Polling_Place_ID AND PP.Polling_PLace_ID = @pollPlaceId
                                INNER JOIN  Election_Precinct P 
		                                ON  P.Precinct_ID = PPP.Precinct_ID 
                                INNER JOIN  Election_Precinct_Split PS
		                                ON  ps.Precinct_Split_Precinct_ID = p.Precinct_ID
                                INNER JOIN  Election_Precinct_Split_Ballot_Style esbs
    	                                ON  esbs.Precinct_Split_ID = ps.Precinct_Split_ID
                                INNER JOIN  Election_Ballot_Style ebs
    	                                ON  ebs.Ballot_Style_ID = esbs.Ballot_Style_ID
                                INNER JOIN  Election_Party ep
    	                                ON  ep.Party_ID = ebs.Ballot_Style_Party_ID
                                INNER JOIN  Election_Jurisdiction_Enumeration_Value jev
    	                                ON  ebs.Ballot_Style_Type_Jurisdiction_Enumeration_Value_Id = jev.Jurisdiction_Enumeration_Value_Id";

            var result =
                await _essSqliteReaderPolldataAdapter.QueryAsync<VoterBallotDto>(sql, new { pollPlaceId });

            return result;
        }

        // Keep this query. VoterBallotDto Cached records do NOT contain a voter id
        public async Task<VoterBallotDto> GetVoterBallotStyleByVoterId(long? voterId, int ballotStylePartyId)
        {
            const string sql = @"SELECT BS.Ballot_Style_ID AS BallotStyleID,
                                PBS.Precinct_Split_Ballot_Style_ID as PrecinctSplitBallotStyleId,
                                 V.Voter_ID,
                                 BS.Ballot_Style_Source_Ballot_Style_Key AS BallotStyleKey,
                                 BS.Ballot_Style_Code AS BallotStyleCode,
                                 BS.Ballot_Style_Short_Description_Text AS BallotStyleDescription,
                                 BS.Ballot_Style_PDF_FileName AS BallotStylePDFFileName
                          FROM Election_Ballot_Style AS BS
                            INNER JOIN Election_Party_Alternate_Ballot AS PAB
                                ON BS.Ballot_Style_Party_ID = PAB.Ballot_Party_ID
                            INNER JOIN Election_Voter AS v
                                ON V.Voter_Party_ID = PAB.Voter_Party_ID
                            INNER JOIN Election_Precinct_Split_Ballot_Style AS PBS
                                ON BS.Ballot_Style_ID = PBS.Ballot_Style_ID 
                                AND V.Voter_Precinct_Split_ID = PBS.Precinct_Split_ID
                            WHERE v.Voter_ID = @VoterId AND BS.Ballot_Style_Party_ID = @Ballot_Style_Party_ID;";

            var result = await _essSqliteReaderPolldataAdapter.QueryFirstOrDefaultAsync<VoterBallotDto>(sql,
                new { VoterId = voterId, Ballot_Style_Party_ID = ballotStylePartyId });
            return result;
        }

        public async Task<bool> ValidateAllBallotStylesHavePdfFilename()
        {
            const string sql = @"SELECT COUNT(*)
                    FROM Election_Ballot_Style 
                    WHERE Ballot_Style_PDF_FileName is NULL or Ballot_Style_PDF_FileName=''";
            return await _essSqliteReaderPolldataAdapter.ExecuteScalarAsync<int>(sql) == 0;
        }

        public async Task<bool> ValidateAllBallotStyleCodes()
        {
            const string sql = @"SELECT COUNT(*)
                    FROM Election_Ballot_Style 
                    WHERE Ballot_Style_Code IS NULL or Ballot_Style_Code=''";
            return await _essSqliteReaderPolldataAdapter.ExecuteScalarAsync<int>(sql) == 0;
        }

        #endregion
    }
}