using Autofac.Features.Indexed;
using ESS.Pollbook.DataAccess.Interfaces;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.UICore;
using System.Linq;
using System.Threading.Tasks;

namespace ESS.Pollbook.Components.Repository.VoterDetails
{
    public class VoterDetailsRepository : IVoterDetailsRepository
    {
        private readonly IEssSqliteReaderAdapter _essSqliteReaderPolldataAdapter;

  public VoterDetailsRepository(IIndex<SqliteDatabaseType, IEssSqliteReaderAdapter> readerAdapters)
        {
            _essSqliteReaderPolldataAdapter = readerAdapters[SqliteDatabaseType.Polldata];
        }

        public async Task<VoterDetailsDto> GetVoterDetailsByVoterIdAsync(long? id)
        {
            const string sql = @"WITH BallotStyle AS (
                                              SELECT ebs.Ballot_Style_Short_Description_Text, 
                                                     v.Voter_Party_ID
                                               FROM Election_Voter v
                                               INNER JOIN Election_Party_Alternate_Ballot PAB
                                                ON v.Voter_Party_ID = PAB.Voter_Party_ID
                                               INNER JOIN Election_Party p
                                                ON p.Party_ID =  PAB.Ballot_Party_ID
                                               INNER JOIN Election_Ballot_Style ebs
                                                ON PAB.ballot_party_id  = ebs.Ballot_Style_Party_ID
                                                AND v.Voter_Ballot_Style_Type_Jurisdiction_Enumeration_Value_ID = ebs.Ballot_Style_Type_Jurisdiction_Enumeration_Value_Id 
                                               INNER JOIN Election_Precinct_Split_Ballot_Style esbs
                                                ON ebs.Ballot_Style_ID = esbs.Ballot_Style_ID
                                                AND esbs.Precinct_Split_ID = v.Voter_Precinct_Split_ID
                                               WHERE v.Voter_Id = @VoterID
                                             )
                        SELECT ps.Precinct_Split_Display_Name as SplitName,
                               bs.Ballot_Style_Short_Description_Text AS BallotStyleDescription,
                               p.Party_Display_Name AS PartyName
                        FROM Election_Voter v
                        INNER JOIN Election_Precinct_Split ps
                         ON v.Voter_Precinct_Split_ID = ps.Precinct_Split_ID
                        INNER JOIN Election_Party p
                         ON p.Party_ID = v.Voter_Party_ID
                        LEFT JOIN BallotStyle bs
                         ON bs.Voter_Party_ID = v.Voter_Party_ID
                        WHERE v.Voter_Id = @VoterID";

            var result = await _essSqliteReaderPolldataAdapter.QueryAsync<VoterDetailsDto>(sql, new { VoterID = id });

            if (result.Count() > 1)
            {
                var finalVoterDetailsResult = result.FirstOrDefault();
                finalVoterDetailsResult.hasMultipleBallotStyles = true;
                return finalVoterDetailsResult;
            }
            return result.FirstOrDefault();
        }

        public async Task<VoterDetailsDto> GetVoterDetailsByVoterIdEditedPrecinctSplitAsync(long? id, int precinctSplitId)
        {
            const string sql = @"WITH BallotStyle AS (
                                                SELECT ebs.Ballot_Style_Short_Description_Text,
                                                       v.Voter_Party_ID
                                                FROM Election_Voter v
                                                INNER JOIN Election_Party_Alternate_Ballot PAB 
                                                 ON v.Voter_Party_ID = PAB.Voter_Party_ID
                                                INNER JOIN Election_Party p 
                                                 ON p.Party_ID =  PAB.Ballot_Party_ID
                                                INNER JOIN Election_Ballot_Style ebs 
                                                 ON PAB.ballot_party_id  = ebs.Ballot_Style_Party_ID
                                                 AND v.Voter_Ballot_Style_Type_Jurisdiction_Enumeration_Value_ID = ebs.Ballot_Style_Type_Jurisdiction_Enumeration_Value_Id
                                                INNER JOIN Election_Precinct_Split_Ballot_Style esbs 
                                                 ON ebs.Ballot_Style_ID = esbs.Ballot_Style_ID
                                                 AND esbs.Precinct_Split_ID = @VoterPrecinctSplitID
                                                WHERE v.Voter_Id = @VoterID
                                               )
                        SELECT ps.Precinct_Split_Display_Name as SplitName,
                               bs.Ballot_Style_Short_Description_Text AS BallotStyleDescription,
                               p.Party_Display_Name AS PartyName
                        FROM Election_Voter v
                        INNER JOIN Election_Precinct_Split ps 
                         ON v.Voter_Precinct_Split_ID = ps.Precinct_Split_ID
                        INNER JOIN Election_Party p 
                         ON p.Party_ID = v.Voter_Party_ID
                        LEFT JOIN BallotStyle bs 
                         ON bs.Voter_Party_ID = v.Voter_Party_ID
                        WHERE v.Voter_Id = @VoterID";

            var result = await _essSqliteReaderPolldataAdapter.QueryAsync<VoterDetailsDto>(sql, new { VoterPrecinctSplitID = precinctSplitId, VoterID = id });

            if (result.Count() > 1)
            {
                var finalVoterDetailsResult = result.FirstOrDefault();
                finalVoterDetailsResult.hasMultipleBallotStyles = true;
                return finalVoterDetailsResult;
            }
            return result.FirstOrDefault();
        }
    }
}
