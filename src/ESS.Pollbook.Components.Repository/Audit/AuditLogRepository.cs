using Autofac.Features.Indexed;
using ESS.Pollbook.DataAccess.Interfaces;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.UICore;
using System;

namespace ESS.Pollbook.Components.Repository.Audit
{
    public class AuditLogRepository : IAuditLogRepository
    {
        private readonly IEssSqliteWriterAdapter _essSqliteWriterAuditLogAdapter;

        public AuditLogRepository(IIndex<SqliteDatabaseType, IEssSqliteWriterAdapter> writerAdapters)
        {
            _essSqliteWriterAuditLogAdapter = writerAdapters[SqliteDatabaseType.AuditLog];
        }

        public void InsertAuditLog(AuditLogDto auditLog)
        {
            const string sql = @"INSERT INTO AuditLog (UserName,
                                                     Screen,
                                                     Description,
                                                     Record_Update_UTC_Datetime,
                                                     Record_Update_Local_Datetime)

                                            VALUES(@UserName,
                                                    @Screen,
                                                    @Description,
                                                    @Record_Update_UTC_Datetime,
                                                    @Record_Update_Local_Datetime);";

            
           _essSqliteWriterAuditLogAdapter.ExecuteNonQuery(sql,
                new
                {
                    UserName = auditLog.UserName,
                    Screen = auditLog.Screen,
                    Description = auditLog.Description,
                    Record_Update_UTC_Datetime = DateTime.UtcNow,
                    Record_Update_Local_Datetime = DateTime.Now
                });
        }
    }
}
