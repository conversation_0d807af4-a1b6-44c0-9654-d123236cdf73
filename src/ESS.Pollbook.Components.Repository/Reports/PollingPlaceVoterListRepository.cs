using Autofac.Features.Indexed;
using ESS.Pollbook.DataAccess.Interfaces;
using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.Model;
using ESS.Pollbook.Core.UICore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ESS.Pollbook.DataAccess;

namespace ESS.Pollbook.Components.Repository.Reports
{
    public class PollingPlaceVoterListRepository : IPollingPlaceVoterListRepository
    {
        private readonly IEssSqliteReaderAdapter _essSqliteReaderTransactionAdapter;
        private readonly IEssSqliteReaderAdapter _essSqliteReaderPolldataAdapter;
        private readonly IEssLogger _essLogger;

        private class PollbookTransactionSourceKey
        {
            public string SourceKey { get; set; }
        }

        public PollingPlaceVoterListRepository(IIndex<SqliteDatabaseType, IEssSqliteWriterAdapter> writerAdapters, 
            IEssLogger essLogger, 
            IIndex<SqliteDatabaseType, IEssSqliteReaderAdapter> readerAdapters)
        {
            _essSqliteReaderTransactionAdapter = readerAdapters[SqliteDatabaseType.Transaction];
            _essSqliteReaderPolldataAdapter = readerAdapters[SqliteDatabaseType.Polldata];

            _essLogger = essLogger;
        }

        public async Task<IEnumerable<PollingPlaceVoterListItemDto>> GetPollingPlaceVoterList(PollingPlaceVoterListRequest pollingPlaceVoterListRequest)
        {
            // performance testing
            DateTime d1 = DateTime.Now;
            _essLogger.LogInformation($"Entering GetPollingPlaceVoterList PollingPlaceId:{pollingPlaceVoterListRequest.PollingPlaceId}");

            IEnumerable<PollingPlaceVoterListItemDto> resultsdto = new List<PollingPlaceVoterListItemDto>();

            string sql = $@"WITH BallotTransactions AS (
                                                     SELECT SourceKey
                                                          , TransactionType
                                                          , ROW_NUMBER() OVER (PARTITION BY tx.SourceKey ORDER BY tx.TransactionDate desc) AS mostRecent
                                                        FROM Transactions tx
                                                        WHERE tx.PollingPlaceId={pollingPlaceVoterListRequest.PollingPlaceId}
                                                            AND TransactionType in ('{TransactionType.BallotIssue.ToString()}', '{TransactionType.BallotCancel.ToString()}')
                                                        )
                            SELECT v.Voter_Id as VoterId
                                , v.Voter_Source_Voter_Key as VoterKey
                                , v.Voter_Last_Name || ', ' || v.Voter_First_Name || ' ' || coalesce(substr(v.Voter_Middle_Name,1,1),'') as FullName
                                , s.Precinct_Split_Display_Name as PrecinctSplitName
                                , j.Jurisdiction_Enumeration_Value_Name as AbsenteeStatus
                                , case when bt.SourceKey is null then 'No' else 'Yes' end as ElectionDay
                                , v.Voter_Precinct_Split_Id as PrecinctSplitId
                            FROM Election_Voter v
                            LEFT JOIN Election_VoterAbsStatusUpdates VASU
                                ON v.Voter_ID = VASU.Voter_ID
                            INNER JOIN Election_Jurisdiction_Enumeration_Value j 
                                ON IFNULL(VASU.Voter_Absentee_Code_Jurisdiction_Enumeration_Value_ID, v.Voter_Absentee_Code_Jurisdiction_Enumeration_Value_ID) = j.Jurisdiction_Enumeration_Value_ID
                            INNER JOIN Election_Precinct_Split s 
                                ON v.Voter_Precinct_Split_ID=s.Precinct_Split_ID
                            INNER JOIN Election_Polling_Place_Precinct p 
                                ON s.Precinct_Split_Precinct_ID=p.Precinct_ID
                            LEFT OUTER JOIN BallotTransactions bt 
                                ON v.Voter_Source_Voter_Key = bt.SourceKey 
                                    AND bt.mostRecent = 1 
                                    AND bt.TransactionType = '{TransactionType.BallotIssue.ToString()}'
                            WHERE p.Polling_Place_ID = {pollingPlaceVoterListRequest.PollingPlaceId} AND IFNULL(VASU.Voter_Deleted, v.Voter_Deleted) = 0";

            try
            {
                resultsdto = await _essSqliteReaderPolldataAdapter.AttachAndExecuteQueryAsync<PollingPlaceVoterListItemDto>(
                    _essSqliteReaderTransactionAdapter.Connection.ConnectionString,
                    "Transactions",
                    sql);
            }
            catch (Exception ex)
            {
                _essLogger.LogError(ex, new Dictionary<string, string>
                {
                    { "Action", $"{GetType().Name}.{nameof(GetPollingPlaceVoterList)}" }
                });
            }

            if (pollingPlaceVoterListRequest.PrecinctSplitId != 0)
                resultsdto = resultsdto.Where(dto => dto.PrecinctSplitId == pollingPlaceVoterListRequest.PrecinctSplitId);

            if (pollingPlaceVoterListRequest.GroupByPrecinct)
                resultsdto = resultsdto.OrderBy(dto => dto.PrecinctSplitName).ThenBy(dto => dto.FullName);
            else
                resultsdto = resultsdto.OrderBy(dto => dto.FullName);

            // performance testing logging
            TimeSpan delta = DateTime.Now - d1;
            _essLogger.LogInformation($"GetPollingPlaceVoterList Completed. Record Count: {resultsdto.Count()} Execution Delta: {delta}");

            return resultsdto;
        }
    }
}
