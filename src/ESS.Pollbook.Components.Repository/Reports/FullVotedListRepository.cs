using Autofac.Features.Indexed;
using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.Model;
using ESS.Pollbook.Core.UICore;
using ESS.Pollbook.DataAccess;
using ESS.Pollbook.DataAccess.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ESS.Pollbook.Components.Repository.Reports
{
    public class FullVotedListRepository : IFullVotedListRepository
    {
        private readonly IEssSqliteReaderAdapter _essSqliteReaderTransactionAdapter;
        private readonly IEssSqliteReaderAdapter _essSqliteReaderPolldataAdapter;
        private readonly IEssLogger _essLogger;

        public FullVotedListRepository(IIndex<SqliteDatabaseType, IEssSqliteWriterAdapter> writerAdapters,
            IEssLogger essLogger,
            IIndex<SqliteDatabaseType, IEssSqliteReaderAdapter> readerAdapters)
        {
            _essSqliteReaderTransactionAdapter = readerAdapters[SqliteDatabaseType.Transaction];
            _essSqliteReaderPolldataAdapter = readerAdapters[SqliteDatabaseType.Polldata];

            _essLogger = essLogger;
        }

        public async Task<Dictionary<string, string>> GetDevicesWritingTransactionsAsync(int pollingPlaceId)
        {
            _essLogger.LogInformation("Entering GetDevicesWritingTransactions()");
            var devices = new Dictionary<string, string>();

            try
            {
                const string sql = @"SELECT DeviceName, Count(DeviceName) as TransactionCount
                             FROM Transactions 
                            WHERE TransactionType='BallotIssue' 
                              AND PollingPlaceId=@PollingPlaceId 
                              AND DeviceName is not null
                         GROUP BY DeviceName;";

                var result = await _essSqliteReaderTransactionAdapter.QueryAsync<DeviceDto>(sql, new { PollingPlaceId = pollingPlaceId });

                foreach (var dto in result)
                {
                    devices.Add(dto.DeviceName, dto.DeviceName);
                }
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string> { { "Action", "GetDevicesWritingTransactions() - Error in process." } };
                _essLogger.LogError(ex, logProps);
            }

            return devices;
        }

        public async Task<IEnumerable<VotedListItemDto>> GetFullVotedListAsync(FullVotedListRequest fullVotedListRequest)
        {
            var d1 = DateTime.Now;
            _essLogger.LogInformation($"Entering GetFullVotedList PollingPlaceId:{fullVotedListRequest.PollingPlaceId}");

            IEnumerable<VotedListItemDto> votedListItemDtos = new List<VotedListItemDto>();

            var transactionWhere = new StringBuilder();
            var pollDataWhere = new StringBuilder();

            if (!fullVotedListRequest.DeviceFilter.Equals("All"))
            {
                transactionWhere.Append($" AND DeviceName = '{fullVotedListRequest.DeviceFilter}' ");
            }

            if (fullVotedListRequest.VoterVerificationIdFilter > 0)
            {
                pollDataWhere.Append($" AND json_extract(json, '$.DynamicData.{fullVotedListRequest.VoterVerificationIdFilter}')='True' ");
            }

            var filterStartDate = fullVotedListRequest.FilterStartDate;

            if (filterStartDate.HasValue && !fullVotedListRequest.FilterEndDate.HasValue)
            {
                transactionWhere.Append($" AND tx.TransactionDate >= '{filterStartDate.Value.Ticks}' ");
            }
            else if (filterStartDate.HasValue && fullVotedListRequest.FilterEndDate.HasValue)
            {
                transactionWhere.Append($" AND tx.TransactionDate >= '{filterStartDate.Value.Ticks}' AND tx.TransactionDate <= '{fullVotedListRequest.FilterEndDate.Value.Ticks}' ");
            }

            if (fullVotedListRequest.PartyIdFilter != 0)
            {
                pollDataWhere.Append($" AND p.Party_ID  =  {fullVotedListRequest.PartyIdFilter} ");
            }

            if (fullVotedListRequest.PrecinctId != 0)
                pollDataWhere.Append($" AND ps.Precinct_Split_Precinct_ID = {fullVotedListRequest.PrecinctId} ");

            if (fullVotedListRequest.RosterIdFilter != 0)
            {
                pollDataWhere.Append($" AND v.Record_Initial_Load_Indicator = {(fullVotedListRequest.RosterIdFilter == 1 ? 1 : 0)} ");
            }

            if (fullVotedListRequest.VoterStatusIdFilter != 0)
            {
                pollDataWhere.Append($" AND  v.Voter_Status_Code_Jurisdiction_Enumeration_Value_ID =  {fullVotedListRequest.VoterStatusIdFilter} ");
            }

            var sql = $@"WITH BallotTransactions AS (
	                            SELECT tx.SourceKey
		                            , tx.TransactionType
		                            , tx.TransactionDate
		                            , json_extract(JSON,'$.BallotStyleId') as BallotStyleId
		                            , json_extract(JSON,'$.PrecinctSplitBallotStyleId') as PrecinctSplitBallotStyleId
                                    , json_extract(JSON, '$.IsProvisional') as IsProvisional
                                    , json_extract(JSON, '$.MediaTypeEnumId') as MediaTypeEnumId
                                    , JSON
		                            , ROW_NUMBER() OVER (
			                            PARTITION BY tx.SourceKey
			                            ORDER BY tx.TransactionDate desc) AS mostRecent
	                            FROM Transactions tx 
	                            WHERE tx.TransactionType IN ('BallotIssue', 'BallotCancel')
                                    AND tx.PollingPlaceId = @PollingPlaceId
                                    {transactionWhere}
                            )
                            SELECT v.Voter_Id AS VoterId
	                            , bt.SourceKey AS VoterKey
	                            , v.Voter_Last_Name||', '|| v.Voter_First_Name AS FullName
	                            , p.Party_Abbreviated_Name AS PartyDisplayName
	                            , ps.Precinct_Split_Display_Name AS PrecinctSplitName
	                            , v.Record_Initial_Load_Indicator AS RecordInitialLoadIndicator
                                , bt.TransactionDate as TransactionDate
                                , bt.IsProvisional
                                , bt.MediaTypeEnumId
                            FROM BallotTransactions bt
                            LEFT JOIN 
                                ( Select vo.Voter_ID,
                                         vo.Voter_Source_Voter_Key,
                                         vo.Voter_Last_Name,
                                         vo.Voter_First_Name,
                                         vo.Record_Initial_Load_Indicator,
                                         IFNULL(vs.Voter_Status_Code_Jurisdiction_Enumeration_Value_ID, vo.Voter_Status_Code_Jurisdiction_Enumeration_Value_ID) as Voter_Status_Code_Jurisdiction_Enumeration_Value_ID
                                  FROM Election_Voter vo 
                                    LEFT JOIN Election_VoterAbsStatusUpdates vs 
                                        ON vo.Voter_ID = vs.Voter_ID
                                ) as v
	                            ON v.Voter_Source_Voter_Key = bt.SourceKey
                            INNER JOIN Election_Ballot_Style bs
	                            ON bs.Ballot_Style_ID = bt.BallotStyleId
                            INNER JOIN Election_Party p
	                            ON p.Party_ID = bs.Ballot_Style_Party_ID
                            INNER JOIN Election_Precinct_Split_Ballot_Style psbs
	                            ON psbs.Precinct_Split_Ballot_Style_ID =  bt.PrecinctSplitBallotStyleId
                            INNER JOIN Election_Precinct_Split ps
	                            ON ps.Precinct_Split_ID = psbs.Precinct_Split_ID                          
                            WHERE bt.mostRecent = 1
	                            AND bt.TransactionType = 'BallotIssue'
                                {pollDataWhere} ";

            try
            {
                votedListItemDtos = await _essSqliteReaderPolldataAdapter.AttachAndExecuteQueryAsync<VotedListItemDto>(
                    _essSqliteReaderTransactionAdapter.Connection.ConnectionString,
                    "Transactions",
                    sql,
                    new { fullVotedListRequest.PollingPlaceId });
            }
            catch (Exception ex)
            {
                _essLogger.LogInformation($"GetFullVotedList Error. {ex.Message}");
            }
            finally
            {
                try
                {
                    await _essSqliteReaderPolldataAdapter.ExecuteNonQueryAsync("DETACH DATABASE Transactions");
                }
                catch
                {
                    // just ignore any failure to detach
                }
            }

            // performance testing logging
            var delta = DateTime.Now - d1;
            _essLogger.LogInformation($"GetFullVotedList Completed. Execution Delta: {delta}");

            return votedListItemDtos;
        }

        public async Task<IEnumerable<NewVoterInfo>> GetNewVotersInfoAsync(string sourceKeys)
        {
            IEnumerable<NewVoterInfo> result = new List<NewVoterInfo>();
            try
            {
                var sql = $@"SELECT SourceKey as SourceKey,
                            (json_extract(JSON, '$.LastName')||', '|| json_extract(JSON, '$.FirstName'))as FullName
                            FROM Transactions
                        WHERE TransactionType = @transactionType AND
                        SourceKey IN ({sourceKeys})";

                result = await _essSqliteReaderTransactionAdapter.QueryAsync<NewVoterInfo>(sql, new
                {
                    transactionType = nameof(TransactionType.VoterAdd)
                });
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string> { { "Action", "GetNewVotersInfo() - Error" } };
                _essLogger.LogError(ex, logProps);
            }
            return result;
        }
    }
    public class DeviceDto
    {
        public string DeviceName { get; set; }
        public int TransactionCount { get; set; }
    }
}
