using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Model;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ESS.Pollbook.Components.Repository.Reports
{
    public interface IBallotReissuedListRepository
    {

        Task<List<string>> GetDevicesListTask(int pollingPlaceId);

        Task<IEnumerable<BallotReissuedListItemDto>> GetBallotReissuedList(BallotReissuedListRequest ballotReissuedListRequest);
    }
}
