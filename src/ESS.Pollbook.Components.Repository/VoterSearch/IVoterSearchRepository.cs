using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Model;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace ESS.Pollbook.Components.Repository.VoterSearch
{
    public interface IVoterSearchRepository
    {
        Task<IEnumerable<VoterDto>> DoVoterSearchAsync(VoterNameSearchRequest request, bool? enableFullDob, string SourceKeySearchType = "", CancellationToken cancellationToken = default);

        Task<ScannedDriversLicenseDto> Resolve1DScanData(string scannedCode);

        Task<VoterDto> GetVoterByVoterSourceKey(string voterKey);
    }
}
