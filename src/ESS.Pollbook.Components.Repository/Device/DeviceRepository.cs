using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Hardware.Device;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Text.RegularExpressions;
using System.Linq;
using ESS.Pollbook.Hardware.Storage;
using System.Threading.Tasks;

namespace ESS.Pollbook.Components.Repository.Device
{
   public class DeviceRepository : IDeviceRepository
   {
      private readonly IDeviceInfo _deviceInfo;
      private readonly IEssLogger _essLogger;

      public DeviceRepository(IDeviceInfo deviceInfo, IEssLogger essLogger)
      {
         _deviceInfo = deviceInfo;
         _essLogger = essLogger;
      }

      public string GetDeviceModel()
      {
         return _deviceInfo.GetDeviceModel();
      }

      public string GetDeviceSerialNumber()
      {
         return _deviceInfo.GetDeviceSerialNumber();
      }

      public string GetPublicIP()
      {
         const string pattern = @"(\b25[0-5]|\b2[0-4][0-9]|\b[01]?[0-9][0-9]?)(\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}";
         try
         {
            var address = string.Empty;
            var addressBuffer = "";
            var url = ConfigurationManager.AppSettings["PublicIPUrl"];

            var req = System.Net.WebRequest.Create(url);

            using (var resp = req.GetResponse())
            using (var sr = new StreamReader(resp.GetResponseStream() ?? throw new InvalidOperationException("GetResponseStream is null")))
            {
               addressBuffer = sr.ReadToEnd();
            }

            var match = Regex.Match(addressBuffer, pattern);
            if (!match.Success) _essLogger.LogWarning("No ip detected.");
            if (match.Success && match.Groups.Count > 0)
            {
               address = match.Groups[0].Value;
            }
            _essLogger.LogDebug($"Calling out to {url}");
            _essLogger.LogDebug($"My public address is {address}.");

            return address;
         }
         catch (Exception ex)
         {
            var logProps = new Dictionary<string, string> { { "Action", "GetPublicIP" } };
            _essLogger.LogError(ex, logProps);
         }

         return null;
      }


      public void CreateBackupFilesInFolder(string folder)
      {
          string tempFolder = Path.Combine(folder, StorageLocator.TempFolder);
          if (!Directory.Exists(tempFolder))
          {
            Directory.CreateDirectory(tempFolder);
          }

          foreach (var file in Directory.GetFiles(folder).Where(x => !x.Contains("pollbook.log")))
          { 
              var fi = new FileInfo(file);
              string destinationFile = Path.Combine(tempFolder, fi.Name); 

              // Check if the destination file already exists and replace it
              if (File.Exists(destinationFile)) 
              { 
                  File.Delete(destinationFile);
              }
              File.Move(file, Path.Combine(folder, StorageLocator.TempFolder, fi.Name));
          }

      }

      public void RestoreFilesInFolder(string folder)
      {
         if (!Directory.Exists(Path.Combine(folder, StorageLocator.TempFolder))) return;

         var files = Directory.GetFiles(Path.Combine(folder, StorageLocator.TempFolder));
         foreach (var file in files)
         {
            var fi = new FileInfo(file);
            File.Move(file, Path.Combine(folder, fi.Name));
         }
      }

      public void DeleteNewFilesInFolder(string folder)
      {
         var files = Directory.GetFiles(folder).Where(x => !x.Contains("pollbook.log"));
         foreach (var file in files) File.Delete(file);
      }

      public void DeleteBackupFilesInFolder(string folder)
      {
         var files = Directory.GetFiles(Path.Combine(folder, StorageLocator.TempFolder));
         foreach (var file in files) File.Delete(file);
      }

      public async Task<string> CopyFileAsync(string source, string destinationFolder)
      {
         var fileInfo = new FileInfo(source);
         var destinationFile = Path.Combine(destinationFolder, fileInfo.Name);

         await Task.Run(() => File.Copy(source,destinationFile, true));

         return destinationFile;
      }

      public async Task ExtractAllFilesFromZipToFolderAsync(string source, string destinationFolder, string password, bool skipExisting = false)
      {
         using (var zipHandler = new ZipHandler(source, password))
         {
            await zipHandler.ExtractAllFilesToFolderAsync(destinationFolder, skipExisting);
         }
      }

      public async Task ExtractFilesFromZipToFolderAsync(string source, string password, string destinationFolder, string[] targets)
      {
         if (targets == null)
            return;

         using (var zipHandler = new ZipHandler(source, password))
         {
            foreach (var fileName in zipHandler.Content.Where(f => targets.Contains(f)))
            {
               await zipHandler.ExtractFileToFolderAsync(fileName, destinationFolder);
            }
         }
      }

      public List<string> GetContentOfZipFile(string source, string password)
      {
         using (var zipHandler = new ZipHandler(source, password))
         {
            return zipHandler.Content;
         }
      }
   }
}
