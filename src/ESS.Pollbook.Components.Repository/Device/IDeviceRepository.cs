using System.Collections.Generic;
using System.Threading.Tasks;

namespace ESS.Pollbook.Components.Repository.Device
{
    public interface IDeviceRepository
    {
        string GetDeviceModel();
        string GetDeviceSerialNumber();
        string GetPublicIP();

        void CreateBackupFilesInFolder(string folder);
        void RestoreFilesInFolder(string folder);
        void DeleteNewFilesInFolder(string folder);
        void DeleteBackupFilesInFolder(string folder);
        Task<string> CopyFileAsync(string source, string destinationFolder);
        Task ExtractAllFilesFromZipToFolderAsync(string source, string destinationFolder, string password, bool skipExisting = false);
        Task ExtractFilesFromZipToFolderAsync(string source, string password, string destinationFolder, string[] targets);
        List<string> GetContentOfZipFile(string source, string password);
    }
}
