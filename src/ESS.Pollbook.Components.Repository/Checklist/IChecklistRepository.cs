using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Model;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ESS.Pollbook.Components.Repository.Checklist
{
    public interface IChecklistRepository
    {
        /// <summary>
        /// Transaction based deletion to remove from checklist and checklist_type
        /// </summary>
        /// <returns></returns>
        bool DeleteChecklist();

        /// <summary>
        /// Inserts the checklist type into the database
        /// </summary>
        bool InsertChecklist(List<Election_Checklist_Type> checklistTypes, List<Election_Checklist> checklist);

        /// <summary>
        /// Retrieve the checklist from the database based on provided params
        /// </summary>
        Task<IEnumerable<ChecklistDto>> GetChecklist(string checklistType, string pollType);
    }
}