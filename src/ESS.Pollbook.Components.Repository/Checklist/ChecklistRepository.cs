using Autofac.Features.Indexed;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.Model;
using ESS.Pollbook.Core.UICore;
using ESS.Pollbook.DataAccess.Interfaces;
using System;
using System.Collections.Generic;
using System.Reflection;
using System.Threading.Tasks;
using ESS.Pollbook.Core.Common;

namespace ESS.Pollbook.Components.Repository.Checklist
{
    internal class ChecklistRepository : IChecklistRepository
    {
        private readonly IEssLogger _logger;
        private readonly IEssSqliteReaderAdapter _essSqliteReaderPolldataAdapter;
        private readonly IEssSqliteWriterAdapter _essSqliteWriterPolldataAdapter;

        private const string DbTableCheckList = "election_checklist";
        private const string DbTableCheckListType = "election_checklist_type";

        public ChecklistRepository(IEssLogger logger,
            IIndex<SqliteDatabaseType, IEssSqliteReaderAdapter> readerAdapters,
            IIndex<SqliteDatabaseType, IEssSqliteWriterAdapter> writerAdapters)
        {
            _logger = logger;
            _essSqliteReaderPolldataAdapter = readerAdapters[SqliteDatabaseType.Polldata];
            _essSqliteWriterPolldataAdapter = writerAdapters[SqliteDatabaseType.Polldata];
        }

        /// <summary>
        /// Clear the checklist and checklist_type tables utilizing a transaction.
        /// Either both or none are deleted.
        ///
        /// Async DB Connection has made transactions obsolete and requires using Sync method.
        /// Please be sure to wrap this in a task or is a method that is async.
        /// </summary>
        public bool DeleteChecklist()
        {
            try
            {
                var savePoint = _essSqliteWriterPolldataAdapter.SaveTransactionPoint();
                _logger.LogInformation($"Entering database transaction {savePoint} ");

                if (!DeleteFrom(DbTableCheckList) || !DeleteFrom(DbTableCheckListType))
                {
                    _essSqliteWriterPolldataAdapter.Rollback();
                    return false;
                }

                _essSqliteWriterPolldataAdapter.Release(savePoint);
                _logger.LogInformation($"Releasing database transaction {savePoint} ");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex,
                    new Dictionary<string, string>
                        { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });

                _essSqliteWriterPolldataAdapter.Rollback();
                _logger.LogError("Rolling back checklist tables truncate.");
                return false;
            }

            return true;
        }

        /// <summary>
        /// You will need to ensure that the table is empty prior to making this call. Ensure the truncate
        /// succeeds prior to loading it with the response.
        /// </summary>
        public bool InsertChecklist(List<Election_Checklist_Type> checklistTypes, List<Election_Checklist> checklist)
        {
            // Types required for steps
            if (checklistTypes == null || checklistTypes.Count == 0)
                return false;

            try
            {
                var savePoint = _essSqliteWriterPolldataAdapter.SaveTransactionPoint();
                _logger.LogInformation($"Entering database transaction {savePoint} ");
                _essSqliteWriterPolldataAdapter.InsertAll(checklistTypes, string.Empty);

                if (checklist?.Count > 0) // can have types, no steps.
                    _essSqliteWriterPolldataAdapter.InsertAll(checklist, string.Empty);

                _essSqliteWriterPolldataAdapter.Release(savePoint);
                _logger.LogInformation($"Releasing database transaction {savePoint}.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex,
                    new Dictionary<string, string>
                        { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });

                _essSqliteWriterPolldataAdapter.Rollback();
                _logger.LogError("Rolling back checklist tables insert.");
                return false;
            }

            return true;
        }

        /// <summary>
        /// Retrieves a Checklist ordered by "step order" based the parameters provided.
        /// </summary>
        public async Task<IEnumerable<ChecklistDto>> GetChecklist(string checklistType, string pollType)
        {


            IEnumerable<ChecklistDto> checklistItems = new List<ChecklistDto>();
            string sql = @"SELECT Checklist_Type_Name
								, Checklist_Step_Order
								, Checklist_Step_Name
								, Checklist_Step_Details
								, Checklist_Step_ButtonText
							FROM Election_Checklist ec
                                JOIN Election_Checklist_Type ect ON ec.Checklist_Type_Code=ect.Checklist_Type_Code
							WHERE ec.Checklist_Type_Code = @ChecklistType AND Checklist_Poll_Type IN (@Both, @ChecklistPollType)
                            ORDER BY Checklist_Step_Order";
            try
            {
                checklistItems = await _essSqliteReaderPolldataAdapter.QueryAsync<ChecklistDto>(sql,
                    new
                    {
                        ChecklistType = checklistType,
                        ChecklistPollType = pollType,
                        Both = nameof(ChecklistPollType.Both)
                    });

            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string> { { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } };
                _logger.LogError(ex, logProps);
            }
            return checklistItems;
        }

        /// <summary>
        /// The Truncate Optimization https://sqlite.org/lang_delete.html
        ///
        /// NOTE: You Checklist has a foreign key restraint on this table and that table
        /// will need to be cleared first. This will not cascade.
        /// </summary>
        private bool DeleteFrom(string tableName)
        {
            if (string.IsNullOrWhiteSpace(tableName))
                return false;

            try
            {
                _essSqliteWriterPolldataAdapter.ExecuteNonQuery($"delete from {tableName}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex,
                    new Dictionary<string, string>
                        { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
                return false;
            }

            return true;
        }
    }
}
