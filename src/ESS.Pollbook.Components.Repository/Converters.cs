using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.EventsDto;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Core.Utilities;
using System;
using System.Collections.Generic;
using System.IO;
using ESS.Pollbook.Hardware.Storage;

namespace ESS.Pollbook.Components.Repository
{
   public static class Converters
   {
      public static List<object> ConvertEditVoterList(VoterEditEventDto voterEdit, string sourceKey, string deviceName, DateTime transactionDateTime)
      {
         // Pre-compute reusable values to avoid redundant calculations
         var currentDateTimeUtc = DateTime.UtcNow;
         var loggedInUsername = CurrentUserInfo.LoggedInUser?.Username ?? "";
         var firstNameSearch = voterEdit.FirstNameSearch ?? Helpers.GetAlphaNumericString(voterEdit.FirstName);
         var lastNameSearch = voterEdit.LastNameSearch ?? Helpers.GetAlphaNumericString(voterEdit.LastName);
         
         // Prepare and return the list
         return new List<object>
         {
            sourceKey,
            voterEdit.CountyId,
            null, // Placeholder for unused/null value
            voterEdit.PrecinctSplitId,
            voterEdit.PartyId,
            voterEdit.RegistrationDate,
            voterEdit.BallotStyleId,
            voterEdit.IdentificationRequirementStatusEnumId,
            voterEdit.NotInRosterReasonsEnumId,
            voterEdit.VoterBallotStyleTypeJurisdictionEnumValueId,
            voterEdit.AffidavitNumber,
            voterEdit.DriversLicenseNumber,
            voterEdit.SsnLast4,
            voterEdit.NamePrefix,
            voterEdit.FirstName,
            voterEdit.MiddleName,
            voterEdit.LastName,
            voterEdit.NameSuffix,
            voterEdit.EmailAddress,
            voterEdit.DateOfBirth,
            voterEdit.Comments,
            voterEdit.LanguageEnumId,
            voterEdit.SignatureImageFileName,
            voterEdit.AbsenteeStatusEnumId,
            voterEdit.VoterStatusEnumId,
            voterEdit.VoterDeleted,
            voterEdit.RecordInitialLoadIndicator,
            1, // Constant value (why not use a constant or enum for readability?)
            currentDateTimeUtc,
            loggedInUsername,
            deviceName,
            ApplicationNameEnum.ExpressPoll.ToString(),
            loggedInUsername,
            transactionDateTime,
            firstNameSearch,
            lastNameSearch,
            voterEdit.VoterId
         };
      }

      public static List<object> ConvertEditVoterAddressList(VoterEditEventDto voterEdit, string deviceName, DateTime transactionDateTime)
      {
         // Pre-compute values to avoid redundant calculations
         var currentDateTimeUtc = DateTime.UtcNow;
         var loggedInUsername = CurrentUserInfo.LoggedInUser?.Username ?? "";
         var address = voterEdit.Address; // Shortcut reference to avoid multiple property access
         
         // Prepare and return the list
         return new List<object>
         {
            address.HouseNumber,
            address.StreetName,
            address.City,
            address.IsoStateProvinceCode,
            address.PostalCode,
            address.AdditionalPostalCode,
            address.IsoCountryCode,
            address.AddressFullText,
            address.HouseNumberFraction,
            address.AddressTypeEnumId,
            address.UnitTypeEnumId,
            address.UnitValue,
            currentDateTimeUtc,         // Precomputed UTC time
            loggedInUsername,           // Precomputed logged-in username
            deviceName,                 // Device name passed into the method
            ApplicationNameEnum.ExpressPoll.ToString(),
            loggedInUsername,           // Again, username reused
            transactionDateTime,        // Passed into the method
            address.AddressId,
            voterEdit.VoterId
         };
      }

      public static List<object> ConvertEditVoterStatusList(VoterStatusEventDto voterStatus, string sourceKey, string deviceName, DateTime transactionDateTime)
      {
         // Pre-compute values to avoid redundant calculations
         var currentDateTimeUtc = DateTime.UtcNow;
         var loggedInUsername = CurrentUserInfo.LoggedInUser?.Username ?? "";
         
         // Prepare and return the list
         return new List<object>
         {
            voterStatus.AbsenteeStatusEnumId,
            voterStatus.VoterStatusEnumId,
            voterStatus.VoterDeleted,
            currentDateTimeUtc,         // Precomputed UTC time
            loggedInUsername,           // Precomputed logged-in username
            nameof(ApplicationNameEnum.ExpressPoll),
            loggedInUsername,           // Again, username reused
            transactionDateTime,        // Passed into the method
            voterStatus.VoterId,
         };
      }
      
      public static List<object> ConvertVoterSignatureList(VoterEditEventDto voterEdit, string sourceKey)
      {
         return new List<object>
         {
            voterEdit.VoterId,
            sourceKey,
            Path.Combine(StorageLocator.DefaultDbLocation, voterEdit.SignatureImageFileName),
            Path.Combine(StorageLocator.DefaultDbLocation, voterEdit.SignatureImageFileName),
            sourceKey,
            DateTime.Now.ToUniversalTime(),
            CurrentUserInfo.LoggedInUser != null ? CurrentUserInfo.LoggedInUser.Username : "",  
            ApplicationNameEnum.ExpressPoll.ToString(),
            CurrentUserInfo.LoggedInUser != null ? CurrentUserInfo.LoggedInUser.Username : "",
            DateTime.Now
         };
      }
   }
}
