using Autofac.Features.Indexed;
using ESS.Pollbook.DataAccess.Interfaces;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.UICore;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ESS.Pollbook.Components.Repository.PrecinctSplit
{
    public class PrecinctSplitRepository : IPrecinctSplitRepository
    {
        #region Private Memebers

        private readonly IEssSqliteReaderAdapter _essSqliteReaderPolldataAdapter;
        #endregion

        #region  Conctructor(s)

        public PrecinctSplitRepository(IIndex<SqliteDatabaseType, IEssSqliteReaderAdapter> readerAdapters)
        {
            _essSqliteReaderPolldataAdapter = readerAdapters[SqliteDatabaseType.Polldata];
        }
        #endregion

        #region public Methods

        public async Task<PrecinctSplitDto>  GetPrecinctSplitNameByPrecinctSplitId(int precinctSplitId)
        {
            const string sql = @"SELECT  S.Precinct_Split_ID as PrecinctSplitID,
                                S.Precinct_Split_Source_Precinct_Split_Key as PrecinctSplitSourcePrecinctSplitKey,
                                S.Precinct_Split_Precinct_ID as PrecinctSplitPrecinctId,
                                S.Precinct_Split_Display_Name as PrecinctSplitName
                                FROM  Election_Precinct_Split S
                                WHERE S.Precinct_Split_ID =@PrecSpltId";

            return await _essSqliteReaderPolldataAdapter.QueryFirstOrDefaultAsync<PrecinctSplitDto>(sql, new { PrecSpltId = precinctSplitId });
        }

        public async Task<IEnumerable<PrecinctSplitDto>> GetPrecinctSplitsByNameSearch(string search)
        {
            const string sql = @"SELECT  S. Precinct_Split_ID as PrecinctSplitID,
                                S.Precinct_Split_Source_Precinct_Split_Key as PrecinctSplitSourcePrecinctSplitKey,
                                S.Precinct_Split_Precinct_ID as PrecinctSplitPrecinctId,
                                S.Precinct_Split_Display_Name as PrecinctSplitName
                                FROM  Election_Precinct_Split S
                                WHERE S.Precinct_Split_Display_Name LIKE @PrecinctSplitName";

            return await _essSqliteReaderPolldataAdapter.QueryAsync<PrecinctSplitDto>(sql, new { PrecinctSplitName = search + "%" });
        }

        public async Task<IEnumerable<PrecinctSplitDto>> GetPrecinctSplitsNameByPollingPlaceAndSearchTerm(int? pollingPlaceId, string search)
        {
            const string sql = @"select 
                        S. Precinct_Split_ID as PrecinctSplitID,
                        S.Precinct_Split_Source_Precinct_Split_Key as PrecinctSplitSourcePrecinctSplitKey,
                        S.Precinct_Split_Precinct_ID as PrecinctSplitPrecinctId,
                        S.Precinct_Split_Display_Name as PrecinctSplitName
                        from Election_Polling_Place epp
                        inner join Election_Polling_Place_Precinct eppp on eppp.Polling_Place_ID = epp.Polling_Place_ID
                        inner join Election_Precinct epr on epr.Precinct_ID = eppp.Precinct_ID
                        inner join Election_Precinct_Split S on S.Precinct_Split_Precinct_ID = epr.Precinct_ID
                        WHERE  epp.Polling_Place_Id= @PollingPlaceID and S.Precinct_Split_Display_Name LIKE @PrecinctSplitName
                        order by S.Precinct_Split_Display_Name";

            return await _essSqliteReaderPolldataAdapter.QueryAsync<PrecinctSplitDto>(sql, new
            {
                PollingPlaceId = pollingPlaceId,
                PrecinctSplitName = search + "%"
            });
        }
        public async Task<IEnumerable<PrecinctSplitDto>> GetPrecinctSplitsNameByPollingPlace(int? pollingPlaceId)
        {
            const string sql = @"select 
                        S. Precinct_Split_ID as PrecinctSplitID,
                        S.Precinct_Split_Source_Precinct_Split_Key as PrecinctSplitSourcePrecinctSplitKey,
                        S.Precinct_Split_Precinct_ID as PrecinctSplitPrecinctId,
                        S.Precinct_Split_Display_Name as PrecinctSplitName,
                        ed.District_Name as DistrictName
                        from Election_Polling_Place epp
                        inner join Election_Polling_Place_Precinct eppp on eppp.Polling_Place_ID = epp.Polling_Place_ID
                        inner join Election_Precinct epr on epr.Precinct_ID = eppp.Precinct_ID
                        inner join Election_Precinct_Split S on S.Precinct_Split_Precinct_ID = epr.Precinct_ID
                        left join Election_Precinct_Split_District dist on S.Precinct_Split_ID=dist.Precinct_Split_ID
                        left join Election_District ed on dist.District_ID=ed.District_ID
                        WHERE  epp.Polling_Place_Id= @PollingPlaceID 
                        order by S.Precinct_Split_Display_Name";

            return await _essSqliteReaderPolldataAdapter.QueryAsync<PrecinctSplitDto>(sql, new
            {
                PollingPlaceId = pollingPlaceId
            });
        }
        #endregion
    }
}
