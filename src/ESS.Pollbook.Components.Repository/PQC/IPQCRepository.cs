using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.UICore;
using ESS.Pollbook.DataAccess.Connection;
using ESS.Pollbook.Hardware.Storage;
using System.Threading.Tasks;
using static ESS.Pollbook.Components.Repository.PQC.PQCRepository;

namespace ESS.Pollbook.Components.Repository.PQC
{
    public interface IPQCRepository
    {
        Task<PqcStatus> InitConnection(string password);

        Task<bool> CloseConnectionsAsync();

        Task CloseDatabaseConnectionAsync(SqliteDatabaseType sqliteType);

        Task RunCommand(SqliteDatabaseType sqliteDatabaseType, PragmaCommand command, bool isReadConnection);

        StorageLocator GetStorageLocator();

        ConnectionResponse CreateEssSqliteWriterConnection(SqliteDatabaseType sqliteDatabaseType, string password);
    }
}
