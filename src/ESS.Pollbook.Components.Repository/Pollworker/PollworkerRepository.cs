using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Autofac.Features.Indexed;
using ESS.Pollbook.DataAccess.Interfaces;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Core.UICore;

namespace ESS.Pollbook.Components.Repository.Pollworker
{
	public class PollworkerRepository : IPollworkerRepository
	{
		private readonly IEssLogger _essLogger;
        private readonly IEssSqliteReaderAdapter _essSqliteReaderPollworkerAdapter;
		private readonly IEssSqliteWriterAdapter _essSqliteWriterPollworkerAdapter;
        private IEnumerable<KeyNameLookup> _activities;
		private IEnumerable<KeyNameLookup> _pollingPlaces;
		private IEnumerable<PollworkerTitleDto> _titles;

		private IEnumerable<KeyNameLookup> _transactionTypes;

		public PollworkerRepository(IEssLogger essLogger, 
			IIndex<SqliteDatabaseType, IEssSqliteReaderAdapter> readerAdapters,
			IIndex<SqliteDatabaseType, IEssSqliteWriterAdapter> writerAdapters)
		{
            _essSqliteReaderPollworkerAdapter = readerAdapters[SqliteDatabaseType.Pollworker];
			_essSqliteWriterPollworkerAdapter = writerAdapters[SqliteDatabaseType.Pollworker];
            _essLogger = essLogger;

			Task.Run(async () => await LoadConfiguration());
		}

		/// <summary>
		///     Retrieves Pollworker record with timecards info.
		///     All datetimes are Utc but in order to retrieve the day's data for that polling place, the datetime must be
		///     converted to local time.
		/// </summary>
		/// <param name="pollworkerGUID">Guid for the Pollworker</param>
		/// <returns>Returns the PollworkerDto</returns>
		public async Task<PollworkerDto> GetPollworkerAsync(Guid pollworkerGUID)
		{
			try
			{
				var sql = @"WITH TimeCards AS (
                                        SELECT   PollworkerGUID, 
                                                        PollingPlaceID, 
                                                        MAX(lastTimeCardGuid) LastTimeCardGuid,
                                                        MAX(laststarttime) LastStartTime, 
                                                        MAX(lastendtime) LastEndTime, 
                                                        MAX(priortolaststarttime) PriorToLastStartTime, 
                                                        MAX(priortolastendtime) PriorToLastEndTime
                                        FROM (
                                            SELECT   PollworkerGUID, 
                                                            PollingPlaceID, 
                                                            CASE WHEN seq=1 THEN TimeCardGuid ELSE null END lastTimeCardGuid,
	                                                        CASE WHEN seq=1 THEN StartTime ELSE null END laststarttime,
	                                                        CASE WHEN seq=1 THEN EndTime ELSE null END lastendtime,
	                                                        CASE WHEN seq=2 THEN StartTime ELSE null END priortolaststarttime,
	                                                        CASE WHEN seq=2 THEN EndTime ELSE null END priortolastendtime
	                                        FROM (
                                                SELECT   row_number() over (partition by PollworkerGUID, pollingplaceid order by starttime desc) seq, 
                                                                PollworkerGUID, 
                                                                PollingPlaceID, 
                                                                TimeCardGuid,
                                                                StartTime,
                                                                EndTime
                                                FROM pollworkermanagement_ppTimeCard tc 
                                                WHERE  date(tc.StartTime / 10000000 - 62135596800, 'unixepoch', 'localtime') = @TimeCardDate ) a ) a
                                        GROUP BY PollworkerGUID, PollingPlaceID
                                    )
                                SELECT pw.PollworkerGUID as PollWorkerGuid,
		                            pw.FirstName,
		                            pw.MiddleName,
		                            pw.LastName,
                                    pw.NameSuffix,
		                            pw.BirthDate,
                                    pw.CurrentPollingPlaceID,
		                            ppp.Polling_Place_Name as PollingPlaceName,
                                    pw.TitleId as TitleID,
		                            ppt.TitleName as TitleName,
                                    pw.PartyID,
                                    pw.SourceKey,
                                    pw.EmailAddress, 
                                    pw.HomePhoneNumber, 
                                    pw.CellPhoneNumber, 
                                    pw.FullAddress, 
                                    pw.CityName, 
                                    pw.StateProvinceCode, 
                                    pw.PostalCode,
                                    pw.SignedOath as SignedOath,
                                    pw.DropOff,
                                    pw.PickUp,
                                    pw.CommentText,
                                    pw.RecordInitialLoadIndicator,
                                    pw.PollworkerDeleted,
                                    tc.LastTimeCardGuid,
                                    tc.LastStartTime as LastStartTime,
                                    tc.LastEndTime as LastEndTime
                            FROM PollworkerManagement_ppPollworker pw
                            LEFT JOIN PollworkerManagement_ppPolling_Place ppp on pw.CurrentPollingPlaceID = ppp.Polling_Place_ID
                            INNER JOIN PollworkerManagement_ppTitle ppt on pw.TitleID = ppt.TitleID
                            LEFT JOIN TimeCards tc ON pw.PollworkerGUID=tc.PollworkerGUID AND pw.CurrentPollingPlaceID=tc.PollingPlaceID
                            WHERE pw.PollworkerGUID = @PollworkerGUID
                                AND pw.PollworkerDeleted = 0;";

				return await _essSqliteReaderPollworkerAdapter.QueryFirstOrDefaultAsync<PollworkerDto>(sql, new
				{
					TimeCardDate = DateTime.UtcNow.ToLocalTime().ToString("yyyy-MM-dd"),
					PollworkerGUID = pollworkerGUID
				});
			}
			catch (Exception ex)
			{
				var logProps = new Dictionary<string, string>();
				logProps.Add("Action", "PollworkerRepository.SavePollworkerAsync");
				_essLogger.LogError(ex, logProps);
			}

			return null;
		}

		public void SavePollworker(PollworkerDto pollworkerDto)
		{
			try
			{
				var sql = @"INSERT INTO PollworkerManagement_ppPollworker (
                                PollworkerGUID, 
                                TitleID, 
                                CurrentPollingPlaceID,
                                PartyID,
                                FirstName, 
                                MiddleName, 
                                LastName, 
                                NameSuffix, 
                                EmailAddress, 
                                HomePhoneNumber, 
                                CellPhoneNumber, 
                                BirthDate, 
                                FullAddress, 
                                CityName, 
                                StateProvinceCode, 
                                PostalCode,
                                SignedOath,
                                DropOff,
                                PickUp,
                                PollworkerDeleted,
                                RecordInitialLoadIndicator,
                                SourceKey,
                                Record_Update_UTC_Datetime,
                                Record_Update_User_Name,
                                Record_Update_Application_Name,
                                Record_Update_Application_User_Name,
                                Record_Update_Application_Datetime)
                            VALUES(
                                @PollworkerGUID, 
                                @TitleID, 
                                @CurrentPollingPlaceID, 
                                @PartyID,
                                @FirstName, 
                                @MiddleName, 
                                @LastName, 
                                @NameSuffix, 
                                @EmailAddress, 
                                @HomePhoneNumber, 
                                @CellPhoneNumber, 
                                @BirthDate, 
                                @FullAddress, 
                                @CityName, 
                                @StateProvinceCode, 
                                @PostalCode,
                                @SignedOath,
                                @DropOff,
                                @PickUp,
                                @PollworkerDeleted,
                                @RecordInitialLoadIndicator,
                                @SourceKey,
                                @RecordUpdateUtcDatetime,
                                @RecordUpdateUserName,
                                @RecordUpdateApplicationName,
                                @RecordUpdateApplicationUserName,
                                @RecordUpdateApplicationDatetime)
                            ON CONFLICT(PollworkerGUID) DO
                            UPDATE SET
                                TitleID = excluded.TitleID, 
                                CurrentPollingPlaceID=excluded.CurrentPollingPlaceID, 
                                PartyID = excluded.PartyID,
                                FirstName=excluded.FirstName, 
                                MiddleName=excluded.MiddleName, 
                                LastName=excluded.LastName, 
                                NameSuffix=excluded.NameSuffix, 
                                EmailAddress=excluded.EmailAddress, 
                                HomePhoneNumber=excluded.HomePhoneNumber, 
                                CellPhoneNumber=excluded.CellPhoneNumber, 
                                BirthDate=excluded.BirthDate, 
                                FullAddress=excluded.FullAddress, 
                                CityName=excluded.CityName, 
                                StateProvinceCode=excluded.StateProvinceCode, 
                                PostalCode=excluded.PostalCode,
                                SignedOath=excluded.SignedOath,
                                DropOff=excluded.DropOff,
                                PickUp=excluded.PickUp,
                                PollworkerDeleted=excluded.PollworkerDeleted,
                                RecordInitialLoadIndicator=excluded.RecordInitialLoadIndicator,
                                SourceKey=excluded.SourceKey,
                                Record_Update_UTC_Datetime=excluded.Record_Update_UTC_Datetime,
                                Record_Update_User_Name=excluded.Record_Update_User_Name,
                                Record_Update_Application_Name=excluded.Record_Update_Application_Name,
                                Record_Update_Application_User_Name=excluded.Record_Update_Application_User_Name,
                                Record_Update_Application_Datetime=excluded.Record_Update_Application_Datetime";

				_essSqliteWriterPollworkerAdapter.ExecuteNonQuery(sql, new
				{
					PollworkerGUID = pollworkerDto.PollworkerGUID ?? Guid.Empty,
					pollworkerDto.TitleID,
					pollworkerDto.CurrentPollingPlaceID,
					pollworkerDto.PartyID,
					pollworkerDto.FirstName,
					pollworkerDto.MiddleName,
					pollworkerDto.LastName,
					pollworkerDto.NameSuffix,
					pollworkerDto.EmailAddress,
					pollworkerDto.HomePhoneNumber,
					pollworkerDto.CellPhoneNumber,
					pollworkerDto.BirthDate,
					pollworkerDto.FullAddress,
					pollworkerDto.CityName,
					pollworkerDto.StateProvinceCode,
					pollworkerDto.PostalCode,
					SignedOath = pollworkerDto.SignedOath ?? false,
					DropOff = pollworkerDto.DropOff ?? false,
					Pickup = pollworkerDto.PickUp ?? false,
					PollworkerDeleted = pollworkerDto.PollworkerDeleted ?? false,
					RecordInitialLoadIndicator = pollworkerDto.RecordInitialLoadIndicator ?? false,
					pollworkerDto.SourceKey,
					Record_Update_UTC_Datetime = DateTime.UtcNow,
					Record_Update_User_Name = CurrentUserInfo.LoggedInUser.Username,
					Record_Update_Application_Name = ApplicationNameEnum.ExpressPoll.ToString(),
					Record_Update_Application_User_Name = CurrentUserInfo.LoggedInUser.Username,
					Record_Update_Application_Datetime = DateTime.UtcNow
				});
			}


			catch (Exception ex)
			{
				var logProps = new Dictionary<string, string>();
				logProps.Add("Action", "PollworkerRepository.SavePollworkerAsync");
				_essLogger.LogError(ex, logProps);
			}
		}

		/// <summary>
		///     Retrieves the Pollworkers from a specific Polling Place and DateTime
		///     All datetimes are Utc but in order to retrieve the day's data for that polling place, the datetime must be
		///     converted to local time.
		/// </summary>
		/// <param name="pollingPlaceId">Id of the polling place</param>
		/// <param name="timeCardDate">DateTime to retrieve</param>
		/// <returns>A enumerable list of PollworkerDto</returns>
		public async Task<IEnumerable<PollworkerDto>> GetPollworkersAsync(int pollingPlaceId, DateTime timeCardDate)
		{
			var sql = @"WITH TimeCards AS (
                                        SELECT   PollworkerGUID, 
                                                        PollingPlaceID, 
                                                        MAX(lastTimeCardGuid) LastTimeCardGuid,
                                                        MAX(laststarttime) LastStartTime, 
                                                        MAX(lastendtime) LastEndTime, 
                                                        MAX(priortolaststarttime) PriorToLastStartTime, 
                                                        MAX(priortolastendtime) PriorToLastEndTime
                                        FROM (
                                            SELECT   PollworkerGUID, 
                                                            PollingPlaceID, 
                                                            CASE WHEN seq=1 THEN TimeCardGuid ELSE null END lastTimeCardGuid,
	                                                        CASE WHEN seq=1 THEN StartTime ELSE null END laststarttime,
	                                                        CASE WHEN seq=1 THEN EndTime ELSE null END lastendtime,
	                                                        CASE WHEN seq=2 THEN StartTime ELSE null END priortolaststarttime,
	                                                        CASE WHEN seq=2 THEN EndTime ELSE null END priortolastendtime
	                                        FROM (
                                                SELECT   row_number() over (partition by PollworkerGUID, pollingplaceid order by starttime desc) seq, 
                                                                PollworkerGUID, 
                                                                PollingPlaceID, 
                                                                TimeCardGuid,
                                                                StartTime,
                                                                EndTime
                                                FROM pollworkermanagement_ppTimeCard tc 
                                                WHERE  date(tc.StartTime / 10000000 - 62135596800, 'unixepoch', 'localtime') = @TimeCardDate 
                                                    AND PollingPlaceID=@PollingPlaceId) a ) a
                                        GROUP BY PollworkerGUID, PollingPlaceID
                                    )
                                    SELECT pw.PollworkerGUID,
                                                pw.CurrentPollingPlaceID,
                                                pw.FirstName,
                                                pw.MiddleName,
                                                pw.LastName,
                                                pw.TitleID as TitleId,
                                                t.TitleName,
                                                tc.LastTimeCardGuid,
                                                tc.LastStartTime,
                                                tc.LastEndTime,
                                                tc.PriorToLastStartTime,
                                                tc.PriorToLastEndTime
                                    FROM PollworkerManagement_ppPollworker pw 
                                    JOIN PollworkerManagement_ppTitle t ON pw.TitleID=t.TitleID
                                    LEFT JOIN TimeCards tc ON pw.PollworkerGUID=tc.PollworkerGUID AND pw.CurrentPollingPlaceID=tc.PollingPlaceID
                                    WHERE pw.CurrentPollingPlaceID=@PollingPlaceId
                                        AND pw.PollworkerDeleted = 0;";

			return await _essSqliteReaderPollworkerAdapter.QueryAsync<PollworkerDto>(sql, new
			{
				TimeCardDate = timeCardDate.ToLocalTime().ToString("yyyy-MM-dd"),
				PollingPlaceId = pollingPlaceId
			});
		}

		public async Task<IEnumerable<TimeCardDto>> GetAllTimeCardsForPollworkerAsync(Guid pollworkerGuid)
		{
			var sql = @"SELECT 
                                            tc.TimeCardGuid,
                                            tc.PollworkerGUID,
                                            tc.TransactionGUID,
                                            pw.FirstName,
                                            pw.LastName,
                                            t.TitleName,
                                            PollingPlaceId,
                                            Polling_Place_Display_Name as PollingPlaceDisplayName,
                                            StartTime,
                                            EndTime,
                                            JudgeApprovedDateTime
                                    FROM PollworkerManagement_ppTimeCard tc
                                    JOIN PollworkerManagement_ppPollworker pw ON tc.PollworkerGUID=pw.PollworkerGUID
                                    JOIN PollworkerManagement_ppTitle t ON pw.TitleID=t.TitleID
                                    JOIN PollworkerManagement_ppPolling_Place pp ON tc.PollingPlaceID=pp.Polling_Place_ID
                                    WHERE tc.PollworkerGUID = @PollworkerGUID";

			return await _essSqliteReaderPollworkerAdapter.QueryAsync<TimeCardDto>(sql,
				new
				{
					PollworkerGUID = pollworkerGuid
				});
		}

		public async Task<IEnumerable<TimeCardDto>> GetAllTimeCardsByDateAndPollingPlaceIdAsync(int pollingPlaceId,
			DateTime timeCardDate)
		{
			var sql = @" SELECT tc.TimeCardGuid,
                                    tc.StartTime,
                                    tc.EndTime,
                                    tc.JudgeApprovedDateTime
                                FROM PollworkerManagement_ppTimeCard tc
                               WHERE date(tc.StartTime / 10000000 - 62135596800, 'unixepoch', 'localtime') = @TimeCardDate
                                 AND tc.PollingPlaceID = @PollingPlaceId
                                 AND JudgeApprovedDateTime is null";

			return await _essSqliteReaderPollworkerAdapter.QueryAsync<TimeCardDto>(sql, new
			{
				TimeCardDate = timeCardDate.ToString("yyyy-MM-dd"),
				PollingPlaceId = pollingPlaceId
			});
		}

		public async Task<IEnumerable<KeyNameLookup>> GetTransactionTypesAsync(bool forceLoad = false)
		{
			if (_transactionTypes != null && !forceLoad)
				return _transactionTypes;

			var sql = @"SELECT TransactionTypeID, TransactionTypeName 
                                   FROM PollworkerManagement_ppTransactionType";

			_transactionTypes = await _essSqliteReaderPollworkerAdapter.QueryAsync<KeyNameLookup>(sql);

			return _transactionTypes;
		}

		public async Task<IEnumerable<PollworkerDto>> GetAllJudgesForPollingPlaceAsync(int pollingPlaceId)
		{
			var sql = @"SELECT PollworkerGUID,
                                FirstName,
                                LastName,
                                strftime('%Y-%m-%d', BirthDate / 10000000 - 62135596800, 'unixepoch') as BirthDate
                            FROM PollworkerManagement_ppPollworker p
                            JOIN PollworkerManagement_ppTitle t ON p.TitleID=t.TitleID
                            WHERE t.TitleName = 'Judge' 
                            AND p.CurrentPollingPlaceID = @PollingPlaceId;";

			return await _essSqliteReaderPollworkerAdapter.QueryAsync<PollworkerDto>(sql, new
			{
				PollingPlaceId = pollingPlaceId
			});
		}

		public async Task<IEnumerable<PollworkerTitleDto>> GetTitlesAsync(bool forceLoad = false)
		{
			if (_titles != null && !forceLoad)
				return _titles;

			var sql = @"SELECT TitleID, TitleName FROM PollworkerManagement_ppTitle";

			_titles = await _essSqliteReaderPollworkerAdapter.QueryAsync<PollworkerTitleDto>(sql);

			return _titles;
		}

		public async Task<IEnumerable<KeyNameLookup>> GetActivitiesAsync(bool forceLoad = false)
		{
			if (_activities != null && !forceLoad)
				return _activities;

			var sql = @"SELECT ActivityID as Key, ActivityName as Name
                                   FROM PollworkerManagement_ppActivity";

			_activities = await _essSqliteReaderPollworkerAdapter.QueryAsync<KeyNameLookup>(sql);

			return _activities;
		}

		/// <summary>
		///     Load all poll places from Pollworker database, name is limited to 35 character length.
		/// </summary>
		public async Task<IEnumerable<KeyNameLookup>> GetPollPlacesAsync(bool forceLoad = false)
		{
			if (_pollingPlaces != null && !forceLoad)
				return _pollingPlaces;

			var sql = @"SELECT Polling_Place_ID as Key, Polling_Place_Display_Name as Name
                                        FROM PollworkerManagement_ppPolling_Place";

			_pollingPlaces = await _essSqliteReaderPollworkerAdapter.QueryAsync<KeyNameLookup>(sql);

			return _pollingPlaces;
		}

		public async Task<bool> IsSourceKeyUnique(string sourceKey)
		{
			var sql = @"SELECT count(SourceKey) = 0 as IsUnique 
                                    FROM PollworkerManagement_ppPollworker 
                                    WHERE SourceKey = @SourceKey";

			return await _essSqliteReaderPollworkerAdapter.ExecuteScalarAsync<bool>(sql, new { SourceKey = sourceKey });
		}

		public bool SavePollworkerTransaction(PollworkerTransactionDto transaction)
		{
			try
			{
				// insert record into PollworkerManagement_ppTransaction
				var sql = @"INSERT INTO PollworkerManagement_ppTransaction (
                                        TransactionGUID, 
                                        SystemIdentifier, 
                                        HostTransactionID, 
                                        TransactionTypeID, 
                                        TransactionPollingPlaceID, 
                                        PollworkerGUID, 
                                        RawJson, 
                                        Record_Update_UTC_Datetime, 
                                        Record_Update_User_Name, 
                                        Record_Update_Application_Name, 
                                        Record_Update_Application_User_Name, 
                                        Record_Update_Application_Datetime)
                                  VALUES(
                                        @TransactionGUID, 
                                        @SystemIdentifier, 
                                         @HostTransactionId, 
                                         @TransactionTypeId, 
                                         @TransactionPollingPlaceId,
                                         @PollworkerGUID,
                                         @RawJson, 
                                         @RecordUpdateUTCDatetime, 
                                         @RecordUpdateUserName, 
                                         @RecordUpdateApplicationName, 
                                         @RecordUpdateApplicationUserName, 
                                         @RecordUpdateApplicationDatetime)
                                    ON CONFLICT(TransactionGUID) DO
                                    UPDATE SET HostTransactionID = excluded.HostTransactionID";

				_essSqliteWriterPollworkerAdapter.ExecuteNonQuery(sql, new
				{
					transaction.TransactionGuid,
					transaction.SystemIdentifier,
					transaction.HostTransactionId,
					transaction.TransactionTypeId,
					transaction.TransactionPollingPlaceId,
					PollworkerGUID = transaction.PollworkerGuid,
					transaction.RawJson,
					RecordUpdateUTCDatetime = transaction.Record_Update_Utc_Datetime,
					RecordUpdateUserName = transaction.Record_Update_User_Name,
					RecordUpdateApplicationName = transaction.Record_Update_Application_Name,
					RecordUpdateApplicationUserName = transaction.Record_Update_Application_User_Name,
					RecordUpdateApplicationDatetime = transaction.Record_Update_Application_Datetime
				});

				return true;
			}
			catch (Exception ex)
			{
				var logProps = new Dictionary<string, string>();
				logProps.Add("Action", "PollworkerRepository.SavePollworkerTransaction");
				_essLogger.LogError(ex, logProps);

				return false;
			}
		}

		public async Task<TimeCardDto> GetLocalTimeCardByGuidAsync(Guid timeCardGuid)
		{
			try
			{
				var sql = @"SELECT
                                TimeCardGuid,
                                PollworkerGUID,
                                PollworkerTitleID,
                                TransactionGUID,
                                StartTimeActivityID,
                                EndTimeActivityID,
                                PollingPlaceID,
                                StartTime,
                                EndTime,
                                Comments,
                                JudgeApprovedDateTime,
                                JudgePollworkerGUID,
                                Record_Update_UTC_Datetime,
                                Record_Update_User_Name,
                                Record_Update_Application_Name,
                                Record_Update_Application_User_Name,
                                Record_Update_Application_Datetime
                            FROM 
                                PollworkerManagement_ppTimeCard
                            WHERE
                                TimeCardGuid = @timeCardGuid";
				return await _essSqliteReaderPollworkerAdapter.QueryFirstOrDefaultAsync<TimeCardDto>(sql, new
				{
					timeCardGuid
				});
			}
			catch (Exception ex)
			{
				var logProps = new Dictionary<string, string>();
				logProps.Add("Action", "PollworkerRepository.GetLocalTimeCardByGuidAsync");
				_essLogger.LogError(ex, logProps);
				return null;
			}
		}

		public void SavePollworkerTimeCard(TimeCardDto timeCard)
		{
			try
			{
				// upsert to PollworkerManagement_ppTimeCard
				var sql = @"INSERT INTO PollworkerManagement_ppTimeCard (
                                                TimeCardGuid,
                                                PollworkerGUID,
                                                PollworkerTitleID,
                                                TransactionGUID,
                                                StartTimeActivityID,
                                                EndTimeActivityID,
                                                PollingPlaceID,
                                                StartTime,
                                                EndTime,
                                                Comments,
                                                JudgeApprovedDateTime,
                                                JudgePollworkerGUID,
                                                Record_Update_UTC_Datetime,
                                                Record_Update_User_Name,
                                                Record_Update_Application_Name,
                                                Record_Update_Application_User_Name,
                                                Record_Update_Application_Datetime)
                                           VALUES(
                                                @TimeCardGuid,
                                                @PollworkerGUID,
                                                @PollworkerTitleId,
                                                @TransactionGUID,
                                                @StartTimeActivityId,
                                                @EndTimeActivityId,
                                                @PollingPlaceId,
                                                @StartTime,
                                                @EndTime,
                                                @Comments,
                                                @JudgeApprovedDateTime,
                                                @JudgePollworkerGUID,
                                                @RecordUpdateUtcDatetime,
                                                @RecordUpdateUserName,
                                                @RecordUpdateApplicationName,
                                                @RecordUpdateApplicationUserName,
                                                @RecordUpdateApplicationDatetime)
                                            ON CONFLICT(TimeCardGuid) DO
                                            UPDATE SET
                                                PollworkerGUID=excluded.PollworkerGUID,
                                                PollworkerTitleID=excluded.PollworkerTitleID,
                                                TransactionGUID=excluded.TransactionGUID,
                                                StartTimeActivityID=excluded.StartTimeActivityID,
                                                EndTimeActivityID=excluded.EndTimeActivityID,
                                                PollingPlaceID=excluded.PollingPlaceID,
                                                StartTime=excluded.StartTime,
                                                EndTime=excluded.EndTime,
                                                Comments=excluded.Comments,
                                                JudgeApprovedDateTime=excluded.JudgeApprovedDateTime,
                                                JudgePollworkerGUID=excluded.JudgePollworkerGUID,
                                                Record_Update_UTC_Datetime=excluded.Record_Update_UTC_Datetime,
                                                Record_Update_User_Name=excluded.Record_Update_User_Name,
                                                Record_Update_Application_Name=excluded.Record_Update_Application_Name,
                                                Record_Update_Application_User_Name=excluded.Record_Update_Application_User_Name,
                                                Record_Update_Application_Datetime=excluded.Record_Update_Application_Datetime";

				_essSqliteWriterPollworkerAdapter.ExecuteNonQuery(sql, new
				{
					timeCard.TimeCardGuid,
					PollworkerGuid = timeCard.PollworkerGUID,
					timeCard.PollworkerTitleId,
					timeCard.TransactionGUID,
					timeCard.StartTimeActivityId,
					timeCard.EndTimeActivityId,
					timeCard.PollingPlaceId,
					timeCard.StartTime,
					timeCard.EndTime,
					timeCard.Comments,
					timeCard.JudgeApprovedDateTime,
					JudgePollworkerGuid = timeCard.JudgePollworkerGUID,
					RecordUpdateUTCDatetime = timeCard.RecordUpdateUtcDatetime == new DateTime()
						? DateTime.UtcNow
						: timeCard.RecordUpdateUtcDatetime,
					RecordUpdateUserName = string.IsNullOrEmpty(timeCard.RecordUpdateUserName)
						? CurrentUserInfo.LoggedInUser.Username
						: timeCard.RecordUpdateUserName,
					RecordUpdateApplicationName = string.IsNullOrEmpty(timeCard.RecordUpdateApplicationName)
						? ApplicationNameEnum.ExpressPoll.ToString()
						: timeCard.RecordUpdateApplicationName,
					RecordUpdateApplicationUserName = string.IsNullOrEmpty(timeCard.RecordUpdateApplicationUserName)
						? CurrentUserInfo.LoggedInUser.Username
						: timeCard.RecordUpdateApplicationUserName,
					RecordUpdateApplicationDatetime = timeCard.RecordUpdateApplicationDatetime == new DateTime()
						? DateTime.UtcNow
						: timeCard.RecordUpdateApplicationDatetime
				});
			}
			catch (Exception ex)
			{
				var logProps = new Dictionary<string, string>();
				logProps.Add("Action", "PollworkerRepository.SavePollworkerTimeCard");
				_essLogger.LogError(ex, logProps);
			}
		}

		public bool ApprovePollworkerTimeCards(List<TimeCardDto> timeCards, Guid judgePollworkerGuid,
			DateTime judgeDateTime)
		{
			try
			{
				var timeCardGuids = string.Join(",", timeCards.Select(t => t.TimeCardGuidToVarchar).ToArray());

				var sql = $@"UPDATE PollworkerManagement_ppTimeCard
                                    SET JudgeApprovedDateTime = @JudgeDateTime,
                                            JudgePollworkerGUID = @JudgePollworkerGUID
                                   WHERE TimeCardGuid IN ({timeCardGuids});";

				_essSqliteWriterPollworkerAdapter.ExecuteNonQuery(sql, new
				{
					JudgeDateTime = judgeDateTime,
					JudgePollworkerGuid = judgePollworkerGuid
				});

				return true;
			}
			catch (Exception ex)
			{
				var logProps = new Dictionary<string, string>();
				logProps.Add("Action", "PollworkerRepository.ApprovePollworkerTimeCardsAsync");
				_essLogger.LogError(ex, logProps);

				return false;
			}
		}

		/// <summary>
		///     Retrieves the Pollworkers that match criteria
		///     All datetimes are Utc but in order to retrieve the day's data for that polling place, the datetime must be
		///     converted to local time.
		/// </summary>
		/// <param name="currentPollPlaceOnly">Id of the polling place</param>
		/// <param name="firstName">First name or portion of first name starting with data</param>
		/// <param name="lastName">Last name or potion of last name starting with data</param>
		/// <param name="dateOfBirth">BirthDate of the Pollworker being searched</param>
		/// <returns></returns>
		public async Task<IEnumerable<PollworkerDto>> GetPollworkerSearchAsync(bool currentPollPlaceOnly,
			string firstName, string lastName, DateTime? dateOfBirth)
		{
			var searchParams = new List<string>();
			var results = default(IEnumerable<PollworkerDto>);

			try
			{
				var sql = @"WITH TimeCards AS (
                                        SELECT   PollworkerGUID, 
                                                        PollingPlaceID, 
                                                        MAX(lastTimeCardGuid) LastTimeCardGuid,
                                                        MAX(laststarttime) LastStartTime, 
                                                        MAX(lastendtime) LastEndTime, 
                                                        MAX(priortolaststarttime) PriorToLastStartTime, 
                                                        MAX(priortolastendtime) PriorToLastEndTime
                                        FROM (
                                            SELECT   PollworkerGUID, 
                                                            PollingPlaceID, 
                                                            CASE WHEN seq=1 THEN TimeCardGuid ELSE null END lastTimeCardGuid,
	                                                        CASE WHEN seq=1 THEN StartTime ELSE null END laststarttime,
	                                                        CASE WHEN seq=1 THEN EndTime ELSE null END lastendtime,
	                                                        CASE WHEN seq=2 THEN StartTime ELSE null END priortolaststarttime,
	                                                        CASE WHEN seq=2 THEN EndTime ELSE null END priortolastendtime
	                                        FROM (
                                                SELECT   row_number() over (partition by PollworkerGUID, pollingplaceid order by starttime desc) seq, 
                                                                PollworkerGUID, 
                                                                PollingPlaceID, 
                                                                TimeCardGuid,
                                                                StartTime,
                                                                EndTime
                                                FROM pollworkermanagement_ppTimeCard tc 
                                                WHERE  date(tc.StartTime / 10000000 - 62135596800, 'unixepoch', 'localtime') = @TimeCardDate 
                                                    ##POLLPLACECLAUSE##) a ) a
                                        GROUP BY PollworkerGUID, PollingPlaceID
                                    )
                                SELECT pw.PollworkerGUID as PollWorkerGuid,
		                            FirstName,
		                            MiddleName,
		                            LastName,
                                    NameSuffix,
		                            BirthDate,
                                    pw.CurrentPollingPlaceID,
		                            ppp.Polling_Place_Name as PollingPlaceName,
                                    pw.TitleId as TitleID,
		                            ppt.TitleName as TitleName,
                                    SignedOath as SignedOath,
                                    tc.LastTimeCardGuid,
                                    tc.LastStartTime as LastStartTime,
                                    tc.LastEndTime as LastEndTime
                            FROM PollworkerManagement_ppPollworker pw
                            LEFT JOIN PollworkerManagement_ppPolling_Place ppp on pw.CurrentPollingPlaceID = ppp.Polling_Place_ID
                            INNER JOIN PollworkerManagement_ppTitle ppt on pw.TitleID = ppt.TitleID
                            LEFT JOIN TimeCards tc ON pw.PollworkerGUID=tc.PollworkerGUID AND pw.CurrentPollingPlaceID=tc.PollingPlaceID";

				if (!string.IsNullOrWhiteSpace(firstName))
					searchParams.Add($"FirstName like '{firstName}%'");

				if (!string.IsNullOrWhiteSpace(lastName))
					searchParams.Add($"LastName like '{lastName}%'");

				if (dateOfBirth.HasValue)
					searchParams.Add($"Birthdate = '{dateOfBirth.Value.Ticks}'");

				if (currentPollPlaceOnly)
					//AND PollingPlaceID=@PollingPlaceId
					searchParams.Add(
						$"pw.CurrentPollingPlaceID = {LoggedInPollplaceInfo.LoggedInPollPlace.PollingPlaceId}");
				sql = sql.Replace(" ##POLLPLACECLAUSE##", string.Empty);
				if (searchParams.Count > 0) sql = $"{sql} WHERE {string.Join(" AND ", searchParams)}";
				results = await _essSqliteReaderPollworkerAdapter.QueryAsync<PollworkerDto>(sql, new
				{
					TimeCardDate = DateTime.UtcNow.ToLocalTime().ToString("yyyy-MM-dd")
				});
			}
			catch (Exception ex)
			{
				var logProps = new Dictionary<string, string>();
				logProps.Add("Action", "PollworkerRepository.GetPollworkerSearchAsync");
				_essLogger.LogError(ex, logProps);
			}

			return results;
		}

		public async Task<IEnumerable<PollworkerTransactionDto>> GetPendingTransactions()
		{
			var results = default(IEnumerable<PollworkerTransactionDto>);
			try
			{
				var sql = @"SELECT * FROM PollworkerManagement_ppTransaction WHERE HostTransactionID=-1";

				results = await _essSqliteReaderPollworkerAdapter.QueryAsync<PollworkerTransactionDto>(sql);

				return results;
			}
			catch (Exception ex)
			{
				var logProps = new Dictionary<string, string>();
				logProps.Add("Action", "PollworkerRepository.GetAllPendingTransactions");
				_essLogger.LogError(ex, logProps);
				return results;
			}
		}

		public async Task<PollworkerTransactionDto> GetMaxPollworkerTransactionIdAsync(string systemId)
		{
			_essLogger.LogDebug("Entered GetMaxHostTransactionIdAsync()");

			var defaulPollworkerTransactionDto = new PollworkerTransactionDto { HostTransactionId = -1 };

			try
			{
				var sql = @"SELECT max (HostTransactionId) as HostTransactionId
                           FROM PollworkerManagement_ppTransaction 
                           WHERE SystemIdentifier <> @Id";

				var result = await _essSqliteReaderPollworkerAdapter.QueryFirstOrDefaultAsync<PollworkerTransactionDto>(sql,
					new { Id = systemId });

				if (result != null)
				{
					_essLogger.LogDebug(
						$"GetMaxPollworkerTransactionIdAsync() - MaxPollworkerTransactionId:{result.HostTransactionId}");
					return result;
				}

				return defaulPollworkerTransactionDto;
			}
			catch (Exception ex)
			{
				var logProps = new Dictionary<string, string>();
				logProps.Add("Action", "Getting maximum pollworker Transaction Id");
				_essLogger.LogError(ex, logProps);

				return defaulPollworkerTransactionDto;
			}
		}

		public async Task<int> GetJudgeApprovalStatusForPollingPlace(int pollingPlaceId)
		{
			try
			{
				var sql = $@"SELECT COUNT(*) 
                          FROM PollworkerManagement_ppTransaction 
                         WHERE TransactionTypeId=5 
                           AND TransactionPollingPlaceID={pollingPlaceId}
                           AND DATE(Record_Update_UTC_Datetime / 10000000 - 62135596800, 'unixepoch', 'localtime') = '{DateTime.UtcNow.ToLocalTime().ToString("yyyy-MM-dd")}'";

				return await _essSqliteReaderPollworkerAdapter.ExecuteScalarAsync<int>(sql);
			}
			catch (Exception ex)
			{
				var logProps = new Dictionary<string, string>();
				logProps.Add("Action", "PollworkerRepository.GetJudgeApprovalStatusForPollingPlace");
				_essLogger.LogError(ex, logProps);

				return -1;
			}
		}

		private async Task LoadConfiguration(bool forceLoad = false)
		{
			if (!SystemDetails.IsPQCVerified) return;

			_transactionTypes = await GetTransactionTypesAsync(forceLoad);
			_activities = await GetActivitiesAsync(forceLoad);
			_titles = await GetTitlesAsync(forceLoad);
			_pollingPlaces = await GetPollPlacesAsync(forceLoad);
		}

		public async Task Refresh()
		{
			await LoadConfiguration(true);
		}

		public async Task<IEnumerable<PollingPlaceTitleDto>> GetPollingPlaceTitlesWithUsageCounts(int pollingPlaceId)
		{
			var sql = @"SELECT 
		                    t.TitleID as TitleId, 
		                    ppt.TitleName as Title,
		                    t.Max_Title_Count as MaxTitleCount,
		                    t.Polling_Place_ID as ppid,
		                    count(p.TitleID) as UsageCount 
	                    FROM PollworkerManagement_ppPolling_Place_Title t
	                    LEFT JOIN PollworkerManagement_ppPollworker p ON 
	                        t.TitleID = p.TitleId AND 
	                        t.polling_place_id=@pollingPlaceId AND 
	                        p.CurrentPollingPlaceID=@pollingPlaceId
	                    JOIN PollworkerManagement_ppTitle ppt 
                            ON t.TitleId = ppt.TitleID
	                    WHERE ppid=@pollingPlaceId
	                    GROUP BY t.TitleID, t.Polling_Place_ID";

			return await _essSqliteReaderPollworkerAdapter.QueryAsync<PollingPlaceTitleDto>(sql,
				new { pollingPlaceId = pollingPlaceId });
		}
	}
}