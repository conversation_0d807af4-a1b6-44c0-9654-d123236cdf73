using Autofac.Features.Indexed;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Core.Storage;
using ESS.Pollbook.Core.UICore;
using ESS.Pollbook.DataAccess.Exceptions;
using ESS.Pollbook.DataAccess.Interfaces;
using ESS.Pollbook.Hardware.Storage;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;

namespace ESS.Pollbook.Components.Repository.Messages
{
	public class MessageRepository : IMessageRepository
	{
        private readonly IEssSqliteReaderAdapter _essSqliteReaderPolldataAdapter;
        private readonly IEssSqliteWriterAdapter _essSqliteWriterPolldataAdapter;
        private readonly IEssLogger _essLogger;

        private readonly string _alertCreateQueuePath = StorageLocator.GetAlertCreateQueueFolder();
        private readonly string _conversationCreateQueuePath = StorageLocator.GetConversationCreateQueueFolder();
        private readonly string _conversationRecordCreateQueuePath = StorageLocator.GetConversationRecordCreateQueueFolder();
        private readonly string _conversationUpdateQueuePath = StorageLocator.GetConversationUpdateQueueFolder();
        private readonly string _conversationUnreadMessageQueuePath = StorageLocator.GetConversationUnreadMessageQueueFolder();
        private readonly string _conversationUpdateUnreadMessageQueuePath = StorageLocator.GetConversationUpdateUnreadMessageQueueFolder();

        private CancellationTokenSource _cancellationTokenSource;

        public MessageRepository(IIndex<SqliteDatabaseType, IEssSqliteWriterAdapter> writerAdapters, 
            IEssLogger essLogger, 
            IIndex<SqliteDatabaseType, IEssSqliteReaderAdapter> readerAdapters)
        {
            _essSqliteWriterPolldataAdapter = writerAdapters[SqliteDatabaseType.Polldata];
            _essSqliteReaderPolldataAdapter = readerAdapters[SqliteDatabaseType.Polldata];
            _essLogger = essLogger;

            Task.Run(async () => await StartQueue());
        }

        private async Task StartMessageRepoTaskAsync()
        {
            try
            {
                CreateAlertRecord(_cancellationTokenSource);

                await CreateConversationRecord(_cancellationTokenSource);

                CreateAlertsAndConversationRecord(_cancellationTokenSource);

                CreateConversationUpdateRecord(_cancellationTokenSource);

                CreateConversationUnreadMessageRecord(_cancellationTokenSource);

                CreateConversationUpdateUnreadMessageRecord(_cancellationTokenSource);
            }
            catch (Exception ex)
            {
                _essLogger.LogError(ex,
                    new Dictionary<string, string>
                        { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
            }
        }

        private void CreateConversationUpdateUnreadMessageRecord(CancellationTokenSource ct)
        {
            try
            {
                foreach (var conversationUpdateUnreadMessageFile in Directory.GetFiles(_conversationUpdateUnreadMessageQueuePath))
                {
                    var jsonContent = File.ReadAllText(conversationUpdateUnreadMessageFile).Trim();
                    try
                    {
                        JToken.Parse(jsonContent);
                    }
                    catch
                    {
                        _essLogger.LogInformation($"Invalid JSON content file found and deleted: {conversationUpdateUnreadMessageFile}");
                        File.Delete(conversationUpdateUnreadMessageFile);
                        continue;
                    }

                    var messageDto = JsonConvert.DeserializeObject<ConversationDto>(jsonContent);
                    var success = UpdateUnreadFlagForNewMessage(messageDto.Conversation_ID, messageDto.Sender, retry: true);
                    if (!success)
                        continue;

                    _essLogger.LogInformation($"Dequeuing conversation unread update, id = {messageDto.Conversation_ID}");
                    File.Delete(conversationUpdateUnreadMessageFile);
                    if (ct.IsCancellationRequested) break;
                }
            }
            catch (Exception ex)
            {
                _essLogger.LogError(ex,
                new Dictionary<string, string>
                    { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
            }
        }

        private void CreateConversationUnreadMessageRecord(CancellationTokenSource ct)
        {
            try
            {
                foreach (var conversationUnreadMessageFile in Directory.GetFiles(_conversationUnreadMessageQueuePath))
                {
                    var jsonContent = File.ReadAllText(conversationUnreadMessageFile).Trim();
                    try
                    {
                        JToken.Parse(jsonContent);
                    }
                    catch
                    {
                        _essLogger.LogInformation($"Invalid JSON content file found and deleted: {conversationUnreadMessageFile}");
                        File.Delete(conversationUnreadMessageFile);
                        continue;
                    }

                    var messageDto =
                        JsonConvert.DeserializeObject<ConversationDto>(jsonContent);
                    var success = SetMessagesRead(messageDto.Conversation_ID, retry: true);
                    if (!success)
                        continue;

                    _essLogger.LogInformation(
                        $"Dequeuing conversation unread message, id = {messageDto.Conversation_ID}");
                    File.Delete(conversationUnreadMessageFile);
                    if (ct.IsCancellationRequested) break;
                }
            }
            catch (Exception ex)
            {
                _essLogger.LogError(ex,
                    new Dictionary<string, string>
                        { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
            }
        }

        private void CreateConversationUpdateRecord(CancellationTokenSource ct)
        {
            try
            {
                foreach (var conversationUpdateFile in Directory.GetFiles(_conversationUpdateQueuePath))
                {
                    var jsonContent = File.ReadAllText(conversationUpdateFile).Trim();
                    try
                    {
                        JToken.Parse(jsonContent);
                    }
                    catch
                    {
                        _essLogger.LogInformation($"Invalid JSON content file found and deleted: {conversationUpdateFile}");
                        File.Delete(conversationUpdateFile);
                        continue;
                    }

                    var messageDto =
                        JsonConvert.DeserializeObject<InstantMessageDto>(jsonContent);
                    var success = AddMessageToConversation(messageDto.ConversationId, messageDto.MessageText,
                        messageDto.MessageSender, retry: true);
                    if (!success)
                        continue;

                    _essLogger.LogInformation($"Dequeuing conversation update, id = {messageDto.ConversationId}");
                    File.Delete(conversationUpdateFile);
                    if (ct.IsCancellationRequested) break;
                }
            }
            catch (Exception ex)
            {
                _essLogger.LogError(ex,
                    new Dictionary<string, string>
                        { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
            }
        }

        private void CreateAlertsAndConversationRecord(CancellationTokenSource ct)
        {
            try
            {
                foreach (var conversationRecordCreateFile in Directory.GetFiles(_conversationRecordCreateQueuePath))
                {
                    var jsonContent = File.ReadAllText(conversationRecordCreateFile).Trim();
                    try
                    {
                        JToken.Parse(jsonContent);
                    }
                    catch
                    {
                        _essLogger.LogInformation($"Invalid JSON content file found and deleted: {conversationRecordCreateFile}");
                        File.Delete(conversationRecordCreateFile);
                        continue;
                    }

                    var alertsAndConversationsDto =
                        JsonConvert.DeserializeObject<AlertsAndConversationsDto>(
                            jsonContent);
                    var success = CreateConversationRecordForNewConversation(alertsAndConversationsDto.Id,
                        alertsAndConversationsDto.Header, alertsAndConversationsDto.GroupId, retry: true);
                    if (!success)
                        continue;

                    _essLogger.LogInformation(
                        $"Dequeuing conversation record create, id = {alertsAndConversationsDto.Id}");
                    File.Delete(conversationRecordCreateFile);
                    if (ct.IsCancellationRequested) break;
                }
            }
            catch (Exception ex)
            {
                _essLogger.LogError(ex,
                    new Dictionary<string, string>
                        { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
            }
        }

        private async Task CreateConversationRecord(CancellationTokenSource ct)
        {
            try
            {
                foreach (var conversationCreateFile in Directory.GetFiles(_conversationCreateQueuePath))
                {
                    var jsonContent = File.ReadAllText(conversationCreateFile).Trim();
                    try
                    {
                        JToken.Parse(jsonContent);
                    }
                    catch
                    {
                        _essLogger.LogInformation($"Invalid JSON content file found and deleted: {conversationCreateFile}");
                        File.Delete(conversationCreateFile);
                        continue;
                    }

                    var messageDto =
                        JsonConvert.DeserializeObject<InstantMessageDto>(jsonContent);
                    var success = await CreateNewConversation(messageDto.ConversationId, messageDto.Group,
                        messageDto.MessageText, messageDto.MessageSender, retry: true);
                    if (!success)
                        continue;

                    _essLogger.LogInformation($"Dequeuing conversation create, id = {messageDto.ConversationId}");
                    File.Delete(conversationCreateFile);
                    if (ct.IsCancellationRequested) break;
                }
            }
            catch (Exception ex)
            {
                _essLogger.LogError(ex,
                    new Dictionary<string, string>
                        { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
            }
        }

        private void CreateAlertRecord(CancellationTokenSource ct)
        {
            try
            {
                foreach (var alertCreateFile in Directory.GetFiles(_alertCreateQueuePath))
                {
                    var jsonContent = File.ReadAllText(alertCreateFile).Trim();
                    try
                    {
                        JToken.Parse(jsonContent);
                    }
                    catch
                    {
                        _essLogger.LogInformation($"Invalid JSON content file found and deleted: {alertCreateFile}");
                        File.Delete(alertCreateFile);
                        continue;
                    }

                    var messageDto =
                        JsonConvert.DeserializeObject<InstantMessageDto>(jsonContent);
                    var success = CreateNewAlert(messageDto.MessageText, retry: true);
                    if (!success)
                        continue;

                    _essLogger.LogInformation("Dequeuing alert");
                    File.Delete(alertCreateFile);
                    if (ct.IsCancellationRequested) break;
                }
            }
            catch (Exception ex)
            {
                _essLogger.LogError(ex,
                    new Dictionary<string, string>
                        { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
            }
        }


        public void CleanConversationQueues()
        {
	        bool restart = CancelQueue();

	        _alertCreateQueuePath.EmptyPath();
	        _conversationCreateQueuePath.EmptyPath();
	        _conversationRecordCreateQueuePath.EmptyPath();
	        _conversationUpdateQueuePath.EmptyPath();
	        _conversationUnreadMessageQueuePath.EmptyPath();
	        _conversationUpdateUnreadMessageQueuePath.EmptyPath();

	        if (restart)
                Task.Run(async () => await StartQueue());
        }

        /// <summary>
        /// Copies the queues to the root destination
        /// </summary>
        public void CopyConversationQueues(string rootDestination, bool createDestination = true)
        {
	        if (string.IsNullOrWhiteSpace(rootDestination))
		        return;

	        if (createDestination && !rootDestination.CreateDir())
		        return;

	        bool restart = CancelQueue();

	        _alertCreateQueuePath.CopyPath(Path.Combine(rootDestination, StorageLocator.ALERT_CREATE_QUEUE_FOLDER), createDestination);
	        _conversationCreateQueuePath.CopyPath(Path.Combine(rootDestination, StorageLocator.CONVERSATION_CREATE_QUEUE_FOLDER), createDestination);
	        _conversationRecordCreateQueuePath.CopyPath(Path.Combine(rootDestination, StorageLocator.CONVERSATION_RECORD_CREATE_QUEUE_FOLDER), createDestination);
	        _conversationUpdateQueuePath.CopyPath(Path.Combine(rootDestination, StorageLocator.CONVERSATION_UPDATE_QUEUE_FOLDER), createDestination);
	        _conversationUnreadMessageQueuePath.CopyPath(Path.Combine(rootDestination, StorageLocator.CONVERSATION_UNREAD_MESSAGE_QUEUE_FOLDER), createDestination);
	        _conversationUpdateUnreadMessageQueuePath.CopyPath(Path.Combine(rootDestination, StorageLocator.CONVERSATION_UPDATE_UNREAD_MESSAGE_QUEUE_FOLDER), createDestination);

	        if (restart)
		        Task.Run(async () => await StartQueue());
        }


        public async Task<IEnumerable<MessageGroupDto>> GetAllMessageGroups()
        {
	        const string sql = @"SELECT Message_Group_ID AS MessageGroupId,
                                  Message_Group_Name as MessageGroupName,
                                  Message_Group_Description as MessageGroupDescription
                           FROM Messaging_Message_Group;";

            return await _essSqliteReaderPolldataAdapter.QueryAsync<MessageGroupDto>(sql);
        }

        public async Task<int> GetConversationCount()
        {
            const string sql = @"SELECT COUNT(*)
                           FROM Messaging_Conversation
                           WHERE Conversation_Has_Unread_Messages = 1;";

            return await _essSqliteReaderPolldataAdapter.ExecuteScalarAsync<int>(sql);
        }

        public async Task<IEnumerable<AlertsAndConversationsDto>> GetAlertsAndConversations()
        {
            const string sqlConversations = @"SELECT Conversation_ID AS Id,
                                  Conversation_Datetime as StartDatetime,
                                  Conversation_Group_ID as GroupId,
                                  Conversation_Header as Header,
                                  false as IsBroadcast,
                                  Conversation_Has_Unread_Messages as HasUnreadMessage
                           FROM Messaging_Conversation;";

            var conversations = await _essSqliteReaderPolldataAdapter.QueryAsync<AlertsAndConversationsDto>(sqlConversations);

            const string sqlAlerts = @"SELECT Message_ID AS Id,
                                  Message_Sent_Datetime as StartDatetime,
                                  0 as GroupId,
                                  Message_Text as Header,
                                  true as IsBroadcast,
                                  false as HasUnreadMessage
                           FROM Messaging_Message
                           WHERE Conversation_ID = 0;";

            var alerts = await _essSqliteReaderPolldataAdapter.QueryAsync<AlertsAndConversationsDto>(sqlAlerts);

            var alertsAndConversations = new List<AlertsAndConversationsDto>();
            alertsAndConversations.AddRange(alerts);
            alertsAndConversations.AddRange(conversations);
            return alertsAndConversations.OrderByDescending(t => t.StartDatetime);
        }

        public async Task<InstantMessageDto> GetAlertMessage(int messageId)
        {
            const string sql = @"SELECT Message_ID AS MessageId,
                                  Message_Text AS MessageText,
                                  Message_Sender as MessageSender,
                                  Message_Sent_Datetime as SentDatetime
                           FROM Messaging_Message
                           WHERE Message_ID = @MessageId;";

            return await _essSqliteReaderPolldataAdapter.QueryFirstOrDefaultAsync<InstantMessageDto>(sql, new { messageId });
        }

        public async Task<IEnumerable<InstantMessageDto>> GetMessagesInConversation(int conversationId)
        {
            const string sql = @"SELECT Message_ID AS MessageId,
                                  Message_Text AS MessageText,
                                  Message_Sender as MessageSender,
                                  Message_Sent_Datetime as SentDatetime
                           FROM Messaging_Message
                           WHERE Conversation_ID = @ConversationId;";

            return await _essSqliteReaderPolldataAdapter.QueryAsync<InstantMessageDto>(sql, new { conversationId });
        }

        public bool AddMessageToConversation(int conversationId, string message, string sender, bool retry = false)
        {
            const string sql = @"INSERT INTO Messaging_Message
                           (Message_Text, Conversation_ID, Message_Sender, Message_Sent_Datetime, Record_Update_UTC_Datetime, Record_Update_User_Name, Record_Update_Application_Datetime, Record_Update_Application_User_Name)
                           VALUES
                           (@MessageText, @ConversationId, @MessageSender, @SentDatetime, @UpdateUTCDatetime, @UpdateUserName, @UpdateApplicationDatetime, @UpdateApplicationUserName)";

            try
            {
                _essSqliteWriterPolldataAdapter.ExecuteNonQuery(sql, new
                {
                    MessageText = message,
                    ConversationId = conversationId,
                    Sender = sender,
                    SentDatetime = DateTime.Now,
                    UpdateUTCDatetime = DateTime.UtcNow,
                    UpdateUserName = CurrentUserInfo.LoggedInUser.Username,
                    UpdateApplicationDatetime = DateTime.Now,
                    UpdateApplicationUserName = CurrentUserInfo.LoggedInUser.Username
                });
            }
            catch (EssSqlException ex)
            {
                if (IsDatabaseBusy(ex))
                {
                    if (!retry)
                    {
                        _essLogger.LogInformation($"Database is busy, enqueuing message for conversation id = {conversationId}");
                        DateTime currentDateTime = DateTime.Now;
                        string filename = $"{conversationId}-{currentDateTime.ToString("yyyyMMddHHmmss")}";
                        var dto = new InstantMessageDto
                        {
                            MessageText = message,
                            ConversationId = conversationId,
                            MessageSender = sender,
                            SentDatetime = DateTime.Now
                        };
                        File.WriteAllText(
                            path: Path.Combine(_conversationUpdateQueuePath, filename),
                            contents: JsonConvert.SerializeObject(dto)
                        );
                    }
                }
                else
                {
                    _essLogger.LogError(ex,
                    new Dictionary<string, string>
                        { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
                }

                return false;
            }

            // If I am the sender, then I don't want to show the message as unread. Otherwise, I do want to show it as unread.
            return UpdateUnreadFlagForNewMessage(conversationId, sender);
        }

        private bool UpdateUnreadFlagForNewMessage(int conversationId, string sender, bool retry = false)
        {
            int hasUnreadMessages = sender == SystemDetails.MachineName ? 0 : 1;

            const string sql2 = @"UPDATE Messaging_Conversation 
                           SET Conversation_Has_Unread_Messages = @HasUnreadMessages, Conversation_Datetime = @Conversation_Datetime
                           WHERE Conversation_ID = @ConversationID";

            try
            {
                _essSqliteWriterPolldataAdapter.ExecuteNonQuery(sql2, new { hasUnreadMessages, DateTime.Now, conversationId });
            }
            catch (EssSqlException ex)
            {
                if (IsDatabaseBusy(ex))
                {
                    if (!retry)
                    {
                        _essLogger.LogInformation($"Database is busy, enqueuing update of unread flag for conversation id = {conversationId}");
                        DateTime currentDateTime = DateTime.Now;
                        string filename = $"{conversationId}-{currentDateTime.ToString("yyyyMMddHHmmss")}";
                        var dto = new ConversationDto
                        {
                            Conversation_ID = conversationId,
                            Conversation_Has_Unread_Messages = hasUnreadMessages,
                            Conversation_Datetime = DateTime.Now,
                            Sender = sender
                        };
                        File.WriteAllText(
                            path: Path.Combine(_conversationUpdateUnreadMessageQueuePath, filename),
                            contents: JsonConvert.SerializeObject(dto)
                        );
                    }
                }
                else
                {
                    _essLogger.LogError(ex,
                        new Dictionary<string, string>
                            { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
                }

                return false;
            }
            return true;
        }

        public bool CreateNewAlert(string message, bool retry = false)
        {
            const string sql = @"INSERT INTO Messaging_Message
                           (Message_Text, Conversation_ID, Message_Sender, Message_Sent_Datetime, Record_Update_UTC_Datetime, Record_Update_User_Name, Record_Update_Application_Datetime, Record_Update_Application_User_Name)
                           VALUES
                           (@MessageText, @ConversationId, @MessageSender, @SentDatetime, @UpdateUTCDatetime, @UpdateUserName, @UpdateApplicationDatetime, @UpdateApplicationUserName)";

            try
            {
                _essSqliteWriterPolldataAdapter.ExecuteNonQuery(sql, new
                {
                    MessageText = message,
                    ConversationId = 0,
                    Sender = string.Empty,
                    SentDatetime = DateTime.Now,
                    UpdateUTCDatetime = DateTime.UtcNow,
                    UpdateUserName = CurrentUserInfo.LoggedInUser.Username,
                    UpdateApplicationDatetime = DateTime.Now,
                    UpdateApplicationUserName = CurrentUserInfo.LoggedInUser.Username
                });
            }
            catch (EssSqlException ex)
            {
                if (IsDatabaseBusy(ex))
                {
                    if (!retry)
                    {
                        _essLogger.LogInformation("Database is busy, enqueuing alert");
                        DateTime currentDateTime = DateTime.Now;
                        string filename = $"{currentDateTime.ToString("yyyyMMddHHmmss")}";
                        var dto = new InstantMessageDto
                        {
                            MessageText = message,
                            ConversationId = 0,
                            MessageSender = string.Empty,
                            SentDatetime = DateTime.Now
                        };
                        File.WriteAllText(
                            path: Path.Combine(_alertCreateQueuePath, filename),
                            contents: JsonConvert.SerializeObject(dto)
                        );
                    }
                }
                else
                {
                    _essLogger.LogError(ex,
                        new Dictionary<string, string>
                            { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
                }

                return false;
            }
            return true;
        }

        public async Task<bool> CreateNewConversation(int conversationId, string group, string message, string sender, bool retry = false)
        {
            const string sql = @"INSERT INTO Messaging_Message
                           (Message_Text, Conversation_ID, Message_Sender, Message_Sent_Datetime, Record_Update_UTC_Datetime, Record_Update_User_Name, Record_Update_Application_Datetime, Record_Update_Application_User_Name)
                           VALUES
                           (@MessageText, @ConversationId, @MessageSender, @SentDatetime, @UpdateUTCDatetime, @UpdateUserName, @UpdateApplicationDatetime, @UpdateApplicationUserName)";


            try
            {
                _essSqliteWriterPolldataAdapter.ExecuteNonQuery(sql, new
                {
                    MessageText = message,
                    ConversationId = conversationId,
                    Sender = sender,
                    SentDatetime = DateTime.Now,
                    UpdateUTCDatetime = DateTime.UtcNow,
                    UpdateUserName = CurrentUserInfo.LoggedInUser.Username,
                    UpdateApplicationDatetime = DateTime.Now,
                    UpdateApplicationUserName = CurrentUserInfo.LoggedInUser.Username
                });
            }
            catch (EssSqlException ex)
            {
                if (IsDatabaseBusy(ex))
                {
                    if (!retry)
                    {
                        _essLogger.LogInformation($"Database is busy, enqueuing new conversation ID = {conversationId}");
                        DateTime currentDateTime = DateTime.Now;
                        string filename = $"{conversationId}-{currentDateTime.ToString("yyyyMMddHHmmss")}";
                        var dto = new InstantMessageDto
                        {
                            MessageText = message,
                            ConversationId = conversationId,
                            MessageSender = sender,
                            Group = group,
                            SentDatetime = DateTime.Now
                        };
                        File.WriteAllText(
                            path: Path.Combine(_conversationCreateQueuePath, filename),
                            contents: JsonConvert.SerializeObject(dto)
                        );
                    }
                }
                else
                {
                    _essLogger.LogError(ex,
                        new Dictionary<string, string>
                            { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
                }

                return false;
            }

            int groupId = await GetGroupIdForGroupName(group);

            return CreateConversationRecordForNewConversation(conversationId, message, groupId);
        }

        private bool CreateConversationRecordForNewConversation(int conversationId, string message, int groupId, bool retry = false)
        {
            const string sql = @"INSERT INTO Messaging_Conversation
                            (Conversation_ID, Conversation_Header, Conversation_Group_ID, Conversation_Is_Broadcast, Conversation_Datetime, Conversation_Has_Unread_Messages, Record_Update_UTC_Datetime, Record_Update_User_Name, Record_Update_Application_Datetime, Record_Update_Application_User_Name)
                            VALUES
                            (@Id, @Header, @GroupId, @IsBroadcast, @StartDatetime, @HasUnreadMessage, @UpdateUTCDatetime, @UpdateUserName, @UpdateApplicationDatetime, @UpdateApplicationUserName)";

            try
            {
                _essSqliteWriterPolldataAdapter.ExecuteNonQuery(sql, new
                {
                    ConversationID = conversationId,
                    Header = HeaderFromMessage(message),
                    ConversationGroupId = groupId,
                    IsBroadcast = false,
                    DateTime.Now,
                    Unread = false,
                    UpdateUTCDatetime = DateTime.UtcNow,
                    UpdateUserName = CurrentUserInfo.LoggedInUser.Username,
                    UpdateApplicationDatetime = DateTime.Now,
                    UpdateApplicationUserName = CurrentUserInfo.LoggedInUser.Username
                });
            }
            catch (EssSqlException ex)
            {
                if (IsDatabaseBusy(ex))
                {
                    if (!retry)
                    {
                        _essLogger.LogInformation($"Database is busy, enqueuing new conversation record for conversation ID = {conversationId}");
                        DateTime currentDateTime = DateTime.Now;
                        string filename = $"{conversationId}-{currentDateTime.ToString("yyyyMMddHHmmss")}";
                        var dto = new AlertsAndConversationsDto
                        {
                            Id = conversationId,
                            Header = HeaderFromMessage(message),
                            GroupId = groupId,
                            IsBroadcast = false,
                            StartDatetime = DateTime.Now,
                            HasUnreadMessage = false
                        };
                        File.WriteAllText(
                            path: Path.Combine(_conversationRecordCreateQueuePath, filename),
                            contents: JsonConvert.SerializeObject(dto)
                        );
                    }
                }
                else
                {
                    _essLogger.LogError(ex,
                        new Dictionary<string, string>
                            { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
                }

                return false;
            }
            return true;
        }

        public bool SetMessagesRead(int conversationId, bool retry = false)
        {
            const string sql = @"UPDATE Messaging_Conversation 
                           SET Conversation_Has_Unread_Messages = 0 
                           WHERE Conversation_ID = @ConversationId";
            try
            {
                _essSqliteWriterPolldataAdapter.ExecuteNonQuery(sql, new { conversationId });
            }
            catch (EssSqlException ex)
            {
                if (IsDatabaseBusy(ex))
                {
                    if (!retry)
                    {
                        _essLogger.LogInformation($"Database is busy, unread message update for conversation ID = {conversationId}");
                        DateTime currentDateTime = DateTime.Now;
                        string filename = $"{conversationId}-{currentDateTime.ToString("yyyyMMddHHmmss")}";
                        File.WriteAllText(
                            path: Path.Combine(_conversationUnreadMessageQueuePath, filename),
                            contents: JsonConvert.SerializeObject(new ConversationDto { Conversation_ID = conversationId })
                        );
                    }
                }
                else
                {
                    _essLogger.LogError(ex,
                        new Dictionary<string, string>
                            { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
                }

                return false;
            }
            return true;
        }

        public async Task<string> GetGroupNameforConversation(int conversationId)
        {
            string sql = @"SELECT Message_Group_Name from Messaging_Message_Group mg
                           INNER JOIN Messaging_Conversation c ON mg.Message_Group_ID = c.Conversation_Group_ID
                           WHERE c.Conversation_ID = @ConversationId";
            return await _essSqliteReaderPolldataAdapter.ExecuteScalarAsync<string>(sql, new ConversationDto { Conversation_ID = conversationId });
        }

        public async Task<int> GetGroupIdForGroupName(string groupName)
        {
            const string sql = @"SELECT Message_Group_ID
                            FROM Messaging_Message_Group 
                            WHERE Message_Group_Name = @MessageGroupName";
            return await _essSqliteReaderPolldataAdapter.ExecuteScalarAsync<int>(sql, new { MessageGroupName = groupName });
        }

        private string HeaderFromMessage(string message)
        {
            if (message.Length > 50)
            {
                return message.Substring(0, 50);
            }
            return message;
        }

        private bool IsDatabaseBusy(EssSqlException ex)
        {
            return ex.Message.Contains("because Busy");
        }

        //Left public for future cancel control
        public bool CancelQueue()
        {
	        try
	        {
                _cancellationTokenSource.Cancel();
                return true;
            }
	        catch (Exception ex)
	        {
		        _essLogger.LogError(ex, new Dictionary<string, string> { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
                return false;
            }
        }

        private Task StartQueue()
        {
                _cancellationTokenSource = new CancellationTokenSource();
                return PeriodicTask.Run(StartMessageRepoTaskAsync, new TimeSpan(0,0,5), _cancellationTokenSource.Token);
        }
    }

	public class ConversationDto
    {
        public int Conversation_ID { get; set; }

        public DateTime Conversation_Datetime { get; set; }

        public int Conversation_Has_Unread_Messages { get; set; }

        public string Sender { get; set; }
    }
}
