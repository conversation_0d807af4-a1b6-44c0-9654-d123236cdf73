using Autofac.Features.Indexed;
using ESS.Pollbook.DataAccess.Interfaces;
using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Model.Configuration;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Core.UICore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace ESS.Pollbook.Components.Repository.Configuration
{
    public class PollbookConfigurationRepository : IPollbookConfigurationRepository
    {
        private readonly IEssSqliteWriterAdapter _essSqliteWriterPolldataAdapter;
        private readonly IEssSqliteReaderAdapter _essSqliteReaderPolldataAdapter;

        private PollbookConfigurationDto _configuration = null;
        private IEnumerable<SqliteConfigurationDto> _rawSqlliteConfiguration = null;

        public PollbookConfigurationRepository(IIndex<SqliteDatabaseType, IEssSqliteWriterAdapter> writerAdapters,
            IIndex<SqliteDatabaseType, IEssSqliteReaderAdapter> readerAdapters)
        {
            _essSqliteWriterPolldataAdapter = writerAdapters[SqliteDatabaseType.Polldata];
            _essSqliteReaderPolldataAdapter = readerAdapters[SqliteDatabaseType.Polldata];
            Init();
        }

        public PollbookConfigurationDto GetPollbookConfiguration()
        {
            if (_configuration == null)
            {
                Task.Run(async () => await LoadConfigurationAsync()).Wait();
            }
            return _configuration;
        }

        public async Task RefreshAsync(bool forceRefresh = false)
        {
            await LoadConfigurationAsync(forceRefresh);
        }

        private async Task Init()
        {
            await RefreshAsync();
        }

        /// <summary>
        /// Load the configuration
        /// </summary>
        /// <param name="forceLoad"></param>
        /// <returns></returns>
        /// <comment>If the configuration has been already loaded and caller doesn't want to load again forcefully,
        /// don't hit the database again.
        /// </comment>
        private async Task LoadConfigurationAsync(bool forceLoad = false)
        {
            if (!SystemDetails.IsPQCVerified)
                return;

            if (_configuration != null && !forceLoad)
            {
                return;
            }

            const string sql = @"SELECT Election_Configuration_Category_Name AS CategoryName,
                                        Election_Configuration_Name AS Name, 
                                        Election_Configuration_Value AS RawValue,
                                        Election_Configuration_Value_Data_Type_Name AS DataTypeName, 
                                        Election_Configuration_Common_Enumeration_Value_ID AS CommonEnumerationValueId    
                                   FROM Election_Election_Configuration";

            _rawSqlliteConfiguration = await _essSqliteReaderPolldataAdapter.QueryAsync<SqliteConfigurationDto>(sql);

            var rawConfiguration = _rawSqlliteConfiguration.ToList<ConfigurationDto>();

            _configuration = rawConfiguration.ConvertFromConfigurationList<PollbookConfigurationDto>();
        }

        public async Task<SqliteConfigurationDto> GetPollbookConfigurationByName(string configName)
        {
            if (!SystemDetails.IsPQCVerified)
            {
                return null;
            }

            if (_rawSqlliteConfiguration != null)
            {
                SqliteConfigurationDto result = _rawSqlliteConfiguration.FirstOrDefault(rs => rs.Name == configName);
                if (result != null)
                {
                    return result;
                }
            }

            const string sql = @"SELECT TOP 1 Election_Configuration_Category_Name AS CategoryName,
                               Election_Configuration_Name AS Name,
                               Election_Configuration_Value AS RawValue,
                               Election_Configuration_Value_Data_Type_Name AS DataTypeName, 
                               Election_Configuration_Common_Enumeration_Value_ID AS CommonEnumerationValueId    
                        FROM Election_Election_Configuration
                        WHERE Election_Configuration_Name = @configName";

            var results = await _essSqliteReaderPolldataAdapter.QueryFirstOrDefaultAsync<SqliteConfigurationDto>(sql,
                new
                {
                    configName = configName
                });

            return results;
        }

        public void UpdateConfig(string name, string value)
        {
            const string sql = @"UPDATE Election_Election_Configuration 
                        SET Election_Configuration_Value = @Election_Configuration_Value
                        WHERE Election_Configuration_Name = @Election_Configuration_Name";

            _essSqliteWriterPolldataAdapter.ExecuteNonQuery(sql,
                 new
                 {
                     Election_Configuration_Value = value,
                     Election_Configuration_Name = name
                 });
        }

        public void UpdatePrinterSettings(PollbookConfigurationDto configuration)
        {
            _configuration.ExpressPollPrinters = configuration.ExpressPollPrinters;
            _configuration.BODPrinters = configuration.BODPrinters;
            _configuration.SelectedExpressPollPrinterId = configuration.SelectedExpressPollPrinterId;
            _configuration.SelectedBODPrinterId = configuration.SelectedBODPrinterId;
        }

        public async Task<IEnumerable<JSONTableAliasDto>> GetJsonTableAliasDetails()
        {
            const string sql = @"SELECT SQLServer_Schema AS SQLServerSchema,
	                                SQLServer_Table AS SQLServerTable,
	                                SQLServer_Field AS SQLServerField,
	                                SQLite_Table AS SQLiteTable,
	                                SQLite_Field AS SQLiteField,
	                                JSONTableAlias,
	                                JSONFieldAlias,
	                                UpdateField AS IsUpdateField,
	                                PK AS IsPK,
	                                InsertRecord AS IsInsertRecord
                        FROM Election_JSONTableAlias";

            var results = await _essSqliteReaderPolldataAdapter.QueryAsync<JSONTableAliasDto>(sql);

            return results;
        }

        public void UpsertConfigurations(string tableName, string keyField, List<string> lstColumns, List<List<object>> values, bool isNewConfig = false)
        {
            string insColumns = keyField;
            string insValues = $"@{keyField}";
            string setStr = string.Empty;
            foreach (var item in lstColumns)
            {
                insColumns = $"{insColumns}, {item}";
                insValues = $"{insValues}, @'{item}'";
                setStr = (string.IsNullOrEmpty(setStr) ? setStr : setStr + ",") + $"{item} = @{item}";
            }
            string sql = isNewConfig ? $"INSERT INTO {tableName}({insColumns}) VALUES({insValues})" :
                                        $"UPDATE {tableName} SET {setStr} WHERE {keyField} = @{keyField}";

            _essSqliteWriterPolldataAdapter.BulkInsert(sql, values);
        }

        public void ResetPrinterTableBasedOnConfigUpdates(string printerTypes)
        {
            const string sql = @"UPDATE Election_PollbookPrinter 
                        SET SelectedExpressPollPrinter = 0,
                            SelectedBODPrinter = 0
                        WHERE BOD_Printer in (@BOD_Printer)";

            _essSqliteWriterPolldataAdapter.ExecuteNonQuery(sql,
                new
                {
                    BOD_Printer = printerTypes
                });
        }
    }

    public class SqliteConfigurationDto : ConfigurationDto
    {
        public string CategoryName { get; set; }
        public string DataTypeName { get; set; }
        public override string Name { get; set; }
        public override string RawValue { get; set; }
        public override int? CommonEnumerationValueId { get; set; }

        public override ConfigurationCategory Category
        {
            get
            {
                if (Enum.TryParse(CategoryName, out ConfigurationCategory result))
                {
                    return result;
                }

                return ConfigurationCategory.Unknown;
            }
        }

        public override Type DataType
        {
            get { return TypeConversionHelper.GetTypeWithFuzzyLogic(DataTypeName); }
        }
    }
}
