using ESS.ELLEGO.Rest;
using ESS.Pollbook.Core.Model;
using System.Threading.Tasks;

namespace ESS.Pollbook.Components.Repository.SyncPointRepository
{
    public interface IPollworkerSyncRepository
    {
        Task<int> SubmitTransactionRequestAsync(EssRestRequest request);
        Task<PollWorkerDetailsResponse> GetPollWorkerDetailsFromHost(EssRestRequest essRestRequest);
        Task<PollworkerSearchResponse> GetPollworkerSearchResultsFromHost(EssRestRequest essRestRequest);
    }
}
