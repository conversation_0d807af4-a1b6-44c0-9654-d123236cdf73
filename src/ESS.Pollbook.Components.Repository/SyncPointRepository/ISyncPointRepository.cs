using ESS.ELLEGO.Rest;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Model;
using ESS.Pollbook.Core.Model.HelpCenter;
using ESS.Pollbook.Core.Model.Transactions;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ESS.Pollbook.Components.Repository.SyncPointRepository
{
	public interface ISyncPointRepository
	{
		/// <summary>
		/// Generic that can be used instead of calls below.
		/// </summary>
		/// <typeparam name="T"></typeparam>
		/// <param name="essRestRequest"></param>
		/// <returns></returns>
		Task<T> MakePostToHostAsync<T>(EssRestRequest essRestRequest);

		Task<TransactionBatchResponse> SubmitTransactionRequestAsync(EssRestRequest essRestRequest);

		Task<List<TransactionModel>> GetVoterTransactionsHistoryFromHostAsync(EssRestRequest essRestRequest);

		Task<TransactionCdnModel> GetVoterTransactionsFromHostAsync(EssRestRequest essRestRequest);

		Task<List<VoterDto>> GetVoterSearchResultsFromHostAsync(EssRestRequest essRestRequest);

		Task<VoterDto> GetVoterDetailsFromHostAsync(EssRestRequest essRestRequest);

        Task<TotalTransactionCountResponse> GetTransactionCountFromHost(EssRestRequest essRestRequest);

        Task<User> GetUsersFromHostAsync(EssRestRequest essRestRequest);

		Task<Help> GetHelpFromHostAsync(EssRestRequest essRestRequest);

		Task<byte[]> GetRegionalResultFilesFromHostAsync(EssRestRequest essRestRequest);

		Task<HelpFileCdnResponse> GetHelpCdnUrisFromHostAsync(EssRestRequest essRestRequest, string stylesheetFilename);

		Task<List<string>> GetCdnUrisFromHostAsync(EssRestRequest essRestRequest);

		Task<bool> SubmitFailedTransactionAsync(EssRestRequest essRestRequest);

		//Pollworker related calls
		Task<PollworkerSearchResponse> GetPollworkerSearchResultsFromHostAsync(EssRestRequest essRestRequest);

		Task<PollWorkerDetailsResponse> GetPollWorkerDetailsFromHostAsync(EssRestRequest essRestRequest);

		Task<TimeCardsResponse> GetTimeCardsFromHostAsync(EssRestRequest essRestRequest);

		Task<List<PollworkerTransactionDto>> GetPollworkerTransactionsFromHostAsync(EssRestRequest essRestRequest);

		Task<DownloadConfigurationsDto> GetConfigurationsUpdateFromHostAsync(EssRestRequest essRestRequest);

		Task<bool> UpdateConfigDownloadedLogToHostAsync(EssRestRequest essRestRequest);

		Task<DownloadConfigurationsDto> GetPollWorkerConfigurationsUpdateFromHostAsync(EssRestRequest essRestRequest);

		Task<bool> UpdatePollWorkerConfigDownloadedLogToHostAsync(EssRestRequest essRestRequest);

        Task<DevicePollPlaceResponse> GetDevicePollPlaceFromHostAsync(EssRestRequest essRestRequest);

    }
}