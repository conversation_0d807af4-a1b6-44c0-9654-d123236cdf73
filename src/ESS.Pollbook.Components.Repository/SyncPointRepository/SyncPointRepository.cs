using ESS.ELLEGO.Rest;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.Model;
using ESS.Pollbook.Core.Model.HelpCenter;
using ESS.Pollbook.Core.Model.Transactions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using ESS.Pollbook.Core.StaticValues;

namespace ESS.Pollbook.Components.Repository.SyncPointRepository
{
	public class SyncPointRepository : ISyncPointRepository
	{
		private readonly IEssRestExecutor _restHelper;
		private readonly IEssLogger _logger;

		public SyncPointRepository(IEssRestExecutor restHelper, IEssLogger logger)
		{
			_restHelper = restHelper;
			_logger = logger;
		}

		/// <summary>
		/// Generic that can be used for most of the call below.
		/// </summary>
		public async Task<T> MakePostToHostAsync<T>(EssRestRequest essRestRequest)
		{
			T response;
			try
			{
				response = await _restHelper.PostAsync<T>(essRestRequest);
			}
			catch (Exception ex)
			{
				LogConnectivityException(essRestRequest, ex,
					GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name + " failed.");
				return default;
			}

			return response;
		}

		/// <summary>
		/// POST transaction to SyncPoint.
		/// </summary>
		public async Task<TransactionBatchResponse> SubmitTransactionRequestAsync(EssRestRequest essRestRequest)
		{
			_logger.LogDebug("Entered SubmitTransactionRequestAsync() - Sending request...");

			var result = await _restHelper.PostAsync<TransactionBatchResponse>(essRestRequest);

			_logger.LogDebug("SubmitTransactionRequestAsync() - Response received");

			return result;
		}

		public async Task<List<VoterDto>> GetVoterSearchResultsFromHostAsync(EssRestRequest essRestRequest)
		{
			List<VoterDto> result = null;
			try
			{
				result = await _restHelper.PostAsync<List<VoterDto>>(essRestRequest);
			}
			catch (Exception ex)
			{
				LogConnectivityException(essRestRequest, ex, " Voter Search Results from the host");
			}

			return result;
		}

		public async Task<PollworkerSearchResponse> GetPollworkerSearchResultsFromHostAsync(
			EssRestRequest essRestRequest)
		{
			PollworkerSearchResponse result = null;
			try
			{
				_logger.LogInformation("Sending the request to the host");
				result = await _restHelper.PostAsync<PollworkerSearchResponse>(essRestRequest);
				_logger.LogInformation("Received the response from the host");
			}
			catch (Exception ex)
			{
				LogConnectivityException(essRestRequest, ex, " Pollworker Search Results from the host");
			}

			return result;
		}

		public async Task<PollWorkerDetailsResponse> GetPollWorkerDetailsFromHostAsync(EssRestRequest essRestRequest)
		{
			PollWorkerDetailsResponse result = null;
			try
			{
				result = await _restHelper.PostAsync<PollWorkerDetailsResponse>(essRestRequest);
			}
			catch (Exception ex)
			{
				LogConnectivityException(essRestRequest, ex, " Get Pollworker Details Results from the host");
			}

			return result;
		}


		public async Task<TimeCardsResponse> GetTimeCardsFromHostAsync(EssRestRequest essRestRequest)
		{
			TimeCardsResponse result = null;
			try
			{
				_logger.LogInformation("Sending the request to the host");
				result = await _restHelper.PostAsync<TimeCardsResponse>(essRestRequest);
				_logger.LogInformation("Received the response from the host");
			}
			catch (Exception ex)
			{
				LogConnectivityException(essRestRequest, ex, " Get Pollworker Details Results from the host");
			}

			return result;
		}

		public async Task<List<PollworkerTransactionDto>> GetPollworkerTransactionsFromHostAsync(
			EssRestRequest essRestRequest)
		{
			List<PollworkerTransactionDto> result = null;
			try
			{
				result = await _restHelper.PostAsync<List<PollworkerTransactionDto>>(essRestRequest);
			}
			catch (Exception ex)
			{
				LogConnectivityException(essRestRequest, ex, " Get Pollworker batch transactions from the host");
			}

			return result;
		}

		public async Task<VoterDto> GetVoterDetailsFromHostAsync(EssRestRequest essRestRequest)
		{
			VoterDto result = null;
			try
			{
				var voterList = await _restHelper.PostAsync<List<VoterDto>>(essRestRequest) ?? new List<VoterDto>();
				if (voterList.Count > 0)
				{
					result = voterList.FirstOrDefault();
				}
			}
			catch (Exception ex)
			{
				LogConnectivityException(essRestRequest, ex, " Voter Details from the host");
			}

			return result;
		}

        public async Task<TotalTransactionCountResponse> GetTransactionCountFromHost(EssRestRequest essRestRequest)
        {
	        TotalTransactionCountResponse result = null;
            try
            {
                var transactionTotalCount = await _restHelper.PostAsync<TotalTransactionCountResponse>(essRestRequest);
                result = transactionTotalCount;
            }
            catch (Exception ex)
            {
                LogConnectivityException(essRestRequest, ex, " Total Transaction Count from host");
            }

            return result;
        }

        public async Task<DevicePollPlaceResponse> GetDevicePollPlaceFromHostAsync(EssRestRequest essRestRequest)
        {
            DevicePollPlaceResponse result = new DevicePollPlaceResponse();
            try
            {
                var devicePollPlaceResponse = await _restHelper.PostAsync<DevicePollPlaceResponse>(essRestRequest);
                result = devicePollPlaceResponse;
            }
            catch (Exception ex)
            {
                LogConnectivityException(essRestRequest, ex, " Polling Place from host");
            }

            return result;
        }

        public async Task<List<TransactionModel>> GetVoterTransactionsHistoryFromHostAsync(
			EssRestRequest essRestRequest)
		{
			List<TransactionModel> result = null;
			try
			{
				result = await _restHelper.PostAsync<List<TransactionModel>>(essRestRequest);
			}
			catch (Exception ex)
			{
				LogConnectivityException(essRestRequest, ex, " Getting the voter statuses from the host");
			}

			return result;
		}

		public async Task<TransactionCdnModel> GetVoterTransactionsFromHostAsync(EssRestRequest essRestRequest)
		{
			var result = new TransactionCdnModel();
			try
			{
				result = await _restHelper.PostAsync<TransactionCdnModel>(essRestRequest);
			}
			catch (Exception ex)
			{
				LogConnectivityException(essRestRequest, ex, " Getting the voter statuses from the host");
			}

			return result;
		}

		public async Task<User> GetUsersFromHostAsync(EssRestRequest essRestRequest)
        {
            if (!SystemDetails.IsHostConnected)
                return null;
			User result = null;
			try
			{
				result = await _restHelper.PostAsync<User>(essRestRequest);
			}
			catch (Exception ex)
			{
				LogConnectivityException(essRestRequest, ex, " Getting the users from the host");
			}

			return result;
		}

		public async Task<Help> GetHelpFromHostAsync(EssRestRequest essRestRequest)
		{
			Help result = null;
			try
			{
				result = await _restHelper.PostAsync<Help>(essRestRequest);
			}
			catch (Exception ex)
			{
				LogConnectivityException(essRestRequest, ex, " Getting the help files from the host");
			}

			return result;
		}

		public async Task<byte[]> GetRegionalResultFilesFromHostAsync(EssRestRequest essRestRequest)
		{
			var result = new byte[] { };
			try
			{
				result = await _restHelper.PostAsync<byte[]>(essRestRequest);
			}
			catch (Exception e)
			{
				LogConnectivityException(essRestRequest, e, "Getting the Regional Results files from the host");
			}

			return result;
		}

		public async Task<List<string>> GetCdnUrisFromHostAsync(EssRestRequest essRestRequest)
		{
			var result = new List<string>();
			try
			{
				result = await _restHelper.PostAsync<List<string>>(essRestRequest);
			}
			catch (Exception ex)
			{
				LogConnectivityException(essRestRequest, ex, " Getting CDN uris from the host");
			}

			return result;
		}

		public async Task<HelpFileCdnResponse> GetHelpCdnUrisFromHostAsync(EssRestRequest essRestRequest,
			string stylesheetFilename)
		{
			HelpFileCdnResponse result = null;
			try
			{
				result = await _restHelper.PostAsync<HelpFileCdnResponse>(essRestRequest);
			}
			catch (Exception ex)
			{
				LogConnectivityException(essRestRequest, ex, " Getting CDN uris from the host");
			}

			return result;
		}

		/// <summary>
		/// POST  Failed transaction GUID and ErrorMessage to SyncPoint.
		/// </summary>
		public async Task<bool> SubmitFailedTransactionAsync(EssRestRequest essRestRequest)
		{
			bool result;
			_logger.LogDebug("Entered SubmitFailedTransactionAsync() - Sending request...");
			try
			{
				result = await _restHelper.PostAsync<bool>(essRestRequest);
				_logger.LogDebug("SubmitFailedTransactionAsync() - Response received");
			}
			catch (Exception ex)
			{
				LogConnectivityException(essRestRequest, ex, " SubmitFailedTransactionAsync() - failed");
				result = false;
			}

			return result;
		}

		public async Task<DownloadConfigurationsDto> GetConfigurationsUpdateFromHostAsync(EssRestRequest essRestRequest)
		{
			DownloadConfigurationsDto result;
			try
			{
				result = await _restHelper.PostAsync<DownloadConfigurationsDto>(essRestRequest);
			}
			catch (Exception ex)
			{
				LogConnectivityException(essRestRequest, ex, " GetConfigurationsUpdateFromHostAsync() - failed");
				result = new DownloadConfigurationsDto();
			}

			return result;
		}

		public async Task<bool> UpdateConfigDownloadedLogToHostAsync(EssRestRequest essRestRequest)
		{
			bool result;
			_logger.LogDebug("Entered UpdateConfigDownloadedLogToHostAsync() - Sending request...");
			try
			{
				result = await _restHelper.PostAsync<bool>(essRestRequest);
				_logger.LogDebug("UpdateConfigDownloadedLogToHostAsync() - Response received");
			}
			catch (Exception ex)
			{
				LogConnectivityException(essRestRequest, ex, " UpdateConfigDownloadedLogToHostAsync() - failed");
				result = false;
			}

			return result;
		}

		public async Task<DownloadConfigurationsDto> GetPollWorkerConfigurationsUpdateFromHostAsync(
			EssRestRequest essRestRequest)
		{
			DownloadConfigurationsDto result;
			_logger.LogDebug("Entered GetPollWorkerConfigurationsUpdateFromHostAsync() - Sending request...");
			try
			{
				result = await _restHelper.PostAsync<DownloadConfigurationsDto>(essRestRequest);
				_logger.LogDebug("GetPollWorkerConfigurationsUpdateFromHostAsync() - Response received");
			}
			catch (Exception ex)
			{
				LogConnectivityException(essRestRequest, ex,
					" GetPollWorkerConfigurationsUpdateFromHostAsync() - failed");
				result = new DownloadConfigurationsDto();
			}

			return result;
		}

		public async Task<bool> UpdatePollWorkerConfigDownloadedLogToHostAsync(EssRestRequest essRestRequest)
		{
			bool result;
			_logger.LogDebug("Entered UpdatePollWorkerConfigDownloadedLogToHostAsync() - Sending request...");
			try
			{
				result = await _restHelper.PostAsync<bool>(essRestRequest);
				_logger.LogDebug("UpdatePollWorkerConfigDownloadedLogToHostAsync() - Response received");
			}
			catch (Exception ex)
			{
				LogConnectivityException(essRestRequest, ex,
					" UpdatePollWorkerConfigDownloadedLogToHostAsync() - failed");
				result = false;
			}

			return result;
		}

		private void LogConnectivityException(EssRestRequest essRestRequest, Exception ex, string action)
		{
			var logProps = new Dictionary<string, string>
			{
				{ "Action", action }
			};
			if (essRestRequest != null)
			{
				logProps.Add("EssRestRequest.BaseUrl", essRestRequest.BaseUrl);
			}

			_logger.LogError(ex, logProps);
		}
	}
}