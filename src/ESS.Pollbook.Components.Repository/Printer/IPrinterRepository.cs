using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.StaticValues;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ESS.Pollbook.Components.Repository.Printer
{
    public interface IPrinterRepository
    {
        //printer configuration handling
        ValueTask<IEnumerable<PrinterDto>> GetAllPrinters(bool forceReload = false);

        void SetSelectedPrinter(PrinterType printerType, int pollbookPrinterId);

        //report fields configuration
        Task<IEnumerable<PrinterFormatFieldDto>> GetReportFieldsAsync();
    }
}
