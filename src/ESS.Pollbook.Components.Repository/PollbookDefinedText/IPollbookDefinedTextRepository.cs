using ESS.Pollbook.Core.DTO;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ESS.Pollbook.Components.Repository.PollbookDefinedText
{
   public interface IPollbookDefinedTextRepository
   {
      Task<IEnumerable<ElectionPollbookDefinedTextDto>> LoadPollbookDefinedTextFromDb();

      string GetPollbookDefinedTextForLanguage(string textName, string language);

      void GetPollbookDefinedTextForLanguageWithDefault(string textName, string language, out string definedText,
         string defaultText);

      string GetPollbookDefinedTextForLanguageWithDefaultLanguage(string textName, string language,
         string defaultLanguage, string defaultText = null);

      List<string> GetPollbookDefinedTextLanguages();

      void SetPollbookDefinedTextCurrentLanguage(string language);

      string GetPollbookDefinedTextCurrentLanguage();

      Task<ElectionPollbookDefinedTextDto> GetPollbookDefinedTextForControlIdAndLanguage(int controlId, string language);

      Task<string> GetPollbookDefinedTextForControlIdAndLanguageAsync(int controlId, string language);
   }
}