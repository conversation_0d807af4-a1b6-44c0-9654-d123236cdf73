using ESS.Pollbook.Core.DTO;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ESS.Pollbook.Components.Repository.CommonEnumerationValue
{
    public interface ICommonEnumerationValueRepository
    {
        Task<IEnumerable<CommonEnumerationValueDto>> CommonEnumerationValuesAsync { get; }

        Task<CommonEnumerationValueDto> GetCommonEnumerationValueAsync(int enumValueNumber, int enumerationId);

    }
}
