<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
	  <TargetFramework>net48</TargetFramework>
	  <Authors>Election Systems and Software</Authors>
	  <Company>Election Systems and Software</Company>
	  <Copyright>Election Systems and Software</Copyright>
	  <AssemblyVersion>7.2.9.0</AssemblyVersion>
	  <FileVersion>7.2.9.0</FileVersion>
	  <CodeAnalysisRuleSet>..\.sonarlint\express-poll-branchescsharp.ruleset</CodeAnalysisRuleSet>
 </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="IdentityModel" Version="7.0.0" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="7.2.0" />
    <PackageReference Include="System.Text.Json" Version="8.0.5" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ESS.Pollbook.Core\ESS.Pollbook.Core.csproj" />
  </ItemGroup>

</Project>
