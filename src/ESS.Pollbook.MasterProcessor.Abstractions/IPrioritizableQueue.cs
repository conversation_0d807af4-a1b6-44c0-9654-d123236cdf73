using ESS.Pollbook.MasterProcessor.Abstractions.Shared;

namespace ESS.Pollbook.MasterProcessor.Abstractions
{
    public interface IPrioritizableQueue
    {
        void Enqueue(PollbookQueueItem item);
        PollbookQueueItem DequeueOrDefault();
        bool IsStandardQueueEmpty();
        bool IsPriorityQueueEmpty();
        bool IsLowPriorityQueueEmpty();
        bool HasHostSyncTransactionItems();
    }
}