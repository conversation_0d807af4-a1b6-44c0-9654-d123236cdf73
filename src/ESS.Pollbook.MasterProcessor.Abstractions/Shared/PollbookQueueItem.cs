using System;
using System.Collections.Generic;

namespace ESS.Pollbook.MasterProcessor.Abstractions.Shared
{
    public class PollbookQueueItem
    {
        /// <summary>
        /// ID used to identify the item for file retrieval/removal purposes. Should be left empty when item will not be written to file.
        /// </summary>
        public Guid ClientId { get; set; }

        /// <summary>
        /// Election Guid for file
        /// </summary>
        public string ElectionGuid { get; set; }

        /// <summary>
        /// Key value pair dictionary to hold the parameters for the queue item.
        /// </summary>
        public Dictionary<string, object> QueueParams { get; set; }

        /// <summary>
        /// Process to be ran by the MasterProcessor.
        /// </summary>
        public QueueProcess ItemQueueProcess { get; set; }

        /// <summary>
        /// Queue type to process.
        /// </summary>
        public QueueType ItemQueueType { get; set; }

        /// <summary>
        /// Queue Priority this queue item should be processed with.
        /// </summary>
        public QueuePriority ItemQueuePriority { get; set; } = QueuePriority.High;
    }
}
