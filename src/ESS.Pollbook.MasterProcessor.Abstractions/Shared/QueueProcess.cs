
namespace ESS.Pollbook.MasterProcessor.Abstractions.Shared
{
    // <summary>
    // Enum representing different queue processes in the Pollbook Master Processor.
    // Each value corresponds to a specific type of processing task.
    // 100x series are for transaction queue,
    // 200x series are for polldata queue.
    // </summary>
    public enum QueueProcess
    {
        HostSyncTransaction = 1001,
        HostSyncVoterPolldata = 2001,
        VoterEditTransaction = 1002,
        VoterEditPolldata = 2002,
        UpdateTransactionStatus = 1003,
        EssServiceBusMessageTransaction = 1004,
        EssServiceBusMessagePolldata = 2004,
        OfflineIncrementalUpdateTransaction = 1005,
        OfflineIncrementalUpdatePolldata = 2005,
        FailedInsertTransaction = 1006,
        SerialNumber = 1007,
        FailedUpdateHostTransaction = 1008,
    }
}
