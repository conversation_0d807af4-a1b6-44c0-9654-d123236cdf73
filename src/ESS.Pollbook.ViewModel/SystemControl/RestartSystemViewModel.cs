using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Core.SystemControl;
using ESS.Pollbook.Facade.AuditLog;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.CommandWpf;
using GalaSoft.MvvmLight.Messaging;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Windows;
using System.Windows.Input;

namespace ESS.Pollbook.ViewModel.SystemControl
{
    public class RestartSystemViewModel : ViewModelBase
    {
        private readonly IFrameNavigationService _navigation;
        private readonly IMessenger _messenger;
        private readonly IEssLogger _essLogger;
        private readonly IAuditLogFacade _auditLogFacade;

        private readonly string _pageName;

        public ICommand RestartSystemCommand { get; }

        public string RestartLabel => UIText.Restart;

        public RestartSystemViewModel(IFrameNavigationService navigationService, IMessenger messenger, IEssLogger essLogger, IAuditLogFacade auditLogFacade)
        {
            _navigation = navigationService;
            _messenger = messenger;
            _essLogger = essLogger;
            _auditLogFacade = auditLogFacade;

            _pageName = _navigation.PageName(this.GetType().FullName);

            RestartSystemCommand = new RelayCommand(RestartSystem);
        }

        private void RestartSystem()
        {
            try
            {
                _auditLogFacade.AddToAuditLog(_pageName, $"'{RestartLabel}' button was activated.");
                if (SystemDetails.IsKioskMode)
                {
                    WindowsControl.RestartWindows();
                }
                else
                {
                    //Just restart the app
                    Process.Start(Application.ResourceAssembly.Location);
                    Application.Current.Shutdown();
                }
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string>();
                logProps.Add("Action", "RestartSystemViewModel.RestartSystem");
                _essLogger.LogError(ex, logProps);
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }
    }
}
