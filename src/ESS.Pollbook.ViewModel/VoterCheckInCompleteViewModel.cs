using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.ViewModel.VoterSearch;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.Command;
using GalaSoft.MvvmLight.Messaging;
using System;
using System.Collections.Generic;
using System.Windows.Input;

namespace ESS.Pollbook.ViewModel
{
    public class VoterCheckInCompleteViewModel : ViewModelBase
    {
        private readonly IFrameNavigationService _navigation;
        private readonly IMessenger _messenger;
        private readonly IEssLogger _essLogger;
        private readonly IAuditLogFacade _auditLogFacade;

        private readonly string _pageName;

        private readonly ICommand _doneCommand;
        public ICommand DoneCommand => _doneCommand;

        public string DoneLabel => UIText.Done;

        public VoterCheckInCompleteViewModel(IFrameNavigationService navigationService,
                                             IMessenger messengerService,
                                             IEssLogger essLogger,
                                             IAuditLogFacade auditLogFacade)
        {
            _navigation = navigationService;
            _messenger = messengerService;
            _essLogger = essLogger;
            _auditLogFacade = auditLogFacade;

            _pageName = _navigation.PageName(this.GetType().FullName);

            _doneCommand = new RelayCommand(Done);
        }

        private void Done()
        {
            try
            {
                _auditLogFacade.AddToAuditLog(_pageName, $"'{DoneLabel}' button was activated.");
                _navigation.NavigateTo<AdvancedVoterSearchViewModel>(NavigationFrameEnum.ContextFrame);
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string>();
                logProps.Add("Action", "VoterCheckInCompleteViewModel.Done");
                _essLogger.LogError(ex, logProps);
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }
    }
}
