using ESS.Pollbook.Core.Commands;
using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Facade.Configuration;
using ESS.Pollbook.Facade.Pollworker;
using ESS.Pollbook.Core.Messaging;
using ESS.Pollbook.ViewModel.Utils;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.Command;
using GalaSoft.MvvmLight.Messaging;
using System;
using System.Collections.Generic;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Input;
using ESS.Pollbook.Core.DTO;

namespace ESS.Pollbook.ViewModel
{
	public sealed class ConfigurationUpdateAlertViewModal : ViewModelBase, IDisposable
	{
		private readonly IFrameNavigationService _navigation;
		private readonly IMessenger _messenger;
		private readonly IEssLogger _essLogger;
		private readonly IAuditLogFacade _auditLogFacade;
		private readonly IPollbookConfigurationFacade _configurationFacade;
		private readonly IPollworkerConfigurationFacade _pollworkerConfigurationFacade;

		private string PageName => _navigation.PageName(GetType().FullName);
		private readonly System.Timers.Timer _delayChangesTimer = new System.Timers.Timer();

		public ICommand DelayChangesCommand => new RelayCommand(DelayChanges);
		public ICommandAsync ApplyChangesCommand => new CommandAsync(ApplyChanges);
		private bool _disposed = false;
		public string DelayChangesLabel => UIText.DelayChanges;
		public string ApplyChangesLabel => UIText.ApplyChanges;
		public string Title => "Configuration Updates";
		public string Message => "Configuration updates have been received. Select Apply Changes, to apply the changes and sign out. Select Delay Changes, to delay the changes.";

		public static bool IsPWConfigUpdate { get; set; }


		public ConfigurationUpdateAlertViewModal(IFrameNavigationService navigationService,
			IMessenger messengerService,
			IEssLogger essLogger,
			IAuditLogFacade auditLogFacade,
			IPollbookConfigurationFacade configurationFacade,
			IPollworkerConfigurationFacade pollworkerConfigurationFacade)
		{
			_navigation = navigationService;
			_messenger = messengerService;
			_essLogger = essLogger;
			_auditLogFacade = auditLogFacade;
			_configurationFacade = configurationFacade;
			_pollworkerConfigurationFacade = pollworkerConfigurationFacade;
			_delayChangesTimer.Elapsed += DelayChangesTimer_Elapsed;
			_delayChangesTimer.Interval = SystemConfiguration.ElectionConfiguration.ConfigurationChangeAlertIntervalSeconds * 1000;
		}

		private void DelayChangesTimer_Elapsed(object sender, System.Timers.ElapsedEventArgs e)
		{
			if (HostAuthorizationMonitor.DelayChanges && SystemDetails.AppState == ApplicationState.SignIn)
			{
				_delayChangesTimer.Stop();
				System.Windows.Application.Current.Dispatcher.Invoke(new ThreadStart(delegate { _navigation.NavigateTo<ConfigurationUpdateAlertViewModal>(NavigationFrameEnum.ModalFrame, false, withVerticalEffect: true); }));
			}
		}

		private void DelayChanges()
		{
			try
			{
				_auditLogFacade.AddToAuditLog(PageName, $"'{DelayChangesLabel}' button was activated.");
				HostAuthorizationMonitor.DelayChanges = true;
				_delayChangesTimer.Start();
				_navigation.CloseModalWindow(withVerticalEffect: true);
			}
			catch (Exception ex)
			{
				_essLogger.LogError(ex, new Dictionary<string, string> { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
				_messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
			}
		}

		private async Task ApplyChanges()
		{
			try
			{
				await _auditLogFacade.AddToAuditLogAsync(PageName, $"'{ApplyChangesLabel}' button was activated.");
				HostAuthorizationMonitor.DelayChanges = false;

				_ = HostAuthorizationMonitor.ShutdownMonitorAsync();
				_messenger.Send(new ModuleTypesMessage(nameof(ModulesEnum.ExpressPoll)));

				// must capture it prior to these navigates else you will lose the payload.
				var config = _navigation.Parameter;

				_navigation.CloseModalWindow(withVerticalEffect: true);
				_navigation.NavigateTo<ApplyingConfigurationUpdatesViewModel>(NavigationFrameEnum.ContextFrame, withVerticalEffect: true);

				int result;
				if (IsPWConfigUpdate)
				{
					result = await _pollworkerConfigurationFacade.CheckAndApplyPollWorkerConfigurationsUpdatesAsync();
				}
				else
				{
					var parameterConfig = config as DownloadConfigurationsDto;
					result = await _configurationFacade
						.CheckAndApplyPollbookConfigurationsUpdatesAsync(parameterConfig);
				}

				if (result == -1)
					_navigation.NavigateTo<ApplyingConfigurationUpdatesFailedViewModel>(NavigationFrameEnum.ContextFrame);
				else
					_navigation.NavigateTo<ApplyingConfigurationUpdatesDoneViewModel>(result, NavigationFrameEnum.ContextFrame);
			}
			catch (Exception ex)
			{
				_essLogger.LogError(ex, new Dictionary<string, string> { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
				_messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
			}
		}

		public void Dispose()
		{
			Dispose(true);
		}

		private void Dispose(bool disposing)
		{
			if (_disposed)
				return;

			if (disposing)
			{
				if (_delayChangesTimer != null)
					_delayChangesTimer.Elapsed -= DelayChangesTimer_Elapsed;

				_delayChangesTimer?.Close();
			}

			_disposed = true;
		}
	}
}
