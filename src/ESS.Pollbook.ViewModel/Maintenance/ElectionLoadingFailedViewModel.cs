using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Core.Messaging;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.Command;
using GalaSoft.MvvmLight.Messaging;
using System;
using System.Collections.Generic;
using System.Windows.Input;
using ESS.Pollbook.Core.DTO;

namespace ESS.Pollbook.ViewModel.Maintenance
{
   public class ElectionLoadingFailedViewModel : ViewModelBase
   {
      private readonly IFrameNavigationService _navigation;
      private readonly IMessenger _messenger;
      private readonly IEssLogger _essLogger;
      private readonly IAuditLogFacade _auditLogFacade;

      private string PageName => _navigation.PageName(this.GetType().FullName);

      public ICommand GoToMaintenanceCommand => new RelayCommand(GoToMaintenance);

      public string BackLabel => UIText.Back;

      public string Title { get; set; } = UIText.LoadElectionFailedTitle;

      public string FailedMsg { get; set; } = UIText.LoadElectionMessageFailed;

      public ElectionLoadingFailedViewModel(IFrameNavigationService navigationService, IMessenger messengerService, IEssLogger essLogger, IAuditLogFacade auditLogFacade)
      {
         _navigation = navigationService;
         _messenger = messengerService;
         _essLogger = essLogger;
         _auditLogFacade = auditLogFacade;
      }

      public void PageInitialized()
      {
         if (_navigation.Parameter == null) return;
         var actionFailed = (ActionFailedDto)_navigation.Parameter;
         if (actionFailed != null)
         {
            Title = actionFailed.Title;
            FailedMsg = actionFailed.Message;

            RaisePropertyChanged(nameof(Title));
            RaisePropertyChanged(nameof(FailedMsg));
         }
      }

      private void GoToMaintenance()
      {
         try
         {
            _auditLogFacade.AddToAuditLog(PageName, $"'{BackLabel}' button was activated.");
            _messenger.Send(new StatusBarMessage(isElectionInfoVisible: false, isSystemInfoVisible: true));
            _navigation.GoBackTo<SelectTaskTypeViewModel>(NavigationFrameEnum.ContextFrame);
         }
         catch (Exception ex)
         {
            var logProps = new Dictionary<string, string>();
            logProps.Add("Action", "ElectionLoadingFailedViewModel.GoToMaintenance");
            _essLogger.LogError(ex, logProps);
            _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
         }
      }
   }
}
