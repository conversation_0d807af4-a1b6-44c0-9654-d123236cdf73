using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.Model;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Core.SystemControl;
using ESS.Pollbook.Facade.AuditLog;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.Command;
using GalaSoft.MvvmLight.Messaging;
using System;
using System.Collections.Generic;
using System.Windows.Input;

namespace ESS.Pollbook.ViewModel.Maintenance
{
   public class TransactionsClearedViewModel : ViewModelBase
   {
      private readonly IFrameNavigationService _navigation;
      private readonly IMessenger _messenger;
      private readonly IEssLogger _essLogger;
      private readonly IAuditLogFacade _auditLogFacade;
      private object _context;

      private string PageName => _navigation.PageName(this.GetType().FullName);

      public ICommand RestartCommand => new RelayCommand(RestartWindows);

      public string GoToMaintenanceLabel => _context is TestModeModel ? UIText.GoToConfigureElection : UIText.GoToMaintenance;
      public string RestartLabel => UIText.Restart;

      private string _title;
      public string Title
      {
         get => _title;
         set => Set(ref _title, value);
      }

      private string _message;
      public string Message
      {
         get => _message;
         set => Set(ref _message, value);
      }

      public TransactionsClearedViewModel(IFrameNavigationService navigationService, IMessenger messengerService, IEssLogger essLogger, IAuditLogFacade auditLogFacade)
      {
         _navigation = navigationService;
         _messenger = messengerService;
         _essLogger = essLogger;
         _auditLogFacade = auditLogFacade;
      }

      public void PageIsLoaded()
      {
         if (_navigation.Parameter == null)
         {
            ResetPage();
            return;
         }

         if (!(_navigation.Parameter is TestModeModel))
            return;

         var context = (TestModeModel)_navigation.Parameter;
         Title = context.Title;
         Message = context.Message;
         _context = context;
         RaisePropertyChanged(nameof(GoToMaintenanceLabel));
      }

      private void ResetPage()
      {
         Title = null;
         Message = null;
      }

      private void RestartWindows()
      {
         try
         {
            _auditLogFacade.AddToAuditLog(PageName, $"'{RestartLabel}' button was activated.");
            _essLogger.LogInformation("Restarting Windows");
            WindowsControl.RestartWindows();
         }
         catch (Exception ex)
         {
            var logProps = new Dictionary<string, string>() { { "Action", "TransactionsClearedViewModel.RestartWindows" } };
            _essLogger.LogError(ex, logProps);
            _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
         }
      }
   }
}
