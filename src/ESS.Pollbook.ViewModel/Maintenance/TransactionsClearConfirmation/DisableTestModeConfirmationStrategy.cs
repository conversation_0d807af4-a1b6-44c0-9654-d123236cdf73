using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.Model;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Facade.Maintenance;
using ESS.Pollbook.Core.Messaging;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.Messaging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ESS.Pollbook.ViewModel.Maintenance.TransactionsClearConfirmation
{
    public class DisableTestModeConfirmationStrategy : AbstractConfirmationStrategy
    {
        public DisableTestModeConfirmationStrategy(
            IFrameNavigationService navigationService,
            IMessenger messengerService,
            IEssLogger essLogger,
            IAuditLogFacade auditLogFacade,
            ILoadElectionFacade loadElectionFacade,
            ITransactionsClearConfirmationFacade transactionsClearConfirmationFacade,
            ViewModelBase parent) : base(navigationService, messengerService, essLogger, auditLogFacade, loadElectionFacade,
            transactionsClearConfirmationFacade, parent)
        {
            // constructor
        }

        public override string GetTitle()
        {
            return @"Are you sure you want to disable Test Mode?";
        }

        public override string GetMessage()
        {
            return @"By selecting Yes, all transactions will be cleared and the Election will be reset.";
        }

        /// <summary>
        ///     DisableTestMode clears transactions and restores the backups.
        /// </summary>
        /// <returns></returns>
        public override async Task Yes()
        {
            var hasDisabledTestMode = false;
            var hasRestoredDb = false;

            try
            {
                await _auditLogFacade.AddToAuditLogAsync(PageName, $"'{GetYesLabel()}' button was activated.");

                _navigation.CloseModalWindow();
                _navigation.NavigateTo<TransactionsClearingViewModel>(NavigationFrameEnum.ContextFrame,
                    suppressHorizontalEffect: true);

                hasDisabledTestMode = _transactionsClearConfirmationFacade.DisableTestMode();
                var loadResponse = await _loadElectionFacade.LoadElectionFromBackupAsync(SystemDetails.PQCPassword);
                if (loadResponse.IsSuccess)
                {
                    _loadElectionFacade.ClearLogs();
                    _loadElectionFacade.ClearQueues();
                }
                hasRestoredDb = loadResponse.IsSuccess;
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string>
                    { { "Action", "TransactionsClearConfirmationViewModel.Yes" } };
                _essLogger.LogError(ex, logProps);
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error,
                    SystemDetails.GenericErrorMessage));
            }
            finally
            {
                if (hasDisabledTestMode && hasRestoredDb)
                {
                    _messenger.Send(new ClearTransactionsDoneMessage());
                    var param = new TestModeModel
                        { Title = UIText.DisableTestModeTitle, Message = UIText.DisableTestModeMessage };
                    _navigation.NavigateTo<TransactionsClearedViewModel>(param, NavigationFrameEnum.ContextFrame,
                        suppressHorizontalEffect: true);
                }
                else
                {
                    await _transactionsClearConfirmationFacade.RollBackTestModeConfiguration(true);
                    _navigation.NavigateTo<TransactionsClearedFailedViewModel>(new TestModeModel(),
                        NavigationFrameEnum.ContextFrame);
                }

                _messenger.Send(new StatusBarMessage(false, true));
                _messenger.Send(new TestModeMessage { IsTestMode = SystemDetails.IsTestMode });
            }
        }
    }
}