using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Facade.Maintenance;
using ESS.Pollbook.Hardware.Storage;
using ESS.Pollbook.Core.Messaging;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.Messaging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace ESS.Pollbook.ViewModel.Maintenance.TransactionsClearConfirmation
{
   public abstract class AbstractConfirmationStrategy
   {
      protected readonly IFrameNavigationService _navigation;
      protected readonly IMessenger _messenger;
      protected readonly IEssLogger _essLogger;
      protected readonly IAuditLogFacade _auditLogFacade;
      protected readonly ILoadElectionFacade _loadElectionFacade;
      protected readonly ITransactionsClearConfirmationFacade _transactionsClearConfirmationFacade;
      protected readonly ViewModelBase _parentViewModel;

      protected string PageName => _navigation.PageName(_parentViewModel.GetType().FullName);

      protected AbstractConfirmationStrategy(
          IFrameNavigationService navigationService,
          IMessenger messengerService,
          IEssLogger essLogger,
          IAuditLogFacade auditLogFacade,
          ILoadElectionFacade loadElectionFacade,
          ITransactionsClearConfirmationFacade transactionsClearConfirmationFacade,
          ViewModelBase parent)
      {
         _navigation = navigationService;
         _messenger = messengerService;
         _essLogger = essLogger;
         _auditLogFacade = auditLogFacade;
         _loadElectionFacade = loadElectionFacade;
         _transactionsClearConfirmationFacade = transactionsClearConfirmationFacade;
         _parentViewModel = parent;
      }

      public abstract string GetTitle();
      public abstract string GetMessage();

      public virtual string GetNoLabel() => UIText.No;

      public virtual string GetYesLabel() => UIText.Yes;

      public virtual async Task Yes()
      {
         var hasRestoredDb = false;

         try
         {
            await _auditLogFacade.AddToAuditLogAsync(PageName, $"'{GetYesLabel()}' button was activated.");

            if (DriveSearcher.GetSdCardPaths().Any())
            {
               _messenger.Send(new SDClearTransactionsMessage());
               return;
            }

            _navigation.CloseModalWindow();
            _navigation.NavigateTo<TransactionsClearingViewModel>(NavigationFrameEnum.ContextFrame,
                suppressHorizontalEffect: true);

            if (!SystemDetails.IsTestMode)
            {
                var loadResponse = await _loadElectionFacade.LoadElectionFromBackupAsync(SystemDetails.PQCPassword);
                if (loadResponse.IsSuccess)
                {
                    _loadElectionFacade.ClearLogs();
                    _loadElectionFacade.ClearQueues();
                }
                hasRestoredDb = loadResponse.IsSuccess;
            }
            else
            {
                hasRestoredDb = await _transactionsClearConfirmationFacade.ClearPollbookTransactionsAsync();
            }
         }
         catch (Exception ex)
         {
            var logProps = new Dictionary<string, string>()
                    {{"Action", "TransactionsClearConfirmationViewModel.Yes"}};
            _essLogger.LogError(ex, logProps);
            _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error,
                SystemDetails.GenericErrorMessage));
         }

         if (hasRestoredDb)
         {
            _messenger.Send(new ClearTransactionsDoneMessage());
            _navigation.NavigateTo<TransactionsClearedViewModel>(NavigationFrameEnum.ContextFrame,
                suppressHorizontalEffect: true);
         }
         else
         {
            _navigation.NavigateTo<TransactionsClearedFailedViewModel>(NavigationFrameEnum.ContextFrame);
         }

         _messenger.Send(new StatusBarMessage(isElectionInfoVisible: false, isSystemInfoVisible: true));

      }

      public virtual void No()
      {
         try
         {
            _auditLogFacade.AddToAuditLog(PageName, $"'{GetNoLabel()}' button was activated.");
            _navigation.CloseModalWindow(withVerticalEffect: true);
            _messenger.Send(new TestModeConfirmationMessage() { IsTestMode = SystemDetails.IsTestMode });
         }
         catch (Exception ex)
         {
            var logProps = new Dictionary<string, string>() { { "Action", "TransactionsClearConfirmationViewModel.No" } };
            _essLogger.LogError(ex, logProps);

            _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
         }
      }
   }
}
