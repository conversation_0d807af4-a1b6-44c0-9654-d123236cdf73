using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Core.SystemControl;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Core.Messaging;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.Command;
using GalaSoft.MvvmLight.Messaging;
using System;
using System.Collections.Generic;
using System.Windows.Input;

namespace ESS.Pollbook.ViewModel.Maintenance
{
    public class ElectionLoadedViewModel : ViewModelBase
    {
        private readonly IFrameNavigationService _navigation;
        private readonly IMessenger _messenger;
        private readonly IEssLogger _essLogger;
        private readonly IAuditLogFacade _auditLogFacade;

        private readonly string _pageName;

        private readonly ICommand _goToMaintenanceCommand;
        private readonly ICommand _restartCommand;

        public ICommand GoToMaintenanceCommand => _goToMaintenanceCommand;
        public ICommand RestartCommand => _restartCommand;

        public string GoToMaintenanceLabel => UIText.GoToMaintenance;
        public string RestartLabel => UIText.Restart;
        public bool MaintenanceButtonVisible => false;

        public ElectionLoadedViewModel(IFrameNavigationService navigationService, IMessenger messengerService, IEssLogger essLogger, IAuditLogFacade auditLogFacade)
        {
            _navigation = navigationService;
            _messenger = messengerService;
            _essLogger = essLogger;
            _auditLogFacade = auditLogFacade;

            _pageName = _navigation.PageName(this.GetType().FullName);

            _goToMaintenanceCommand = new RelayCommand(GoToMaintenance);
            _restartCommand = new RelayCommand(RestartWindows);
        }

        private void GoToMaintenance()
        {
            try
            {
                _auditLogFacade.AddToAuditLog(_navigation.PageName(this.GetType().FullName), $"'{GoToMaintenanceLabel}' button was activated.");
                _messenger.Send(new StatusBarMessage(isElectionInfoVisible: false, isSystemInfoVisible: true));
                _navigation.NavigateTo<MaintenanceViewModel>();
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string>();
                logProps.Add("Action", "ElectionLoadedViewModel.GoToMaintenance");
                _essLogger.LogError(ex, logProps);
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }

        private void RestartWindows()
        {
            try
            {
                _auditLogFacade.AddToAuditLog(_pageName, $"'{RestartLabel}' button was activated.");
                _essLogger.LogInformation("Restarting Windows");
                WindowsControl.RestartWindows();
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string>();
                logProps.Add("Action", "ElectionLoadedViewModel.RestartWindows");
                _essLogger.LogError(ex, logProps);
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }
    }
}
