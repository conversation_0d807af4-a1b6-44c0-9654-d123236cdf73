using ESS.Pollbook.Core.Commands;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Facade.Maintenance;
using ESS.Pollbook.ViewModel.Maintenance.TransactionsClearConfirmation;
using ESS.Pollbook.Core.Messaging;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.Command;
using GalaSoft.MvvmLight.Messaging;
using System.Threading.Tasks;
using System.Windows.Input;

namespace ESS.Pollbook.ViewModel.Maintenance
{
    public class TransactionsClearConfirmationViewModel : ViewModelBase
    {
        private readonly IFrameNavigationService _navigation;
        private readonly IMessenger _messenger;
        private readonly IEssLogger _essLogger;
        private readonly IAuditLogFacade _auditLogFacade;
        private readonly ILoadElectionFacade _loadElectionFacade;
        private readonly ITransactionsClearConfirmationFacade _transactionsClearConfirmationFacade;

        private ContextConfirmation _context = new ContextConfirmation();

        public ICommand NoCommand => new RelayCommand(No);
        public ICommandAsync YesCommand => new CommandAsync(Yes);

        public string NoLabel => _context.GetNoLabel();
        public string YesLabel => _context.GetYesLabel();
        public string Title => _context.GetTitle();
        public string Message => _context.GetMessage();

        public TransactionsClearConfirmationViewModel(IFrameNavigationService navigationService,
            IMessenger messengerService,
            IEssLogger essLogger,
            ILoadElectionFacade loadElectionFacade,
            ITransactionsClearConfirmationFacade transactionsClearConfirmationFacade,
            IAuditLogFacade auditLogFacade)
        {
            _navigation = navigationService;
            _messenger = messengerService;
            _essLogger = essLogger;
            _loadElectionFacade = loadElectionFacade;
            _transactionsClearConfirmationFacade = transactionsClearConfirmationFacade;
            _auditLogFacade = auditLogFacade;

            _messenger.Register<ClearTransactionsResetMessage>(this, ResetMessage);
            _messenger.Register<TestModeMessage>(this, TestModeMessageHandler);
            _messenger.Register<SDClearTransactionsMessage>(this, SDMessageHandler);

            ResetMessage(null);
        }

        public void PageIsLoaded()
        {
            if (_navigation.Parameter == null)
            {
                ResetMessage(new ClearTransactionsResetMessage());
                return;
            }

            var message = _navigation.Parameter as TestModeMessage;
            if (message == null) return;

            TestModeMessageHandler(message);
        }

        private void ResetMessage(ClearTransactionsResetMessage msg)
        {
            _context.SetContext(new ClearTransactionConfirmationStrategy(_navigation, _messenger, _essLogger, _auditLogFacade, _loadElectionFacade, _transactionsClearConfirmationFacade, this));
            Refresh();
        }

        private void TestModeMessageHandler(TestModeMessage msg)
        {
	        if (msg.IsTestMode)
	        {
		        _context.SetContext(new EnableTestModeConfirmationStrategy(_navigation, _messenger, _essLogger, _auditLogFacade, _loadElectionFacade, _transactionsClearConfirmationFacade, this));
	        }
	        else
	        {
		        _context.SetContext(new DisableTestModeConfirmationStrategy(_navigation, _messenger, _essLogger, _auditLogFacade, _loadElectionFacade, _transactionsClearConfirmationFacade, this));
	        }
            Refresh();
        }

        private void SDMessageHandler(SDClearTransactionsMessage msg)
        {
          _context.SetContext(new SdConfirmationStrategy(_navigation, _messenger, _essLogger, _auditLogFacade, _loadElectionFacade, _transactionsClearConfirmationFacade, this));
          Refresh();
        }

        private void Refresh()
        {
            RaisePropertyChanged(nameof(NoLabel));
            RaisePropertyChanged(nameof(YesLabel));
            RaisePropertyChanged(nameof(Title));
            RaisePropertyChanged(nameof(Message));
        }

        private void No() => _context.No();

        private Task Yes() => _context.Yes();
    }
}
