using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.AuditLog;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.Command;
using GalaSoft.MvvmLight.Messaging;
using System;
using System.Collections.Generic;
using System.Windows.Input;

namespace ESS.Pollbook.ViewModel.Maintenance
{
    public class FailedViewModel : ViewModelBase
    {
        private readonly IFrameNavigationService _navigation;
        private readonly IMessenger _messenger;
        private readonly IEssLogger _essLogger;
        private readonly IAuditLogFacade _auditLogFacade;

        private readonly string _pageName;

        private readonly ICommand _goBackCommand;

        public ICommand GoBackCommand => _goBackCommand;

        public string BackLabel => UIText.Back;

        public string FailedTitle => (_navigation.Parameter as ActionFailedDto)?.Title;

        public string FailedMessage => (_navigation.Parameter as ActionFailedDto)?.Message;

        public FailedViewModel(IFrameNavigationService navigationService, IMessenger messengerService, IEssLogger essLogger, IAuditLogFacade auditLogFacade)
        {
            _navigation = navigationService;
            _messenger = messengerService;
            _essLogger = essLogger;
            _auditLogFacade = auditLogFacade;

            _pageName = _navigation.PageName(this.GetType().FullName);

            _goBackCommand = new RelayCommand(GoBack);
        }

        private void GoBack()
        {
            try
            {
                _auditLogFacade.AddToAuditLog(_pageName, $"'{BackLabel}' button was activated.");
                _navigation.GoBackTo<ManageElectionViewModel>(NavigationFrameEnum.ContextFrame);
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string>();
                logProps.Add("Action", "FailedViewModel.GoBack");
                _essLogger.LogError(ex, logProps);
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }
    }
}
