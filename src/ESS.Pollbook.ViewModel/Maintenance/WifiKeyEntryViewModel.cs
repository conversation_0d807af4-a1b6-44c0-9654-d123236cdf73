using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.Model;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Facade.Maintenance;
using GalaSoft.MvvmLight;
using System;
using System.Collections.Generic;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using ESS.Pollbook.Core.Commands;
using System.Net.NetworkInformation;

namespace ESS.Pollbook.ViewModel.Maintenance
{
    public class WifiKeyEntryViewModel : ViewModelBase
    {
        private readonly IFrameNavigationService _navigation;
        private readonly IEssLogger _essLogger;
        private readonly IWifiMaintenanceFacade _wifiMaintenanceFacade;
        private readonly IAuditLogFacade _auditLogFacade;

        public ICommandAsync SubmitCommand => new CommandAsync(OnSubmitPasswordAsync);

        private RadioItem _radioItem;
        private ProfileItem _profile;
        private readonly string _pageName;
        public string SubmitButton => UIText.Submit;

        private static readonly TimeSpan ScanTimeout = TimeSpan.FromSeconds(10);
        public string WindowTitle => "WiFi Network Password Required";

        public bool SubmitEnabled => !InProcess && !string.IsNullOrEmpty(Password);

        private string _cred = string.Empty;

        public string Password
        {
            get => _cred;
            set
            {
                Set(ref _cred, value);
                if (string.IsNullOrEmpty(_cred))
                {
                    IsAuthenticationValid = true;
                }

                RaisePropertyChanged(nameof(SubmitEnabled));
            }
        }
        private bool _isAuthenticationValid = true;
        public bool IsAuthenticationValid
        {
            get => _isAuthenticationValid;
            set => Set(ref _isAuthenticationValid, value);
        }

        private bool _inProcess;
        public bool InProcess
        {
            get => _inProcess;
            set
            {
                Set(ref _inProcess, value);
                RaisePropertyChanged(nameof(SubmitEnabled));
            }
        }

        public WifiKeyEntryViewModel(IFrameNavigationService navigationService, IEssLogger essLogger, IWifiMaintenanceFacade wifiMaintenanceFacade, IAuditLogFacade auditLogFacade)
        {
            _essLogger = essLogger;
            _navigation = navigationService;
            _wifiMaintenanceFacade = wifiMaintenanceFacade;
            _auditLogFacade = auditLogFacade;

            _pageName = _navigation.PageName(this.GetType().FullName);

        }

        public void OnPageLoaded()
        {
            IsAuthenticationValid = true;
            try
            {

                _radioItem = _navigation.Parameter as RadioItem;
                if (_radioItem?.Ssid == null) return;
                var profile = _wifiMaintenanceFacade.CreateProfileItemFromRadioItem(_radioItem);

                _radioItem.Profile = profile;
                _radioItem.ProfileName = profile?.Name;

            }
            catch (Exception ex)
            {
                _essLogger.LogError(ex,
                new Dictionary<string, string>
                    { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
            }
        }

        private async Task OnSubmitPasswordAsync()
        {
            try
            {
                await _auditLogFacade.AddToAuditLogAsync(_pageName, $"'{SubmitButton}' button was activated.");
                _essLogger.LogInformation($"Submitting password.");

                InProcess = true;

                _profile = _radioItem.Profile;
                if (_profile == null)
                    _essLogger.LogInformation("RadioItem has no profile.");

                if (_profile != null && !string.IsNullOrWhiteSpace(_cred))
                {
                    _essLogger.LogInformation($"Trying to connect with new password...({_profile?.Name})");

	                var conn = await Task.Run(async () =>
		                await _wifiMaintenanceFacade.ConnectWithNewPasswordAsync(_profile, _cred, ScanTimeout,
			                CancellationToken.None));

                    if (await ValidateNetworkConnection(conn)) return;
                }
            }
            catch (Exception ex)
            {
                _essLogger.LogError(ex,
                new Dictionary<string, string>
                    { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
            }
            InProcess = false;
        }

        private async Task<bool> ValidateNetworkConnection(bool conn)
        {
            //If we successfully connected we do not need to check again
            if (conn)
            {
                ExitWithSuccess();
                InProcess = false;
                return true;
            }
            
            //Windows takes around 2 seconds to complete a connection after a successful password
            await Task.Delay(2500);
            

            /*
            * When testing the connection for success ConnectWithNewPasswordAsync()
            * does have a chance to send a false negative due to a delayed response
            * from the network driver (Observed mainly in the Go2, and Go3)
            */
            if (NetworkInterface.GetIsNetworkAvailable())
            {
                ExitWithSuccess();
            }
            else
            {
                _essLogger.LogInformation($"Failed to connect to network {_profile?.Name}. Wrong passcode possible.");
                IsAuthenticationValid = false;
            }
            return false;
        }

        private void ExitWithSuccess()
        {
            _essLogger.LogInformation("Connected with new password");
            IsAuthenticationValid = true;
            _navigation.CloseModalDialogWindow();
        }
    }
}
