using ESS.Pollbook.Core.Commands;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.SignatureHandler;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Facade.IncrementalUpdates;
using ESS.Pollbook.Hardware.Storage;
using ESS.Pollbook.ViewModel.IncrementalUpdates;
using ESS.Pollbook.Core.Messaging;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.Command;
using GalaSoft.MvvmLight.Messaging;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Threading;

namespace ESS.Pollbook.ViewModel.Maintenance
{
    public class SelectTaskTypeViewModel : ViewModelBase
    {
        private readonly IFrameNavigationService _navigation;
        private readonly IMessenger _messenger;
        private readonly IIncrementalUpdatesFacade _incrementalUpdatesFacade;
        private readonly IEssLogger _essLogger;
        private readonly IAuditLogFacade _auditLogFacade;
        private readonly IStorageLocator _storageLocator;

        private string PageName => _navigation.PageName(GetType().FullName);
        private static string Usb => "usb";
        public ICommand BackCommand => new RelayCommand(Back);
        public ICommandAsync<string> SelectTypeCommand => new CommandAsync<string>(PerformActionAsync);

        public string BackLabel => UIText.Back;

        public string FromUsbDriveLabel => UIText.FromUSBDrive;

        public string TitleText { get; set; }

        public string RequestedAction { get; set; }

        private MaintenanceItemsEnum _maintenanceTypeName;
        private readonly string _className;

        public bool FromHostVisibility { get; set; }

        public SelectTaskTypeViewModel(
            IFrameNavigationService navigationService,
            IMessenger messengerService,
            IIncrementalUpdatesFacade incrementalUpdatesFacade,
            IEssLogger essLogger,
            IAuditLogFacade auditLogFacade,
            IStorageLocator storageLocator)
        {
            _navigation = navigationService;
            _messenger = messengerService;
            _incrementalUpdatesFacade = incrementalUpdatesFacade;
            _essLogger = essLogger;
            _auditLogFacade = auditLogFacade;

            _messenger.Register<MaintenanceItemsMessage>(this, MaintenanceTypeMessage);
            _storageLocator = storageLocator;
            _className = GetType().Name;
        }

        private void MaintenanceTypeMessage(MaintenanceItemsMessage message)
        {
            //Based on which functionality we came to this page from, msg on the UI changes
            _maintenanceTypeName = (MaintenanceItemsEnum)Enum.Parse(typeof(MaintenanceItemsEnum), message.MaintenanceItemName, true);
            switch (_maintenanceTypeName)
            {
                case MaintenanceItemsEnum.LoadElection:
                    TitleText = UIText.LoadElection;
                    RequestedAction = nameof(MaintenanceItemsEnum.LoadElection);
                    FromHostVisibility = false;
                    break;
                case MaintenanceItemsEnum.IncrementalUpdate:
                    TitleText = UIText.IncrementalUpdates;
                    RequestedAction = nameof(MaintenanceItemsEnum.IncrementalUpdate);
                    FromHostVisibility = true;
                    break;
            }
        }

        private async Task PerformActionAsync(string selectedType)
        {
            try
            {
                _essLogger.LogInformation($"{FromUsbDriveLabel} activated.");

                _messenger.Send(new StatusBarMessage(false, true));

                _messenger.Send(new ParentControlTextMessage(selectedType));

                switch (_maintenanceTypeName)
                {
                    case MaintenanceItemsEnum.LoadElection:
                        //Create the task dispatcher
                        if (selectedType.Equals(Usb, StringComparison.CurrentCultureIgnoreCase))
                        {
                            var directory = _storageLocator.GetRegionalResultsFolder();
                            if (Directory.Exists(directory))
                            {
                                Directory.Delete(directory, true);
                            }

                            _navigation.NavigateTo<PQCViewModel>(typeof(LoadElectionByUSBViewModel), NavigationFrameEnum.ModalDialogFrame, false);
                        }
                        break;
                    case MaintenanceItemsEnum.IncrementalUpdate:
                        if (selectedType.Equals(Usb, StringComparison.CurrentCultureIgnoreCase))
                        {
                            _messenger.Send(new ModuleTypesMessage(nameof(ModulesEnum.Maintainance)));

                            _navigation.NavigateTo<IncrementalUpdatesLoadingViewModel>(NavigationFrameEnum.ContextFrame);
                            await IncrementalUsbWorkerAsync();
                        }
                        break;
                }
            }
            catch (Exception ex)
            {
                _essLogger.LogError(ex, new Dictionary<string, string>
                {
                    ["Action"] = $"{_className}.{nameof(PerformActionAsync)}"
                });
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }

        private async Task IncrementalUsbWorkerAsync()
        {
            try
            {
                _essLogger.LogInformation("Starting incremental update from USB");
                var localResponse = await _incrementalUpdatesFacade.DownloadIncrementalUpdatesUsbAsync();

                if (localResponse.IsSuccess)
                {
                    _essLogger.LogInformation("Incremental update from USB completed successfully");

                    await Application.Current.Dispatcher.BeginInvoke(DispatcherPriority.Normal, new Action(() =>
                    _navigation.NavigateTo<IncrementalUpdatesLoadedViewModel>(NavigationFrameEnum.ContextFrame)));

                    ZipStreamHandler.LoadSignatureZip(StorageLocator.DefaultDbLocation, _essLogger);
                    _messenger.Send(new UpdateSystemStatsMessage());
                }
                else
                {
                    _essLogger.LogInformation(localResponse.LocalException?.Message ??
                                              "Incremental Update for USB failed.");
                    _messenger.Send(new LocalResponseMessage(localResponse));
                    await Application.Current.Dispatcher.BeginInvoke(DispatcherPriority.Normal, new Action(() =>
                    _navigation.NavigateTo<IncrementalUpdatesFailedViewModel>(NavigationFrameEnum.ContextFrame)));
                }
            }
            catch (Exception ex)
            {
                _essLogger.LogError(ex, new Dictionary<string, string>
                {
                    ["Action"] = $"{_className}.{nameof(IncrementalUsbWorkerAsync)}"
                });
            }
        }

        private void Back()
        {
            try
            {
                _auditLogFacade.AddToAuditLog(PageName, $"'{BackLabel}' button was activated.");
                switch (_maintenanceTypeName)
                {
                    case MaintenanceItemsEnum.LoadElection:
                        _navigation.GoBackTo<MaintenanceViewModel>(withVerticalEffect: true);
                        break;
                    case MaintenanceItemsEnum.IncrementalUpdate:
                        _navigation.GoBackTo<ManageElectionViewModel>(NavigationFrameEnum.ContextFrame);
                        break;
                }
            }
            catch (Exception ex)
            {
                _essLogger.LogError(ex, new Dictionary<string, string>
                {
                    ["Action"] = $"{_className}.{nameof(Back)}"
                });
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }
    }
}
