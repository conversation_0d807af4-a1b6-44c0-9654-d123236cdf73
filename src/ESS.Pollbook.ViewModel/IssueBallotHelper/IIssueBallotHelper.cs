using System.Threading.Tasks;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Facade.Party;
using ESS.Pollbook.ViewModel.Utils;
using GalaSoft.MvvmLight.Messaging;

namespace ESS.Pollbook.ViewModel.IssueBallotHelperInterfaced
{
    public interface IIssueBallotHelper
    {
        Task NavigateToNextPage(IFrameNavigationService navigation, IPartyFacade partyService, IMessenger messenger, IPrintBallotUtil printBallotUtil);
    }
}
