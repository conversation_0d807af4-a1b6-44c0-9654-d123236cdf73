using System;
using System.Collections.Generic;
using System.Reflection;
using System.Threading.Tasks;
using System.Windows;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Facade.Authentication;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.Messaging;
using System.Windows.Input;
using System.Windows.Threading;
using ESS.Pollbook.Core.Commands;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.Messaging;

namespace ESS.Pollbook.ViewModel
{
    public class ManageDeviceLoginViewModel : ViewModelBase
    {
        private readonly IFrameNavigationService _navigation;
        private readonly IMessenger _messenger;
        private readonly IAuthenticationFacade _authentication;
        private readonly IEssLogger _essLogger;
        private readonly IAuditLogFacade _auditLogFacade;

        public ICommand SubmitCommand => new CommandAsync(AuthorizeDeviceManagementAsync);

        public SupervisorPwEntryTargetType TargetType { get; set; }

        public string SubmitLabel => UIText.Submit;

        private string _authenticationQuery = "";

        public string AuthenticationQuery
        {
            get => _authenticationQuery;
            set
            {
                _authenticationQuery = value;

                if (string.IsNullOrEmpty(_authenticationQuery))
                {
                    IsAuthenticationValid = true;
                }

                RaisePropertyChanged();
                RaisePropertyChanged(nameof(SubmitEnabled));
            }
        }

        public bool SubmitEnabled => !string.IsNullOrEmpty(AuthenticationQuery);

        private bool _isAuthenticationValid = true;

        public bool IsAuthenticationValid
        {
            get => _isAuthenticationValid;
            set
            {
                _isAuthenticationValid = value;
                RaisePropertyChanged();
            }
        }

        public ManageDeviceLoginViewModel(IFrameNavigationService navigationService,
            IMessenger messengerService,
            IAuthenticationFacade authenticationService,
            IEssLogger essLogger,
            IAuditLogFacade auditLogFacade)
        {
            _navigation = navigationService;
            _messenger = messengerService;
            _authentication = authenticationService;
            _essLogger = essLogger;
            _auditLogFacade = auditLogFacade;
        }

        public void PageLoaded()
        {
            IsAuthenticationValid = true;
        }

        /// <summary>
        /// Checks for Device Management validation
        /// </summary>
        /// <returns></returns>
        private async Task AuthorizeDeviceManagementAsync()
        {
            await _auditLogFacade.AddToAuditLogAsync(_navigation.PageName(this.GetType().FullName), $"'{SubmitLabel}' button was activated.");

            if (!SubmitEnabled)
            {
                return;
            }

            try
            {
                IsAuthenticationValid = await _authentication.AuthenticateManageDeviceUser(AuthenticationQuery);
                if (IsAuthenticationValid)
                {
                    _messenger.Send(new StatusBarMessage(isVisible: true));
                    _navigation.NavigateTo<ManageDeviceViewModel>(NavigationFrameEnum.ContextFrame, false);
                    _navigation.CloseModalDialogWindow();
                }
                else
                {
                    _essLogger.LogInformation("Unsuccessful attempt to log into Manage Device");
                }
            }
            catch (UnauthorizedAccessException ex)
            {
                _essLogger.LogError(ex,
                    new Dictionary<string, string>
                        { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
                IsAuthenticationValid = false;
            }
            catch (Exception ex)
            {
                _essLogger.LogError(ex,
                    new Dictionary<string, string>
                        { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
                Application.Current.Dispatcher.Invoke(DispatcherPriority.Normal, new Action(() =>
                    {
                        _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error,
                            SystemDetails.GenericErrorMessage));
                    }
                ));
            }
        }
    }
}
