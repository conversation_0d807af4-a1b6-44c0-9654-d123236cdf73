using ESS.Pollbook.Components.Repository.Messages;
using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.Messaging;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Core.UICore;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Facade.Device;
using ESS.ELLEGO.Rest.Stats;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.Messaging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Reflection;
using ESS.Pollbook.Core.Commands;

namespace ESS.Pollbook.ViewModel.SystemStats
{
	public class SystemStatsViewModel : ViewModelBase
	{
      private readonly IMessenger _messenger;
      private readonly IDeviceFacade _deviceFacade;
      private readonly IPrinterFacade _printerFacade;
      private readonly IExpressVotePrinterFacade _expressVotePrinterFacade;
      private readonly ISmartCardFacade _smartCardFacade;
      private readonly IMessageRepository _messageRepository;
      private readonly IAuditLogFacade _audit;
      private readonly IEssLogger _essLogger;

      public ICommandAsync ShowTimeRemainingCommand => new CommandAsync(ShowBatteryTimeRemainingAsync);


      private const int DefaultSlowHostThresholdMilliseconds = 10000;
      private DateTime _systemDateTime;
      private SystemStatsDto _systemStats = new SystemStatsDto();
      private float _batteryLife;
      private bool _isBatteryCharging;
      private readonly CancellationTokenSource _cancelBatteryLevelToken = new CancellationTokenSource();
      private readonly CancellationTokenSource _cancelHostConnectionStatusToken = new CancellationTokenSource();
      private bool _hasWarnedBatteryLevel10Percent;
      private bool _hasWarnedBatteryLevel20Percent;

      public string ElectionName => _systemStats.ElectionName;

      public int VotersCheckedIn => _systemStats.VotersCheckedIn;

      public int BallotsIssued => _systemStats.BallotsIssued;

      public int BallotsNet => _systemStats.BallotsNet;

      public int BallotsReissued => _systemStats.BallotsReissued;

      public int BallotsCanceled => _systemStats.BallotsCanceled;

      public bool IsPeerConnected { get; set; }

      public bool IsPeerIconVisible { get; set; }

      private bool _isInstantMessageIconVisible;
      public bool IsInstantMessageIconVisible
      {
         get => _isInstantMessageIconVisible;
         set => Set(ref _isInstantMessageIconVisible, value);
      }

      public bool IsHostOnIconVisible => SystemDetails.IsHostConnected && !SystemDetails.IsHostSlow;

      public bool IsHostSlowIconVisible => SystemDetails.IsHostConnected && SystemDetails.IsHostSlow;

      public bool IsHostOffIconVisible => !SystemDetails.IsHostConnected;

      public bool IsDownloadIconVisible => (SystemDetails.DownloadInProgress || SystemDetails.ReconciliationInProgress) && SystemDetails.AppState == ApplicationState.SignIn;

      public bool IsHostViewBoxVisible => (SystemConfiguration.ElectionConfiguration != null) && SystemConfiguration.ElectionConfiguration.HostSync;

      public bool IsPeerToPeerOnIconVisible => IsPeerIconVisible && IsPeerConnected;

      public bool IsPeerToPeerOffIconVisible => IsPeerIconVisible && !IsPeerConnected;

      public bool IsPeerToPeerViewBoxVisible => IsPeerToPeerOnIconVisible || IsPeerToPeerOffIconVisible;

      private bool _isPrinterIconVisible = true;
      public bool IsPrinterIconVisible
      {
         get => _isPrinterIconVisible;
         set => Set(ref _isPrinterIconVisible, value);
      }

      private bool? _printerIconColor;
      public bool? PrinterIconColor
      {
         get => _printerIconColor;
         set => Set(ref _printerIconColor, value);
      }
      
      public string BatteryLifeKey => GetBatteryLifeKey();

      public bool IsBatteryLifeLow => BatteryLife <= .3f;

      public SystemStatsDto SystemStats
      {
         get => _systemStats;
         set
         {
            Set(ref _systemStats, value);
            RaisePropertyChanged(nameof(ElectionName));
            RaisePropertyChanged(nameof(VotersCheckedIn));
            RaisePropertyChanged(nameof(BallotsIssued));
            RaisePropertyChanged(nameof(BallotsNet));
            RaisePropertyChanged(nameof(BallotsReissued));
            RaisePropertyChanged(nameof(BallotsCanceled));
         }
      }

      //Value that the UI is bound to
      public float BatteryLife
      {
         get => _batteryLife;
         set
         {
            Set(ref _batteryLife, value);
            RaisePropertyChanged(nameof(BatteryLifeKey));
            RaisePropertyChanged(nameof(IsBatteryLifeLow));
            RaisePropertyChanged(nameof(BatteryLifeStatusText));
         }
      }

      public string BatteryLifeStatusText => GetBatteryLifeStatusText();

      private bool _isBatteryTimeShowing;

      public bool IsBatteryCharging
      {
         get => _isBatteryCharging;
         set
         {
            Set(ref _isBatteryCharging, value);
            RaisePropertyChanged(nameof(BatteryLifeKey));
         }
      }


      public bool IsBatteryTimeEnabled => (SystemConfiguration.ElectionConfiguration != null) &&
                                          SystemConfiguration.ElectionConfiguration.DisplayBatteryTime;
      public DateTime SystemDateTime
      {
         get => _systemDateTime;
         set => Set(ref _systemDateTime, value);
      }

      public SystemStatsViewModel(
	      IMessenger messengerService,
	      IDeviceFacade deviceFacade,
	      IExpressVotePrinterFacade expressVotePrinterFacade,
	      ISmartCardFacade smartCardFacade,
	      IPrinterFacade printerFacade,
	      IMessageRepository messageRepository,
	      IEssLogger essLogger,
	      IAuditLogFacade audit,
	      IApiMonitor apiMonitor)
      {
         _messenger = messengerService;
         _deviceFacade = deviceFacade;
         _expressVotePrinterFacade = expressVotePrinterFacade;
         _smartCardFacade = smartCardFacade;
         _printerFacade = printerFacade;
         _messageRepository = messageRepository;
         _essLogger = essLogger;
         _audit = audit;

         _messenger.Register<SystemStatsUpdatedMessage>(this, SystemStatsUpdated);
         _messenger.Register<PeerToPeerConnectedStatusMessage>(this, PeerToPeerStatusChanged);
         _messenger.Register<DownloadInProgressMessage>(this, DownloadInProgressChanged);
         _messenger.Register<LiveChatResponseMessage>(this, NewMessageArrived);
         _messenger.Register<LiveChatReadMessage>(this, MessageRead);

         SystemDetails.HostSlowChanged += SystemDetails_HostSlowChanged;
         SystemDetails.HostConnectedChanged += SystemDetails_HostConnectedChanged;
         SystemConfiguration.ConfigurationChanged += SystemConfiguration_ConfigurationChanged;

         apiMonitor.ApiMonitorStatus += OnApiMonitorStatus;

         UpdateClockAndBatteryLevel();
         UpdatePeerToPeerConnectedStatus();

         Task.Run(async () => await UpdatePrinterStatusAsync());
         Task.Run(async () => await SetupTimer());
         Task.Run(async () => await SetupPrinterMonitor());
      }

      private void OnApiMonitorStatus(object sender, ApiMonitorInfo info)
      {
	      if (info == null)
		      return;

	      // Simple, easy to remember.
	      if (info.LastFiveStatusCodes?.Count >= 2 && info.LastFiveStatusCodes.GetRange(0, 2).Count(x => x == null) == 2)
	      {
		      _essLogger.LogError("Monitor Status: IsHostSlow=true Reason=Last two API calls have timed out.");
		      SystemDetails.IsHostSlow = true;
		      return;
	      }

	      // Only checked if the config value is greater than equal to 1000, else skipped.
         if (SystemConfiguration.ElectionConfiguration != null && 
             SystemConfiguration.ElectionConfiguration.SlowHostDurationThresholdMilliseconds >= 1000 &&
             info.DurationAvg > TimeSpan.FromMilliseconds(SystemConfiguration.ElectionConfiguration.SlowHostDurationThresholdMilliseconds))
         {
            _essLogger.LogError(
               $"Monitor Status: IsHostSlow=true Reason=Average Duration of {info.DurationAvg} exceeds configured limit of {SystemConfiguration.ElectionConfiguration.SlowHostDurationThresholdMilliseconds}.");
            SystemDetails.IsHostSlow = true;
            return;
         }
         
	      if (!SystemDetails.IsHostSlow)
		      return;

	      _essLogger.LogInformation("Monitor Status: IsHostSlow=false Reason=Acceptance criteria for full API calls has been met.");
	      SystemDetails.IsHostSlow = false;
      }

      private void MessageRead(LiveChatReadMessage message)
      {
         try
         {
            int numberOfMessages = Task.Run(async () => await _messageRepository.GetConversationCount()).Result;
            IsInstantMessageIconVisible = numberOfMessages != 0;
         }
         catch (Exception ex)
         {
            var logProps = new Dictionary<string, string> { { "Action", "SystemStatsViewModel.MessageRead" } };
            _essLogger.LogError(ex, logProps);
         }
      }

      private void NewMessageArrived(LiveChatResponseMessage message)
      {
         IsInstantMessageIconVisible = true;
      }

      private void SystemDetails_HostConnectedChanged(object sender, EventArgs e)
      {
         RefreshHostIconsVisibility();
         _audit.AddToAuditLog("System", $"Host connectivity changed. Pollbook {(SystemDetails.IsHostConnected ? "connected" : "disconnected")} to host.");
      }

      private void SystemDetails_HostSlowChanged(object sender, EventArgs e)
      {
         RefreshHostIconsVisibility();
      }

      private void DownloadInProgressChanged(DownloadInProgressMessage message)
      {
         RaisePropertyChanged(nameof(IsDownloadIconVisible));
      }

      private void SystemConfiguration_ConfigurationChanged(object sender, EventArgs e)
      { 
          RaisePropertyChanged(nameof(IsBatteryTimeEnabled));
         RefreshHostIconsVisibility();
         UpdatePeerToPeerConnectedStatus();
      }

      private void RefreshHostIconsVisibility()
      {
         RaisePropertyChanged(nameof(IsHostOnIconVisible));
         RaisePropertyChanged(nameof(IsHostOffIconVisible));
         RaisePropertyChanged(nameof(IsHostSlowIconVisible));
         RaisePropertyChanged(nameof(IsHostViewBoxVisible));
      }

      private void SystemStatsUpdated(SystemStatsUpdatedMessage message)
      {
         SystemStats = message.SystemStats;
      }

      private void PeerToPeerStatusChanged(PeerToPeerConnectedStatusMessage msg)
      {
         IsPeerConnected = SystemDetails.IsPeerConnected;
         UpdatePeerToPeerConnectedStatus();
      }

      private Task SetupTimer()
      {
         return PeriodicTask.Run(UpdateClockAndBatteryLevel, new TimeSpan(0, 0, 5), _cancelBatteryLevelToken.Token);
      }

      private Task SetupPrinterMonitor()
      {
         return PeriodicTask.Run(UpdatePrinterStatusAsync, new TimeSpan(0, 0, 10), _cancelBatteryLevelToken.Token);
      }

      private void UpdateClockAndBatteryLevel()
      {
         try
         {

            SystemDateTime = DateTime.Now;

            BatteryLife = _deviceFacade.GetBatteryPercentRemaining() / 100; //Needs to be a decimal value because of formatting in UI.

            if (!SystemDetails.IsMainWindowLoaded) return;

            if (BatteryLife > 0.2f)
            {
               _hasWarnedBatteryLevel10Percent = false;
               _hasWarnedBatteryLevel20Percent = false;
            }

            IsBatteryCharging = _deviceFacade.GetIsBatteryCharging();

            if (Math.Abs(BatteryLife - 0.2f) == 0 && !_hasWarnedBatteryLevel20Percent && !IsBatteryCharging)
            {
               SystemDetails.BatteryWarningLevel = 20;
               _messenger.Send(new BatteryWarningMessage());
               _hasWarnedBatteryLevel20Percent = true;
            }
            else if (Math.Abs(BatteryLife - 0.1f) == 0 && !_hasWarnedBatteryLevel10Percent && !IsBatteryCharging)
            {
               SystemDetails.BatteryWarningLevel = 10;
               _messenger.Send(new BatteryWarningMessage());
               _hasWarnedBatteryLevel10Percent = true;
            }
         }
         catch (Exception ex)
         {
            var logProps = new Dictionary<string, string> { { "Action", "SystemStatsViewModel.UpdateClockAndBatteryLevel" } };
            _essLogger.LogError(ex, logProps);
         }
      }

      private void UpdatePeerToPeerConnectedStatus()
      {
         if (SystemConfiguration.ElectionConfiguration == null)
         {
            return;
         }

         if (SystemConfiguration.ElectionConfiguration.PeerSync)
         {
            IsPeerIconVisible = true;
         }
         else
         {
            IsPeerIconVisible = false;
            IsPeerConnected = false;
         }

         RaisePropertyChanged(nameof(IsPeerConnected));
         RaisePropertyChanged(nameof(IsPeerToPeerOnIconVisible));
         RaisePropertyChanged(nameof(IsPeerToPeerOffIconVisible));
         RaisePropertyChanged(nameof(IsPeerToPeerViewBoxVisible));
      }

      private async Task UpdatePrinterStatusAsync()
      {
         try
         {
            IsPrinterIconVisible = false;

            if (SystemConfiguration.ElectionConfiguration == null)
               return;

            var bodEnabled = PrinterFacade.IsBodPrintingEnabled();
            var receiptEnabled = PrinterFacade.IsReceiptPrintingEnabled();
            var expressEnabled = ExpressVotePrinterFacade.IsExpressVotePrintingEnabled();

            if (bodEnabled || receiptEnabled || expressEnabled)
            {
               IsPrinterIconVisible = true;

               var pColor = new List<bool>();
                if (expressEnabled)
               {
                  if (!SystemConfiguration.ElectionConfiguration.DacEnabled)
                  {
                     var expressVote = _expressVotePrinterFacade.Verify();
                     pColor.Add(expressVote);
                  }
                  else
                  {
                     var dac = await _smartCardFacade.IsSmartCardReaderAttachedAsync();
                     pColor.Add(dac);
                  }
               }

               if (receiptEnabled)
               {
                  var printer = _printerFacade.GetPrinterInformationFromConfiguration(SystemConfiguration.ElectionConfiguration.SelectedExpressPollPrinterId);
                  pColor.Add(printer?.PrinterAvailable() ?? false);
               }

               if (bodEnabled)
               {
                  var printer = _printerFacade.GetPrinterInformationFromConfiguration(SystemConfiguration.ElectionConfiguration.SelectedBODPrinterId);
                  pColor.Add(printer?.PrinterAvailable() ?? false);
               }

               var trueCount = pColor.Count(x => x);
               if (pColor.Count == trueCount)
               {
                  PrinterIconColor = true;
               }
               else
               {
                  if (trueCount == 0)
                  {
                     PrinterIconColor = false;
                  }
                  else
                  {
                     PrinterIconColor = null;
                  }
               }
               _messenger.Send(new PrinterStatusUpdated());
            }
         }
         catch (Exception ex)
         {
            var logProps = new Dictionary<string, string> { { "Action", "SystemStatsViewModel.UpdatePrinterStatus" } };
            _essLogger.LogError(ex, logProps);
         }
      }

      /// <summary>
      /// Ultimately determine which range the battery life value falls within.
      /// This is done so that we can determine which battery image to use that will
      /// represent the percent remaining.
      /// </summary>
      /// <returns></returns>
      private string GetBatteryLifeKey()
      {
         if (IsBatteryCharging)
         {
            if (IsBatteryLifeLow)
            {
               return "ChargingLow";
            }
            else
            {
               return "ChargingHigh";
            }
         }

         List<BatteryRange> ranges = new List<BatteryRange>();
         long batteryLife = Convert.ToInt64(BatteryLife * 100);

         ranges.Add(new BatteryRange("Battery00", 0, 3));
         ranges.Add(new BatteryRange("Battery04", 4, 8));
         ranges.Add(new BatteryRange("Battery09", 9, 15));
         ranges.Add(new BatteryRange("Battery16", 16, 22));
         ranges.Add(new BatteryRange("Battery23", 23, 30));
         ranges.Add(new BatteryRange("Battery31", 31, 36));
         ranges.Add(new BatteryRange("Battery37", 37, 43));
         ranges.Add(new BatteryRange("Battery44", 44, 50));
         ranges.Add(new BatteryRange("Battery51", 51, 57));
         ranges.Add(new BatteryRange("Battery58", 58, 64));
         ranges.Add(new BatteryRange("Battery65", 65, 71));
         ranges.Add(new BatteryRange("Battery72", 72, 78));
         ranges.Add(new BatteryRange("Battery79", 79, 85));
         ranges.Add(new BatteryRange("Battery86", 86, 92));
         ranges.Add(new BatteryRange("Battery93", 93, 100));

         var found = ranges.FirstOrDefault(range => range.Eval(batteryLife));

         return found == null ? "00" : found.Key;
      }

      private string GetBatteryLifeStatusText()
      {

          if (_isBatteryTimeShowing)
          {
              return IsBatteryCharging ? "Charging" : _deviceFacade.GetBatteryRemainingTime();
          }

          return $"{Math.Round(BatteryLife, 2) * 100}%";

      }

      private async Task ShowBatteryTimeRemainingAsync()
        {
          try
          {
              await _audit.AddToAuditLogAsync("SystemStatsViewModel","Battery button was pressed");
              _isBatteryTimeShowing = true;
              RaisePropertyChanged(nameof(BatteryLifeStatusText));
              await Task.Delay(5000);
              _isBatteryTimeShowing = false;
              RaisePropertyChanged(nameof(BatteryLifeStatusText));

          }
          catch (Exception ex)
          { 
              _essLogger.LogError(ex,
                  new Dictionary<string, string>
                      { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
          }
        }
        public override void Cleanup()
        {
            base.Cleanup();

            _cancelBatteryLevelToken.Cancel(false);
            _cancelHostConnectionStatusToken.Cancel(false);
        }
   }
}