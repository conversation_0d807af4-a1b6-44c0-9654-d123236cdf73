namespace ESS.Pollbook
{
    public static class CaseConverter
    {
        public static string ToUpperFirstLetter(this string source)
        {
            if (string.IsNullOrWhiteSpace(source))
            {
                return source;
            }
            else
            {
                string result = string.Empty;
                string[] words = source.Split(' ');

                foreach (var word in words)
                {
                    if (!string.IsNullOrEmpty(word))
                    {
                        char[] letters = word.ToCharArray();
                        result += char.ToUpper(letters[0]) + word.Substring(1).ToLower() + " ";
                    }
                }

                return result.TrimEnd();
            }
        }
    }
}
