using ESS.Pollbook.Core;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Facade.PollPlace;
using ESS.Pollbook.Core.Messaging;
using ESS.Pollbook.ViewModel.PollPlaceDetails;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.Command;
using GalaSoft.MvvmLight.Messaging;
using System;
using System.Collections.Generic;
using System.Reflection;
using System.Threading.Tasks;
using System.Windows.Input;

namespace ESS.Pollbook.ViewModel.Voter
{
    public class WrongPollLocationViewModel : ViewModelBase
    {
	    private readonly IFrameNavigationService _navigation;
	    private readonly IPollPlaceFacade _pollPlaceFacade;
	    private readonly IPrintPollPlaceFacade _printPollLocationFacade;
	    private readonly IMessenger _messenger;
        private readonly IEssLogger _essLogger;
	    private readonly IAuditLogFacade _auditLogFacade;

	    private string PageName => _navigation.PageName(GetType().FullName);

	    public ICommand PrintDetailsCommand => new RelayCommand(PrintDetails);
	    public ICommand SendTextCommand => new RelayCommand(SendTextMessage);

	    public string PrintDetailsLabel => UIText.PrintDetails;
	    public string SendTextLabel => UIText.SendText;

	    private PollPlaceDto _votersPollPlace;

	    #region Public Properties

	    public bool IsSendTextVisible => SystemConfiguration.ElectionConfiguration.EnableSmsMessages;

        public PollPlaceDto VotersPollPlace
	    {
		    get => _votersPollPlace;
		    private set => Set(ref _votersPollPlace, value);
	    }

	    public string LocationInfo => VotersPollPlace == null ? string.Empty : VotersPollPlace.Formatted;

	    #endregion

	    #region Constructor

	    public WrongPollLocationViewModel(IMessenger messengerService,
		    IFrameNavigationService navigationService,
		    IPollPlaceFacade pollPlaceFacade,
		    IPrintPollPlaceFacade printPollLocationFacade,
		    IEssLogger essLogger,
		    IAuditLogFacade auditLogFacade)
	    {
		    _messenger = messengerService;
		    _navigation = navigationService;
		    _pollPlaceFacade = pollPlaceFacade;
		    _printPollLocationFacade = printPollLocationFacade;
            _essLogger = essLogger;
		    _auditLogFacade = auditLogFacade;

		    _messenger.Register<VoterSelectedMessage>(this, VoterSelected);
	    }

	    #endregion

	    #region Private Methods

	    private void VoterSelected(VoterSelectedMessage message)
	    {
		    Task.Run(async () =>
		    {
			    VoterDto voter = message.Voter;
			    if (!voter.HasHostResults && LoggedInPollplaceInfo.LoggedInPollPlace.PollingPlaceVoteCenterIndicator == 0)
			    {
				    VotersPollPlace = await _pollPlaceFacade.GetPollPlaceDetailsByPrecinctSplitIDAsync(voter.PrecinctSplitId);
			    }
			    else
			    {
				    VotersPollPlace = voter.VoterPollPlaceInfo;
			    }
		    });
	    }

	    private void PrintDetails()
	    {
		    try
		    {
			    _auditLogFacade.AddToAuditLog(PageName, $"'{PrintDetailsLabel}' button was activated.");
			    string pollName = VotersPollPlace.PollingPlaceName;
			    string pollHouseNum = VotersPollPlace.PollingPlaceHouseNumber;
			    string pollStreet = VotersPollPlace.PollingPlaceStreetName;
			    string pollStreet2 = VotersPollPlace.PollingPlaceUnitName;
			    string pollCity = VotersPollPlace.PollingPlaceCityName;
			    string pollState = VotersPollPlace.PollingPlaceStateProvinceCode;
			    string pollPostalCode = VotersPollPlace.PollingPlacePostalCode;

			    var printResponse = _printPollLocationFacade.PrintPollPlace(pollName, pollHouseNum, pollStreet, pollStreet2, pollState, pollCity, pollPostalCode);

			    if (printResponse == null)
			    {
				    _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, PrinterErrorMessages.PrinterNotAvailble));
			    }
			    else if (!string.IsNullOrEmpty(printResponse.PrinterErrorMessage))
			    {
				    _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, printResponse.PrinterErrorMessage));
			    }
		    }
		    catch (Exception ex)
		    {
				_essLogger.LogError(ex,
				new Dictionary<string, string>
					{ { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
			    _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
		    }
	    }

	    private void SendTextMessage()
	    {
		    try
		    {
			    _auditLogFacade.AddToAuditLog(PageName, $"'{SendTextLabel}' button was activated.");
			    _messenger.Send(new StatusBarMessage(isVisible: false));
			    _messenger.Send(new ResetSendTextMessage(VotersPollPlace));
			    _messenger.Send(new ParentControlTextMessage("WrongPollLocationViewModel"));

			    _navigation.NavigateTo<SendTextViewModel>();
		    }
		    catch (Exception ex)
		    {
                _essLogger.LogError(ex,
                    new Dictionary<string, string>
                        { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
		    }
	    }

	    #endregion
    }
}
