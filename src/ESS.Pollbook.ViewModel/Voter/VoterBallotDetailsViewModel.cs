using ESS.Pollbook.Core.Commands;
using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.NavigationParameters;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Facade.Device;
using ESS.Pollbook.Facade.DynamicControls;
using ESS.Pollbook.Facade.ElectionJurisdictionEnum;
using ESS.Pollbook.Facade.Party;
using ESS.Pollbook.Facade.PollbookDefinedText;
using ESS.Pollbook.Facade.PollbookTransaction;
using ESS.Pollbook.Facade.PrecinctSplit;
using ESS.Pollbook.Facade.VoterBallot;
using ESS.Pollbook.Facade.VRSignatures;
using ESS.Pollbook.Core.Messaging;
using ESS.Pollbook.ViewModel.Supervisor;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.CommandWpf;
using GalaSoft.MvvmLight.Messaging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using System.Windows.Input;

namespace ESS.Pollbook.ViewModel.Voter
{
    public class VoterBallotDetailsViewModel : ViewModelBase
    {
        private readonly IFrameNavigationService _navigation;
        private readonly IMessenger _messenger;
        private readonly IPartyFacade _partyFacade;
        private readonly ISignatureComparisonFacade _vrSignaturesFacade;
        private readonly IPollbookTransactionFacade _pollbookTransactionFacade;
        private readonly IVoterJurisdictionEnumFacade _voterJurisdictionEnumFacade;
        private readonly IVoterBallotFacade _voterBallotFacade;
        private readonly IPrinterFacade _printerFacade;
        private readonly IPrecinctSplitFacade _precinctSplitFacade;
        private readonly IPollbookDefinedTextFacade _pollbookDefinedTextFacade;
        private readonly IDynamicControlsFacade _dynamicControlsFacade;
        private readonly IEssLogger _essLogger;
        private readonly IAuditLogFacade _auditLogFacade;

        private string PageName => _navigation.PageName(GetType().FullName);

        public string AddressChangedLabel => DefinedText.PollbookDefinedTextDtos.FirstOrDefault(n => n.PollbookDefinedTextLanguage.Equals("English") && n.PollbookDefinedTextName.Equals("AddressChangedButton"))?.PollbookDefinedTextValue;

        public ICommand BackCommand => new RelayCommand(Back);
        public ICommandAsync PrintCommand => new CommandAsync(PrintAuthDocAsync);

        public string BackLabel => UIText.Back;
        public string PrintLabel => UIText.Print;

        private VoterHistoryDto BallotDetails { get; set; }

        private VoterDto Voter { get; set; }

        public string Timestamp => BallotDetails.TimeStampString;

        public string Pollplace => BallotDetails.Location;

        public string SystemId => BallotDetails.DeviceId;

        public string SerialNumber => BallotDetails.SerialNumber;

        public string Username => BallotDetails.Username;

        private int BallotStyle => BallotDetails.BallotStyleId ?? 0;

        private string _ballotStyleDescription;

        public string BallotStyleDescription
        {
            get => _ballotStyleDescription;
            set => Set(ref _ballotStyleDescription, value);
        }

        private string _ballotType;

        public string BallotType
        {
	        get => _ballotType;
	        set => Set(ref _ballotType, value);
        }

        private string _ballotParty;

        public string BallotParty
        {
	        get => _ballotParty;
	        set => Set(ref _ballotParty, value);
        }

        public string VotingMethod => BallotDetails.IsProvisional
	        ? _pollbookDefinedTextFacade.GetPollbookDefinedTextForLanguageWithDefault(
		        "ProvisionalLabel", "English", UIText.ProvisionalLabelDefault)
	        : "Standard";

        private bool _capturedBallotNumberVisibility;

        public bool CapturedBallotNumberVisibility
        {
	        get => _capturedBallotNumberVisibility;
	        set => Set(ref _capturedBallotNumberVisibility, value);
        }

        private string _capturedBallotNumber;

        public string CapturedBallotNumber
        {
	        get => _capturedBallotNumber;
	        set => Set(ref _capturedBallotNumber, value);
        }

        public bool ProvisionalIdVisibility => BallotDetails.IsProvisional;

        public string ProvisionalId => BallotDetails.ProvisionalId;

        public string AddressChanged => BallotDetails.HasAddressChanged ? "Yes" : "No";

        public bool ShowAddressChanged => SystemConfiguration.ElectionConfiguration.AddressVerification;

        public bool ShowSignature => SystemConfiguration.ElectionConfiguration.CaptureElectronicSignature;

        public bool ShowSignatureCaptured =>
            Voter.VoterTransactionSignature != null && Voter.VoterTransactionSignature.Length > 0;

        public bool ShowSignatureOnPaper =>
            Voter.VoterTransactionSignature == null || Voter.VoterTransactionSignature.Length == 0;

        public bool ShowSignatureOnFile => SystemConfiguration.ElectionConfiguration.VerifyElectronicSignatures;

        public string DetailHeaderCopy
        {
            get
            {
                if (BallotDetails.IsReissue) return "Ballot Reissued Details";

                return BallotDetails.ActivityTypeCode == nameof(TransactionType.BallotIssue)
                    ? "Ballot Issued Details"
                    : "";
            }
        }


        public byte[] SignatureOnFile => Voter.VoterVRSignatureImage;

        public byte[] SignatureOnTransaction => Voter.VoterTransactionSignature;

        public bool PrintEnabled => !Printing && !BallotDetails.IsThisBallotCancelled;
        private bool _printing;

        public bool Printing
        {
            get => _printing;
            set
            {
                Set(ref _printing, value);
                RaisePropertyChanged(nameof(PrintEnabled));
            }
        }

        public bool PrintVisible => SystemConfiguration.ElectionConfiguration.PrintingEnabled &&
                                    SystemConfiguration.ElectionConfiguration.AuthEnableReprint;

        private List<DynamicControlsDto> _dynamicFormControls;
        public IEnumerable<DynamicControlsDto> GetDynamicControl => _dynamicFormControls;

        public string ProvisionalIdLabel { get; }

        public VoterBallotDetailsViewModel(IFrameNavigationService navigation,
            IMessenger messengerService,
            IPartyFacade partyFacade,
            ISignatureComparisonFacade vrSignaturesFacade,
            IPollbookTransactionFacade pollbookTransactionFacade,
            IVoterJurisdictionEnumFacade voterJurisdictionEnumFacade,
            IVoterBallotFacade voterBallotFacade,
            IPrinterFacade printerFacade,
            IPrecinctSplitFacade precinctSplitFacade,
            IDynamicControlsFacade dynamicControlsFacade,
            IPollbookDefinedTextFacade pollbookDefinedTextFacade,
            IEssLogger logger,
            IAuditLogFacade auditLogFacade)
        {
            _navigation = navigation;
            _messenger = messengerService;
            _partyFacade = partyFacade;
            _vrSignaturesFacade = vrSignaturesFacade;
            _pollbookTransactionFacade = pollbookTransactionFacade;
            _voterJurisdictionEnumFacade = voterJurisdictionEnumFacade;
            _voterBallotFacade = voterBallotFacade;
            _printerFacade = printerFacade;
            _precinctSplitFacade = precinctSplitFacade;
            _dynamicControlsFacade = dynamicControlsFacade;
            _pollbookDefinedTextFacade = pollbookDefinedTextFacade;
            _essLogger = logger;
            _auditLogFacade = auditLogFacade;

            _messenger.Register<VoterBallotDetailsRequestedMessage>(this, BallotDetailsClicked);
            _messenger.Register<SuccessSupervisorPWForAuthDocPrintingMessage>(this,
	            async message => await PrintAsync());

            // Set once to avoid multiple calls
            ProvisionalIdLabel = _pollbookDefinedTextFacade.GetPollbookDefinedTextForLanguageWithDefault(
	            "ProvisionalLabel", "English", UIText.ProvisionalLabelDefault) + " ID";
        }

        private async Task PrintAuthDocAsync()
        {
            try
            {
                await _auditLogFacade.AddToAuditLogAsync(PageName, $"'{PrintLabel}' button was activated.");

                if (SystemConfiguration.ElectionConfiguration.AuthEnableReprintPassword)
                {
                    var navParameter = new SupervisorPwEntryParameter
                    {
                        Voter = Voter,
                        TargetType = SupervisorPwEntryTargetType.AuthDoc
                    };

                    _navigation.NavigateTo<SupervisorPwEntryViewModel>(navParameter,
                        NavigationFrameEnum.ModalDialogFrame,
                        false);

                    return;
                }

                await PrintAsync();
            }
            catch (Exception ex)
            {
	            _essLogger.LogError(ex,
		            new Dictionary<string, string>
			            { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
	            _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error,
		            SystemDetails.GenericErrorMessage));
            }
        }

        private async Task PrintAsync()
        {
            _navigation.NavigateTo<PrintingReceiptViewModel>(NavigationFrameEnum.ContextFrame, false);

            Printing = true;

            var dto =
                await _precinctSplitFacade.GetPrecinctSplitDisplayNameByBallotStyleIdAndPrecinctSplitBallotStyleId(
                    BallotDetails.BallotStyleId ?? 0, BallotDetails.PrecinctSplitBallotStyleId);

            Voter.PrecinctParty = new PrecinctPartyDto
            {
                PartyDisplayName = BallotParty,
                PrecinctSplitDisplayName = dto.PrecinctSplitName,
                BallotStyleShortDescriptionText = BallotStyleDescription
            };

            Voter.VoterBallotDto = new VoterBallotDto
            {
                ProvisionalId = BallotDetails.ProvisionalId,
                IsProvisional = BallotDetails.IsProvisional,
                TimeStamp = BallotDetails.TimeStamp,
                BallotType = BallotType
            };

            Voter.Affidavits =
                BallotDetails.Affidavits?.ToDictionary(m => m.AffidavitType, m => m);

            var printDateTime = BallotDetails.TimeStamp;

            var printResponse = await _printerFacade.PrintVoterAuthorityDocument(Voter, printDateTime);
            if (_printerFacade.DidPrintingFail(printResponse))
            {
               var navParameter = new VoterAuthorityDocParameter
               {
                  Voter = Voter,
                  IsInBallotIssueWorkFlow = false,
                  PrintDateTime = printDateTime
               };

               _navigation.NavigateTo<PrintAuthDocFailedViewModel>(navParameter, NavigationFrameEnum.ContextFrame,
                    false);
            }
            else
            {
                _navigation.NavigateTo<VoterBallotDetailsViewModel>(NavigationFrameEnum.ContextFrame);
            }

            Printing = false;
        }

        private void Back()
        {
            try
            {
                _auditLogFacade.AddToAuditLog(PageName, $"'{BackLabel}' button was activated.");
                _navigation.GoBack();
            }
            catch (Exception ex)
            {
	            _essLogger.LogError(ex,
		            new Dictionary<string, string>
			            { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
	            _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error,
		            SystemDetails.GenericErrorMessage));
            }
        }

        /// <summary>
        /// Handles the message, by setting the voter and ballot details information, so it can be displayed
        /// </summary>
        /// <param name="message"></param>
        private void BallotDetailsClicked(VoterBallotDetailsRequestedMessage message)
        {
            // Voter and BallotDetails must be set as a result of this message, or other methods will throw NullReferenceException!
            BallotDetails = message.VoterBallotDetails;
            Voter = JsonConvert.DeserializeObject<VoterDto>(JsonConvert.SerializeObject(message.Voter));

            //if connected to host, got the signature from host transaction
            Voter.VoterTransactionSignature = message.VoterBallotDetails.SignatureImage;
            CompleteVoterBallotInfoAsync().ConfigureAwait(false);
        }

        private async Task CompleteVoterBallotInfoAsync()
        {
            //Get the Ballot Style Description
            var ballotInfo = _voterBallotFacade.GetVoterBallotDescription(BallotDetails.BallotStyleId ?? 0);
            BallotStyleDescription = ballotInfo.BallotStyleDescription;

            //Get the party name     
            BallotParty = (await _partyFacade.GetPartyByBallotStyleId(BallotStyle)).PartyDisplayName;

            var results = await _voterJurisdictionEnumFacade.GetEnumValueNameAsync((int)EssEnumeration.MediaType,
                Convert.ToInt32(BallotDetails.MediaType));
            var electionJurisdictionEnumValueDtos =
                results as IList<ElectionJurisdictionEnumValueDto> ?? results.ToList();
            var mediaTypeEnumName =
                electionJurisdictionEnumValueDtos.Select(x => x.EnumerationValueName).FirstOrDefault();
            var mediaTypeDefinedText =
                _pollbookDefinedTextFacade.GetPollbookDefinedTextForLanguage(mediaTypeEnumName, "English");
            BallotType = string.IsNullOrEmpty(mediaTypeDefinedText) ? mediaTypeEnumName : mediaTypeDefinedText;

            var vrSignResult = await _vrSignaturesFacade.GetVrSignatureByVoterIdAsync(Voter.VoterId);
            if (vrSignResult != null)
            {
                Voter.VoterVRSignatureImage = vrSignResult;
                RaisePropertyChanged(nameof(SignatureOnFile));
            }

            //not connected to host and need to get the transaction from local
            if (Voter.VoterTransactionSignature == null)
            {
                //Get the signature captured in the transaction
                var vrTxSignResult =
                    await _pollbookTransactionFacade.GetVoterSignatureFromTransactionAsync(
                        BallotDetails.TransactionGuid);

                if (vrTxSignResult != null)
                {
                    Voter.VoterTransactionSignature = vrTxSignResult.VoterTransactionSignature;
                    RaisePropertyChanged(nameof(SignatureOnTransaction));
                    RaisePropertyChanged(nameof(SignatureOnFile));
                    RaisePropertyChanged(nameof(ShowSignatureCaptured));
                    RaisePropertyChanged(nameof(ShowSignatureOnPaper));
                    RaisePropertyChanged(nameof(ShowSignature));
                }
            }
        }

        public void PageIsLoaded()
        {
            try
            {
                Printing = false;

                CapturedBallotNumberVisibility = false;

                // get a list of dynamic controls that would have been on VoterVerificationView page
                _dynamicFormControls = Task.Run(async () =>
                        await _dynamicControlsFacade.GetDynamicControlsByFormNameAsync("VoterVerificationView"))
                    .Result
                    .ToList();

                foreach (var control in BallotDetails.DynamicData)
                {
                    // find the control that matches the dynamic data item
                    var formControl = _dynamicFormControls.Find(x => x.Control_ID == int.Parse(control.Key));
                    if (formControl != null)
                    {
                        formControl.Value = control.Value;

                        if (!string.IsNullOrEmpty(formControl.Control_Binding_Property) &&
                            formControl.Control_Binding_Property.Equals("CapturedBallotNumber")
                            && !string.IsNullOrEmpty(control.Value))
                        {
                            CapturedBallotNumber = control.Value;
                            CapturedBallotNumberVisibility = true;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
	            _essLogger.LogError(ex,
		            new Dictionary<string, string>
			            { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
            }
        }
    }
}
