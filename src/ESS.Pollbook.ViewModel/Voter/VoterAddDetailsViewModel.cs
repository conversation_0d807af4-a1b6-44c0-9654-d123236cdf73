using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Core.Messaging;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.Command;
using GalaSoft.MvvmLight.Messaging;
using System;
using System.Collections.Generic;
using System.Windows.Input;

namespace ESS.Pollbook.ViewModel.Voter
{
    public class VoterAddDetailsViewModel : ViewModelBase
    {
        private readonly IFrameNavigationService _navigation;
        private readonly IMessenger _messenger;
        private readonly IEssLogger _essLogger;
        public ICommand BackCommand => new RelayCommand(Back);
        private string _timestamp;
        public string Timestamp
        {
            get => _timestamp;
            set => Set(ref _timestamp, value);
        }

        private string _pollplace;
        public string Pollplace
        {
            get => _pollplace;
            set => Set(ref _pollplace, value);
        }

        private string _systemId;
        public string SystemId
        {
            get => _systemId;
            set => Set(ref _systemId, value);
        }

        private string _serialNumber;

        public string SerialNumber
        {
            get => _serialNumber;
            set => Set(ref _serialNumber, value);
        }

        private string _username;
        public string Username
        {
            get => _username;
            set => Set(ref _username, value);
        }

        private string _title;
        public string Title
        {
            get => _title;
            set => Set(ref _title, value);
        }

        private string _detailsTitle;
        public string DetailsTitle
        {
            get => _detailsTitle;
            set => Set(ref _detailsTitle, value);
        }

        private string _name;
        public string Name
        {
            get => _name;
            set => Set(ref _name, value);
        }

        private string _sourceKey;
        public string SourceKey
        {
            get => _sourceKey;
            set => Set(ref _sourceKey, value);
        }

        public VoterAddDetailsViewModel(IFrameNavigationService navigation, IMessenger messenger, IEssLogger essLogger)
        {
            _navigation = navigation;
            _messenger = messenger;
            _essLogger = essLogger;

            _messenger.Register<VoterAddMessage>(this, VoterAddDetailsRequested);

            //Initialy, the page title and the details section title will be the same, so initalize them in the constructor
            Title = "Add Voter Details";
            DetailsTitle = "Add Voter Details";
        }

        private void Back()
        {
            try
            {
                _messenger.Send(new StatusBarMessage(isVisible: true));
                _navigation.NavigateTo<DashboardViewModel>(NavigationFrameEnum.MainFrame, false);
                _navigation.GoBack();
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string> { { "Action", "BallotCancelDetailsViewModel.Back" } };
                _essLogger.LogError(ex, logProps);
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }

        private void VoterAddDetailsRequested(VoterAddMessage message)
        {
            Timestamp = message.VoterAddDetails.TimeStampString;
            Pollplace = message.VoterAddDetails.Location;
            Username = message.VoterAddDetails.Username;
            SystemId = message.VoterAddDetails.DeviceId;
            SerialNumber = message.VoterAddDetails.SerialNumber;
            Name = BuildVoterFullName(message.VoterAddDetails);
            SourceKey = message.VoterAddDetails.VoterSourceKey;
        }

        private string BuildVoterFullName(VoterHistoryDto voterHistoryDto)
        {
            var fullName = string.Empty;

            fullName += string.IsNullOrWhiteSpace(voterHistoryDto.NameSuffix) ? "" : voterHistoryDto.NameSuffix + " ";
            fullName += voterHistoryDto.LastName + ", ";
            fullName += voterHistoryDto.FirstName;
            fullName += string.IsNullOrWhiteSpace(voterHistoryDto.MiddleName) ? "" : " " + voterHistoryDto.MiddleName;

            return fullName;
        }
    }
}
