using ESS.Pollbook.Core.Commands;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.Model;
using ESS.Pollbook.Core.NavigationParameters;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Core.Utilities;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Facade.Device;
using ESS.Pollbook.Facade.ElectionJurisdictionEnum;
using ESS.Pollbook.Facade.PollPlace;
using ESS.Pollbook.Facade.PrecinctSplit;
using ESS.Pollbook.Facade.StreetSearch;
using ESS.Pollbook.Facade.VoterDetails;
using ESS.Pollbook.Facade.VoterEditInfo;
using ESS.Pollbook.ViewModel.Affidavit;
using ESS.Pollbook.Core.Messaging;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.Command;
using GalaSoft.MvvmLight.Messaging;
using KellermanSoftware.CompareNetObjects;
using NClone;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Input;
using ESS.Pollbook.Core.Common;

namespace ESS.Pollbook.ViewModel.Voter
{
    public class VoterEditViewModel : ViewModelBase
    {
        private readonly IEssLogger _essLogger;
        private readonly IMessenger _messenger;
        private readonly IFrameNavigationService _navigation;
        private readonly IPrecinctSplitFacade _precinctSplitFacade;
        private readonly IVoterAddEditInfoFacade _voterAddEditInfoFacade;
        private readonly IPollPlaceFacade _pollPlaceFacade;
        private readonly IStreetSearchFacade _streetSearchFacade;
        private readonly IAuditLogFacade _auditLogFacade;
        private readonly IVoterDetailsFacade _voterDetailsFacade;
        private readonly IVoterJurisdictionEnumFacade _voterJurisdictionEnumFacade;
        private readonly IPrinterFacade _printerFacade;

        private string PageName => _navigation.PageName(this.GetType().FullName);

        private SemaphoreSlim _semaphore = new SemaphoreSlim(1, 1);
        private bool _disposed;

        public ICommand ResetAddressCommand => new RelayCommand(ResetAddress);
        public ICommandAsync SaveCommand => new CommandAsync(Save);
        public ICommand CancelCommand => new RelayCommand(Cancel);
        public ICommand ResetPrecinctCommand => new RelayCommand(ResetAndClearPrecinct);

        public string CancelLabel => UIText.Cancel;
        public string SaveLabel => _enableAddressChangeAffidavit ? UIText.Next : UIText.Save;

        public string AddressSearchLabel => UIText.AddressSearch;

        private VoterDto _editedVoter;
        private List<PrecinctSplitDto> _results = new List<PrecinctSplitDto>();
        private bool _enableAddressChangeAffidavit => (ElectionAddressOnlyChangeType == Core.Common.AddressOnlyChangeType.Addr_Affidavit ||
                                                       ElectionAddressOnlyChangeType == Core.Common.AddressOnlyChangeType.Addr_All) && _isModal;
        private bool _enableAddressChangedWorkflow => ElectionAddressOnlyChangeType != Core.Common.AddressOnlyChangeType.None && _isModal;

        public string PrecinctLabel => DefinedText.PollbookDefinedTextDtos.FirstOrDefault(n => n.PollbookDefinedTextLanguage.Equals("English") && n.PollbookDefinedTextName.Equals("Precinct"))?.PollbookDefinedTextValue;
        public string PrecinctErrorLabel => string.Format(UIText.VoterEditPrecinctErrorLabel, PrecinctLabel);
        public string PageTitle => string.Format(UIText.VoterEditViewTitle, Voter?.Surname);
        private static readonly Regex ZipCodeRegex = new Regex(@"^\d{5}(-\d{4})?$");
        public AddressOnlyChangeType ElectionAddressOnlyChangeType => SystemConfiguration.ElectionConfiguration.AddressOnlyChangeType;

        public bool IsSaveEnabled
        {
            get
            {
                try
                {
                    IsEmailAddressValid = ValidateEmailAddress();
                    IsFirstNameValid = Helpers.IsTextFieldValid(FirstName, false, 50);
                    IsMiddleNameValid = Helpers.IsTextFieldValid(MiddleName, true, 50);
                    IsLastNameValid = Helpers.IsTextFieldValid(LastName, false, 50);
                    IsNameSuffixValid = Helpers.IsTextFieldValid(NameSuffix, true, 10);

                    IsHouseNumberValid = !string.IsNullOrWhiteSpace(HouseNumber) && HouseNumber.Length <= 50;
                    IsHouseNumberFractionValid = Helpers.IsTextFieldValid(HouseNumberFraction, true, 50);
                    IsStreetNameValid = !string.IsNullOrWhiteSpace(StreetName) && StreetName.Length <= 100;
                    IsUnitValueValid = Helpers.IsTextFieldValid(UnitValue, true, 50);

                    IsCityValid = !string.IsNullOrWhiteSpace(City) && City.Length <= 50 && !City.Any(char.IsDigit);
                    IsZipValid = !string.IsNullOrWhiteSpace(Zip) && (Zip.Length >= 5 && Zip.Length <= 10) && ZipCodeRegex.IsMatch(Zip);
                    IsStateValid = !string.IsNullOrEmpty(State) && State.Length == 2;

                    IsPrecinctValid = !(SearchTerm.Length == 0 || SelectedPrecinctSplitItem == null);

                    DidDataChange();

                    return IsEmailAddressValid && IsFirstNameValid && IsMiddleNameValid && IsLastNameValid && IsNameSuffixValid && IsHouseNumberValid && IsHouseNumberFractionValid && IsStreetNameValid && IsUnitValueValid && IsCityValid && IsStateValid && IsZipValid && IsPrecinctValid && IsDataChanged && _semaphore.CurrentCount == 1;
                }
                catch (Exception ex)
                {
                    var logProps = new Dictionary<string, string> { { "Action", "VoterEditViewModel.IsSaveEnabled" } };
                    _essLogger.LogError(ex, logProps);
                    return false;
                }
            }
        }

        private bool _hideVoterName;
        public bool HideVoterName
        {
            get => _hideVoterName;
            set => Set(ref _hideVoterName, value);
        }

        private bool _isModal;
        public bool IsModal
        {
            get => _isModal;
            set => Set(ref _isModal, value);
        }

        private bool _isDataChanged;
        private bool IsDataChanged
        {
            get => _isDataChanged;
            set => Set(ref _isDataChanged, value);
        }

        private VoterDto _voter;
        private VoterDto Voter
        {
            get => _voter;
            set
            {
                if (Set(ref _voter, value))
                    RaisePropertyChanged(nameof(PageTitle));
            }
        }

        public string EmailAddress
        {
            get => _editedVoter.EmailAddress;
            set
            {
                _editedVoter.EmailAddress = value;
                RaisePropertyChanged();
                RaisePropertyChanged(nameof(IsSaveEnabled));
            }
        }

        public string FirstName
        {
            get => _editedVoter.FirstName;
            set
            {
                _editedVoter.FirstName = value;
                _editedVoter.FirstNameSearch = Helpers.GetAlphaNumericString(value);
                RaisePropertyChanged();
                RaisePropertyChanged(nameof(IsSaveEnabled));
            }
        }

        public string MiddleName
        {
            get => _editedVoter.MiddleName;
            set
            {
                _editedVoter.MiddleName = value;
                RaisePropertyChanged();
                RaisePropertyChanged(nameof(IsSaveEnabled));
            }
        }

        public string LastName
        {
            get => _editedVoter.LastName;
            set
            {
                _editedVoter.LastName = value;
                _editedVoter.LastNameSearch = Helpers.GetAlphaNumericString(value);
                RaisePropertyChanged();
                RaisePropertyChanged(nameof(IsSaveEnabled));
            }
        }

        public string NameSuffix
        {
            get => _editedVoter.NameSuffix;
            set
            {
                _editedVoter.NameSuffix = value;
                RaisePropertyChanged();
                RaisePropertyChanged(nameof(IsSaveEnabled));
            }
        }

        public string HouseNumber
        {
            get => _editedVoter.HouseNumber;
            set
            {
                _editedVoter.HouseNumber = value;
                RaisePropertyChanged();
                RaisePropertyChanged(nameof(IsSaveEnabled));
            }
        }

        public string HouseNumberFraction
        {
            get => _editedVoter.HouseNumberFractionValue;
            set
            {
                _editedVoter.HouseNumberFractionValue = value;
                RaisePropertyChanged();
                RaisePropertyChanged(nameof(IsSaveEnabled));
            }
        }

        public string StreetName
        {
            get => _editedVoter.StreetName;
            set
            {
                _editedVoter.StreetName = value;
                RaisePropertyChanged();
                RaisePropertyChanged(nameof(IsSaveEnabled));
            }
        }

        private ElectionJurisdictionEnumValueDto _selectedUnitType;
        public ElectionJurisdictionEnumValueDto SelectedUnitType
        {
            get => _selectedUnitType;
            set
            {
                Set(ref _selectedUnitType, value);
                if (_editedVoter == null) return;
                _editedVoter.UnitTypeCodeId = value?.JurisdictionEnumerationValueId;
                _editedVoter.UnitTypeName = value?.JurisdictionEnumerationValueName;

                RaisePropertyChanged(nameof(IsSaveEnabled));
            }
        }

        public string UnitValue
        {
            get => _editedVoter.UnitValue;
            set
            {
                _editedVoter.UnitValue = value;
                RaisePropertyChanged();
                RaisePropertyChanged(nameof(IsSaveEnabled));
            }
        }

        public string City
        {
            get => _editedVoter.City;
            set
            {
                _editedVoter.City = value;
                RaisePropertyChanged();
                RaisePropertyChanged(nameof(IsSaveEnabled));
            }
        }

        public string State
        {
            get => _editedVoter.State;
            set
            {
                _editedVoter.State = value;
                RaisePropertyChanged();
                RaisePropertyChanged(nameof(IsSaveEnabled));
            }
        }

        public string Zip
        {
            get => _editedVoter.Zip;
            set
            {
                _editedVoter.Zip = value;
                RaisePropertyChanged();
                RaisePropertyChanged(nameof(IsSaveEnabled));
            }
        }

        private bool _isEmailAddressValid;
        public bool IsEmailAddressValid
        {
            get => _isEmailAddressValid;
            set => Set(ref _isEmailAddressValid, value);
        }

        private bool _isFirstNameValid;
        public bool IsFirstNameValid
        {
            get => _isFirstNameValid;
            set => Set(ref _isFirstNameValid, value);
        }

        private bool _isMiddleNameValid;
        public bool IsMiddleNameValid
        {
            get => _isMiddleNameValid;
            set => Set(ref _isMiddleNameValid, value);
        }

        private bool _isLastNameValid;
        public bool IsLastNameValid
        {
            get => _isLastNameValid;
            set => Set(ref _isLastNameValid, value);
        }

        private bool _isNameSuffixValid;
        public bool IsNameSuffixValid
        {
            get => _isNameSuffixValid;
            set => Set(ref _isNameSuffixValid, value);
        }

        private bool _isHouseNumberValid;
        public bool IsHouseNumberValid
        {
            get => _isHouseNumberValid;
            set => Set(ref _isHouseNumberValid, value);
        }

        private bool _isHouseNumberFractionValid;

        public bool IsHouseNumberFractionValid
        {
            get => _isHouseNumberFractionValid;
            set => Set(ref _isHouseNumberFractionValid, value);
        }

        private bool _isStreetNameValid;
        public bool IsStreetNameValid
        {
            get => _isStreetNameValid;
            set => Set(ref _isStreetNameValid, value);
        }

        private bool _isUnitValueValid;
        public bool IsUnitValueValid
        {
            get => _isUnitValueValid;
            set => Set(ref _isUnitValueValid, value);
        }

        private bool _isCityValid;
        public bool IsCityValid
        {
            get => _isCityValid;
            set => Set(ref _isCityValid, value);
        }

        private bool _isStateValid;
        public bool IsStateValid
        {
            get => _isStateValid;
            set => Set(ref _isStateValid, value);
        }

        private bool _isZipValid;
        public bool IsZipValid
        {
            get => _isZipValid;
            set => Set(ref _isZipValid, value);
        }

        private bool _isPrecinctValid;
        public bool IsPrecinctValid
        {
            get => _isPrecinctValid;
            set => Set(ref _isPrecinctValid, value);
        }

        private bool _displayPrecinctResults;
        public bool DisplayPrecinctResults
        {
            get => _displayPrecinctResults && SearchTerm.Length > 0 && Results.Count > 0 && SelectedPrecinctSplitItem == null;
            set => Set(ref _displayPrecinctResults, value);
        }

        public bool EnableEmailEdit => SystemConfiguration.ElectionConfiguration.EnableEmailEdit;

        private string _searchTerm = string.Empty;
        public string SearchTerm
        {
            get => _searchTerm;
            set
            {
                if (Set(ref _searchTerm, value))
                {
                    ClearPrecinctResults();
                    RaisePropertyChanged(nameof(DisplayPrecinctResults));
                    RaisePropertyChanged(nameof(NoResults));
                    PrecinctSearch();
                }
            }
        }

        private int _selectedTab;
        public int SelectedTab
        {
            get => _selectedTab;
            set => Set(ref _selectedTab, value);
        }

        public bool AdditionalInfoTabVisible => EnableEmailEdit; // add more conditions as the tab gains fields

        private PrecinctSplitDto _selectedPrecinctSplitItem;
        public PrecinctSplitDto SelectedPrecinctSplitItem
        {
            get => _selectedPrecinctSplitItem;
            set
            {
                Set(ref _selectedPrecinctSplitItem, value);

                if (_selectedPrecinctSplitItem != null)
                {
                    ClearPrecinctResults();

                    _searchTerm = _selectedPrecinctSplitItem.PrecinctSplitName;
                    _editedVoter.PrecinctId = SelectedPrecinctSplitItem.PrecinctSplitPrecinctId;
                    _editedVoter.PrecinctSplitId = SelectedPrecinctSplitItem.PrecinctSplitId;
                    _editedVoter.PrecinctSplitName = SelectedPrecinctSplitItem.PrecinctSplitName;

                    RaisePropertyChanged(nameof(SearchTerm));
                    RaisePropertyChanged(nameof(DisplayPrecinctResults));
                }
                RaisePropertyChanged(nameof(IsSaveEnabled));
                RaisePropertyChanged(nameof(IsPrecinctValid));
            }
        }

        public bool NoResults => SearchTerm.Length > 0 && Results.Count == 0 && SelectedPrecinctSplitItem == null;
        public ReadOnlyObservableCollection<PrecinctSplitDto> Results => GetResults();

        private bool _isManualEntry;
        public bool IsManualEntry
        {
            get => _isManualEntry;
            set
            {
                Set(ref _isManualEntry, value);
                IsHouseNumberFractionEnabled = value;
                IsUnitTypeEnabled = value;
                IsUnitValueEnabled = value;
                RaisePropertyChanged(nameof(HouseNumber));
                RaisePropertyChanged(nameof(HouseNumberFraction));
                RaisePropertyChanged(nameof(StreetName));
                RaisePropertyChanged(nameof(SelectedUnitType));
                RaisePropertyChanged(nameof(UnitValue));
                RaisePropertyChanged(nameof(City));
                RaisePropertyChanged(nameof(State));
                RaisePropertyChanged(nameof(Zip));
            }
        }

        private bool _isHouseNumberFractionEnabled;

        public bool IsHouseNumberFractionEnabled
        {
            get => _isHouseNumberFractionEnabled;
            set => Set(ref _isHouseNumberFractionEnabled, value);
        }

        private bool _isUnitTypeEnabled;

        public bool IsUnitTypeEnabled
        {
            get => _isUnitTypeEnabled;
            set => Set(ref _isUnitTypeEnabled, value);
        }

        private bool _isUnitValueEnabled;

        public bool IsUnitValueEnabled
        {
            get => _isUnitValueEnabled;
            set => Set(ref _isUnitValueEnabled, value);
        }

        public bool IsStreetSearchable => Task.Run(async () => await _streetSearchFacade.IsStreetsPopulatedAsync()).Result;
        public ReadOnlyObservableCollection<StreetDto> AddressResults => GetAddressResults();

        private List<StreetDto> _addressResults = new List<StreetDto>();
        private bool _displayStreetResults;
        public bool DisplayStreetResults
        {
            get => _displayStreetResults && AddressSearchTerm.Length > 0 && AddressResults.Count > 0 && SelectedStreetDto == null;
            set => Set(ref _displayStreetResults, value);
        }

        private string _addressSearchTerm = string.Empty;
        public string AddressSearchTerm
        {
            get => _addressSearchTerm;
            set
            {
                if (Set(ref _addressSearchTerm, value))
                {
                    ClearStreetResults();
                    Set(ref _addressSearchTerm, value);
                    RaisePropertyChanged(nameof(DisplayStreetResults));
                    StreetSearch();
                }
            }
        }

        private StreetDto _selectedStreetDto;
        public StreetDto SelectedStreetDto
        {
            get => _selectedStreetDto;
            set
            {
                try
                {
                    Set(ref _selectedStreetDto, value);
                    if (_selectedStreetDto != null)
                        IsManualEntry = _selectedStreetDto.StreetId > 0;

                    if (_selectedStreetDto != null)
                    {
                        ClearStreetResults();

                        if (_selectedStreetDto.StreetId != 0)
                        {
                            IsManualEntry = false;
                            _addressSearchTerm = _selectedStreetDto.FullStreetAddress;
                            var addressArray = _addressSearchTerm.Split(',');

                            if (_selectedStreetDto.PrecinctSplitId.HasValue) _editedVoter.PrecinctSplitId = _selectedStreetDto.PrecinctSplitId.Value;

                            _editedVoter.PrecinctSplitName = _selectedStreetDto.PrecinctSplitDisplayName;

                            SelectedPrecinctSplitItem = Task.Run(async () => await _precinctSplitFacade.GetPrecinctSplitNameByPrecinctSplitId(_editedVoter.PrecinctSplitId)).Result;

                            HouseNumber = (addressArray[0]).Split(' ')[0];
                            StreetName = _selectedStreetDto.StreetName;
                            City = _selectedStreetDto.StreetCityName;
                            Zip = _selectedStreetDto.StreetPostalCode;
                        }
                        else
                        {
                            ResetPrecinct();
                            IsManualEntry = true;
                            IsPrecinctValid = false;
                            var searchTerm = _addressSearchTerm;

                            _addressSearchTerm = _selectedStreetDto.FullStreetAddress;
                            var addressArray = searchTerm.Split(',');

                            HouseNumber = (addressArray[0]).Split(' ')[0];
                            StreetName = addressArray[0].Replace(HouseNumber, "").Trim();
                            if (addressArray.Length > 1)
                            {
                                City = addressArray[1].Trim();
                            }
                            else
                            {
                                City = string.Empty;
                                Zip = string.Empty;
                            }
                        }

                        HouseNumberFraction = string.Empty;
                        SelectedUnitType = null;
                        UnitValue = string.Empty;

                        IsHouseNumberFractionEnabled = true;
                        IsUnitTypeEnabled = true;
                        IsUnitValueEnabled = true;

                        DidDataChange();

                        RaisePropertyChanged(nameof(AddressSearchTerm));
                        RaisePropertyChanged(nameof(DisplayStreetResults));
                        RaisePropertyChanged(nameof(SearchTerm));
                    }

                    RaisePropertyChanged(nameof(IsManualEntry));
                    RaisePropertyChanged(nameof(IsSaveEnabled));
                }
                catch (Exception ex)
                {
                    var logProps = new Dictionary<string, string> { { "Action", "VoterEditViewModel.SelectedStreetDto" } };
                    _essLogger.LogError(ex, logProps);
                }
            }
        }

        public List<ElectionJurisdictionEnumValueDto> BallotStyleTypeList { get; set; }
        public List<ElectionJurisdictionEnumValueDto> UnitTypeList { get; set; }

        public VoterEditViewModel(IEssLogger essLogger,
            IMessenger messenger,
            IFrameNavigationService navigationService,
            IPrecinctSplitFacade precinctSplitFacade,
            IVoterAddEditInfoFacade voterAddEditInfoFacade,
            IPollPlaceFacade pollPlaceFacade,
            IStreetSearchFacade streetSearchFacade,
            IAuditLogFacade auditLogFacade,
            IVoterDetailsFacade voterDetailsFacade,
            IVoterJurisdictionEnumFacade voterJurisdictionEnumFacade,
            IPrinterFacade printerFacade)
        {
            _essLogger = essLogger;
            _messenger = messenger;
            _navigation = navigationService;
            _precinctSplitFacade = precinctSplitFacade;
            _voterAddEditInfoFacade = voterAddEditInfoFacade;
            _pollPlaceFacade = pollPlaceFacade;
            _streetSearchFacade = streetSearchFacade;
            _auditLogFacade = auditLogFacade;
            _voterDetailsFacade = voterDetailsFacade;
            _voterJurisdictionEnumFacade = voterJurisdictionEnumFacade;
            _printerFacade = printerFacade;

            BallotStyleTypeList = Task.Run(async () => await LoadBallotStyleType()).Result;
            UnitTypeList = Task.Run(async () => await LoadAddressUnitTypes().ConfigureAwait(false)).Result;
            _messenger.Register<VoterEditMessage>(this, VoterEditMessageReceived);
        }

        public void VoterEditMessageReceived(VoterEditMessage message)
        {
            // Voter must be set as a result of this message, or other methods will throw NullReferenceException!
            Voter = message.Voter;
            _editedVoter = Clone.ObjectGraph(Voter);

            SelectedPrecinctSplitItem = Task.Run(async () => await _precinctSplitFacade.GetPrecinctSplitNameByPrecinctSplitId(_editedVoter.PrecinctSplitId)).Result;
            SelectedUnitType = _editedVoter?.UnitTypeCodeId != null ? UnitTypeList
                .FirstOrDefault(x =>
                    x.JurisdictionEnumerationValueId == _editedVoter?.UnitTypeCodeId) : null;

            ClearAddressSearch();
            SelectedTab = 0;

            RaisePropertyChanged(nameof(FirstName));
            RaisePropertyChanged(nameof(LastName));
            RaisePropertyChanged(nameof(MiddleName));
            RaisePropertyChanged(nameof(NameSuffix));

            IsManualEntry = !IsStreetSearchable;

            IsPrecinctValid = true;
        }

        public void PageIsLoaded()
        {
            if (_navigation.Parameter is bool param)
            {
                HideVoterName = param;
                IsModal = param;
            }
            else
            {
                HideVoterName = false;
                IsModal = false;
            }

            RaisePropertyChanged(nameof(HideVoterName));
            RaisePropertyChanged(nameof(IsModal));
            RaisePropertyChanged(nameof(SaveLabel));
        }

        private ReadOnlyObservableCollection<PrecinctSplitDto> GetResults()
        {
            return new ReadOnlyObservableCollection<PrecinctSplitDto>(new ObservableCollection<PrecinctSplitDto>(_results));
        }

        private void PrecinctSearch()
        {
            try
            {
                if (string.IsNullOrWhiteSpace(SearchTerm))
                {
                    ClearPrecinctResults();
                }
                else
                {
                    SelectedPrecinctSplitItem = null;
                    if (_precinctSplitFacade != null)
                    {
                        IEnumerable<PrecinctSplitDto> response = Task.Run(async () => await _precinctSplitFacade.GetPrecinctSplitsByNameSearch(SearchTerm)).Result;

                        _results = response.ToList();

                        DisplayPrecinctResults = true;

                        RaisePropertyChanged(nameof(Results));
                        RaisePropertyChanged(nameof(NoResults));
                    }
                }
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string> { { "Action", "VoterEditViewModel.PrecinctSearch" } };
                _essLogger.LogError(ex, logProps);
            }
        }

        private void ClearPrecinctResults()
        {
            DisplayPrecinctResults = false;
        }

        private void DidDataChange()
        {
            var comparer = new CompareLogic();

            comparer.Config.MembersToInclude.Add("EmailAddress");
            comparer.Config.MembersToInclude.Add("FirstName");
            comparer.Config.MembersToInclude.Add("MiddleName");
            comparer.Config.MembersToInclude.Add("LastName");
            comparer.Config.MembersToInclude.Add("NameSuffix");
            comparer.Config.MembersToInclude.Add("HouseNumber");
            comparer.Config.MembersToInclude.Add("HouseNumberFractionValue");
            comparer.Config.MembersToInclude.Add("StreetName");
            comparer.Config.MembersToInclude.Add("UnitTypeCodeId");
            comparer.Config.MembersToInclude.Add("UnitTypeName");
            comparer.Config.MembersToInclude.Add("UnitValue");
            comparer.Config.MembersToInclude.Add("City");
            comparer.Config.MembersToInclude.Add("State");
            comparer.Config.MembersToInclude.Add("Zip");
            comparer.Config.MembersToInclude.Add("PrecinctSplitId");
            comparer.Config.MembersToInclude.Add("VoterBallotStyleTypeJurisdictionEnumValueId");

            IsDataChanged = !comparer.Compare(Voter, _editedVoter).AreEqual;
        }

        private void ResetAndClearPrecinct()
        {
            try
            {
                _auditLogFacade.AddToAuditLog(PageName, $"{PrecinctLabel} '{UIText.Clear}' button was activated.");
                ResetPrecinct();
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string> { { "Action", "VoterEditViewModel.ResetAndClearPrecinct" } };
                _essLogger.LogError(ex, logProps);
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }

        private void ResetPrecinct()
        {
            SearchTerm = string.Empty;
            SelectedPrecinctSplitItem = null;
            _results = new List<PrecinctSplitDto>();
            ClearPrecinctResults();

            RaisePropertyChanged(nameof(Results));
            RaisePropertyChanged(nameof(NoResults));
        }

        private async Task Save()
        {
            try
            {
                await _auditLogFacade.AddToAuditLogAsync(PageName, $"'{SaveLabel}' button was activated.");

                if (!_semaphore.Wait(0)) // Don't want async as this only blocks a double tap of the button and not to get in.
                    return;

                _editedVoter.CompleteAddress = _voterAddEditInfoFacade.BuildFullAddress(_editedVoter);

                if (_enableAddressChangeAffidavit)
                {
                    _messenger.Send(new VoterSelectedMessage(_editedVoter));
                    _messenger.Send(new AffirmationOfResidenceAffidavitMessage(Clone.ObjectGraph(_editedVoter), Clone.ObjectGraph(Voter)));

                    NavigateToAffirmationOfResidenceAffidavit();
                }
                else
                {
                    var pollPlace = await _pollPlaceFacade.GetPollPlaceDetailsByPrecinctSplitIDAsync(_editedVoter.PrecinctSplitId);
                    _editedVoter.ValidPoll = LoggedInPollplaceInfo.LoggedInPollPlace.PollingPlaceId == pollPlace?.PollingPlaceId;
                    _editedVoter.VoterPollPlaceInfo = pollPlace;
                    if (Voter.HasHostResults)
                    {
                        var precinctSplitBallotStyles = await _voterDetailsFacade.GetVoterDetailsByVoterIdEditedPrecinctSplitAsync(_editedVoter.VoterId, _editedVoter.PrecinctSplitId);
                        if (precinctSplitBallotStyles != null)
                        {
                            _editedVoter.HasMultipleBallots = precinctSplitBallotStyles.hasMultipleBallotStyles;
                            if (!_editedVoter.HasMultipleBallots)
                            {
                                _editedVoter.BallotStyleDescription = precinctSplitBallotStyles.BallotStyleDescription;
                            }
                        }
                    }

                    await _voterAddEditInfoFacade.EditVoterAsync(_editedVoter, Clone.ObjectGraph(Voter));

                    if ((ElectionAddressOnlyChangeType == AddressOnlyChangeType.Addr_Receipt || ElectionAddressOnlyChangeType == AddressOnlyChangeType.Addr_All) &&
                        SystemConfiguration.ElectionConfiguration.PrintingEnabled && _enableAddressChangedWorkflow)
                    {
                        PrintResponse printer = _printerFacade.GetPrinterInformationFromConfiguration(SystemConfiguration.ElectionConfiguration.SelectedExpressPollPrinterId);
                        if (printer != null && !printer.IsLabelPaperType)
                        {
                            _navigation.NavigateTo<PrintingReceiptViewModel>(null, NavigationFrameEnum.ContextFrame);
                            var printDateTime = DateTime.Now;
                            var printResponse = await _printerFacade.PrintVoterAddressChangedDocument(_editedVoter, Voter, printDateTime);
                            if (_printerFacade.DidPrintingFail(printResponse))
                            {
                                var navParameter = new PrintAddressChangedDocParameter()
                                {
                                    EditedVoter = _editedVoter,
                                    PreviousVoter = Voter,
                                    PrintDateTime = printDateTime
                                };

                                _navigation.NavigateTo<PrintAddressChangedDocFailedViewModel>(navParameter, NavigationFrameEnum.ContextFrame);
                                return;
                            }
                        }
                    }

                    Voter = _editedVoter;
                    _messenger.Send(new VoterSelectedMessage(Voter));

                    NavigateToVoterView();
                }
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string> { { "Action", "VoterEditViewModel.Save" } };
                _essLogger.LogError(ex, logProps);
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
            finally
            {
                _semaphore.Release();
            }
        }

        private void Cancel()
        {
            try
            {
                _auditLogFacade.AddToAuditLog(PageName, $"'{CancelLabel}' button was activated.");
                NavigateToVoterView();
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string> { { "Action", "VoterEditViewModel.Cancel" } };
                _essLogger.LogError(ex, logProps);
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }

        private void NavigateToVoterView()
        {
            _messenger.Send(new StatusBarMessage(isVisible: true));

            _navigation.NavigateTo<DashboardViewModel>(NavigationFrameEnum.MainFrame, false);
            _navigation.NavigateTo<VoterViewModel>(NavigationFrameEnum.ContextFrame);
        }

        private void NavigateToAffirmationOfResidenceAffidavit()
        {
            if (IsModal)
                _navigation.NavigateTo<AffirmationOfResidenceAffidavitViewModel>(IsModal, NavigationFrameEnum.ContextFrame);
            else
            {
                _navigation.NavigateTo<DashboardViewModel>(NavigationFrameEnum.MainFrame, false);
                _navigation.NavigateTo<AffirmationOfResidenceAffidavitViewModel>(IsModal, NavigationFrameEnum.ContextFrame);
            }
        }

        private void StreetSearch()
        {
            try
            {
                var canSearch = _streetSearchFacade.CanSearch(AddressSearchTerm);

                if (string.IsNullOrWhiteSpace(AddressSearchTerm) || !canSearch)
                {
                    ClearStreetResults();
                }
                else
                {
                    SelectedStreetDto = null;
                    if (_precinctSplitFacade != null)
                    {
                        Core.Model.StreetSearchResponse response = Task.Run(async () => await _streetSearchFacade.GetStreetsSearchResultsWithStateAsync(AddressSearchTerm, _editedVoter.State)).Result;

                        _addressResults = response.StreetDtoResults;

                        _addressResults.Add(new StreetDto()
                        {
                            StreetId = 0,
                            SearchedText = AddressSearchTerm,
                            FullStreetAddress = "NO MATCH FOUND - ENTER ADDRESS MANUALLY",

                        });

                        DisplayStreetResults = true;

                        RaisePropertyChanged(nameof(AddressResults));
                        RaisePropertyChanged(nameof(DisplayStreetResults));
                    }
                }
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string> { { "Action", "VoterEditViewModel.StreetSearch" } };
                _essLogger.LogError(ex, logProps);
            }
        }

        private ReadOnlyObservableCollection<StreetDto> GetAddressResults()
        {
            return new ReadOnlyObservableCollection<StreetDto>(new ObservableCollection<StreetDto>(_addressResults));
        }

        private void ClearStreetResults()
        {
            DisplayStreetResults = false;
        }

        private void ResetAddress()
        {
            try
            {
                _auditLogFacade.AddToAuditLog(PageName, $"{AddressSearchLabel} '{UIText.Clear}' button was activated.");
                _editedVoter = Clone.ObjectGraph(Voter);

                SelectedPrecinctSplitItem = Task.Run(async () => await _precinctSplitFacade.GetPrecinctSplitNameByPrecinctSplitId(_editedVoter.PrecinctSplitId)).Result;
                SelectedUnitType = UnitTypeList
                    .FirstOrDefault(x =>
                        x.JurisdictionEnumerationValueId == _editedVoter?.UnitTypeCodeId);
                ClearAddressSearch();
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string> { { "Action", "VoterEditViewModel.ResetAddress" } };
                _essLogger.LogError(ex, logProps);
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }

        private void ClearAddressSearch()
        {
            AddressSearchTerm = string.Empty;
            SelectedStreetDto = null;
            _addressResults = new List<StreetDto>();
            ClearStreetResults();

            IsManualEntry = false;
            IsPrecinctValid = true;
            IsHouseNumberFractionEnabled = false;
            IsUnitTypeEnabled = false;
            IsUnitValueEnabled = false;

            RaisePropertyChanged(nameof(AddressResults));
        }

        private async Task<List<ElectionJurisdictionEnumValueDto>> LoadBallotStyleType()
        {
            return await _voterJurisdictionEnumFacade.GetBallotStyleTypesAsync();
        }

        private Task<List<ElectionJurisdictionEnumValueDto>> LoadAddressUnitTypes() =>
            _voterJurisdictionEnumFacade.GetAddressUnitTypesAsync();

        private bool ValidateEmailAddress()
        {
            if (String.IsNullOrWhiteSpace(EmailAddress)) return true;

            var trimmedEmail = EmailAddress.Trim();
            if (trimmedEmail.EndsWith(".")) return false;

            try
            {
                var addr = new System.Net.Mail.MailAddress(EmailAddress);
                return addr.Address == trimmedEmail;
            }
            catch
            {
                return false;
            }
        }

        public void Dispose()
        {
            Dispose(true);
        }

        private void Dispose(bool disposing)
        {
            if (_disposed)
                return;

            if (disposing)
            {
                _semaphore?.Dispose();
                _semaphore = null;
            }

            _disposed = true;
        }
    }
}
