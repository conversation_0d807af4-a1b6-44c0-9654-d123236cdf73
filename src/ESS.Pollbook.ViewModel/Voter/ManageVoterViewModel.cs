using ESS.Pollbook.Core.Commands;
using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.Messaging;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Facade.BallotCancellationStateTransition;
using ESS.Pollbook.Facade.ElectionJurisdictionEnum;
using ESS.Pollbook.Facade.ManageVoter;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.Command;
using GalaSoft.MvvmLight.Messaging;
using NClone;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;

namespace ESS.Pollbook.ViewModel.Voter
{
   public class ManageVoterViewModel : ViewModelBase
   {
      private readonly IFrameNavigationService _navigation;
      private readonly IMessenger _messenger;
      private readonly IVoterJurisdictionEnumFacade _voterJurisdictionEnumFacade;
      private readonly IManageVoterFacade _manageVoterFacade;
      private readonly IEssLogger _essLogger;
      private readonly IBallotCancellationStateTransitionFacade _cancellationStateTransitionFacade;
      private readonly IAuditLogFacade _auditLogFacade;

      private string PageName => _navigation.PageName(GetType().FullName);

      public ICommand CancelCommand => new RelayCommand(Cancel);
      public ICommandAsync SaveCommand => new CommandAsync(Save);
      public ICommandAsync CancelBallotCommand => new CommandAsync(CancelBallot);

      public string CancelLabel => UIText.Cancel;
      public string SaveLabel => UIText.Save;
      public string CancelBallotLabel => UIText.CancelBallot;

      private bool _isInitialLoad;
      private int _previousAbsenteeStatusEnumId;
      private bool _isIdRequiredInitialLoad;
      private int _previousIdRequiredStatusEnumId;
      private int _previousVoterStatus;

      private readonly List<int> _absenteeReturnedIds = new List<int>();
      private readonly List<int> _earlyBallotIssuedIds = new List<int>();

      public bool IsCancelBallotButtonEnabled => GetButtonEnabledStatus();

      #region Properties

      private List<ElectionJurisdictionEnumValueDto> _voterStatuses;

      public List<ElectionJurisdictionEnumValueDto> VoterStatuses
      {
         get => _voterStatuses;
         set => Set(ref _voterStatuses, value);
      }

      private VoterDto _voter;

      public VoterDto Voter
      {
         get => _voter;
         set
         {
            if (Set(ref _voter, value))
               RaisePropertyChanged(nameof(VoterFullName));
         }
      }

      private ElectionJurisdictionEnumValueDto _voterStatus;

      public ElectionJurisdictionEnumValueDto SelectedVoterStatus
      {
         get => _voterStatus;
         set => Set(ref _voterStatus, value);
      }

      public string VoterFullName => GetVoterFullName();

      private List<ElectionJurisdictionEnumValueDto> _idRequiredStatuses;

      public List<ElectionJurisdictionEnumValueDto> IdRequiredStatuses
      {
         get => _idRequiredStatuses;
         set => Set(ref _idRequiredStatuses, value);
      }

      private ElectionJurisdictionEnumValueDto _selectedIdRequiredStatus;

      public ElectionJurisdictionEnumValueDto SelectedIdRequiredStatus
      {
         get => _selectedIdRequiredStatus;
         set
         {
            if (Set(ref _selectedIdRequiredStatus, value))
            {
               HasIdRequiredSelection = _selectedIdRequiredStatus != null;
               RaisePropertyChanged(nameof(HasIdRequiredSelection));
            }
         }
      }

      public bool HasIdRequiredSelection { get; private set; }

      private List<ElectionJurisdictionEnumValueDto> _absenteeStatuses;

      public List<ElectionJurisdictionEnumValueDto> AbsenteeStatuses
      {
         get => _absenteeStatuses;
         set => Set(ref _absenteeStatuses, value);
      }

      private ElectionJurisdictionEnumValueDto _selectedAbsenteeStatus;

      public ElectionJurisdictionEnumValueDto SelectedAbsenteeStatus
      {
         get => _selectedAbsenteeStatus;
         set
         {
            if (Set(ref _selectedAbsenteeStatus, value))
            {
               HasSelection = _selectedAbsenteeStatus != null;
               RaisePropertyChanged(nameof(HasSelection));
            }
         }
      }

      public bool HasSelection { get; private set; }

      public bool CanChangeStatuses => !GetEnabledStatus();

      private bool IsAbsenteeReturnedOrEarlyBallotIssued()
      {
         if (SelectedAbsenteeStatus == null) return false;
         return _absenteeReturnedIds.Contains(SelectedAbsenteeStatus.JurisdictionEnumerationValueId) ||
                _earlyBallotIssuedIds.Contains(SelectedAbsenteeStatus.JurisdictionEnumerationValueId);
      }

      private bool IsEarlyBallotIssued()
      {
         if (SelectedAbsenteeStatus == null) return false;
         return _earlyBallotIssuedIds.Contains(SelectedAbsenteeStatus.JurisdictionEnumerationValueId);
      }

      #endregion


      #region Constructor

      public ManageVoterViewModel(
         IBallotCancellationStateTransitionFacade cancellationStateTransitionFacade,
         IAuditLogFacade auditLogFacade,
         IManageVoterFacade manageVoterFacade,
         IVoterJurisdictionEnumFacade voterJurisdictionEnumFacade,
         IFrameNavigationService navigation,
         IMessenger messenger,
         IEssLogger logger
      )
      {
         _manageVoterFacade = manageVoterFacade;
         _voterJurisdictionEnumFacade = voterJurisdictionEnumFacade;
         _cancellationStateTransitionFacade = cancellationStateTransitionFacade;
         _auditLogFacade = auditLogFacade;
         _navigation = navigation;
         _messenger = messenger;
         _essLogger = logger;

         _messenger.Register<ManageVoterMessage>(this, ManageVoter);

         try
         {
            LoadVoterStatuses();

            LoadAbsentees();
            _isInitialLoad = false;

            LoadIdRequiredStatuses();
            _isIdRequiredInitialLoad = false;

            GetAbsenteeReturnedEarlyBallotIssuedId();
         }
         catch (Exception ex)
         {
            var logProps = new Dictionary<string, string> {{"Action", "ManageVoterViewModel constructor"}};
            _essLogger.LogError(ex, logProps);
         }
      }

      #endregion

      /// <summary>
      ///    Message Listener for the ManageVoterMessage message
      /// </summary>
      /// <param name="message"></param>
      private void ManageVoter(ManageVoterMessage message)
      {
         // Voter is set here. If this message is not received, then other methods will throw NullReferenceException!
         Voter = message.Voter;
         _isInitialLoad = false;
         _isIdRequiredInitialLoad = false;
         ShowCurrentVoterStatus();
         GetSelectedAbsenteeStatus();
         GetSelectedIdRequiredStatus();
      }

      /// <summary>
      ///    Selects the correct voter status from the list
      /// </summary>
      private void ShowCurrentVoterStatus()
      {
         SelectedVoterStatus =
            VoterStatuses.FirstOrDefault(s => s.JurisdictionEnumerationValueId == Voter.VoterStatusEnumId);
         _previousVoterStatus = Voter.VoterStatusEnumId;
      }

      /// <summary>
      ///    Returns the voter's full name formatted appropriately
      /// </summary>
      /// <returns></returns>
      private string GetVoterFullName()
      {
         /* There's a copy of this method in the VoterViewModel class. We should refactor these so this method is moved
          * to the VoterDto class. However, the VoterDto class already contains a very similar one that should be analyzed before
          * refactoring. */

         try
         {
            var temp = $"{Voter.FirstName} {Voter.MiddleName}";

            if (!string.IsNullOrEmpty(Voter.LastName)) temp = $"{Voter.LastName}, " + temp.Trim();

            return temp.Trim();
         }
         catch (Exception ex)
         {
            var logProps = new Dictionary<string, string> {{"Action", "ManageVoterViewModel.GetVoterFullName"}};
            _essLogger.LogError(ex, logProps);
            return string.Empty;
         }
      }

      private List<ElectionJurisdictionEnumValueDto> LoadStatusesSync(EssEnumeration statusType,
         bool excludeNotInRoster = true)
      {
         const string NotInRosterFlag = "NOT IN ROSTER";

         return Task.Run(async () =>
         {
            // Fetch asynchronously
            var fetchedStatuses =
               await _voterJurisdictionEnumFacade.GetEnumerationDetailsByStatusesAsync((int) statusType);

            // Apply filtering and sorting
            return fetchedStatuses
               .Where(x => !excludeNotInRoster || !x.JurisdictionEnumerationValueName.Equals(NotInRosterFlag))
               .OrderBy(x => x.JurisdictionEnumerationValueName)
               .ToList();
         }).GetAwaiter().GetResult() ?? new List<ElectionJurisdictionEnumValueDto>();
      }

      private void LoadVoterStatuses()
      {
         VoterStatuses = LoadStatusesSync(EssEnumeration.VoterStatus);
      }

      private void LoadAbsentees()
      {
         HasSelection = false;
         AbsenteeStatuses = LoadStatusesSync(EssEnumeration.AbsenteeStatus);
      }

      private void LoadIdRequiredStatuses()
      {
         HasIdRequiredSelection = false;
         IdRequiredStatuses = LoadStatusesSync(EssEnumeration.IdentificationRequirementStatus, false);
      }

      private void GetSelectedAbsenteeStatus()
      {
         if (!_isInitialLoad)
            foreach (var v in AbsenteeStatuses)
               if (v.JurisdictionEnumerationValueId == Voter.AbsenteeStatusEnumId)
               {
                  _selectedAbsenteeStatus = v;
                  _previousAbsenteeStatusEnumId = Voter.AbsenteeStatusEnumId;
                  _isInitialLoad = true;
                  HasSelection = true;
                  break;
               }
      }

      private void GetSelectedIdRequiredStatus()
      {
         if (!_isIdRequiredInitialLoad)
            foreach (var v in IdRequiredStatuses)
               if (v.JurisdictionEnumerationValueId == Voter.IdentificationStatusEnumId)
               {
                  _selectedIdRequiredStatus = v;
                  _previousIdRequiredStatusEnumId = Voter.IdentificationStatusEnumId;
                  HasIdRequiredSelection = true;
                  _isIdRequiredInitialLoad = true;
                  break;
               }
      }

      /// <summary>
      ///    Performs any cancellation tasks
      /// </summary>
      private void Cancel()
      {
         var stopwatch = Stopwatch.StartNew();
         _essLogger.LogInformation($"Cancel operation started in {nameof(ManageVoterViewModel)}.");

         try
         {
            _auditLogFacade.AddToAuditLog(PageName, $"'{CancelLabel}' button was activated.");
            _messenger.Send(new StatusBarMessage(true));

            _navigation.NavigateTo<DashboardViewModel>(NavigationFrameEnum.MainFrame, false);
            _navigation.GoBack();

            stopwatch.Stop();
            _essLogger.LogInformation(
               $"Cancel operation completed successfully in {stopwatch.ElapsedMilliseconds} ms.");
         }
         catch (Exception ex)
         {
            stopwatch.Stop();

            var logProps = new Dictionary<string, string>
               {{"Action", "ManageVoterViewModel.Cancel"}, {"Duration", $"{stopwatch.ElapsedMilliseconds} ms"}};
            _essLogger.LogError(ex, logProps);
            _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error,
               SystemDetails.GenericErrorMessage));
         }
      }

      /// <summary>
      ///    Saves a status change transaction based on the previous and the just selected voter status.
      /// </summary>
      private async Task Save()
      {
         try
         {
            await _auditLogFacade.AddToAuditLogAsync(PageName, $"'{SaveLabel}' button was activated.");
            var previousVoterDto = Clone.ObjectGraph(Voter);
            var hasChanged = false;

            //Voter status changed
            if (_previousVoterStatus != SelectedVoterStatus.JurisdictionEnumerationValueId)
            {
               Voter.VoterStatusEnumId = SelectedVoterStatus.JurisdictionEnumerationValueId;
               hasChanged = true;
            }

            //Absentee status changed
            if (_previousAbsenteeStatusEnumId != SelectedAbsenteeStatus.JurisdictionEnumerationValueId)
            {
               Voter.AbsenteeStatusEnumId = SelectedAbsenteeStatus.JurisdictionEnumerationValueId;
               hasChanged = true;
            }

            //Id Required status changed
            if (_previousIdRequiredStatusEnumId != SelectedIdRequiredStatus.JurisdictionEnumerationValueId)
            {
               Voter.IdentificationStatusEnumId = SelectedIdRequiredStatus.JurisdictionEnumerationValueId;
               hasChanged = true;
               Voter.IdRequired = EnumerationValues.EnumerationList?.Where(x =>
                     x.JurisdictionEnumerationValueId.Equals(Voter.IdentificationStatusEnumId)).FirstOrDefault()
                  ?.JurisdictionEnumerationValueName;
            }

            if (hasChanged) await _manageVoterFacade.ManageVoterAsync(Voter, previousVoterDto);

            _messenger.Send(new VoterActivatedMessage(Voter));

            _messenger.Send(new UpdateSystemStatsMessage());

            _isIdRequiredInitialLoad = false;
            _isInitialLoad = false;

            _messenger.Send(new StatusBarMessage(true));
            _messenger.Send(new VoterSelectedMessage(Voter));

            _navigation.NavigateTo<DashboardViewModel>(NavigationFrameEnum.MainFrame, false);
            _navigation.GoBack();
         }
         catch (Exception ex)
         {
            var logProps = new Dictionary<string, string> {{"Action", "ManageVoterViewModel.Save"}};
            if (Voter != null) logProps.Add("Voter.VoterKey", Voter.VoterKey);
            _essLogger.LogError(ex, logProps);
            _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error,
               SystemDetails.GenericErrorMessage));
         }
      }

      private async Task CancelBallot()
      {
         try
         {
            await _auditLogFacade.AddToAuditLogAsync(PageName, $"'{CancelBallotLabel}' button was activated.");
            _isIdRequiredInitialLoad = false;
            _isInitialLoad = false;

            int? targetStatus =
               await _cancellationStateTransitionFacade.GetCancelledStateAsync(Voter.AbsenteeStatusEnumId);
            var cancelBallotDto = new CancelBallotDto
            {
               TargetStatus = targetStatus.HasValue && targetStatus.Value > 0 ? targetStatus : null,
               CancelBallotandAbsenteeTx = false
            };

            if (Voter.IsPreviouslyVoted && IsEarlyBallotIssued() && LoggedInPollplaceInfo.IsEarlyVotingPollPlace)
               cancelBallotDto.CancelBallotandAbsenteeTx = true;

            _navigation.NavigateTo<CancelBallotViewModel>(cancelBallotDto, NavigationFrameEnum.ModalFrame, false,
               withVerticalEffect: true);
         }
         catch (Exception ex)
         {
            var logProps = new Dictionary<string, string> {{"Action", "ManageVoterViewModel.CancelBallot"}};
            _essLogger.LogError(ex, logProps);
            _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error,
               SystemDetails.GenericErrorMessage));
         }
      }

      private void GetAbsenteeReturnedEarlyBallotIssuedId()
      {
         foreach (var ab in AbsenteeStatuses)
         {
            if (ab.EnumerationValueNumber == (int) AbsenteeStatus.Absentee)
               _absenteeReturnedIds.Add(ab.JurisdictionEnumerationValueId);

            if (ab.EnumerationValueNumber == (int) AbsenteeStatus.EarlyVoteIssued)
               _earlyBallotIssuedIds.Add(ab.JurisdictionEnumerationValueId);
         }
      }

      private bool GetButtonEnabledStatus()
      {
         try
         {
            if (Voter.IsPreviouslyVoted && LoggedInPollplaceInfo.LoggedInPollPlace.PollingPlaceId != Voter.BallotEventPollPlaceId)
               return false;

            if (_absenteeReturnedIds.Contains(Voter.AbsenteeStatusEnumId))
               return SystemConfiguration.ElectionConfiguration.AllowAbsReturnedCancel;

            return Voter.IsPreviouslyVoted && (SystemConfiguration.ElectionConfiguration.AllowEarlyBallotIssuedCancel
                                               || (!SystemConfiguration.ElectionConfiguration
                                                      .AllowEarlyBallotIssuedCancel
                                                   && Voter.BallotRecordUpdateApplicationDatetime?.ToLocalTime()
                                                      .Date == DateTime.Now.Date));
         }
         catch (Exception ex)
         {
            var logProps = new Dictionary<string, string> {{"Action", "ManageVoterViewModel.GetButtonEnabledStatus"}};
            _essLogger.LogError(ex, logProps);
            return false;
         }
      }

      private bool GetEnabledStatus()
      {
         try
         {
            if (!SystemConfiguration.ElectionConfiguration.AllowAbsReturnedCancel &&
                _absenteeReturnedIds.Contains(Voter.AbsenteeStatusEnumId) && !Voter.IsPreviouslyVoted)
               return true;

            return Voter.IsPreviouslyVoted || _absenteeReturnedIds.Contains(Voter.AbsenteeStatusEnumId) ||
                   _earlyBallotIssuedIds.Contains(Voter.AbsenteeStatusEnumId);
         }
         catch (Exception ex)
         {
            var logProps = new Dictionary<string, string> {{"Action", "ManageVoterViewModel.GetEnabledStatus"}};
            _essLogger.LogError(ex, logProps);
            return false;
         }
      }
   }
}
