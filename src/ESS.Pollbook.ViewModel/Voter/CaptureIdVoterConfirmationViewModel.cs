using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Facade.PollbookDefinedText;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.Command;
using GalaSoft.MvvmLight.Messaging;
using System;
using System.Collections.Generic;
using System.Reflection;
using System.Threading.Tasks;
using System.Windows.Input;
using ESS.Pollbook.Core.Commands;
using ESS.Pollbook.Core.Messaging;
using ESS.Pollbook.ViewModel.VoterSearch;

namespace ESS.Pollbook.ViewModel.Voter
{
	/// <summary>
	/// Navigation Parameter Required:  VoterDto
	/// </summary>
	public class CaptureIdVoterConfirmationViewModel : ViewModelBase
	{
		private string PageName => _navigation.PageName(GetType().FullName);

		public ICommandAsync BackCommand => new CommandAsync(Back);

		public ICommandAsync NextCommand => new CommandAsync(Next);

		private bool _noEnabled;

		public bool ToggleNo
		{
			get => _noEnabled;
			set
			{
				Set(ref _noEnabled, value);
				Set(ref _yesEnabled);
				NextEnabled = _noEnabled;
				RaisePropertyChanged(nameof(ToggleYes));
			}
		}

		private bool _yesEnabled;

		public bool ToggleYes
		{
			get => _yesEnabled;
			set
			{
				Set(ref _yesEnabled, value);
				Set(ref _noEnabled);
				NextEnabled = _yesEnabled;
				RaisePropertyChanged(nameof(ToggleNo));
			}
		}

		private bool _nextEnabled;

		public bool NextEnabled
		{
			get => _nextEnabled;
			set => Set(ref _nextEnabled, value);
		}

		#region View Labels

		public string BackLabel => UIText.Back;

		public string NextLabel => UIText.Next;

		public string PageTitle => _definedTextFacade.GetPollbookDefinedTextForLanguageWithDefault(
			"CaptureVoterID_PageTitle", "English", UIText.CaptureIdDefaultLabel);

		public string YesNoQuestion => _definedTextFacade.GetPollbookDefinedTextForLanguageWithDefault(
			"CaptureVoterID_ConfirmationHeader", "English", UIText.CaptureIdDefaultQuestion);

		public string YesButtonLabel => _definedTextFacade.GetPollbookDefinedTextForLanguageWithDefault(
			"CaptureVoterID_YesButtonLabel", "English", UIText.Yes);

		public string NoButtonLabel => _definedTextFacade.GetPollbookDefinedTextForLanguageWithDefault(
			"CaptureVoterID_NoButtonLabel", "English", UIText.No);

		#endregion

		#region Dependency Injection

		private readonly IFrameNavigationService _navigation;
		private readonly IMessenger _messenger;
		private readonly IEssLogger _essLogger;
		private readonly IAuditLogFacade _auditLogFacade;
		private readonly IPollbookDefinedTextFacade _definedTextFacade;

		#endregion

		public CaptureIdVoterConfirmationViewModel(IMessenger messenger, IFrameNavigationService navigation,
			IEssLogger essLogger, IAuditLogFacade auditLogFacade, IPollbookDefinedTextFacade definedText)
		{
			_messenger = messenger;
			_navigation = navigation;
			_essLogger = essLogger;
			_auditLogFacade = auditLogFacade;
			_definedTextFacade = definedText;
		}

		public void PageLoaded()
		{
			ToggleYes = false;
			ToggleNo = false;
		}

		private async Task Next()
		{
			try
			{
				await _auditLogFacade.AddToAuditLogAsync(PageName, $"'{NextLabel}' button was activated.");

				if (!(_navigation.Parameter is VoterDto voter))
				{
					_essLogger.LogError(
						"Capture ID was attempted, but the voter information was not passed into current node to update.  Returning back.");
					_navigation.GoBack();
					return;
				}

				// Add on the CaptureID DTO at this time or clean it out and reset.
				voter.CaptureId = new CaptureIdDto
				{
					IdWasPresentedByVoter = _yesEnabled
				};

				// If VoterCaptureIDType is enabled, we are still moving forward with CaptureId
				if (_yesEnabled && SystemConfiguration.ElectionConfiguration.VoterCaptureIdType)
				{
					_navigation.NavigateTo<CaptureIdVoterVerificationViewModel>(voter,
						NavigationFrameEnum.ContextFrame);
					return;
				}

				// Else we must now go to EnableVoterAddressChange if enabled
				if (SystemConfiguration.ElectionConfiguration.AddressOnlyChangeType != AddressOnlyChangeType.None)
				{
					_navigation.NavigateTo<AddressChangedViewModel>(voter, NavigationFrameEnum.ContextFrame);
					return;
				}

				// Else its VoterViewModel
				_navigation.NavigateTo<VoterViewModel>(voter, NavigationFrameEnum.ContextFrame);
			}

			catch (Exception ex)
			{
				_essLogger.LogError(ex,
					new Dictionary<string, string>
						{ { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name }, });
				_messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error,
					SystemDetails.GenericErrorMessage));
			}
		}

		private async Task Back()
		{
			try
			{
				await _auditLogFacade.AddToAuditLogAsync(PageName, $"'{BackLabel}' button was activated.");
				
                _messenger.Send(new StatusBarMessage(isVisible: true));
				_navigation.NavigateTo<DashboardViewModel>(NavigationFrameEnum.MainFrame, false);
                _navigation.NavigateTo<AdvancedVoterSearchViewModel>(NavigationFrameEnum.ContextFrame, withVerticalEffect:true);
            }
			catch (Exception ex)
			{
				_essLogger.LogError(ex,
					new Dictionary<string, string>
						{ { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
				_messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error,
					SystemDetails.GenericErrorMessage));
			}
		}
	}
}
