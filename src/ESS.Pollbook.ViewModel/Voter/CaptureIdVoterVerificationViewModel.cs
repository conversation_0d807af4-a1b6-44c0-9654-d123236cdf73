using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Facade.CaptureVoterId;
using ESS.Pollbook.Facade.PollbookDefinedText;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.Command;
using GalaSoft.MvvmLight.Messaging;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Reflection;
using System.Threading.Tasks;
using System.Windows.Input;
using ESS.Pollbook.Core.Commands;

namespace ESS.Pollbook.ViewModel.Voter
{
	/// <summary>
	/// Navigation Parameter Required:  VoterDto
	/// </summary>
	public class CaptureIdVoterVerificationViewModel : ViewModelBase
	{
		private string PageName => _navigation.PageName(GetType().FullName);

		public ICommandAsync BackCommand => new CommandAsync(Back);

		public ICommandAsync NextCommand => new CommandAsync(Next);

		// Screen can handle maximum of 15.  This is concrete.
		public const int MaximumIds = 15;
		private VoterDto _voter;

		private ObservableCollection<ElectionCaptureIdVoterTypeDbo> _voterCaptureIdTypes;

		public ObservableCollection<ElectionCaptureIdVoterTypeDbo> VoterCaptureTypes
		{
			get => _voterCaptureIdTypes;
			set => Set(ref _voterCaptureIdTypes, value);
		}

		private ElectionCaptureIdVoterTypeDbo _selectedVerificationType;

		public ElectionCaptureIdVoterTypeDbo SelectedVerificationType
		{
			get => _selectedVerificationType;
			set
			{
				if (!Set(ref _selectedVerificationType, value))
					return;
				HasVerificationTypeSelection = _selectedVerificationType != null;
			}
		}

		private bool _hasVerificationTypeSelection;

		public bool HasVerificationTypeSelection
		{
			get => _hasVerificationTypeSelection;
			set => Set(ref _hasVerificationTypeSelection, value);
		}

		#region View Labels

		public string LabelContent => _definedTextFacade.GetPollbookDefinedTextForLanguageWithDefault(
			"CaptureVoterID_PageTitle", "English", UIText.CaptureIdDefaultLabel);

		public string SubLabelContent => _definedTextFacade.GetPollbookDefinedTextForLanguageWithDefault(
			"CaptureVoterID_IDSelectionTitle", "English", UIText.CaptureIdDefaultVoterVerification);

		public string CaptureIdTypeStatement => _definedTextFacade.GetPollbookDefinedTextForLanguageWithDefault(
			"CaptureVoterID_IDSelectionSubtitle", "English", UIText.CaptureIdDefaultSelectVerification);

		public string BackLabel => UIText.Back;

		public string NextLabel => UIText.Next;

		#endregion

		#region Dependency Injection

		private readonly IMessenger _messenger;
		private readonly IFrameNavigationService _navigation;
		private readonly IEssLogger _essLogger;
		private readonly IAuditLogFacade _auditLogFacade;
		private readonly IPollbookDefinedTextFacade _definedTextFacade;
		private readonly ICaptureIdVoterFacade _captureIdFacade;

		#endregion

		public CaptureIdVoterVerificationViewModel(IMessenger messenger, IFrameNavigationService navigation,
			IEssLogger essLogger, IAuditLogFacade auditLogFacade, IPollbookDefinedTextFacade definedText,
			ICaptureIdVoterFacade captureVoter)
		{
			_messenger = messenger;
			_navigation = navigation;
			_essLogger = essLogger;
			_auditLogFacade = auditLogFacade;
			_definedTextFacade = definedText;
			_captureIdFacade = captureVoter;
		}

		public async Task PageLoaded()
		{
			_voterCaptureIdTypes = null;
			_voter = _navigation.Parameter as VoterDto;

			if (VoterCaptureTypes != null)
				VoterCaptureTypes.Clear();
			else
				VoterCaptureTypes = new ObservableCollection<ElectionCaptureIdVoterTypeDbo>();

			try
			{
				VoterCaptureTypes =
					new ObservableCollection<ElectionCaptureIdVoterTypeDbo>(
						await _captureIdFacade.RetrieveCaptureIdTypes());
			}
			catch (Exception ex)
			{
				_essLogger.LogError(ex,
					new Dictionary<string, string>
						{ { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name }, });
				_messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error,
					SystemDetails.GenericErrorMessage));
			}

			ValidateAndResizeCaptureTypes();
			SelectedVerificationType = null;
		}

		/// <summary>
		/// Ensures that at most 15 id types.  The list comes sorted ascending on sort order, so
		/// the removes are at the end of it.
		/// </summary>
		private void ValidateAndResizeCaptureTypes()
		{
			if (!(VoterCaptureTypes?.Count > MaximumIds))
				return;

			// no RemoveRange()
			for (int i = VoterCaptureTypes.Count; i > MaximumIds; --i)
				VoterCaptureTypes.RemoveAt(i - 1);
		}

		private async Task Next()
		{
			try
			{
				await _auditLogFacade.AddToAuditLogAsync(PageName, $"'{NextLabel}' button was activated.");
				_voter.CaptureId.CaptureIdType = SelectedVerificationType;

				// One more screen if form name is present, and that will take us to AddressChangedViewModel if enabled.
				if (!string.IsNullOrWhiteSpace(SelectedVerificationType.Capture_Voter_ID_Type_Form_Name))
				{
					_navigation.NavigateTo<CaptureIdVoterPresentFormViewModel>(_voter,
						NavigationFrameEnum.ModalFrame,
						false, withVerticalEffect: true);
					return;
				}

				if (SystemConfiguration.ElectionConfiguration.AddressOnlyChangeType != AddressOnlyChangeType.None)
					_navigation.NavigateTo<AddressChangedViewModel>(_voter, NavigationFrameEnum.ContextFrame);
				else
					_navigation.NavigateTo<VoterViewModel>(_voter, NavigationFrameEnum.ContextFrame);
			}
			catch (Exception ex)
			{
				_essLogger.LogError(ex,
					new Dictionary<string, string>
						{ { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name }, });
				_messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error,
					SystemDetails.GenericErrorMessage));
			}
		}

		private async Task Back()
		{
			try
			{
				await _auditLogFacade.AddToAuditLogAsync(PageName, $"'{BackLabel}' button was activated.");
				_navigation.GoBack(_voter);
			}
			catch (Exception ex)
			{
				_essLogger.LogError(ex,
					new Dictionary<string, string>
						{ { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name }, });
				_messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error,
					SystemDetails.GenericErrorMessage));
			}
		}
	}
}
