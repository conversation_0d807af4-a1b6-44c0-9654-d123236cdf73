using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Net.NetworkInformation;
using System.Reflection;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using ESS.Pollbook.Core.Commands;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.Model;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Facade.Barcode;
using ESS.Pollbook.Facade.ElectionSettings;
using ESS.Pollbook.Facade.Maintenance;
using ESS.Pollbook.Facade.PQC;
using ESS.Pollbook.ViewModel.Maintenance;
using ESS.Pollbook.Core.Messaging;
using ESS.Pollbook.ViewModel.Password;
using ESS.Pollbook.ViewModel.SystemStats;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.Command;
using GalaSoft.MvvmLight.Messaging;

namespace ESS.Pollbook.ViewModel
{
    public class LaunchViewModel : ViewModelBase
    {
        private readonly IFrameNavigationService _navigation;
        private readonly IMessenger _messenger;
        private readonly IEssLogger _essLogger;
        private readonly IElectionSettingsFacade _electionSettingsFacade;
        private readonly ILoadElectionFacade _loadElectionFacade;
        private readonly IPQCFacade _pqcFacade;
        private readonly IBarcodeHandler _barcodeHandler;
        private readonly IAuditLogFacade _auditLogFacade;

        private NotifyTaskCompletion<ElectionSettingsModel> _electionInfo;

        private string PageName => _navigation.PageName(GetType().FullName);
        public ICommandAsync LaunchCommand => new CommandAsync(Launch);
        public ICommand ShutdownCommand => new RelayCommand(ShutdownDialogue);
        public ICommand MaintenanceCommand => new RelayCommand(Maintenance);
        public ICommand CloseErrorWindowCommand => new RelayCommand(CloseErrorWindow);

        public static string LaunchLabel =>
            SystemDetails.IsTestMode ? $"{UIText.Launch} {UIText.TestModeLabel}" : UIText.Launch;

        public static string ShutdownLabel => UIText.ShutDown;
        public static string MaintenanceLabel => UIText.Maintenance;
        public static string OkLabel => UIText.OK;

        public SystemStatsViewModel SystemStats { get; set; }

        private bool _errorWindowVisible;

        public bool ErrorWindowVisible
        {
            get => _errorWindowVisible;
            set => Set(ref _errorWindowVisible, value);
        }

        public bool IsNetWorkAttached => NetworkInterface.GetIsNetworkAvailable();

        public string ElectionName => ElectionSettings.ElectionName;
        public string JurisdictionName => ElectionSettings.JurisdictionName;
        public string AppVersion => SystemDetails.VersionNumber;
        public string DeviceName => SystemDetails.MachineName;
        public string LastSyncDateTime => ElectionSettings.LastSync;
        public string DatabaseVersion => ElectionSettings.DatabaseVersion;
        public string ConfigurationVersion => ElectionSettings.ConfigurationVersion;
        public string LocalTransactions => ElectionSettings.LocalTransactions;
        public string SentToServer => ElectionSettings.SentToServer;
        public double UploadSyncStatus => GetUploadSyncStatus();
        public static bool TestMode => SystemDetails.IsTestMode;
        public static string TestModeLabel => UIText.TestModeLabel;

        public LaunchViewModel(IFrameNavigationService navigationService,
            IMessenger messengerService,
            IEssLogger essLogger,
            IElectionSettingsFacade electionSettingsFacade,
            ILoadElectionFacade loadElectionFacade,
            IBarcodeHandler barcodeHandler,
            IPQCFacade pqcFacade,
            IAuditLogFacade auditLogFacade)
        {
            _navigation = navigationService;
            _messenger = messengerService;
            _essLogger = essLogger;
            _electionSettingsFacade = electionSettingsFacade;
            _loadElectionFacade = loadElectionFacade;
            _barcodeHandler = barcodeHandler;
            _pqcFacade = pqcFacade;
            _auditLogFacade = auditLogFacade;

            NetworkChange.NetworkAvailabilityChanged += NetworkChange_NetworkAvailabilityChanged;

            SystemStats = ((MainWindowViewModel)Application.Current.MainWindow?.DataContext)
                ?.SystemStats;

            _messenger.Register<TestModeMessage>(this, RefreshTestMode);
            _messenger.Register<CumulativeUpdateFailureMessage>(this, CumulativeUpdateErrorMessageHandler);
        }

        public void GetElectionInfo()
        {
           _electionInfo = new NotifyTaskCompletion<ElectionSettingsModel>();
           _electionInfo.PropertyChanged += ElectionTaskCompleted;
           _electionInfo.ExecuteTask(_electionSettingsFacade.GetElectionInfo());
        }

        private void ElectionTaskCompleted(object sender, PropertyChangedEventArgs e)
        {
           if (!e.PropertyName.Equals("Result")) return;
           if (_electionInfo.Result == null) return;

           RaisePropertyChanged(nameof(ElectionName));
           RaisePropertyChanged(nameof(JurisdictionName));
           RaisePropertyChanged(nameof(DeviceName));
           RaisePropertyChanged(nameof(LastSyncDateTime));
           RaisePropertyChanged(nameof(DatabaseVersion));
           RaisePropertyChanged(nameof(ConfigurationVersion));
           RaisePropertyChanged(nameof(LocalTransactions));
           RaisePropertyChanged(nameof(SentToServer));
           RaisePropertyChanged(nameof(UploadSyncStatus));
        }

        private void NetworkChange_NetworkAvailabilityChanged(object sender, NetworkAvailabilityEventArgs e)
        {
            RaisePropertyChanged(nameof(IsNetWorkAttached));
        }

        public async Task PageInitialized()
        {
            ErrorWindowVisible = false;

            //disconnect databases, reset PQC status
            SystemDetails.IsPQCVerified = false;
            SystemDetails.PQCPassword = null;

            try
            {
                _ = await _pqcFacade.CloseConnectionsAsync();
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string>
                    { { "Action", "LaunchViewModel Init: Closing database connections failed." } };
                _essLogger.LogError(ex, logProps);
            }
        }

        public void CumulativeUpdateErrorMessageHandler(CumulativeUpdateFailureMessage _)
        {
            ErrorWindowVisible = true;
        }

        private async Task Launch()
        {
            try
            {
                await _auditLogFacade.AddToAuditLogAsync(PageName, $"'{LaunchLabel}' button was activated.");

                if (!_loadElectionFacade.IsElectionLoaded())
                {
                    _messenger.Send(new SetTitleMessage(typeof(LaunchViewModel)));
                    _navigation.NavigateTo<ElectionNotFoundViewModel>(typeof(LaunchViewModel));
                    return;
                }

                _navigation.NavigateTo<PQCViewModel>(typeof(LoginViewModel), NavigationFrameEnum.ModalDialogFrame,
                    false);
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string>
                    { { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } };
                _essLogger.LogError(ex, logProps);
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error,
                    SystemDetails.GenericErrorMessage));
            }
        }

        private void Maintenance()
        {
            try
            {
                _auditLogFacade.AddToAuditLog(PageName, $"'{MaintenanceLabel}' button was activated.");
                _navigation.NavigateTo<PasswordViewModel>(NavigationFrameEnum.ModalDialogFrame, false);
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string>
                    { { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } };
                _essLogger.LogError(ex, logProps);
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error,
                    SystemDetails.GenericErrorMessage));
            }
        }

        public async Task PageLoaded()
        {
            GetElectionInfo();

            if (!SystemDetails.IsScannerInitialized)
            {
                await Task.Run(() => _barcodeHandler.InitializeScanner());
            }
        }

        private void CloseErrorWindow()
        {
            ErrorWindowVisible = false;
        }

        private void ShutdownDialogue()
        {
            try
            {
                _auditLogFacade.AddToAuditLog(PageName, $"'{ShutdownLabel}' button was activated.");
                var shutdownDto = new ShutDownDto
                {
                    Title = "Are you sure you want to shut down this device?",
                    Message = string.Empty
                };

                _navigation.NavigateTo<ShutDownViewModel>(shutdownDto, NavigationFrameEnum.ModalFrame, false,
                    withVerticalEffect: true);
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string>
                    { { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } };
                _essLogger.LogError(ex, logProps);
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error,
                    SystemDetails.GenericErrorMessage));
            }
        }

        private void RefreshTestMode(TestModeMessage msg)
        {
            RaisePropertyChanged(nameof(TestMode));
        }

        private double GetUploadSyncStatus()
        {
            if (!double.TryParse(LocalTransactions, out var localTransactionNum) ||
                !double.TryParse(SentToServer, out var sentToServerNum)) return 0;

            return localTransactionNum.Equals(0.0d) ? 100 : Math.Min(100, Math.Floor((sentToServerNum / localTransactionNum) * 100));
        }
    }
}