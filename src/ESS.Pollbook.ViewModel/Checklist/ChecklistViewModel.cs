using System;
using System.Collections.Generic;
using System.Reflection;
using System.Windows.Input;
using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Facade.Checklist;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.Command;
using GalaSoft.MvvmLight.Messaging;
using ChecklistType = ESS.Pollbook.Core.Common.ChecklistType;

namespace ESS.Pollbook.ViewModel.Checklist
{
	public class ChecklistViewModel : ViewModelBase
	{
		private readonly IFrameNavigationService _navigation;
		private readonly IMessenger _messenger;
		private readonly IEssLogger _essLogger;
		private readonly IChecklistFacade _checklistFacade;
		private readonly IAuditLogFacade _auditLogFacade;

		public ICommand NextCommand => new RelayCommand(Next);
		public ICommand BackCommand => new RelayCommand(Back);

		private PollPlaceDto PollPlace => LoggedInPollplaceInfo.LoggedInPollPlace;
		private string PageName => _navigation.PageName(this.GetType().FullName);
		private List<ChecklistDto> _checklistSteps = new List<ChecklistDto>();

		public List<ChecklistDto> ChecklistSteps
		{
			get => _checklistSteps;
			set => _checklistSteps = value;
		}

		private bool _isChecked;

		public bool IsChecked
		{
			get => _isChecked;
			set
			{
				_auditLogFacade.AddToAuditLog(PageName, $"'Checkbox' button was activated.");
				Set(ref _isChecked, value);
				RaisePropertyChanged();
				RaisePropertyChanged(nameof(IsNextEnabled));
			}
		}

		private string _stepTextLabel;

		public string StepTextLabel
		{
			get => _stepTextLabel;
			set
			{
				Set(ref _stepTextLabel, value);
				RaisePropertyChanged();
			}
		}

		public bool IsNextEnabled
		{
			get => IsChecked;
		}

		private static int _currentStep;

		private int CurrentStep
		{
			get => _currentStep;
			set
			{
				Set(ref _currentStep, value);
				RaisePropertyChanged();
			}
		}

		private string _currentStepLabel;

		public string CurrentStepLabel
		{
			get => _currentStepLabel;
			set
			{
				Set(ref _currentStepLabel, value);
				RaisePropertyChanged($"{nameof(CurrentStepLabel)}");
			}
		}

		public string BackLabel => UIText.Back;
		public string NextLabel => UIText.Next;

		private string _pageTitleLabel;

		public string PageTitleLabel
		{
			get => _pageTitleLabel;
			set
			{
				Set(ref _pageTitleLabel, value);
				RaisePropertyChanged();
			}
		}

		private string _titleLabel;

		public string TitleLabel
		{
			get => _titleLabel;
			set
			{
				Set(ref _titleLabel, value);
				RaisePropertyChanged();
			}
		}

		private string _detailsText;

		public string DetailsText
		{
			get => _detailsText;
			set
			{
				Set(ref _detailsText, value);
				RaisePropertyChanged();
			}
		}

		private int _totalSteps;

		public int TotalSteps
		{
			get => _totalSteps;
			set => Set(ref _totalSteps, value);
		}

		private string _buttonText;

		public string ButtonText
		{
			get => _buttonText;
			set
			{
				Set(ref _buttonText, value);
				RaisePropertyChanged();
			}
		}

		public ChecklistViewModel(IFrameNavigationService navigationService,
			IMessenger messengerService,
			IEssLogger essLogger,
			IChecklistFacade checklistFacade,
			IAuditLogFacade auditLogFacade)
		{
			_navigation = navigationService;
			_messenger = messengerService;
			_essLogger = essLogger;
			_checklistFacade = checklistFacade;
			_auditLogFacade = auditLogFacade;
		}

		public async void PageLoaded()
		{
			try
			{
				if (!(_navigation.Parameter is ChecklistType checklistType))
				{
					_navigation.NavigateTo<DashboardViewModel>();
					return;
				}

				ChecklistSteps = await _checklistFacade.RetrieveChecklist(checklistType.ToString(),
					GetCurrentPollPlaceType().ToString());

				_isChecked = false;

				if (ChecklistSteps != null && ChecklistSteps.Count != 0)
				{
					TotalSteps = ChecklistSteps.Count;
					PageTitleLabel = (_checklistSteps[0].Checklist_Type_Name) + " Checklist";
					UpdateText();
				}
				else
				{
					_navigation.NavigateTo<DashboardViewModel>();
				}
			}
			catch (Exception ex)
			{
				var logProps = new Dictionary<string, string>
					{ { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } };
				_essLogger.LogError(ex, logProps);
			}
		}

		public void PageUnloaded()
		{
			try
			{
				if (!LoggedInPollplaceInfo.IsPollOpen)
				{
					PromptSignOut();
				}
			}
			catch (Exception ex)
			{
				var logProps = new Dictionary<string, string>
					{ { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } };
				_essLogger.LogError(ex, logProps);
			}
		}

		public void Next()
		{
			_auditLogFacade.AddToAuditLog(PageName, $"'{NextLabel}' button was activated.");
			if (_totalSteps == _currentStep + 1)
			{
				Clear();
				if (LoggedInPollplaceInfo.IsPollOpen)
				{
					_navigation.NavigateTo<DashboardViewModel>();
				}
				else
				{
					PromptSignOut();
				}
			}
			else
			{
				CurrentStep++;
				IsChecked = false;
				UpdateText();
			}
		}

		public void Back()
		{
			_auditLogFacade.AddToAuditLog(PageName, $"'{BackLabel}' button was activated.");
			if (_currentStep <= 0)
			{
				Clear();
				_navigation.NavigateTo<MonitorPollViewModel>(NavigationFrameEnum.ContextFrame);
			}
			else
			{
				_currentStep--;
				UpdateText();
			}
		}

		/// <summary>
		/// Updates the text displayed on screen
		/// </summary>
		public void UpdateText()
		{
			try
			{
				TitleLabel = _checklistSteps[_currentStep].Checklist_Step_Name;
				DetailsText = _checklistSteps[_currentStep].Checklist_Step_Details;
				ButtonText = _checklistSteps[_currentStep].Checklist_Step_ButtonText;
				CurrentStepLabel = $"Step {_currentStep + 1} of {_totalSteps}";
			}
			catch (Exception ex)
			{
				var logProps = new Dictionary<string, string>
					{ { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } };
				_essLogger.LogError(ex, logProps);
			}
		}

		/// <summary>
		/// Resets the items displayed on screen
		/// </summary>
		private void Clear()
		{
			_checklistSteps.Clear();
			CurrentStep = 0;
			TotalSteps = 0;
			IsChecked = false;
		}

		private void PromptSignOut()
		{
			try
			{
				_navigation.NavigateTo<DashboardViewModel>();
				if (SystemConfiguration.ElectionConfiguration.DualLogout)
				{
					_navigation.NavigateTo<DashboardViewModel>(NavigationFrameEnum.MainFrame, false);
				}
				else
				{
					var signOutDto = new SignOutDto
					{
						PollPlace = PollPlace
					};
					_navigation.NavigateTo<SignOutViewModel>(signOutDto, NavigationFrameEnum.ModalFrame, false,
						withVerticalEffect: true);
				}
			}
			catch (Exception ex)
			{
				var logProps = new Dictionary<string, string>
					{ { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } };
				_essLogger.LogError(ex, logProps);
				_messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error,
					SystemDetails.GenericErrorMessage));
			}
		}

		/// <summary>
		/// Gets the pollplace type of the current poll
		/// </summary>
		/// <returns>A string representing the state of the polls "ED", "EV", "BOTH"</returns>
		private ChecklistPollType GetCurrentPollPlaceType()
		{
			switch (LoggedInPollplaceInfo.LoggedInPollPlace.PollingPlacePollTypeEnumID)
			{
				case 196:
					return ChecklistPollType.EV;
				case 195:
					return ChecklistPollType.ED;
				default:
					return ChecklistPollType.Both;
			}
		}
	}
}