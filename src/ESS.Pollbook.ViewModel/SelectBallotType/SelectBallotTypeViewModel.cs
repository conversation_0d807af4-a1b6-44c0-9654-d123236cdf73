using ESS.Pollbook.Core.Commands;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Facade.PagesTitle;
using ESS.Pollbook.Facade.PollbookDefinedText;
using ESS.Pollbook.Core.Messaging;
using ESS.Pollbook.ViewModel.Utils;
using ESS.Pollbook.ViewModel.VoterBallot;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.Command;
using GalaSoft.MvvmLight.Messaging;
using System;
using System.Collections.Generic;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Input;

namespace ESS.Pollbook.ViewModel.SelectBallotType
{
   public sealed class SelectBallotTypeViewModel : ViewModelBase, IDisposable
   {
      private readonly IFrameNavigationService _navigation;
      private readonly IMessenger _messenger;
      private readonly IPagesTitleFacade _pagesTitleFacade;
      private readonly IEssLogger _essLogger;
      private readonly IPollbookDefinedTextFacade _pollbookDefinedTextFacade;
      private readonly IAuditLogFacade _auditLogFacade;
      private readonly IPrintBallotUtil _printBallotUtil;

      /// <summary>
      /// Used to protect a double tap of Issue Ballot.
      /// </summary>
      private SemaphoreSlim _semaphore = new SemaphoreSlim(1, 1);

      private bool _disposed;

      private string PageName => _navigation.PageName(GetType().FullName);

      public ICommand BackCommand => new RelayCommand(Back);
      public ICommandAsync IssueBallotCommandAsync => new CommandAsync(IssueBallotAsync);

      public string BackLabel => UIText.Back;

      public string LabelContent => _pagesTitleFacade.PopulateTitleContent(IssueBallotHelper.Voter.VoterBallotDto.IsProvisional, IssueBallotHelper.IsReissuingBallot, DefinedText.DefaultLanguage);

      public bool IsReissuingBallot => IssueBallotHelper.IsReissuingBallot;

      public bool NextEnabled => (_paperBallotIsChecked || _bodIsChecked || _expressVoteActivationIsChecked) && _semaphore.CurrentCount == 1;

      private bool _expressVoteActivationIsChecked;

      public bool ExpressVoteActivationIsChecked
      {
         get => _expressVoteActivationIsChecked;
         set
         {
            Set(ref _expressVoteActivationIsChecked, value);
            if (_expressVoteActivationIsChecked)
            {
               _paperBallotIsChecked = false;
               _bodIsChecked = false;

               RaisePropertyChanged(nameof(PaperBallotIsChecked));
               RaisePropertyChanged(nameof(BODIsChecked));
            }

            RaisePropertyChanged(nameof(NextEnabled));
         }
      }

      // ***************************************************************
      // bod is dead to us. We should look into removing the bod code. 7.2.9.0
      private bool _bodIsChecked;

      public bool BODIsChecked
      {
         get => _bodIsChecked;
         set
         {
            Set(ref _bodIsChecked, value);
            if (_bodIsChecked)
            {
               _paperBallotIsChecked = false;
               _expressVoteActivationIsChecked = false;

               RaisePropertyChanged(nameof(PaperBallotIsChecked));
               RaisePropertyChanged(nameof(ExpressVoteActivationIsChecked));
            }

            RaisePropertyChanged(nameof(NextEnabled));
         }
      }
      // bod is dead
      // *******************************************************************

      private bool _paperBallotIsChecked;

      public bool PaperBallotIsChecked
      {
         get => _paperBallotIsChecked;
         set
         {
            Set(ref _paperBallotIsChecked, value);
            if (_paperBallotIsChecked)
            {
               _expressVoteActivationIsChecked = false;
               _bodIsChecked = false;

               RaisePropertyChanged(nameof(ExpressVoteActivationIsChecked));
               RaisePropertyChanged(nameof(BODIsChecked));
            }

            RaisePropertyChanged(nameof(NextEnabled));
         }
      }

      public bool PaperBallotEnabled => SystemConfiguration.ElectionConfiguration.PaperBallotEnabled(LoggedInPollplaceInfo.IsEarlyVotingPollPlace, IssueBallotHelper.Voter.VoterBallotDto.IsProvisional);

      public bool ExpressVoteEnabled => SystemConfiguration.ElectionConfiguration.ExpressVoteEnabled(LoggedInPollplaceInfo.IsEarlyVotingPollPlace, IssueBallotHelper.Voter.VoterBallotDto.IsProvisional);

      // *****************************************
      // bod is dead - think about removing this
      public bool BalotarEnabled => false;
      // bod is dead
      // *****************************************

      private List<string> _expressVoteLabelText;

      public List<string> ExpressVoteLabelText => _expressVoteLabelText;

      private List<string> _paperLabelText;
      public List<string> PaperLabelText => _paperLabelText;

      public VoterDto Voter => IssueBallotHelper.Voter;

      public SelectBallotTypeViewModel(
          IFrameNavigationService navigationService,
          IMessenger messengerService,
          IPagesTitleFacade pagesTitleFacade,
          IEssLogger essLogger,
          IPollbookDefinedTextFacade pollbookDefinedTextFacade,
          IAuditLogFacade auditLogFacade,
          IPrintBallotUtil printBallotUtil)
      {
         _navigation = navigationService;
         _messenger = messengerService;
         _pagesTitleFacade = pagesTitleFacade;
         _essLogger = essLogger;
         _pollbookDefinedTextFacade = pollbookDefinedTextFacade;
         _auditLogFacade = auditLogFacade;
         _printBallotUtil = printBallotUtil;
      }

      public void PageIsLoaded()
      {
         try
         {
            var expressVoteLabelText = _pollbookDefinedTextFacade.GetPollbookDefinedTextForLanguage("ExpressVote_Button_Label", DefinedText.PollbookDefinedTextCurrentLanguage);
            var paperLabelText = _pollbookDefinedTextFacade.GetPollbookDefinedTextForLanguage("Paper_Ballot_Button_Label", DefinedText.PollbookDefinedTextCurrentLanguage);

            const int maxLabelLength = 20; // Only about 20 characters will fit without breaking into multiple strings. And, if the string is that long, we expect it to contain some spaces.

            _expressVoteLabelText = CreateButtonTextLabelLayout(expressVoteLabelText, maxLabelLength);
            _paperLabelText = CreateButtonTextLabelLayout(paperLabelText, maxLabelLength);

            RaisePropertyChanged(nameof(ExpressVoteLabelText));
            RaisePropertyChanged(nameof(PaperLabelText));

            ResetButtons();
         }
         catch (Exception ex)
         {
            var logProps = new Dictionary<string, string> { { "Action", "SelectBallotTypeViewModel.PageIsLoaded" } };
            _essLogger.LogError(ex, logProps);
         }
      }

      public List<string> CreateButtonTextLabelLayout(string buttonLabelText, int maxLength)
      {
         if (buttonLabelText.Length <= maxLength)
            return new List<string> { buttonLabelText };

         // Break as evenly as possible - assume max of 2 lines
         var half = buttonLabelText.Length / 2;
         var precedingSpace = buttonLabelText.Substring(0, half).LastIndexOf(' ');
         var nextSpace = buttonLabelText.Substring(half).IndexOf(' ');

         if (precedingSpace + nextSpace == -2)
            return ReturnList(buttonLabelText, half, half);

         if (precedingSpace == -1)
            return ReturnList(buttonLabelText, half + nextSpace, half + nextSpace + 1);

         if (nextSpace == -1)
            return ReturnList(buttonLabelText, precedingSpace, precedingSpace + 1);

         return (half - precedingSpace < nextSpace)
             ? ReturnList(buttonLabelText, precedingSpace, precedingSpace + 1)
             : ReturnList(buttonLabelText, half + nextSpace, half + nextSpace + 1);
      }

      private static List<string> ReturnList(string input, int breakpoint1, int breakpoint2)
      {
         return new List<string>
            {
                input.Substring(0, breakpoint1),
                input.Substring(breakpoint2)
            };
      }

      private void Back()
      {
         try
         {
            _auditLogFacade.AddToAuditLog(PageName, $"'{BackLabel}' button was activated.");
            _messenger.Send(new StatusBarMessage(isVisible: true));

            IssueBallotHelper.SelectedBallotType = BallotType.Unknown;
            _navigation.GoBack();
            ResetButtons();
         }
         catch (Exception ex)
         {
            _essLogger.LogError(ex, new Dictionary<string, string> { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
            _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
         }
      }

      public async Task IssueBallotAsync()
      {
         if (!_semaphore.Wait(0)) // Don't want async as this only blocks a double tap of the button and not to get in.
            return;

         try
         {
            var label = IsReissuingBallot ? UIText.ReissueBallot : UIText.IssueBallot;
            await _auditLogFacade.AddToAuditLogAsync(PageName, $"'{label}' button was activated.");

            if (!SystemDetails.IsPQCVerified)
               return;

            RaisePropertyChanged(nameof(NextEnabled));

            SetSelectedBallotType();

            switch (IssueBallotHelper.SelectedBallotType)
            {
               case BallotType.ExpressVote:
                  if (SystemConfiguration.ElectionConfiguration.ExpressVotePrintingEnabled(
                         LoggedInPollplaceInfo.IsEarlyVotingPollPlace,
                         IssueBallotHelper.Voter.VoterBallotDto.IsProvisional))
                  {
                     IssueBallotHelper.Voter.NeedExpressVoteCard = true;

                     _messenger.Send(new ExpressVoteCardActivationMessage());

                     _navigation.NavigateTo<ExpressVoteActivationCardViewModel>(
                        NavigationFrameEnum.ContextFrame, false, suppressHorizontalEffect: true);
                     break;
                  }

                  IssueBallotHelper.Voter.NeedExpressVoteCard = false;
                  _messenger.Send(new IssueBallotMessage(GetType().Name));
                  _navigation.NavigateTo<VoterIssueBallotViewModel>(NavigationFrameEnum.ContextFrame, false);
                  break;

               case BallotType.DAC:
                  if (SystemConfiguration.ElectionConfiguration.ExpressVotePrintingEnabled(
                         LoggedInPollplaceInfo.IsEarlyVotingPollPlace, IssueBallotHelper.Voter.VoterBallotDto.IsProvisional))
                  {
                     _navigation.NavigateTo<ActivationCardViewModel>(NavigationFrameEnum.ContextFrame, suppressHorizontalEffect: true);
                     _messenger.Send(new RegisterSmartCardEventsMessage());
                  }
                  else
                  {
                     _messenger.Send(new IssueBallotMessage(GetType().Name));
                     _navigation.NavigateTo<VoterIssueBallotViewModel>(NavigationFrameEnum.ContextFrame, false);
                  }
                  break;
               case BallotType.Paper:
                  await _printBallotUtil.PrintBallotAsync(Voter);
                  break;

               default:
                  _messenger.Send(new IssueBallotMessage(GetType().Name));
                  _navigation.NavigateTo<VoterIssueBallotViewModel>(NavigationFrameEnum.ContextFrame, false);
                  break;
            }

            ResetButtons();
         }
         catch (Exception ex)
         {
            _essLogger.LogError(ex, new Dictionary<string, string> { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
            _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            _navigation.NavigateTo<PrintBallotFailedViewModel>(NavigationFrameEnum.ContextFrame, false); // this was added 7240 - is it necessary?
         }
         finally
         {
            _semaphore.Release();
         }
      }

      private void SetSelectedBallotType()
      {
         IssueBallotHelper.SelectedBallotType = BallotType.Unknown;

         if (_bodIsChecked)
            IssueBallotHelper.SelectedBallotType = BallotType.Balotar;

         if (_paperBallotIsChecked)
            IssueBallotHelper.SelectedBallotType = BallotType.Paper;

         if (_expressVoteActivationIsChecked)
            IssueBallotHelper.SelectedBallotType = SystemConfiguration.ElectionConfiguration.DacEnabled
               ? BallotType.DAC
               : BallotType.ExpressVote;
      }

      private void ResetButtons()
      {
         ExpressVoteActivationIsChecked = false;
         PaperBallotIsChecked = false;
         BODIsChecked = false;
      }

      public void Dispose()
      {
         Dispose(true);
      }

      private void Dispose(bool disposing)
      {
         if (_disposed)
            return;

         if (disposing)
         {
            _semaphore?.Dispose();
            _semaphore = null;
         }

         _disposed = true;
      }
   }
}

