using ESS.Pollbook.Core;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.Model;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Facade.Reports;
using GalaSoft.MvvmLight.Messaging;
using System;
using System.Collections.Generic;

namespace ESS.Pollbook.ViewModel
{
    public class PrintVotedListFailedViewModel : PrintFailedViewModelBase
    {
        private readonly IFullVotedListFacade _fullVotedListFacade;
        private readonly IAuditLogFacade _auditLogFacade;
        private readonly string _pageName;

        public PrintVotedListFailedViewModel(IFrameNavigationService navigationService,
            IMessenger messengerService,
            IEssLogger essLogger,
            IFullVotedListFacade fullVotedListFacade,
            IAuditLogFacade auditLogFacade)
            : base(navigationService, messengerService, essLogger, auditLogFacade)
        {
            _fullVotedListFacade = fullVotedListFacade;
            _auditLogFacade = auditLogFacade;

            _pageName = _navigation.PageName(this.GetType().FullName);
        }

        protected override void Retry()
        {
            try
            {
                _auditLogFacade.AddToAuditLog(_pageName, $"'{RetryLabel}' button was activated.");
                var votedList = (VotedList)(_navigation.Parameter);
                var printResponse = _fullVotedListFacade.PrintFullVotedList(votedList, false); //TODO: find a way to populate second parameter in Failed VM so Retry works
                if (printResponse != null &&
                    printResponse.PrinterErrorMessage != PrinterErrorMessages.PrinterNotConnected &&
                    printResponse.PrinterErrorMessage != PrinterErrorMessages.PrinterNotSelected &&
                    printResponse.PrinterErrorMessage != PrinterErrorMessages.PrinterOffline)
                {
                    _navigation.GoBack();
                }
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string> { { "Action", "PrintVotedListFailedViewModel.Retry" } };
                _essLogger.LogError(ex, logProps);
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }
    }
}
