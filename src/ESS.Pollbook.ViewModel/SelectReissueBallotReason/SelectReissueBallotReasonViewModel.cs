using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Facade.ElectionJurisdictionEnum;
using ESS.Pollbook.Facade.Party;
using ESS.Pollbook.Core.Messaging;
using ESS.Pollbook.ViewModel.VoterSignature;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.Command;
using GalaSoft.MvvmLight.Messaging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using ESS.Pollbook.Core.Commands;
using ESS.Pollbook.ViewModel.Utils;
using System.Reflection;

namespace ESS.Pollbook.ViewModel.SelectReissueBallotReason
{
    public class SelectReissueBallotReasonViewModel : ViewModelBase
    {
        private readonly IMessenger _messenger;
        private readonly IFrameNavigationService _navigation;
        private readonly IVoterJurisdictionEnumFacade _voterJurisdictionEnumFacade;
        private readonly IPartyFacade _partyService;
        private readonly IEssLogger _essLogger;
        private readonly IAuditLogFacade _auditLogFacade;
        private readonly IPrintBallotUtil _printBallotUtil;

        private string PageName => _navigation.PageName(this.GetType().FullName);

        public ICommand BackCommand => new RelayCommand(Back);

        public ICommandAsync NextCommand => new CommandAsync(NextAsync);

        public static string BackLabel => UIText.Back;
        public static string NextLabel => UIText.Next;

        private List<ElectionJurisdictionEnumValueDto> _ballotReissueReasons;
        public List<ElectionJurisdictionEnumValueDto> BallotReissueReasons
        {
            get => _ballotReissueReasons;
            set => Set(ref _ballotReissueReasons, value);
        }

        private ElectionJurisdictionEnumValueDto _selectedBallotReissueReason;
        public ElectionJurisdictionEnumValueDto SelectedBallotReissueReason
        {
            get => _selectedBallotReissueReason;
            set
            {
                if (Set(ref _selectedBallotReissueReason, value))
                {
                    HasBallotReissueReasonSelection = _selectedBallotReissueReason != null;
                }
            }
        }

        private bool _hasBallotReissueReasonSelection;

        public bool HasBallotReissueReasonSelection
        {
            get => _hasBallotReissueReasonSelection;
            set => Set(ref _hasBallotReissueReasonSelection, value);
        }

        public SelectReissueBallotReasonViewModel(IMessenger messenger,
            IFrameNavigationService navigation,
            IVoterJurisdictionEnumFacade voterJurisdictionEnumFacade,
            IPartyFacade partyService,
            IEssLogger essLogger,
            IAuditLogFacade auditLogFacade,
            IPrintBallotUtil printBallotUtil)
        {
            _messenger = messenger;
            _navigation = navigation;
            _voterJurisdictionEnumFacade = voterJurisdictionEnumFacade;
            _partyService = partyService;
            _essLogger = essLogger;
            _auditLogFacade = auditLogFacade;
            _printBallotUtil = printBallotUtil;
        }

        public void PageLoaded()
        {
            SelectedBallotReissueReason = null;
            HasBallotReissueReasonSelection = false;
            LoadBallotReissueReasons();
        }

        private void LoadBallotReissueReasons()
        {
            try
            {
                BallotReissueReasons = Task.Run(async () => await _voterJurisdictionEnumFacade.GetEnumerationDetailsByStatusesAsync(
                    (int)EssEnumeration.BallotReissueReason)).Result.OrderBy(x => x.JurisdictionEnumerationValueId).ToList();
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string> { { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } };
                _essLogger.LogError(ex, logProps);
            }
        }

        private void Back()
        {
            try
            {
                _auditLogFacade.AddToAuditLog(PageName, $"'{BackLabel}' button was activated.");
                _navigation.GoBack();
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string> { { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } };
                _essLogger.LogError(ex, logProps);
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }

        private async Task NextAsync()
        {
            try
            {
                await _auditLogFacade.AddToAuditLogAsync(PageName, $"'{NextLabel}' button was activated.");
                IssueBallotHelper.Voter.VoterBallotDto.ReissueReasonEnumId = SelectedBallotReissueReason.JurisdictionEnumerationValueId;
                if (SystemConfiguration.ElectionConfiguration.CaptureElectronicSignature)
                {
                    _messenger.Send(new StatusBarMessage(isVisible: true));
                    _navigation.NavigateTo<VoterSignatureViewModel>(NavigationFrameEnum.ContextFrame);
                }
                else
                {
                    await IssueBallotHelper.NavigateToNextPageAsync(_navigation, _partyService, _messenger, _printBallotUtil);
                }
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string> { { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } };
                _essLogger.LogError(ex, logProps);
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }
    }
}
