using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.AuditLog;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.Command;
using System.Windows.Input;

namespace ESS.Pollbook.ViewModel
{
    public class ApplyingConfigurationUpdatesFailedViewModel : ViewModelBase
    {
        private readonly IFrameNavigationService _navigation;
        private readonly IAuditLogFacade _auditLogFacade;


        private readonly string _pageName;
        public string BackLabel => UIText.Back;
        public ICommand BackCommand => new RelayCommand(Back);

        public ApplyingConfigurationUpdatesFailedViewModel(IFrameNavigationService navigationService,
                                          IAuditLogFacade auditLogFacade)
        {
            _navigation = navigationService;
            _auditLogFacade = auditLogFacade;
            _pageName = _navigation.PageName(GetType().FullName);
        }
        private void Back()
        {
            _auditLogFacade.AddToAuditLog(_pageName, $"'{BackLabel}' button was activated.");
            _navigation.NavigateTo<DashboardViewModel>(NavigationFrameEnum.MainFrame, false);
        }
    }
}
