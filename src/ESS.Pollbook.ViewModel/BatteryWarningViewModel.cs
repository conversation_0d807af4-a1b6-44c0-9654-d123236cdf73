using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Core.Messaging;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.Command;
using GalaSoft.MvvmLight.Messaging;
using System.Windows.Input;

namespace ESS.Pollbook.ViewModel
{
    public class BatteryWarningViewModel : ViewModelBase
    {
        private readonly IFrameNavigationService _navigation;
        private readonly IMessenger _messenger;
        private readonly IAuditLogFacade _auditLogFacade;

        private readonly string _pageName;

        private readonly ICommand _okCommand;

        public ICommand OKCommand => _okCommand;

        public string OKLabel => UIText.OK;

        public string Title
        {
            get
            {
                return $"{SystemDetails.BatteryWarningLevel}% Battery Remaining";
            }
        }

        public string Message
        {
            get
            {
                return "Please connect to a power outlet to charge device.";
            }
        }

        public BatteryWarningViewModel(IFrameNavigationService frameNavigation, IMessenger messengerService, IAuditLogFacade auditLogFacade)
        {
            _navigation = frameNavigation;
            _messenger = messengerService;
            _auditLogFacade = auditLogFacade;

            _pageName = _navigation.PageName(this.GetType().FullName);

            _okCommand = new RelayCommand(Ok);

            _messenger.Register<BatteryLevelMessage>(this, SetBatteryLevel);
        }

        private void SetBatteryLevel(BatteryLevelMessage message)
        {
            RaisePropertyChanged("Title");
        }

        private void Ok()
        {
            _auditLogFacade.AddToAuditLog(_pageName, $"'{OKLabel}' button was activated.");
            _navigation.CloseModalWindow(withVerticalEffect: true);
        }
    }
}
