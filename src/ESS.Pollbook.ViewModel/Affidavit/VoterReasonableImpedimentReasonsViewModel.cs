using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Facade.DynamicControls;
using ESS.Pollbook.Facade.PollbookDefinedText;
using ESS.Pollbook.Core.Messaging;
using ESS.Pollbook.ViewModel.VoterDisabilityOath;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.Command;
using GalaSoft.MvvmLight.Messaging;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Dynamic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using System.Windows.Input;

namespace ESS.Pollbook.ViewModel.Affidavit
{
   public class VoterReasonableImpedimentReasonsViewModel : ViewModelBase
   {
      private const string _formName = "VoterReasonableImpedimentReasonsView";

      private readonly IFrameNavigationService _navigation;
      private readonly IEssLogger _essLogger;
      private readonly IMessenger _messenger;
      private readonly IPollbookDefinedTextFacade _pollbookDefinedTextFacade;
      private readonly IDynamicControlsFacade _dynamicControlsFacade;
      private readonly IAuditLogFacade _auditLogFacade;
      private string PageName => _navigation.PageName(this.GetType().FullName);

      private List<ReasonControlDto> _reasonsStateControls = new List<ReasonControlDto>();
      private readonly bool _hasDisabilityOathControls = VoterDisabilityOathControls.Controls.Any();

      public dynamic DynamicReasons { get; } = new ExpandoObject();

      public ICommand OkCommand => new RelayCommand(Ok);
      public ICommand RefusesCommand => new RelayCommand(VoterRefuses);

      public bool AllowRefuse => !_hasDisabilityOathControls;
      public bool ResetState { get; set; } = true;

      public bool IsOkButtonEnabled => _reasonsStateControls.Any(x => x.IsChecked);

      public string ReasonsHeader => _pollbookDefinedTextFacade.GetPollbookDefinedTextForLanguage("ReasonableImpedimentReasonsHeader", SelectedLanguage);
      public string OkCaption => _pollbookDefinedTextFacade.GetPollbookDefinedTextForLanguage("ReasonableImpedimentReasonsOkCaption", SelectedLanguage);
      public string RefusesCaption => _pollbookDefinedTextFacade.GetPollbookDefinedTextForLanguage("ReasonableImpedimentReasonsRefusesCaption", SelectedLanguage);

      public VoterReasonableImpedimentReasonsViewModel(IFrameNavigationService navigationService,
                                                          IMessenger messengerService,
                                                          IEssLogger essLogger,
                                                          IPollbookDefinedTextFacade pollbookDefinedTextFacade,
                                                          IDynamicControlsFacade dynamicControlsFacade,
                                                          IAuditLogFacade auditLogFacade)
      {
         _navigation = navigationService;
         _messenger = messengerService;
         _essLogger = essLogger;
         _pollbookDefinedTextFacade = pollbookDefinedTextFacade;
         _dynamicControlsFacade = dynamicControlsFacade;
         _auditLogFacade = auditLogFacade;

         _messenger.Register<LanguageSelectedMessage>(this, SetLanguage);
         _messenger.Register<ReasonableImpedimentReasonsMessage>(this, SetSelectedControls);

         try
         {
            AddProperty("Language", DefinedText.PollbookDefinedTextCurrentLanguage); //initial language property
            AddEvent("LanguageChanged", (sender, eventArgs) =>
            {
               dynamic exp = sender as ExpandoObject;
               var langArgs = eventArgs as LanguageChangedEventArgs;
               exp.Language = langArgs?.Language;

               foreach (var control in GetReasonsControls)
                  ((IDictionary<string, object>)sender)[control.Control_Name + "Content"] = _pollbookDefinedTextFacade.GetPollbookDefinedTextForLanguage(control.Control_Pollbook_Defined_Text_Name, exp.Language);

               _reasonsStateControls.ForEach(x => ((IDictionary<string, object>)sender)[x.Name + "IsChecked"] = x.IsChecked);

               RaisePropertyChanged(nameof(OkCaption));
               RaisePropertyChanged(nameof(RefusesCaption));
            });

            ((INotifyPropertyChanged)DynamicReasons).PropertyChanged += HandlerLanguagePropertyChanges;
         }
         catch (Exception ex)
         {
            _essLogger.LogError(ex, new Dictionary<string, string> { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
         }
      }

      private void Ok()
      {
         try
         {
            _auditLogFacade.AddToAuditLog(PageName, $"'{OkCaption}' button was activated.");
            _messenger.Send(new ReasonableImpedimentReasonsMessage(_reasonsStateControls, voterRefuses: false));
            _navigation.CloseModalWindow(withVerticalEffect: true);

         }
         catch (Exception ex)
         {
            _essLogger.LogError(ex, new Dictionary<string, string> { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
            _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
         }
      }

      private void VoterRefuses()
      {
         try
         {
            _auditLogFacade.AddToAuditLog(PageName, $"'{RefusesCaption}' button was activated.");
            ReasonsStateControls_Reset(); // A refusal is effectively a "false" to all checkbox reasons.
            _messenger.Send(new ReasonableImpedimentReasonsMessage(_reasonsStateControls, voterRefuses: true));
            _navigation.CloseModalWindow(withVerticalEffect: true);
         }
         catch (Exception ex)
         {
            _essLogger.LogError(ex,
                new Dictionary<string, string> { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
            _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
         }
      }

      public void PageIsLoaded()
      {
         ReasonsControls_Reset(); // First time we retrieve the reasons from the Election Database.  The caller does not have them.

         if (_navigation.Parameter is bool resetState)
         {
            ResetState = resetState;
            RaisePropertyChanged(nameof(ResetState));
         }

         if (ResetState)
         {
            ReasonsStateControls_Reset(); // Moved out of ReasonControl_Reset() for clarity and a separation of implementations.
         }
      }

      public bool GetCheckBoxControlStateValue(string controlName)
      {
         var control = _reasonsStateControls.FirstOrDefault(x => x.Name.Equals(controlName, StringComparison.OrdinalIgnoreCase));

         return control?.IsChecked ?? false;
      }

      private void SetLanguage(LanguageSelectedMessage input)
      {
         SelectedLanguage = input.Language;
      }

      /// <summary>
      /// The reasons received are used to set the DynamicObjects and then the reference to the
      /// input reasons is made.
      /// </summary>
      /// <param name="input"></param>
      public void SetSelectedControls(ReasonableImpedimentReasonsMessage input)
      {
         ReasonsStateControls_Reset();
         input.Reasons.ForEach(x => ((IDictionary<string, object>)DynamicReasons)[x.Name + "IsChecked"] = x.IsChecked);
         input.Reasons.ForEach(x => ((IDictionary<string, object>)DynamicReasons)[x.Name + "Revert"] = x.IsChecked);
         _reasonsStateControls = input.Reasons;
         DynamicReasons.Language = SelectedLanguage;

         RaisePropertyChanged(nameof(IsOkButtonEnabled));
      }

      public string SelectedLanguage
      {
         get
         {
            return _pollbookDefinedTextFacade.GetPollbookDefinedTextCurrentLanguage();
         }
         set
         {
            _pollbookDefinedTextFacade.SetPollbookDefinedTextCurrentLanguage(value);
            DynamicReasons.Language = value;
            RaisePropertyChanged();
            RaisePropertyChanged(nameof(ReasonsHeader));
         }
      }

      /// <summary>
      /// Loads the Reasons from the database and then resets the ReasonStateControl
      /// </summary>
      private void ReasonsControls_Reset()
      {
         GetReasonsControls = Task.Run(async () =>
                 await _dynamicControlsFacade.GetDynamicControlsWithAffidavitsDataByFormNameAsync(_formName))
                             .Result.Where(n => n.Visible == 1).OrderBy(n => n.Sort_Order);
      }

      public void ReasonsStateControls_Reset()
      {
         _reasonsStateControls = new List<ReasonControlDto>();
         foreach (var control in GetReasonsControls)
            _reasonsStateControls.Add(new ReasonControlDto(control.Control_ID, control.Control_Name, false, control.Control_JSON_Tag));
         RaisePropertyChanged(nameof(IsOkButtonEnabled));
      }

      public void ReasonStateControl_Revert()
      {
         foreach (ReasonControlDto dto in _reasonsStateControls)
         {
            // So we change from the revert value to the _reasonsStateControl
            dto.IsChecked = (bool)((IDictionary<string, object>)DynamicReasons)[dto.Name + "Revert"];
            // And then take the reasons state control to update IsChecked.
            ((IDictionary<string, object>)DynamicReasons)[dto.Name + "IsChecked"] = dto.IsChecked;
         }

         RaisePropertyChanged(nameof(IsOkButtonEnabled));
      }

      public IEnumerable<DynamicControlsDto> GetReasonsControls { get; private set; } = Enumerable.Empty<DynamicControlsDto>();

      public void DynamicCheckBox_Click(string controlName, bool isChecked)
      {
         var control = _reasonsStateControls.ToList().Find(x => x.Name.Equals(controlName));
         control.IsChecked = isChecked;

         RaisePropertyChanged(nameof(IsOkButtonEnabled));
      }

      public string GetDefinedText(string key, string language) => _pollbookDefinedTextFacade.GetPollbookDefinedTextForLanguage(key, language);

      public void AddProperty(string propertyName, object propertyValue)
      {
         ((IDictionary<string, object>)DynamicReasons)[propertyName] = propertyValue;
      }

      public void AddEvent(string eventName, Action<object, EventArgs> handler)
      {
         ((IDictionary<string, object>)DynamicReasons)[eventName] = handler;
      }

      public class LanguageChangedEventArgs : EventArgs
      {
         public string Language { get; set; }
      }

      private static void HandlerLanguagePropertyChanges(object sender, PropertyChangedEventArgs e)
      {
         dynamic exp = sender;
         if (e.PropertyName.Equals("Language"))
            exp.LanguageChanged(exp, new LanguageChangedEventArgs { Language = exp.Language });
      }
   }
}
