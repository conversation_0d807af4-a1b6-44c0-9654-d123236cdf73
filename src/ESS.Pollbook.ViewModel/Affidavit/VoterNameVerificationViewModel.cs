using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Facade.PollbookDefinedText;
using ESS.Pollbook.Core.Messaging;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.Command;
using GalaSoft.MvvmLight.Messaging;
using System;
using System.Collections.Generic;
using System.Reflection;
using System.Windows.Input;

namespace ESS.Pollbook.ViewModel.Affidavit
{
    public class VoterNameVerificationViewModel : ViewModelBase
    {
        #region Private Properties
        private readonly IAuditLogFacade _auditLogFacade;
        private readonly IEssLogger _essLogger;
        private readonly IMessenger _messenger;
        private readonly IFrameNavigationService _navigation;
        private readonly IPollbookDefinedTextFacade _pollbookDefinedTextFacade;
        #endregion

        #region Public Properties
        public ICommand OkCommand => new RelayCommand(Ok);

        private string _selectedLanguage;
        public string SelectedLanguage
        {
            get => string.IsNullOrEmpty(_selectedLanguage) ? DefinedText.DefaultLanguage : _selectedLanguage;
            set
            {
                Set(ref _selectedLanguage, value);
                RaisePropertyChanged(nameof(Title));
                RaisePropertyChanged(nameof(Message));
                RaisePropertyChanged(nameof(OkCaption));
            }
        }

        public string Title =>
            _pollbookDefinedTextFacade.GetPollbookDefinedTextForLanguage("SimilarNameTitle", SelectedLanguage);

        public string Message =>
            _pollbookDefinedTextFacade.GetPollbookDefinedTextForLanguage("SimilarNameMessage", SelectedLanguage);

        public string OkCaption => 
            _pollbookDefinedTextFacade.GetPollbookDefinedTextForLanguage("ReasonableImpedimentReasonsOkCaption", SelectedLanguage);

        public string VoterFullNameLabel => "Voter Name: ";
        public string VoterAddressLabel => "Address: ";
        public string VoterDateOfBirthLabel => "Date of Birth: ";

        private DynamicControlsDto _affidavit;
        private AffidavitDataDto _affidavitData;

        private VoterDto _voter;
        public VoterDto Voter
        {
            get => _voter;
            set => Set(ref _voter, value);
        }

        private string _voterFullName;
        public string VoterFullName
        {
            get => _voterFullName;
            set => Set(ref _voterFullName, value);
        }

        private string _voterAddress;
        public string VoterAddress
        {
            get => _voterAddress;
            set => Set(ref _voterAddress, value);
        }

        private string _voterDateOfBirth;
        public string VoterDateOfBirth
        {
            get => _voterDateOfBirth;
            set => Set(ref _voterDateOfBirth, value);
        }
        #endregion

        #region Constructor
        public VoterNameVerificationViewModel(IAuditLogFacade auditLogFacade,
            IEssLogger essLogger,
            IMessenger messengerService,
            IFrameNavigationService navigationService,
            IPollbookDefinedTextFacade pollbookDefinedTextFacade
            )
        {
            _auditLogFacade = auditLogFacade;
            _essLogger = essLogger;
            _messenger = messengerService;
            _navigation = navigationService;
            _pollbookDefinedTextFacade = pollbookDefinedTextFacade;

            _messenger.Register<AffidavitCaptureDataMessage>(this, CaptureDataHandler);
            _messenger.Register<VoterSelectedMessage>(this, SelectedVoterHandler);
            _messenger.Register<LanguageSelectedMessage>(this, SetLanguage);
        }
        #endregion

        #region Public Methods
        public void SelectedVoterHandler(VoterSelectedMessage msg)
        {
            SetVoterDetails(msg.Voter);
        }

        public void CaptureDataHandler(AffidavitCaptureDataMessage msg)
        {
            _affidavit = msg.Affidavit;
            _affidavitData = msg.Data;
        }

        public void SetLanguage(LanguageSelectedMessage input)
        {
            SelectedLanguage = input.Language;
        }
        #endregion

        #region Private Methods
        private void Ok()
        {
            try
            {
                _auditLogFacade.AddToAuditLog(_navigation.PageName(GetType().FullName), $"'{OkCaption}' button was activated.");

                _messenger.Send(new AffidavitDataMessage(info: CreateCapturedDataDto()));
                _navigation.CloseModalWindow(withVerticalEffect: true);

                if (_affidavit.Affidavit_Type_Enable_Confirmation && _affidavit.Affidavit_Type_Enable_Printing)
                {
                    _messenger.Send(new AffidavitPrintMessage(skipIt: SystemConfiguration.ElectionConfiguration.EnableTexasWorkflow));
                }
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string> { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name }, { "Message", ex.Message } };
                _essLogger.LogError(ex, logProps);
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error,
                    SystemDetails.GenericErrorMessage));
            }
        }

        private void SetVoterDetails(VoterDto voter)
        {
            Voter = voter;
            VoterFullName = voter?.FullName;
            VoterAddress = voter?.GetAddress();

            if (DateTime.TryParse(voter?.DateOfBirth, out DateTime dateOfBirth))
                VoterDateOfBirth = dateOfBirth.ToString("MM/dd/yyyy");
            else
                VoterDateOfBirth = string.Empty;
        }

        private AffidavitDataDto CreateCapturedDataDto()
        {
            var data = new AffidavitDataDto()
            {
                AffidavitTypeName = _affidavit.Affidavit_Type_Name
            };

            if (_affidavitData != null)
            {
                if (_affidavit.Affidavit_Type_Enable_Name_Capture)
                {
                    data.FirstName = _affidavitData.FirstName;
                    data.MiddleName = _affidavitData.MiddleName;
                    data.LastName = _affidavitData.LastName;
                    data.Suffix = _affidavitData.Suffix;
                }

                if (_affidavit.Affidavit_Type_Enable_Address_Capture)
                {
                    data.HouseNumber = _affidavitData.HouseNumber;
                    data.Street = _affidavitData.Street;
                    data.City = _affidavitData.City;
                    data.Zip = _affidavitData.Zip;
                }

                if (_affidavit.Affidavit_Type_Enable_Relationship_Capture)
                {
                    data.Relationship = _affidavitData.Relationship;
                }

                if (_affidavit.Affidavit_Type_Enable_Compensation_Capture)
                {
                    data.CompensationReceived = _affidavitData.CompensationReceived;
                }
            }

            return data;
        }
        #endregion
    }
}
