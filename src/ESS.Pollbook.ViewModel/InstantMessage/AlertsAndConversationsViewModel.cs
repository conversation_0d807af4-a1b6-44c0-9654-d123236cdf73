using ESS.Pollbook.Components.Repository.Messages;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.Model;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Core.Messaging;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.Command;
using GalaSoft.MvvmLight.Messaging;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;

namespace ESS.Pollbook.ViewModel.InstantMessage
{
    public class AlertsAndConversationsViewModel : ViewModelBase
    {
        private readonly IFrameNavigationService _navigation;
        private readonly IMessenger _messenger;
        private readonly IMessageRepository _messageRepository;
        private readonly IEssLogger _essLogger;
        private readonly IAuditLogFacade _auditLogFacade;

        private readonly string _pageName;

        private readonly ICommand _selectConversationCommand;
        private readonly ICommand _createTicketCommand;

        public ICommand SelectConversationCommand => _selectConversationCommand;

        public ICommand CreateTicketCommand => _createTicketCommand;

        public string ComposeLabel => "Compose";

        private ObservableCollection<ConversationModel> _conversations;
        public ObservableCollection<ConversationModel> Conversations
        {
            get
            {
                return _conversations;
            }
            set
            {
                _conversations = value;
                RaisePropertyChanged();
            }
        }

        public AlertsAndConversationsViewModel(IFrameNavigationService navigationService, IMessenger messengerService, IMessageRepository messageRepository, IEssLogger essLogger, IAuditLogFacade auditLogFacade)
        {
            _navigation = navigationService;
            _messenger = messengerService;
            _messageRepository = messageRepository;
            _essLogger = essLogger;
            _auditLogFacade = auditLogFacade;

            _pageName = _navigation.PageName(this.GetType().FullName);

            _selectConversationCommand = new RelayCommand<object>(ConversationSelected);
            _createTicketCommand = new RelayCommand(CreateTicket);

            _messenger.Register<LiveChatResponseMessage>(this, UpdateMessageList);
            _messenger.Register<LiveChatComposedMessage>(this, NewMessage);

            LoadConversations();
        }

        private void NewMessage(LiveChatComposedMessage message)
        {
            LoadConversations();
        }

        private void UpdateMessageList(LiveChatResponseMessage message)
        {
            LoadConversations();
        }

        private void CreateTicket()
        {
            try
            {
                _auditLogFacade.AddToAuditLog(_pageName, $"'{ComposeLabel}' button was activated.");
                _navigation.NavigateTo<ComposeViewModel>(NavigationFrameEnum.ContextFrame, withVerticalEffect: true);
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string>();
                logProps.Add("Action", "AlertsAndConversationsViewModel.CreateTicket");
                _essLogger.LogError(ex, logProps);
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }

        private string GetGroupName(int groupId, List<MessageGroupDto> groupList)
        {
            var groupDto = groupList.Where(g => g.MessageGroupId == groupId).FirstOrDefault();

            if (groupDto == null)
            {
                return SystemConfiguration.ElectionConfiguration.AlertMessageSender;
            }

            return groupDto.MessageGroupName;
        }

        private void ConversationSelected(object parameter)
        {
            try
            {
                _auditLogFacade.AddToAuditLog(_pageName, "A conversation was selected from the list.");
                var conversation = parameter as ConversationModel;
                if (conversation == null)
                {
                    _essLogger.LogError("in AlertsAndConversationsViewModel, unable to convert parameter to Conversation");
                    return;
                }

                if (conversation.Type == "Broadcast")
                {
                    _navigation.NavigateTo<AlertDetailViewModel>(parameter, NavigationFrameEnum.ContextFrame, withVerticalEffect: true);
                }
                else if (conversation.Type == "Ticket")
                {
                    _navigation.NavigateTo<ConversationViewModel>(parameter, NavigationFrameEnum.ContextFrame, withVerticalEffect: true);
                }
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string>();
                logProps.Add("Action", "AlertsAndConversationsViewModel.ConversationSelected");
                _essLogger.LogError(ex, logProps);
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }

        public void PageIsLoaded()
        {
            LoadConversations();
        }

        private void LoadConversations()
        {
            try
            {
                var groups = Task.Run(async () => await _messageRepository.GetAllMessageGroups()).Result;
                var groupList = groups.ToList();

                var conversations = Task.Run(async () => await _messageRepository.GetAlertsAndConversations()).Result;
                var conversationList = conversations.ToList();

                var conversationsOC = new ObservableCollection<ConversationModel>();

                foreach (var conversation in conversationList)
                {
                    conversationsOC.Add(new ConversationModel
                    {
                        ID = conversation.Id,
                        ConversationDatetime = conversation.StartDatetime,
                        Header = conversation.Header,
                        Type = (conversation.IsBroadcast ? "Broadcast" : "Ticket"),
                        Group = GetGroupName(conversation.GroupId, groupList),
                        HasUnreadMessage = conversation.HasUnreadMessage
                    });
                };
                Conversations = conversationsOC;
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string>();
                logProps.Add("Action", "AlertsAndConversationsViewModel.LoadConversations");
                _essLogger.LogError(ex, logProps);
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }
    }
}
