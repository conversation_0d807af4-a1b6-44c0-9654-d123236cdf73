using ESS.Pollbook.Components.Repository.Messages;
using ESS.Pollbook.Core.Commands;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Core.Messaging;
using ESS.Pollbook.ViewModel.Utils;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.Messaging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace ESS.Pollbook.ViewModel.InstantMessage
{
    public class ComposeViewModel : ViewModelBase
    {
        private readonly IFrameNavigationService _navigation;
        private readonly IMessenger _messenger;
        private readonly IMessageRepository _messageRepository;
        private readonly IEssLogger _essLogger;
        private readonly IAuditLogFacade _auditLogFacade;

        private string PageName => _navigation.PageName(GetType().FullName);

        private bool _messageBeingSent;

        public ICommandAsync SubmitCommand => new CommandAsync(Submit);

        public string SendLabel => UIText.Send;

        private List<string> _groups;
        public List<string> Groups
        {
            get => _groups;
            set => Set(ref _groups, value);
        }

        private string _group;
        public string Group
        {
            get => _group;
            set
            {
                if (Set(ref _group, value))
                    RaisePropertyChanged(nameof(SubmitIsEnabled));
            }
        }

        private string _messageText;
        public string MessageText
        {
            get => _messageText;
            set
            {
                if (Set(ref _messageText, value))
                    RaisePropertyChanged(nameof(SubmitIsEnabled));

            }
        }

        public bool ImConnected => InstantMessagingDetails.IsIMConnected;

        public bool ImDisconnected => !InstantMessagingDetails.IsIMConnected;

        public bool SubmitIsEnabled => !string.IsNullOrEmpty(Group) && !string.IsNullOrEmpty(MessageText) && !_messageBeingSent;

        public ComposeViewModel(IFrameNavigationService navigationService, IMessenger messengerService, IMessageRepository messageRepository, IEssLogger essLogger, IAuditLogFacade auditLogFacade)
        {
            _navigation = navigationService;
            _messenger = messengerService;
            _messageRepository = messageRepository;
            _essLogger = essLogger;
            _auditLogFacade = auditLogFacade;

            InstantMessagingDetails.IMConnectedChanged += InstantMessagingDetails_IMConnectedChanged;
        }

        private void InstantMessagingDetails_IMConnectedChanged(object sender, EventArgs e)
        {
            RaisePropertyChanged(nameof(ImConnected));
            RaisePropertyChanged(nameof(ImDisconnected));
        }

        public async Task PageInitialized()
        {
            Group = null;
            MessageText = null;
            _messageBeingSent = false;

            try
            {
                _groups = new List<string>();

                var groups = await _messageRepository.GetAllMessageGroups();
                var groupList = groups.ToList();

                foreach (var group in groupList)
                {
                    _groups.Add(group.MessageGroupName);
                }
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string> { { "Action", "ComposeViewModel constructor" } };
                _essLogger.LogError(ex, logProps);
            }
            RaisePropertyChanged(nameof(Groups));
            RaisePropertyChanged(nameof(Group));
            RaisePropertyChanged(nameof(MessageText));
        }

        private async Task Submit()
        {
            if (Group == null)
                return;

            try
            {
                await _auditLogFacade.AddToAuditLogAsync(PageName, $"'{SendLabel}' button was activated.");
                if (InstantMessagingDetails.IsIMConnected)
                {
                    _messageBeingSent = true;
                    RaisePropertyChanged(nameof(SubmitIsEnabled));

                    int serverConversationId = await MessagingUtil.CreateNewConversation(Group, MessageText);

                    if (serverConversationId == -1)
                    {
                        // An error occurred at the server end and we didn't get a ConversationId. It has already been logged. Show the disconnect message.
                        InstantMessagingDetails.IsIMConnected = false;
                        _messageBeingSent = false;
                        RaisePropertyChanged(nameof(SubmitIsEnabled)); // Allow the user to try again if it gets reconnected
                    }
                    else
                    {
                        await _messageRepository.CreateNewConversation(serverConversationId, Group, MessageText, SystemDetails.MachineName);

                        _messenger.Send(new LiveChatComposedMessage());

                        _navigation.GoBackTo<AlertsAndConversationsViewModel>(NavigationFrameEnum.ContextFrame);
                    }

                }
                else
                {
                    // At this time, we are checking the connection every 5 seconds, so it won't be long before we realize it is not connected and show the user.
                    // Since the message didn't get sent, don't clear the text box and don't navigate back. They can re-send it easily once the connection
                    // is restored.
                }
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string> { { "Action", "ComposeViewModel.Submit" } };
                _essLogger.LogError(ex, logProps);
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }
    }
}
