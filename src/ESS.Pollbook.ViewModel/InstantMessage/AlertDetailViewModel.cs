using ESS.Pollbook.Components.Repository.Messages;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.Model;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.AuditLog;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.Command;
using GalaSoft.MvvmLight.Messaging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Windows.Input;

namespace ESS.Pollbook.ViewModel.InstantMessage
{
    public class AlertDetailViewModel : ViewModelBase
    {
        private readonly IFrameNavigationService _navigation;
        private readonly IMessageRepository _messageRepository;
        private readonly IMessenger _messenger;
        private readonly IEssLogger _essLogger;
        private readonly IAuditLogFacade _auditLogFacade;

        private readonly string _pageName;

        private readonly ICommand _backCommand;

        public ICommand BackCommand => _backCommand;

        public string BackLabel => UIText.Back;

        private ConversationModel _selectedItem;

        private string _messageTitle;
        public string MessageTitle
        {
            get
            {
                return _messageTitle;
            }
            set
            {
                _messageTitle = value;
                RaisePropertyChanged();
            }
        }

        private string _alertMessage;
        public string AlertMessage
        {
            get
            {
                return _alertMessage;
            }
            set
            {
                _alertMessage = value;
                RaisePropertyChanged();
            }
        }

        private string _messageTime;
        public string MessageTime

        {
            get
            {
                return _messageTime;
            }
            set
            {
                _messageTime = value;
                RaisePropertyChanged();
            }
        }

        public AlertDetailViewModel(IFrameNavigationService navigationService, IMessageRepository messageRepository, IMessenger messenger, IEssLogger essLogger, IAuditLogFacade auditLogFacade)
        {
            _navigation = navigationService;
            _messageRepository = messageRepository;
            _messenger = messenger;
            _essLogger = essLogger;
            _auditLogFacade = auditLogFacade;

            _pageName = _navigation.PageName(this.GetType().FullName);

            _backCommand = new RelayCommand(Back);
        }

        public void PageIsLoaded()
        {
            try
            {
                _selectedItem = _navigation.Parameter as ConversationModel;

                if (_selectedItem == null)
                {
                    return;
                }

                var alert = Task.Run(async () => await _messageRepository.GetAlertMessage(_selectedItem.ID)).Result;

                MessageTitle = $"From {SystemConfiguration.ElectionConfiguration.AlertMessageSender} Broadcast";
                AlertMessage = alert.MessageText;
                MessageTime = $"Time Sent: {alert.SentDatetime.ToString("MM/dd/yyyy hh:mm tt")}";
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string>();
                logProps.Add("Action", "AlertDetailViewModel.PageIsLoaded");
                _essLogger.LogError(ex, logProps);
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }

        private void Back()
        {
            try
            {
                _auditLogFacade.AddToAuditLog(_pageName, $"'{BackLabel}' button was activated.");
                _navigation.GoBackTo<AlertsAndConversationsViewModel>(NavigationFrameEnum.ContextFrame);
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string>();
                logProps.Add("Action", "AlertDetailViewModel.Back");
                _essLogger.LogError(ex, logProps);
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }
    }
}
