using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Core.Messaging;
using ESS.Pollbook.ViewModel.Voter;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.Command;
using GalaSoft.MvvmLight.Messaging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Input;
using System.Reflection;

namespace ESS.Pollbook.ViewModel
{
    public class AddressChangedViewModel : ViewModelBase
    {
        private readonly IFrameNavigationService _navigation;
        private readonly IMessenger _messenger;
        private readonly IEssLogger _essLogger;
        private readonly IAuditLogFacade _auditLogFacade;

        private readonly string _pageName;

        private readonly ICommand _noCommand;
        private readonly ICommand _yesCommand;

        public ICommand NoCommand => _noCommand;
        public ICommand YesCommand => _yesCommand;

        public bool EnableVoterAddressChange => SystemConfiguration.ElectionConfiguration.AddressOnlyChangeType != Core.Common.AddressOnlyChangeType.None;
        
        private VoterDto _voter;
        public VoterDto Voter
        {
            get => _voter;
            set => Set(ref _voter, value);
        }

        private string _voterName;
        public string VoterName
        {
            get => _voterName;
            set => Set(ref _voterName, value);
        }

        private string _voterAddress;
        public string VoterAddress
        {
            get => _voterAddress;
            set => Set(ref _voterAddress, value);
        }

        private string _voterDOB;
        public string VoterDOB
        {
            get
            {
                var nameFormat = "Voter Date of Birth: {0}";
                if (_voter != null)
                    _voterDOB = string.Format(nameFormat, _voter.DateOfBirth);

                return _voterDOB;
            }
            set => Set(ref _voterDOB, value);
        }

        public string Title => DefinedText.PollbookDefinedTextDtos.FirstOrDefault(n => n.PollbookDefinedTextLanguage.Equals("English") && n.PollbookDefinedTextName.Equals("AddressUnchangedTitle"))?.PollbookDefinedTextValue;

        public string Message => DefinedText.PollbookDefinedTextDtos.FirstOrDefault(n => n.PollbookDefinedTextLanguage.Equals("English") && n.PollbookDefinedTextName.Equals("AddressUnchangedInstructions"))?.PollbookDefinedTextValue;

        public string AddressUnchangedLabel => DefinedText.PollbookDefinedTextDtos.FirstOrDefault(n => n.PollbookDefinedTextLanguage.Equals("English") && n.PollbookDefinedTextName.Equals("AddressUnchangedButton"))?.PollbookDefinedTextValue;

        public string AddressChangedLabel => DefinedText.PollbookDefinedTextDtos.FirstOrDefault(n => n.PollbookDefinedTextLanguage.Equals("English") && n.PollbookDefinedTextName.Equals("AddressChangedButton"))?.PollbookDefinedTextValue;

        #region Constructor
        public AddressChangedViewModel(IFrameNavigationService navigationService,
                                IMessenger messengerService,
                                IEssLogger essLogger,
                                IAuditLogFacade auditLogFacade)
        {
            _navigation = navigationService;
            _messenger = messengerService;
            _essLogger = essLogger;
            _auditLogFacade = auditLogFacade;

            _pageName = _navigation.PageName(this.GetType().FullName);

            _noCommand = new RelayCommand(No);
            _yesCommand = new RelayCommand(Yes);

            _messenger.Register<VoterSelectedMessage>(this, VoterSelected);
        }
        #endregion Constructor

        #region Public Methods
        public void No()
        {
            try
            {
                _auditLogFacade.AddToAuditLog(_pageName, $"'{AddressUnchangedLabel}' button was activated.");
                _messenger.Send(new AddressChangedMessage(hasAddressChanged: false));

                if (EnableVoterAddressChange)
                {
                    _navigation.NavigateTo<VoterViewModel>(NavigationFrameEnum.ContextFrame);
                }
                else
                {
                    _navigation.CloseModalWindow(withVerticalEffect: true);
                }                                            
            }
            catch (Exception ex)
            {
                _essLogger.LogError(ex, new Dictionary<string, string> { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }

        public void Yes()
        {
            try
            {
                _auditLogFacade.AddToAuditLog(_pageName, $"'{AddressChangedLabel}' button was activated.");
                _messenger.Send(new AddressChangedMessage(hasAddressChanged: true));

                if (EnableVoterAddressChange)
                {
                    _messenger.Send(new VoterEditMessage(Voter));
                    _navigation.NavigateTo<VoterEditViewModel>(true, NavigationFrameEnum.ContextFrame);
                }
                else
                {
                    _navigation.CloseModalWindow(withVerticalEffect: true);
                }                              
            }
            catch (Exception ex)
            {
                _essLogger.LogError(ex, new Dictionary<string, string> { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }
        #endregion Public Methods

        #region Private Methods
        private void VoterSelected(VoterSelectedMessage msg)
        {
            UpdateVoterDetails(msg.Voter);
        }

        private void UpdateVoterDetails(VoterDto voter)
        {
            Voter = voter;

            var nameFormat = "Voter Name: {0}";
            VoterName = string.Format(nameFormat, voter?.Surname);

            var addressFormat = "Voter Address: {0}";
            VoterAddress = string.Format(addressFormat, voter?.CompleteAddress);
        }
        #endregion Private Methods
    }
}
