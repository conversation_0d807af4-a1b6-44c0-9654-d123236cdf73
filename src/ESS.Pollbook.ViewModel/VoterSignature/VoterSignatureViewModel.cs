using ESS.Pollbook.Core.Commands;
using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Core.UICore;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Facade.Party;
using ESS.Pollbook.Facade.PollbookDefinedText;
using ESS.Pollbook.Facade.VRSignatures;
using ESS.Pollbook.ViewModel.IssueBallotHelperInterfaced;
using ESS.Pollbook.Core.Messaging;
using ESS.Pollbook.ViewModel.Utils;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.Command;
using GalaSoft.MvvmLight.Messaging;
using System;
using System.Collections.Generic;
using System.Reflection;
using System.Threading.Tasks;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;

namespace ESS.Pollbook.ViewModel.VoterSignature
{
    public sealed class VoterSignatureViewModel : ViewModelBase
    {
        private readonly IFrameNavigationService _navigation;
        private readonly IMessenger _messenger;
        private readonly ISignatureComparisonFacade _vrSignaturesFacade;
        private readonly IPollbookDefinedTextFacade _pollbookDefinedTextFacade;
        private readonly IPartyFacade _partyService;
        private readonly IEssLogger _essLogger;
        private readonly IAuditLogFacade _auditLogFacade;
        private readonly IIssueBallotHelper _issueBallotHelper;
        private readonly IPrintBallotUtil _printBallotUtil;

        private bool _isLoaded;
        private string PageName => _navigation.PageName(GetType().FullName);

        public ICommand BackCommand => new RelayCommand(Back);
        public ICommandAsync SkipCommand => new CommandAsync(SkipSignatureAsync);
        public ICommandAsync<object> DoneDeliveringCommand => new CommandAsync<object>(DoneDelivering);
        public ICommandAsync SignatureCapturedOnPaperCommand => new CommandAsync(SignatureCapturedOnPaperActivated);

        #region properties

        public VoterDto Voter => IssueBallotHelper.Voter;

        private bool _hasStrokes;
        public bool HasStrokes
        {
            get => _hasStrokes;
            set => Set(ref _hasStrokes, value);
        }

        private string _selectedLanguage;
        public string SelectedLanguage
        {
            get => _selectedLanguage;
            set
            {
                Set(ref _selectedLanguage, value);
                RaisePropertyChanged(nameof(SignatureHeader));
                RaisePropertyChanged(nameof(OathText));
                RaisePropertyChanged(nameof(DoneSigning));
                RaisePropertyChanged(nameof(ClearSignature));
                RaisePropertyChanged(nameof(SignatureCapturedOnPaper));
            }
        }

        private SignatureRotationType _voterSignatureRotationType => SystemConfiguration.ElectionConfiguration.VoterSignatureRotationType;
        public SignatureRotationType VoterSignatureRotationType => _voterSignatureRotationType;

        private bool _showFlipScreen => SystemConfiguration.ElectionConfiguration.EnableScreenRotation;
        public bool ShowFlipScreen => _showFlipScreen && (_voterSignatureRotationType == SignatureRotationType.None || _voterSignatureRotationType == SignatureRotationType.SigButton);

        public string SignatureHeader => _pollbookDefinedTextFacade.GetPollbookDefinedTextForLanguage("Signature_Header", SelectedLanguage);

        public string OathText => (LoggedInPollplaceInfo.IsEarlyVotingPollPlace) ? _pollbookDefinedTextFacade.GetPollbookDefinedTextForLanguage("EV_Signature_Oath", SelectedLanguage) : _pollbookDefinedTextFacade.GetPollbookDefinedTextForLanguage("Signature_Oath", SelectedLanguage);

        public string DoneSigning => _pollbookDefinedTextFacade.GetPollbookDefinedTextForLanguage("Done_Signing", SelectedLanguage);

        public string ClearSignature => _pollbookDefinedTextFacade.GetPollbookDefinedTextForLanguage("Clear_Signature", SelectedLanguage);

        public string SignatureCapturedOnPaper => _pollbookDefinedTextFacade.GetPollbookDefinedTextForLanguage("Signature_Captured_On_Paper", SelectedLanguage);

        public bool ShowSignatureCapturedOnPaper => SystemConfiguration.ElectionConfiguration.CapturePaperSignature;

        public bool ShowSkipSignature => SystemConfiguration.ElectionConfiguration.EnableSkipSignature;

        public string BackLabel => UIText.Back;

        public string UnableToSignLabel => UIText.VoterUnableToSignLabel;

        #endregion properties

        public VoterSignatureViewModel(IFrameNavigationService navigationService,
            IMessenger messengerService,
            IPartyFacade partyService,
            ISignatureComparisonFacade vrSignaturesFacade,
            IEssLogger essLogger,
            IPollbookDefinedTextFacade pollbookDefinedTextFacade,
            IAuditLogFacade auditLogFacade,
            IIssueBallotHelper issueBallotHelper,
            IPrintBallotUtil printBallotUtil)
        {
            _navigation = navigationService;
            _messenger = messengerService;
            _partyService = partyService;
            _vrSignaturesFacade = vrSignaturesFacade;
            _essLogger = essLogger;
            _pollbookDefinedTextFacade = pollbookDefinedTextFacade;
            _auditLogFacade = auditLogFacade;
            _issueBallotHelper = issueBallotHelper;
            _printBallotUtil = printBallotUtil;

            _messenger.Register<LanguageSelectedMessage>(this, LanguageSelectedMessageHandler);
        }

        public void PageInitialized()
        {
            SelectedLanguage = _pollbookDefinedTextFacade.GetPollbookDefinedTextCurrentLanguage();

            if (!_isLoaded)
            {
                //reset these values
                IssueBallotHelper.Voter.VoterTransactionSignature = null;
                IssueBallotHelper.Voter.SignatureSkipReasonEnumId = null;
            }

            HasStrokes = false;
            _isLoaded = true;

            Task.Delay(115).ContinueWith(t =>
            {
                if (_voterSignatureRotationType == SignatureRotationType.AutoFlip || _voterSignatureRotationType == SignatureRotationType.Both)
                {
                    ScreenDisplayFlip.FlipTo180();
                }
            }, TaskScheduler.FromCurrentSynchronizationContext());
        }

        private void Back()
        {
            try
            {
                _auditLogFacade.AddToAuditLog(PageName, $"'{BackLabel}' button was activated.");
                ScreenDisplayFlip.FlipToDefault();
                DefinedText.ResetPollbookDefinedTextCurrentLanguage();
                _messenger.Send(new StatusBarMessage(isVisible: true));

                _navigation.NavigateTo<DashboardViewModel>(NavigationFrameEnum.MainFrame, false);
                _navigation.GoBack();
            }
            catch (Exception ex)
            {
                _essLogger.LogError(ex, new Dictionary<string, string> { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name }, { "Message", ex.Message } });
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }

        public void ResetSignature()
        {
            HasStrokes = false;
        }

        private async Task SignatureCapturedOnPaperActivated()
        {
            try
            {
                await _auditLogFacade.AddToAuditLogAsync(PageName, $"'{SignatureCapturedOnPaper}' button was activated.");
                _messenger.Send(new StatusBarMessage(isVisible: true));
                IssueBallotHelper.Voter.VoterTransactionSignature = null;
                _navigation.NavigateTo<DashboardViewModel>(NavigationFrameEnum.MainFrame, false);

                if (SystemConfiguration.ElectionConfiguration.UnableSignType != ReasonsDisplayType.None)
                {
                    //capture a reason for not being unable to provide a signature
                    _navigation.NavigateTo<SelectSkipSignatureReasonViewModel>(NavigationFrameEnum.ContextFrame);
                }
                else
                {
                    //continue w/o a reason required
                    IssueBallotHelper.Voter.SignatureSkipReasonEnumId = null;
                    _navigation.NavigateTo<SignatureComparisonViewModel>(NavigationFrameEnum.ContextFrame);
                }  
            }
            catch (Exception ex)
            {
                _essLogger.LogError(ex, new Dictionary<string, string> { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name }, { "Message", ex.Message } });
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }

        private async Task DoneDelivering(object parameter)
        {
            try
            {
                IssueBallotHelper.Voter.SignatureSkipReasonEnumId = null;
                await _auditLogFacade.AddToAuditLogAsync(PageName, $"'{DoneSigning}' button was activated.");
                ScreenDisplayFlip.FlipToDefault();

                var inkCanvas = (InkCanvas)parameter;
                var rtb = new RenderTargetBitmap((int)inkCanvas.ActualWidth, (int)inkCanvas.ActualHeight, 96d, 96d, PixelFormats.Default);
                rtb.Render(inkCanvas);
                var encoder = new PngBitmapEncoder();
                encoder.Frames.Add(BitmapFrame.Create(rtb));
                IssueBallotHelper.Voter.VoterTransactionSignature = Conversion.ConvertToGray(encoder, Conversion.GrayPixelFormatOptions.BlackWhite, Conversion.DefaultPixelWidth);

                if (IssueBallotHelper.Voter.RecordInitialLoadIndicator)
                {
                    try
                    {
                        var vrSignResult = await _vrSignaturesFacade.GetVrSignatureByVoterIdAsync(IssueBallotHelper.Voter.VoterId);
                        if (vrSignResult != null)
                        {
                            IssueBallotHelper.Voter.VoterVRSignatureImage = vrSignResult;
                        }
                    }
                    catch (Exception ex)
                    {
                        _essLogger.LogError(ex, new Dictionary<string, string> { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name }, { "Message", ex.Message } });
                    }
                }

                _messenger.Send(new StatusBarMessage(isVisible: true));
                _navigation.NavigateTo<DashboardViewModel>(NavigationFrameEnum.MainFrame, false, suppressHorizontalEffect: true);
                _navigation.NavigateTo<SignatureComparisonViewModel>(NavigationFrameEnum.ContextFrame, suppressHorizontalEffect: true);
            }
            catch (Exception ex)
            {
                _essLogger.LogError(ex, new Dictionary<string, string> { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name }, { "Message", ex.Message } });
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }

        private async Task SkipSignatureAsync()
        {
            await _auditLogFacade.AddToAuditLogAsync(PageName, "Skip signature button was activated.");

            if (SystemConfiguration.ElectionConfiguration.UnableSignType != ReasonsDisplayType.None)
            {
                //capture a reason for not being unable to provide a signature
                IssueBallotHelper.Voter.VoterTransactionSignature = null;
                _navigation.NavigateTo<SelectSkipSignatureReasonViewModel>(NavigationFrameEnum.ContextFrame);
            }
            else
            {
                //continue w/o a reason required
                IssueBallotHelper.Voter.SignatureSkipReasonEnumId = null;
                IssueBallotHelper.Voter.VoterTransactionSignature = null;

                _messenger.Send(new StatusBarMessage(isVisible: true));
                _navigation.NavigateTo<DashboardViewModel>(NavigationFrameEnum.MainFrame, false);
                await _issueBallotHelper.NavigateToNextPage(_navigation, _partyService, _messenger, _printBallotUtil);
            }
            if (_voterSignatureRotationType == SignatureRotationType.AutoFlip || _voterSignatureRotationType == SignatureRotationType.Both)
            {
                ScreenDisplayFlip.FlipToDefault();
            }
        }

        private void LanguageSelectedMessageHandler(LanguageSelectedMessage msg)
        {
            SelectedLanguage = msg.Language;
        }
    }
}
