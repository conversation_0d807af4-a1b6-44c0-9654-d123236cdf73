using ESS.Pollbook.Facade.PagesTitle;
using GalaSoft.MvvmLight;

namespace ESS.Pollbook.ViewModel
{
    public class PrintingBallotViewModel : ViewModelBase
    {
        private readonly IPagesTitleFacade _pagesTitleFacade;
        public string LabelContent => _pagesTitleFacade.PopulateTitleContent(IssueBallotHelper.Voter.VoterBallotDto.IsProvisional, IssueBallotHelper.IsReissuingBallot, "English");
        public PrintingBallotViewModel(IPagesTitleFacade pagesTitleFacade)
        {
            _pagesTitleFacade = pagesTitleFacade;
        }
    }
}