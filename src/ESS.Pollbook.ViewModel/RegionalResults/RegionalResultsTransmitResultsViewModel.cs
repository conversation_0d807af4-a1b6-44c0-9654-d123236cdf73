using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Management;
using System.Reflection;
using System.Text.Json;
using System.Threading.Tasks;
using ESS.Pollbook.Core.Commands;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Facade.RegionalResults;
using ESS.Pollbook.Hardware.Storage;
using ESS.Pollbook.ViewModel.Reports;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.Messaging;
using Renci.SshNet;
using Renci.SshNet.Common;

namespace ESS.Pollbook.ViewModel.RegionalResults
{
	public class RegionalResultsTransmitResultsViewModel : ViewModelBase, IDisposable
	{
		private readonly IAuditLogFacade _auditLogFacade;
		private readonly IEssLogger _essLogger;
		private readonly IMessenger _messenger;
		private readonly IFrameNavigationService _navigation;
		private readonly IRegionalResultsTransmitResultsFacade _transmitResultsFacade;
		private readonly StorageLocator _storageLocator;
		private ManagementEventWatcher _insertWatcher;
		private ManagementEventWatcher _removeWatcher;
		private bool _disposed;

		public ICommandAsync BackCommand => new CommandAsync(Back);
		public ICommandAsync ExportCommand => new CommandAsync(Export);
		public ICommandAsync ResetCommand => new CommandAsync(Reset);
		private string PageName => _navigation.PageName(GetType().FullName);
		public string BackLabel => UIText.Back;
		public string ExportLogsLabel => UIText.ExportLogs;
		public string ResetAllLabel => UIText.ResetAll;
		public string PageLabel => "Regional Results";
		public string ElectionName => ElectionSettings.ElectionName;
		public string ElectionDate => SystemConfiguration.ElectionConfiguration.ElectionDate.ToString("MM-dd-yyyy");
		public string ExpectedMedia => "Expected Media";
		public string ReadMedia => "Read Media";
		public string SentMedia => "Sent Media";

		private int _pollPlaceId;

		public int PollPlaceId
		{
			get => _pollPlaceId;
			set => Set(ref _pollPlaceId, value);
		}

		private int _selectedTab;

		public int SelectedTab
		{
			get => _selectedTab;
			set
			{
				Set(ref _selectedTab, value);
				IsButtonVisible = _selectedTab != 0;
				switch (_selectedTab)
				{
					case 0:
						Task.Run(async () => await GetTransmitResultsActivity());
						break;
					case 1:
						Task.Run(async () => await GetMediaActivity());
						break;
					case 2:
						Task.Run(async () => await GetLogActivity());
						break;
				}
			}
		}

		private bool _isButtonVisible;

		public bool IsButtonVisible
		{
			get => _isButtonVisible;
			set => Set(ref _isButtonVisible, value);
		}

		private int? _expectedMedia;

		public int? ExpectedMediaCount
		{
			get => _expectedMedia;
			set => Set(ref _expectedMedia, value);
		}

		private int? _sentMedia;

		public int? SentMediaCount
		{
			get => _sentMedia;
			set => Set(ref _sentMedia, value);
		}

		private int? _readMedia;

		public int? ReadMediaCount
		{
			get => _readMedia;
			set => Set(ref _readMedia, value);
		}

		private RegionalResultsTransmitResultsDisplayStatusEnum _insertStatus;

		public RegionalResultsTransmitResultsDisplayStatusEnum InsertStatus
		{
			get => _insertStatus;
			set => Set(ref _insertStatus, value);
		}

		private RegionalResultsTransmitResultsDisplayStatusEnum _validateStatus;

		public RegionalResultsTransmitResultsDisplayStatusEnum ValidateStatus
		{
			get => _validateStatus;
			set => Set(ref _validateStatus, value);
		}

		private RegionalResultsTransmitResultsDisplayStatusEnum _sendStatus;

		public RegionalResultsTransmitResultsDisplayStatusEnum SendStatus
		{
			get => _sendStatus;
			set => Set(ref _sendStatus, value);
		}

		private RegionalResultsTransmitResultsDisplayStatusEnum _completeStatus;

		public RegionalResultsTransmitResultsDisplayStatusEnum CompleteStatus
		{
			get => _completeStatus;
			set => Set(ref _completeStatus, value);
		}

		private bool _loadingResults;

		public bool LoadingResults
		{
			get => _loadingResults;
			set => Set(ref _loadingResults, value);
		}

		private bool _noResults;

		public bool NoResults
		{
			get => _noResults;
			set => Set(ref _noResults, value);
		}

		private VirtualList<RegionalResultsTransmissionActivityDto> _activityList;

		public VirtualList<RegionalResultsTransmissionActivityDto> ActivityList
		{
			get => _activityList;
			set
			{
				if (Set(ref _activityList, value)) NoResults = _activityList.Count == 0;
			}
		}

		private VirtualList<RegionalResultsMediaActivityDto> _mediaActivityList;

		public VirtualList<RegionalResultsMediaActivityDto> MediaActivityList
		{
			get => _mediaActivityList;
			set => Set(ref _mediaActivityList, value);
		}

		private VirtualList<RegionalResultsLogViewerDto> _logViewerList;

		public VirtualList<RegionalResultsLogViewerDto> LogViewerList
		{
			get => _logViewerList;
			set => Set(ref _logViewerList, value);
		}

		private RegionalResultsFileValidationsDto _validationData;

		public RegionalResultsFileValidationsDto ValidationData
		{
			get => _validationData;
			set => Set(ref _validationData, value);
		}

		private bool _resetAll;

		public RegionalResultsTransmitResultsViewModel(IFrameNavigationService navigation,
			IMessenger messenger,
			IEssLogger essLogger,
			IAuditLogFacade auditLogFacade,
			IRegionalResultsTransmitResultsFacade transmitResultsFacade,
			StorageLocator storageLocator)
		{
			_navigation = navigation;
			_essLogger = essLogger;
			_messenger = messenger;
			_auditLogFacade = auditLogFacade;
			_transmitResultsFacade = transmitResultsFacade;
			_storageLocator = storageLocator;

			StartWatchers();
		}

		public async Task PageLoadedAsync()
		{
			try
			{
				ExpectedMediaCount = 0;
				SentMediaCount = 0;
				ReadMediaCount = 0;
				_resetAll = false;
				InsertStatus = RegionalResultsTransmitResultsDisplayStatusEnum.Current;
				ValidateStatus = RegionalResultsTransmitResultsDisplayStatusEnum.Initial;
				SendStatus = RegionalResultsTransmitResultsDisplayStatusEnum.Initial;
				CompleteStatus = RegionalResultsTransmitResultsDisplayStatusEnum.Initial;
				if (_navigation.Parameter != null) PollPlaceId = (int)_navigation.Parameter;
				await GetPollPlaceName(PollPlaceId);
				await GetCounts();
				SelectedTab = 0;
				ValidationData = new RegionalResultsFileValidationsDto();
				RaisePropertyChanged(nameof(NoResults));
				RaisePropertyChanged(nameof(ActivityList));
			}
			catch (Exception e)
			{
				_essLogger.LogError(e, new Dictionary<string, string>
					{ { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
			}
		}

		private async Task GetCounts()
		{
			try
			{
				var result = await _transmitResultsFacade.GetPollPlaceMediaCounts(PollPlaceId);
				ExpectedMediaCount = result.ExpectedMedia;
				ReadMediaCount = result.ReadMedia;
				SentMediaCount = result.SentMedia;
				RaisePropertyChanged(nameof(ExpectedMediaCount));
				RaisePropertyChanged(nameof(ReadMediaCount));
				RaisePropertyChanged(nameof(SentMediaCount));
			}
			catch (Exception e)
			{
				var logProps = new Dictionary<string, string>
					{ { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } };
				_essLogger.LogError(e, logProps);
			}
		}

		private void StartWatchers()
		{
			var insertQuery = new WqlEventQuery("SELECT * FROM Win32_VolumeChangeEvent WHERE EventType = 2");
			var removeQuery = new WqlEventQuery("SELECT * FROM Win32_VolumeChangeEvent WHERE EventType = 3");
			_insertWatcher = new ManagementEventWatcher(insertQuery);
			_insertWatcher.EventArrived += DeviceInsertedEvent;
			_insertWatcher.Start();

			_removeWatcher = new ManagementEventWatcher(removeQuery);
			_removeWatcher.EventArrived += DeviceRemovedEvent;
			_removeWatcher.Start();
		}

		private void DeviceInsertedEvent(object sender, EventArrivedEventArgs e)
		{
			//Handle device inserted event
			if (SelectedTab == 0)
			{
				Task.Run(async () => await ReadUsbDrives());
			}
		}

		private void DeviceRemovedEvent(object sender, EventArrivedEventArgs e)
		{
			//Handle device removed event
			if (SelectedTab == 0 && CompleteStatus != RegionalResultsTransmitResultsDisplayStatusEnum.Complete && !_resetAll)
			{
				Task.Run(async () => await UpdateMediaStatus(ValidationData.MediaCreationId, "Media Ejected", "Warning"));
			}

			switch (SelectedTab)
			{
				case 1:
					Task.Run(async () => await GetMediaActivity());
					break;
				case 2:
					Task.Run(async () => await GetLogActivity());
					break;
			}

			if (ValidateStatus == RegionalResultsTransmitResultsDisplayStatusEnum.Initial ||
			    ValidateStatus == RegionalResultsTransmitResultsDisplayStatusEnum.Error ||
			    ValidateStatus == RegionalResultsTransmitResultsDisplayStatusEnum.Current ||
			    ValidateStatus == RegionalResultsTransmitResultsDisplayStatusEnum.Complete)
				SetStatuses();

			if (SendStatus == RegionalResultsTransmitResultsDisplayStatusEnum.Initial ||
			    SendStatus == RegionalResultsTransmitResultsDisplayStatusEnum.Error ||
			    SendStatus == RegionalResultsTransmitResultsDisplayStatusEnum.Current ||
			    SendStatus == RegionalResultsTransmitResultsDisplayStatusEnum.Complete)
				SetStatuses();

			if (CompleteStatus == RegionalResultsTransmitResultsDisplayStatusEnum.Initial ||
			    CompleteStatus == RegionalResultsTransmitResultsDisplayStatusEnum.Current ||
			    CompleteStatus == RegionalResultsTransmitResultsDisplayStatusEnum.Complete)
				SetStatuses();
		}

		private void SetStatuses()
		{
			InsertStatus = RegionalResultsTransmitResultsDisplayStatusEnum.Current;
			ValidateStatus = RegionalResultsTransmitResultsDisplayStatusEnum.Initial;
			SendStatus = RegionalResultsTransmitResultsDisplayStatusEnum.Initial;
			CompleteStatus = RegionalResultsTransmitResultsDisplayStatusEnum.Initial;
		}

		private async Task ReadUsbDrives()
		{
			const string fileNameToRead = "Readme.txt";
			foreach (var drive in DriveInfo.GetDrives())
				if (drive.DriveType == DriveType.Removable && drive.IsReady)
				{
					if (drive.RootDirectory.GetFiles().Length == 0 && drive.RootDirectory.GetDirectories().Length == 0)
					{
						InsertStatus = RegionalResultsTransmitResultsDisplayStatusEnum.Complete;
						ValidateStatus = RegionalResultsTransmitResultsDisplayStatusEnum.Error;
						await UpdateLogEntity("Invalid Poll Media", "Error");
					}
					else
					{
						var textFiles = Directory.GetFiles(drive.RootDirectory.FullName, "*.txt");
						var filePath = Array.Find(textFiles,
							file => Path.GetFileName(file).Equals(fileNameToRead, StringComparison.OrdinalIgnoreCase));

						if (!string.IsNullOrEmpty(filePath))
						{
							var fileContent = File.ReadAllLines(filePath);
							var validationStatus = await Validate(fileContent);
							if (validationStatus)
							{
								await UploadResults(drive);
							}
						}
						else
						{
							InsertStatus = RegionalResultsTransmitResultsDisplayStatusEnum.Complete;
							ValidateStatus = RegionalResultsTransmitResultsDisplayStatusEnum.Error;
							await UpdateLogEntity("Invalid Poll Media", "Error");
						}
					}
				}
		}

		private async Task<bool> Validate(string[] lines)
		{
			try
			{
				InsertStatus = RegionalResultsTransmitResultsDisplayStatusEnum.Complete;
				ValidateStatus = RegionalResultsTransmitResultsDisplayStatusEnum.Current;

				foreach (var line in lines)
					if (!string.IsNullOrWhiteSpace(line))
					{
						if (line.StartsWith("ElectionId")) ValidationData.ElectionId = GetString(line);

						if (line.StartsWith("PollPlaceInternalID"))
						{
							int.TryParse(GetString(line), out var c);
							ValidationData.PollPlaceInternalId = c;
						}

						if (line.StartsWith("PollPlaceName")) ValidationData.PollPlaceName = GetString(line);

						if (line.StartsWith("MediaCreationID"))
						{
							int.TryParse(GetString(line), out var c);
							ValidationData.MediaCreationId = c;
						}

						if (line.StartsWith("OriginalMediaSerialNumber")) ValidationData.SerialNumber = GetString(line);
						if (line.StartsWith("EquipmentType")) ValidationData.EquipmentType = GetString(line);
					}

				await UpdateMediaTime(ValidationData.MediaCreationId, "TimeRead");
				await UpdateMediaStatus(ValidationData.MediaCreationId, "Reading Media", "Info");

				if (PollPlaceId == ValidationData.PollPlaceInternalId)
				{
					var result = await _transmitResultsFacade.ValidateMedia(ValidationData);
					if (!result)
					{
						ValidateStatus = RegionalResultsTransmitResultsDisplayStatusEnum.Error;
						await UpdateMediaStatus(ValidationData.MediaCreationId, "Invalid Poll Media", "Error");
					}
					else
					{
						ValidateStatus = RegionalResultsTransmitResultsDisplayStatusEnum.Complete;
						await UpdateMediaStatus(ValidationData.MediaCreationId, "Validation Complete", "Info");
						SendStatus = RegionalResultsTransmitResultsDisplayStatusEnum.Current;

						var isProcessed = await _transmitResultsFacade.IsResultsProcessed(ValidationData);
						if (isProcessed)
						{
							SendStatus = RegionalResultsTransmitResultsDisplayStatusEnum.Error;
							await UpdateMediaStatus(ValidationData.MediaCreationId, "Already Processed", "Error");
							return false;
						}
					}

					return result;
				}

				ValidateStatus = RegionalResultsTransmitResultsDisplayStatusEnum.Error;
				await UpdateMediaStatus(ValidationData.MediaCreationId, "Invalid Poll Media", "Error");
			}
			catch (Exception e)
			{
				ValidateStatus = RegionalResultsTransmitResultsDisplayStatusEnum.Error;
				var logProps = new Dictionary<string, string>
					{ { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } };
				_essLogger.LogError(e, logProps);
			}

			return false;
		}

		private string GetString(string line)
		{
			try
			{
				var colonIndex = line.IndexOf(':');
				if (colonIndex != -1 && colonIndex + 1 < line.Length)
				{
					var value = line.Substring(colonIndex + 1).Trim();
					if (!string.IsNullOrWhiteSpace(value)) return value;
				}
			}
			catch (Exception e)
			{
				var logProps = new Dictionary<string, string>
					{ { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } };
				_essLogger.LogError(e, logProps);
			}

			return string.Empty;
		}

		private RegionalResultsCredentialsDto GetSftpInfo()
		{
			var result = new RegionalResultsCredentialsDto();
			try
			{
				var directory = _storageLocator.GetRegionalResultsFolder();
				var credentialsFile = Path.Combine(directory, "credentials.json");

				if (File.Exists(credentialsFile))
				{
					var jsonContent = File.ReadAllText(credentialsFile);

					result = JsonSerializer.Deserialize<RegionalResultsCredentialsDto>(jsonContent);
				}
				return result;
			}
			catch (Exception e)
			{
				var logProps = new Dictionary<string, string>
					{ { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } };
				_essLogger.LogError(e, logProps);
			}

			return result;
		}

		private async Task UploadResults(DriveInfo drive)
		{
			try
			{
				var paths = GetDirectoryPaths(drive);

				if (paths.Any())
				{
					await UpdateMediaStatus(ValidationData.MediaCreationId, $"This media contains {paths.Count} - {ValidationData.EquipmentType} Equipments", "Info");

					foreach (var path in paths)
					{
						var equipmentName = string.Empty;
						if (string.Equals(ValidationData.EquipmentType, "ExpressVoteXL"))
						{
							equipmentName = Path.GetFileName(path);
						}

						var files = GetFiles(path);

						if (!files.Any())
						{
							var errorMessage = string.Equals(ValidationData.EquipmentType, "ExpressVoteXL")
								? $"Polls are not closed on {Path.GetFileName(path)} Equipment"
								: "Polls are not closed";

							await UpdateMediaStatus(ValidationData.MediaCreationId, errorMessage, "Error");

							SendStatus = RegionalResultsTransmitResultsDisplayStatusEnum.Error;

							return;
						}

						var credentials = GetSftpInfo();

						await UpdateMediaStatus(ValidationData.MediaCreationId, "Logging into the SFTP server",
							"Info");

						using (var sftp = new SftpClient(credentials.SftpUri, credentials.UserName,
							       credentials.Password))
						{
							sftp.Connect();

							var statusMessage = !string.IsNullOrEmpty(equipmentName)
								? $"Sending {equipmentName} results to SFTP"
								: "Sending results to SFTP";

							await UpdateMediaStatus(ValidationData.MediaCreationId, statusMessage, "Info");

							foreach (var file in files)
							{
								using (var fs = new FileStream(file, FileMode.Open, FileAccess.Read, FileShare.Read,
									       65536,
									       true))
								{
									var remoteFilename = "/" + Path.GetFileName(file);
									await Task.Run(() => sftp.UploadFile(fs, remoteFilename));
								}
							}

							await UpdateMediaStatus(ValidationData.MediaCreationId, "Sent to SFTP", "Info");
							await UpdateMediaTime(ValidationData.MediaCreationId, "TimeSent");
							sftp.Disconnect();
						}

						SendStatus = RegionalResultsTransmitResultsDisplayStatusEnum.Complete;
						CompleteStatus = RegionalResultsTransmitResultsDisplayStatusEnum.Complete;
						await GetCounts();
					}
				}
			}
			catch (SshAuthenticationException e)
			{
				SendStatus = RegionalResultsTransmitResultsDisplayStatusEnum.Error;
				await UpdateMediaStatus(ValidationData.MediaCreationId, "Unable to Authenticate to Sftp", "Error");
				var logProps = new Dictionary<string, string>
					{ { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } };
				_essLogger.LogError(e, logProps);
			}
			catch (SshConnectionException e)
			{
				SendStatus = RegionalResultsTransmitResultsDisplayStatusEnum.Error;
				await UpdateMediaStatus(ValidationData.MediaCreationId, "Unable to Connect to Sftp", "Error");
				var logProps = new Dictionary<string, string>
					{ { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } };
				_essLogger.LogError(e, logProps);
			}
			catch (SftpPermissionDeniedException e)
			{
				SendStatus = RegionalResultsTransmitResultsDisplayStatusEnum.Error;
				await UpdateMediaStatus(ValidationData.MediaCreationId, "Already Processed", "Error");
				var logProps = new Dictionary<string, string>
					{ { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } };
				_essLogger.LogError(e, logProps);
			}
			catch (SshException e)
			{
				SendStatus = RegionalResultsTransmitResultsDisplayStatusEnum.Error;
				await UpdateMediaStatus(ValidationData.MediaCreationId, "Exception Occured", "Error");
				var logProps = new Dictionary<string, string>
					{ { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } };
				_essLogger.LogError(e, logProps);
			}
			catch (Exception e)
			{
				SendStatus = RegionalResultsTransmitResultsDisplayStatusEnum.Error;
				await UpdateMediaStatus(ValidationData.MediaCreationId, "Exception Occured", "Error");
				var logProps = new Dictionary<string, string>
					{ { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } };
				_essLogger.LogError(e, logProps);
			}
		}

		private List<string> GetDirectoryPaths(DriveInfo drive)
		{
			var paths = new List<string>();

			try
			{
				if (string.Equals(ValidationData.EquipmentType, "ExpressVoteXL"))
				{
					var directories = Directory
						.EnumerateDirectories(drive.RootDirectory.FullName, "XL*", SearchOption.TopDirectoryOnly)
						.ToList();

					if (directories.Any()) paths.AddRange(directories);
				}
				else
				{
					paths.Add(drive.RootDirectory.FullName);
				}
			}
			catch (Exception e)
			{
				var logProps = new Dictionary<string, string>
					{ { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } };
				_essLogger.LogError(e, logProps);
			}

			return paths;
		}

		private string[] GetFiles(string path)
		{
			try
			{
				var filesPath = Path.Combine(path, "results");
				const string filenameEndsWith = "*.zip.eeff";

				var files = Directory.GetFiles(filesPath, filenameEndsWith, SearchOption.TopDirectoryOnly);

				return files;
			}
			catch (Exception e)
			{
				var logProps = new Dictionary<string, string>
					{ { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } };
				_essLogger.LogError(e, logProps);
			}

			return new string[] { };
		}

		private async Task Back()
		{
			try
			{
				await _auditLogFacade.AddToAuditLogAsync(PageName, $"'{BackLabel}' button was activated.");
				PollPlaceId = 0;
				_navigation.NavigateTo<RegionalResultsPollPlaceSearchViewModel>(NavigationFrameEnum.ContextFrame, false);
			}
			catch (Exception e)
			{
				var logProps = new Dictionary<string, string>
					{ { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } };
				_essLogger.LogError(e, logProps);
				_messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error,
					SystemDetails.GenericErrorMessage));
			}
		}

		private async Task Export()
		{
			try
			{
				await _auditLogFacade.AddToAuditLogAsync(PageName, $"'{ExportLogsLabel}' button was activated.");
				var isMounted = _storageLocator.IsUsbMounted();
				if (isMounted)
				{
					await UpdateLogEntity("Exporting the AuditLog", "Info");
					await _transmitResultsFacade.GenerateFile();
					await UpdateLogEntity("Exported the AuditLog Successfully", "Info");
				}
				else
				{
					await UpdateLogEntity("Exporting AuditLog Failed - Check USB media", "Error");
				}
			}
			catch (Exception e)
			{
				var logProps = new Dictionary<string, string>
					{ { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } };
				_essLogger.LogError(e, logProps);
			}
		}

		private async Task Reset()
		{
			try
			{
				await _auditLogFacade.AddToAuditLogAsync(PageName, $"'{ResetAllLabel}' button was activated.");
				await _transmitResultsFacade.ResetAll();
				await GetCounts();
				_resetAll = true;
				SetStatuses();

				switch (SelectedTab)
				{
					case 1:
						await GetMediaActivity();
						break;
					case 2:
						await GetLogActivity();
						break;
				}
			}
			catch (Exception e)
			{
				var logProps = new Dictionary<string, string>
					{ { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } };
				_essLogger.LogError(e, logProps);
			}
		}

		private async Task GetTransmitResultsActivity()
		{
			try
			{
				LoadingResults = true;
				var result = (await _transmitResultsFacade.GetTransmitResultsActivity(PollPlaceId)).ToList();
				ActivityList = new VirtualList<RegionalResultsTransmissionActivityDto>(result);
				LoadingResults = false;
			}
			catch (Exception e)
			{
				var logProps = new Dictionary<string, string>
					{ { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } };
				_essLogger.LogError(e, logProps);
			}
		}

		private async Task GetMediaActivity()
		{
			try
			{
				LoadingResults = true;
				var result = (await _transmitResultsFacade.GetMediaActivity(PollPlaceId)).ToList();
				MediaActivityList = new VirtualList<RegionalResultsMediaActivityDto>(result);
				LoadingResults = false;
			}
			catch (Exception e)
			{
				var logProps = new Dictionary<string, string>
					{ { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } };
				_essLogger.LogError(e, logProps);
			}
		}

		private async Task GetLogActivity()
		{
			try
			{
				LoadingResults = true;
				var result = (await _transmitResultsFacade.GetLogActivity()).ToList();
				LogViewerList = new VirtualList<RegionalResultsLogViewerDto>(result);
				LoadingResults = false;
			}
			catch (Exception e)
			{
				var logProps = new Dictionary<string, string>
					{ { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } };
				_essLogger.LogError(e, logProps);
			}
		}

		private async Task UpdateMediaStatus(int id, string status, string severity)
		{
			try
			{
				if (id > 0)
				{
					var result = await _transmitResultsFacade.UpdateMediaStatus(id, status, severity);
					if (result)
					{
						LoadingResults = true;
						switch (SelectedTab)
						{
							case 0:
								var data = (await _transmitResultsFacade.GetTransmitResultsActivity(PollPlaceId)).ToList();
								ActivityList = new VirtualList<RegionalResultsTransmissionActivityDto>(data);
								break;
							case 1:
								await GetMediaActivity();
								break;
							case 2:
								await GetLogActivity();
								break;
						}
						LoadingResults = false;
					}
				}
			}
			catch (Exception e)
			{
				var logProps = new Dictionary<string, string>
					{ { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } };
				_essLogger.LogError(e, logProps);
			}
		}

		private async Task UpdateLogEntity(string status, string severity)
		{
			try
			{
				await _transmitResultsFacade.UpdateLogEntity(status, severity);
				await GetLogActivity();
			}
			catch (Exception e)
			{
				var logProps = new Dictionary<string, string>
					{ { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } };
				_essLogger.LogError(e, logProps);
			}
		}

		private async Task UpdateMediaTime(int id, string colName)
		{
			try
			{
				await _transmitResultsFacade.UpdateMediaTime(id, colName);
			}
			catch (Exception e)
			{
				var logProps = new Dictionary<string, string>
					{ { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } };
				_essLogger.LogError(e, logProps);
			}
		}

		private async Task GetPollPlaceName(int id)
		{
			try
			{
				if (id > 0)
				{
					var pollPlaceName = await _transmitResultsFacade.GetPollPlaceName(id);

					await UpdateLogEntity($"{pollPlaceName} - Polling location selected", "Info");
				}
			}
			catch (Exception e)
			{
				var logProps = new Dictionary<string, string>
					{ { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } };
				_essLogger.LogError(e, logProps);
			}
		}

		public void Dispose()
		{
			Dispose(true);
			GC.SuppressFinalize(this);
		}

		protected virtual void Dispose(bool disposing)
		{
			if (_disposed)
				return;

			if (disposing)
			{
				// To prevent memory leaks and ensure watchers are properly disposed
				if (_insertWatcher != null)
				{
					_insertWatcher.EventArrived -= DeviceInsertedEvent;
					_insertWatcher.Dispose();
				}

				if (_removeWatcher != null)
				{
					_removeWatcher.EventArrived -= DeviceRemovedEvent;
					_removeWatcher.Dispose();
				}
			}

			_disposed = true;
		}
	}
}