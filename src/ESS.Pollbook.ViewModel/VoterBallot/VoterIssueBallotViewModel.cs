using ESS.Pollbook.Core.Commands;
using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.Messaging;
using ESS.Pollbook.Core.NavigationParameters;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Facade.Device;
using ESS.Pollbook.Facade.ElectionJurisdictionEnum;
using ESS.Pollbook.Facade.PagesTitle;
using ESS.Pollbook.Facade.VoterTransaction;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.Messaging;
using NClone;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using static ESS.Pollbook.ViewModel.IssueBallotHelper;

namespace ESS.Pollbook.ViewModel.VoterBallot
{
   public class VoterIssueBallotViewModel : ViewModelBase
   {
      private readonly IFrameNavigationService _navigation;
      private readonly IMessenger _messenger;
      private readonly IPagesTitleFacade _pagesTitleFacade;
      private readonly IPrinterFacade _printerFacade;
      private readonly IVoterJurisdictionEnumFacade _voterJurisdictionEnumFacade;
      private readonly IEssLogger _essLogger;
      private readonly IAuditLogFacade _auditLogFacade;
      private readonly IVoterTransactionFacade _voterTransactionFacade;

      private string PageName => _navigation.PageName(GetType().FullName);

      public ICommandAsync CompleteCheckInCommand => new CommandAsync(CompleteCheckIn);

      public string CompleteCheckInLabel => UIText.CompleteCheckIn;

      private bool _suppressBodPrinting;

      public string LabelContent => _pagesTitleFacade.PopulateTitleContent(IssueBallotHelper.Voter.VoterBallotDto.IsProvisional,
          IssueBallotHelper.IsReissuingBallot, "English");

      public bool IssueExpressVoteCard => SelectedBallotType == BallotType.ExpressVote;

      public string InstructionText => GetInstructionText();

      private static string GetInstructionText()
      {
         switch (SelectedBallotType)
         {
            case BallotType.ExpressVote:
               return UIText.VoterIssueBallotInstructionExpressVote;
            case BallotType.DAC:
               return UIText.VoterIssueBallotInstructionDAC;
            case BallotType.Paper:
            case BallotType.Unknown:
            default:
               return IssueBallotHelper.Voter.VoterBallotDto.IsProvisional ?
                  UIText.VoterIssueBallotInstructionProvisional :
                  UIText.VoterIssueBallotInstructionStandard;
         }
      }

      public string HeaderText
      {
         get
         {
            switch (SelectedBallotType)
            {
               case BallotType.ExpressVote:
                  return UIText.IssueBallotExpressVoteHeader;
               case BallotType.DAC:
                  return UIText.IssueBallotActivationHeader;
               default:
                  return UIText.IssueBallotDefaultHeader;
            }
         }
      }

      public VoterDto Voter => IssueBallotHelper.Voter;

      public VoterIssueBallotViewModel(IFrameNavigationService navigationService,
          IMessenger messengerService,
          IPagesTitleFacade pagesTitleFacade,
          IPrinterFacade printerFacade,
          IVoterJurisdictionEnumFacade voterJurisdictionEnumFacade,
          IEssLogger essLogger,
          IAuditLogFacade auditLogFacade,
          IVoterTransactionFacade voterTransactionFacade)
      {
         _navigation = navigationService;
         _messenger = messengerService;
         _pagesTitleFacade = pagesTitleFacade;
         _printerFacade = printerFacade;
         _voterJurisdictionEnumFacade = voterJurisdictionEnumFacade;
         _essLogger = essLogger;
         _auditLogFacade = auditLogFacade;
         _voterTransactionFacade = voterTransactionFacade;

         _messenger.Register<IssueBallotMessage>(this, async (msg) => await IssueBallots(msg));
         _messenger.Register<VoterVerificationMessage>(this, VoterVerifications);
         _messenger.Register<VerifyVoterIdMessage>(this, VerifyVoterIdHandler);
         _messenger.Register<ReasonableImpedimentAffidavitMessage>(this, ReasonableImpedimentAffidavitHandler);
         _messenger.Register<AffidavitTemplateMessage>(this, AffidavitTemplateHandler);
         _messenger.Register<SurrenderMailInBallotCompleteMessage>(this, SurrenderMailInBallotCompleteHandler);
      }

      public void PageInitialized()
      {
         if (_navigation.Parameter is SuppressBODPrintingMessage)
         {
            _suppressBodPrinting = true;
         }
      }

      private async Task IssueBallots(IssueBallotMessage msg)
      {
         var sourcePage = msg.SenderPageName;

         _essLogger.LogDebug($"IssueBallots called with ballot type {SelectedBallotType} from {sourcePage}",
            new Dictionary<string, string>
            {
               { "Action", "IssueBallots" },
               { "Caller", sourcePage }
            });

         switch (SelectedBallotType)
         {
            case BallotType.Paper:
            case BallotType.ExpressVote:
            case BallotType.DAC:
               _essLogger.LogDebug($"Processing paper / ExpressVote / DAC ballot type, caller: {sourcePage}");
               await IssueBallot((int)SelectedBallotType);
               break;

            default:
               var logProps = new Dictionary<string, string>
               {
                  { "Action", "Trying to issue a ballot" }
               };
               _essLogger.LogError($"In VoterIssueBallotViewModel, ballot type SelectedBallotType is not setup for use, caller: {sourcePage}. ", logProps);
               break;
         }

         _essLogger.LogDebug($"IssueBallots completed. caller: {sourcePage}");
      }

      private async Task IssueBallot(int mediaType)
      {

         try
         {
            var currentVoter = Clone.ObjectGraph(Voter);
            _essLogger.LogDebug($"IssueBallot called with media type {mediaType}", new Dictionary<string, string>
               { { "Action", "IssueBallot" } });

            _messenger.Send(new ResetSearchMessage(scanBarcode: false));

            _essLogger.LogDebug("ResetSearchMessage sent.");
            var mediaTypeEnumId = _voterJurisdictionEnumFacade.GetMediaTypeEnumId((int)EssEnumeration.MediaType, mediaType);
            currentVoter.MediaTypeEnumId = mediaTypeEnumId;

            var earlyBallotEnumId = await _voterJurisdictionEnumFacade.GetEarlyBallotIssuedIdAsync(
               (int)EssEnumeration.AbsenteeStatus,
               (int)AbsenteeStatus.EarlyVoteIssued);

            _essLogger.LogDebug($"Voter conditions: IsProvisional: {currentVoter.VoterBallotDto.IsProvisional}, IsEarlyVotingPollPlace: {LoggedInPollplaceInfo.IsEarlyVotingPollPlace}, AbsenteeStatusEnumId: {currentVoter.AbsenteeStatusEnumId}, earlyBallotEnumId: {earlyBallotEnumId}");

            if (!currentVoter.VoterBallotDto.IsProvisional &&
                LoggedInPollplaceInfo.IsEarlyVotingPollPlace &&
                currentVoter.AbsenteeStatusEnumId != earlyBallotEnumId)
            {
               var previousVoterDto = Clone.ObjectGraph(Voter);
               currentVoter.AbsenteeStatusEnumId = earlyBallotEnumId;
               await _voterTransactionFacade.IssueEarlyVoteBallot(currentVoter, previousVoterDto, LoggedInPollplaceInfo.LoggedInPollPlace);
               _essLogger.LogDebug("Early vote ballot issued.");
               _messenger.Send(new VoterRegisteredMessage());
            }
            else
            {
               await _voterTransactionFacade.IssueBallot(currentVoter, LoggedInPollplaceInfo.LoggedInPollPlace);
               _essLogger.LogDebug("Ballot issued.");
               _messenger.Send(new UpdateBallotCountsMessage());
            }


            _essLogger.LogInformation($"Issued ballot to {currentVoter.VoterKey}");

            _messenger.Send(new StatusBarMessage(true));
            _essLogger.LogDebug("StatusBarMessage sent.");

            _essLogger.LogDebug("IssueBallot completed.");
         }
         catch (Exception ex)
         {
            _essLogger.LogError(ex,
               new Dictionary<string, string>
                  { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
         }
      }

      private async Task CompleteCheckIn()
      {
         try
         {
            _essLogger.LogDebug($"CompleteCheckIn started with ballot type {SelectedBallotType}.", new Dictionary<string, string>{
            {
               "Action", "CompleteCheckIn.Start"
            }});

                await _auditLogFacade.AddToAuditLogAsync(PageName, $"'{CompleteCheckInLabel}' button was activated.");

                _essLogger.LogDebug($"Setting the ballot type to {SelectedBallotType}");
            IssueBallotHelper.Voter.VoterBallotDto.BallotType = SelectedBallotType.ToString();
            _essLogger.LogDebug($"Resetting the selected ballot type to unknown.");
            SelectedBallotType = BallotType.Unknown;
            _suppressBodPrinting = false;

            _navigation.RemoveHistory();

            _essLogger.LogDebug($"Checking if printing is enabled: {SystemConfiguration.ElectionConfiguration.PrintingEnabled}");
            if (SystemConfiguration.ElectionConfiguration.PrintingEnabled)
            {
               _essLogger.LogDebug("Printing is enabled, navigating to PrintingReceiptViewModel.");
               _navigation.NavigateTo<PrintingReceiptViewModel>(null, NavigationFrameEnum.ContextFrame, suppressHorizontalEffect: true);
               var printDateTime = DateTime.Now;
               var printResponse = await _printerFacade.PrintVoterAuthorityDocument(IssueBallotHelper.Voter, printDateTime);
               _essLogger.LogDebug("Print Response received, checking if print failed.");
               if (_printerFacade.DidPrintingFail(printResponse))
               {
                  _essLogger.LogDebug("Printing failed with response: {printResponse?.PrinterErrorMessage}");
                  var navParameter = new VoterAuthorityDocParameter()
                  {
                     Voter = IssueBallotHelper.Voter,
                     IsInBallotIssueWorkFlow = true,
                     PrintDateTime = printDateTime
                  };

                  _navigation.NavigateTo<PrintAuthDocFailedViewModel>(navParameter, NavigationFrameEnum.ContextFrame, suppressHorizontalEffect: true);
                  _essLogger.LogDebug("Navigation to PrintAuthDocFailedViewModel complete.");
                  return;
               }
               _essLogger.LogDebug("Printing succeeded.");
            }

            _essLogger.LogDebug("Navigating to DashboardViewModel.");
            _navigation.NavigateTo<DashboardViewModel>();
            _essLogger.LogDebug("CompleteCheckIn completed.");
         }
         catch (Exception ex)
         {
            var logProps = new Dictionary<string, string>
            {
               { "Action", "VoterIssueBallotViewModel.CompleteCheckIn" },
               {"BallotType", SelectedBallotType.ToString()},
               {"voterKey", IssueBallotHelper.Voter.VoterKey ?? "Unknown"}
            };
            _essLogger.LogError(ex, logProps);
            _essLogger.LogDebug($"Exception details: {ex.Message}, stackTrace: {ex.StackTrace}");
            _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            _essLogger.LogDebug("Error notification sent to user.");
         }
      }

      private void VoterVerifications(VoterVerificationMessage msg)
      {
         if (msg.VoterVerifications == null)
            return;

         var voterVerifications = msg.VoterVerifications.ToList();

         if (IssueBallotHelper.Voter.Dynamic == null) IssueBallotHelper.Voter.Dynamic = new DynamicDto();

         // checkbox data
         foreach (var vv in voterVerifications)
         {
            IssueBallotHelper.Voter.Dynamic.DynamicData[vv.Control_ID.ToString()] = vv.IsChecked.ToString();
         }

         // ballot number info
         foreach (var entry in msg.VoterVerificationsTextualData)
         {
            IssueBallotHelper.Voter.Dynamic.DynamicData[entry.Key.ToString()] = entry.Value;
         }

      }

      private static void VerifyVoterIdHandler(VerifyVoterIdMessage msg)
      {
         if (IssueBallotHelper.Voter.TexasVerifyVoterId == null)
            IssueBallotHelper.Voter.TexasVerifyVoterId = new VerifyVoterIdDto();

         if (IssueBallotHelper.Voter.Dynamic == null)
            IssueBallotHelper.Voter.Dynamic = new DynamicDto();

         if (msg.NIR_VR_Verified.HasValue)
            IssueBallotHelper.Voter.TexasVerifyVoterId.NIR_VR_Verified = msg.NIR_VR_Verified.Value;

         if (msg.ExemptSelection.HasValue)
            IssueBallotHelper.Voter.TexasVerifyVoterId.Exempt = msg.ExemptSelection.Value;

         if (msg.ListASelection.HasValue)
         {
            IssueBallotHelper.Voter.TexasVerifyVoterId.ListA = msg.ListASelection.Value;
         }

         if (msg.ListBSelection.HasValue)
         {
            IssueBallotHelper.Voter.TexasVerifyVoterId.ListB = msg.ListBSelection.Value;
         }

         if (msg.FormsComplete.HasValue)
         {
            IssueBallotHelper.Voter.TexasVerifyVoterId.FormsComplete = msg.FormsComplete.Value;
         }

         if (!(msg.VerifyVoterIdOptions?.Count > 0)) return;

         foreach (var kvp in msg.VerifyVoterIdOptions)
         {
            IssueBallotHelper.Voter.Dynamic.DynamicData[kvp.Key.ToString()] = kvp.Value.ToString();
         }
      }
      private static void ReasonableImpedimentAffidavitHandler(ReasonableImpedimentAffidavitMessage msg)
      {
         if (IssueBallotHelper.Voter.Affidavits == null) IssueBallotHelper.Voter.Affidavits = new Dictionary<string, AffidavitTemplateDto>();
         IssueBallotHelper.Voter.Affidavits[msg.Affidavit.AffidavitType] = msg.Affidavit;
      }

      private static void AffidavitTemplateHandler(AffidavitTemplateMessage msg)
      {
         if (IssueBallotHelper.Voter.Affidavits == null) IssueBallotHelper.Voter.Affidavits = new Dictionary<string, AffidavitTemplateDto>();
         IssueBallotHelper.Voter.Affidavits[msg.Affidavit.AffidavitType] = msg.Affidavit;
      }

      private static void SurrenderMailInBallotCompleteHandler(SurrenderMailInBallotCompleteMessage msg)
      {
         if (IssueBallotHelper.Voter.VoterBallotDto.SurrenderAbsentee != msg.HasSurrenderedMailInBallot)
            IssueBallotHelper.Voter.VoterBallotDto.SurrenderAbsentee = msg.HasSurrenderedMailInBallot;
      }
   }
}

