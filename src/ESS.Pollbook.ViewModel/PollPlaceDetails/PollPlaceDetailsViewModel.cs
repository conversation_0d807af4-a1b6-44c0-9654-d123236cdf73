using ESS.Pollbook.Core.Commands;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.Model;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Facade.Device;
using ESS.Pollbook.Facade.PollPlace;
using ESS.Pollbook.Core.Messaging;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.Command;
using GalaSoft.MvvmLight.Messaging;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Reflection;
using System.Windows.Input;

namespace ESS.Pollbook.ViewModel.PollPlaceDetails
{
    public class PollPlaceDetailsViewModel : ViewModelBase
    {
        private readonly IFrameNavigationService _navigation;
        private readonly IPollPlaceFacade _pollPlaceFacade;
        private readonly IPrintPollPlaceFacade _printPollLocationFacade;
        private readonly ICommand _backCommand;
        private readonly ICommand _printInfoCommand;
        private readonly ICommand _sendTextCommand;
        private readonly IMessenger _messenger;
        private readonly IEssLogger _essLogger;
        private readonly IAuditLogFacade _auditLogFacade;
        private readonly IPrinterFacade _printerFacade;

        private readonly string _pageName;

        public NotifyTaskCompletion<PollPlaceDto> PollPlaceTaskResults = new NotifyTaskCompletion<PollPlaceDto>();
        public int CurrentStreetId;

        #region Commands

        public ICommand BackCommand => _backCommand;
        public ICommand PrintInfoCommand => _printInfoCommand;
        public ICommand SendTextCommand => _sendTextCommand;

        public string BackLabel => UIText.Back;
        public string PrintInfoLabel => UIText.PrintInfo;
        public string SendTextLabel => UIText.SendText;

        public bool IsSendTextVisible => SystemConfiguration.ElectionConfiguration.EnableSmsMessages;

        #endregion

        #region Properties

        private StreetDto _streetDetails;
        public StreetDto StreetDetails
        {
            get
            {
                if (_navigation.Parameter != null)
                {
                    _streetDetails = (StreetDto)_navigation.Parameter;
                }

                return _streetDetails;
            }
        }

        private ObservableCollection<PollPlaceDto> _pollPlaceDto = new ObservableCollection<PollPlaceDto>();
        public ObservableCollection<PollPlaceDto> PollPlaceCollection
        {
            get
            {
                GetPollPlaceDetails();
                return _pollPlaceDto;
            }
            set => _pollPlaceDto = value;
        }

        public PollPlaceDto DesignatedPollPlace => PollPlaceCollection[0];

        public bool PrintEnable
        {
            get
            {
                PrintResponse pr = _printerFacade.GetPrinterInformationFromConfiguration(SystemConfiguration.ElectionConfiguration.SelectedExpressPollPrinterId);
                return pr != null;
            }
        }

        public bool PrintVisible => SystemConfiguration.ElectionConfiguration.PrintingEnabled;

        #endregion

        #region Constructor

        public PollPlaceDetailsViewModel(IFrameNavigationService navigationService,
                                            IPollPlaceFacade pollPlaceFacade,
                                            IMessenger messengerService,
                                            IPrintPollPlaceFacade printPollLocationFacade,
                                            IEssLogger essLogger,
                                            IAuditLogFacade auditLogFacade,
                                            IPrinterFacade printerFacade)
        {
            _navigation = navigationService;
            _messenger = messengerService;
            _pollPlaceFacade = pollPlaceFacade;
            _printPollLocationFacade = printPollLocationFacade;
            _essLogger = essLogger;
            _auditLogFacade = auditLogFacade;
            _printerFacade = printerFacade;

            _pageName = _navigation.PageName(this.GetType().FullName);

            _backCommand = new RelayCommand(Back);
            _printInfoCommand = new RelayCommand(PrintInfo);
            _sendTextCommand = new RelayCommand(SendText);

            PollPlaceTaskResults.PropertyChanged += OnPollPlaceTaskCompletion;
            GetPollPlaceDetails();
        }

        #endregion

        #region Methods

        private void SendText()
        {
            try
            {
                _auditLogFacade.AddToAuditLog(_pageName, $"'{SendTextLabel}' button was activated.");
                _messenger.Send(new StatusBarMessage(isVisible: false));
                _messenger.Send(new ResetSendTextMessage(DesignatedPollPlace));
                _messenger.Send(new ParentControlTextMessage("PollPlaceDetailsViewModel"));

                _navigation.NavigateTo<SendTextViewModel>();
            }
            catch (Exception ex)
            {
                _essLogger.LogError(ex,
                    new Dictionary<string, string>
                        { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }

        private void GetPollPlaceDetails()
        {
            try
            {
                if (_navigation.Parameter != null
                    && ((StreetDto)_navigation.Parameter).StreetId != CurrentStreetId)
                {
                    _pollPlaceDto?.Clear();
                    CurrentStreetId = ((StreetDto)_navigation.Parameter).StreetId;

                    PollPlaceTaskResults.ExecuteTask(_pollPlaceFacade.GetPollPlaceDetailsAsync(CurrentStreetId));
                }
            }
            catch (Exception ex)
            {
                _essLogger.LogError(ex,
                    new Dictionary<string, string>
                        { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
            }
        }

        private void OnPollPlaceTaskCompletion(object sender, PropertyChangedEventArgs e)
        {
            if (!e.PropertyName.Equals("Result") ||
                PollPlaceTaskResults.Result == null)
            {
                return;
            }

            try
            {
                var pollCollection = PollPlaceTaskResults.Result;

                if (pollCollection != null)
                {
                    _pollPlaceDto.Add(pollCollection);
                }
            }
            catch (Exception ex)
            {
                _essLogger.LogError(ex,
                    new Dictionary<string, string>
                        { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
            }
        }

        private void Back()
        {
            try
            {
                _auditLogFacade.AddToAuditLog(_pageName, $"'{BackLabel}' button was activated.");
                _navigation.GoBack();
            }
            catch (Exception ex)
            {
                _essLogger.LogError(ex,
                    new Dictionary<string, string>
                        { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }

        private void PrintInfo()
        {
            try
            {
                _auditLogFacade.AddToAuditLog(_pageName, $"'{PrintInfoLabel}' button was activated.");
                string pollDisplayName = PollPlaceCollection[0].PollingPlaceDisplayName;
                string pollHouseNum = PollPlaceCollection[0].PollingPlaceHouseNumber;
                string pollStreet = PollPlaceCollection[0].PollingPlaceStreetName;
                string pollStreet2 = PollPlaceCollection[0].PollingPlaceUnitName;
                string pollCity = PollPlaceCollection[0].PollingPlaceCityName;
                string pollState = PollPlaceCollection[0].PollingPlaceStateProvinceCode;
                string pollPostalCode = PollPlaceCollection[0].PollingPlacePostalCode;


                var printResponse = _printPollLocationFacade.PrintPollPlace(pollDisplayName, pollHouseNum, pollStreet, pollStreet2, pollState, pollCity, pollPostalCode);

                if (printResponse == null || !string.IsNullOrEmpty(printResponse.PrinterErrorMessage))
                {
                    _navigation.NavigateTo<PrintPollPlaceDetailsFailedViewModel>(PollPlaceCollection[0], NavigationFrameEnum.ContextFrame);
                }
            }
            catch (Exception ex)
            {
                _essLogger.LogError(ex,
                    new Dictionary<string, string>
                        { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }

        #endregion
    }
}
