using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Facade.TextMessaging;
using ESS.Pollbook.Core.Messaging;
using ESS.Pollbook.ViewModel.Voter;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.Command;
using GalaSoft.MvvmLight.Messaging;
using System;
using System.Collections.Generic;
using System.Windows.Input;

namespace ESS.Pollbook.ViewModel.PollPlaceDetails
{
    public class SendTextViewModel : ViewModelBase
    {
        private readonly IFrameNavigationService _navigation;
        private readonly ITextMessagingFacade _textMessagingFacade;
        private readonly IMessenger _messenger;
        private readonly IEssLogger _essLogger;
        private readonly IAuditLogFacade _auditLogFacade;

        private readonly string _pageName;

        private static readonly string TEXT_MESSAGE_PREFIX = "Your Designated Poll Place:";

        private readonly ICommand _cancelCommand;
        private readonly ICommand _sendCommand;

        public ICommand CancelCommand => _cancelCommand;
        public ICommand SendCommand => _sendCommand;

        public string CancelLabel => UIText.Cancel;
        public string SendLabel => UIText.Send;

        public string TextMessageNumber { get; set; }

        public EditListItemViewModel<string> CellPhoneNumber { get; set; }



        public bool IsValid => !CellPhoneNumber.IsInvalid;

        public string PhoneNumber => CleanPhoneNumber(CellPhoneNumber.Value);

        private PollPlaceDto _designatedPollPlace;
        public PollPlaceDto DesignatedPollPlace
        {
            get { return _designatedPollPlace; }
            set
            {
                _designatedPollPlace = value;

                RaisePropertyChanged("DesignatedPollPlace");
                RaisePropertyChanged("TextMessage");
            }
        }

        private string _parentControlName;
        public string ParentControlName
        {
            get { return _parentControlName; }
            set
            {
                _parentControlName = value;
                RaisePropertyChanged("ParentControlName");
            }
        }

        public string TextMessage
        {
            get
            {
                if (DesignatedPollPlace == null)
                {
                    return string.Empty;
                }
                return TEXT_MESSAGE_PREFIX + Environment.NewLine + Environment.NewLine + DesignatedPollPlace.Formatted;
            }
        }

        public SendTextViewModel(IFrameNavigationService navigationService, ITextMessagingFacade textMessagingFacade, IMessenger messengerService, IEssLogger logger, IAuditLogFacade auditLogFacade)
        {
            _navigation = navigationService;
            _messenger = messengerService;
            _textMessagingFacade = textMessagingFacade;
            _essLogger = logger;
            _auditLogFacade = auditLogFacade;

            _pageName = _navigation.PageName(this.GetType().FullName);

            _cancelCommand = new RelayCommand(Cancel);
            _sendCommand = new RelayCommand(Send);

            _messenger.Register<ResetSendTextMessage>(this, ResetSendText);
            _messenger.Register<ParentControlTextMessage>(this, ParentControlTextMessage);

            CellPhoneNumber = new EditListItemViewModel<string>(TextMessageNumber, a => a) { Label = "Cell Phone Number", ErrorText = "Please enter a valid phone number." };
        }

        private void ResetSendText(ResetSendTextMessage message)
        {
            Clear();

            DesignatedPollPlace = message.DesignatedPollPlace;
        }

        private void ParentControlTextMessage(ParentControlTextMessage message)
        {
            Clear();

            ParentControlName = message.ParentControlName;
        }

        private void Clear()
        {
            CellPhoneNumber.Value = "";
            CellPhoneNumber.IsInvalid = false;

            RaisePropertyChanged("IsValid");
        }

        private void Cancel()
        {
            try
            {
                _auditLogFacade.AddToAuditLog(_pageName, $"'{CancelLabel}' button was activated.");
                _messenger.Send(new StatusBarMessage(isVisible: true));

                _navigation.NavigateTo<DashboardViewModel>(NavigationFrameEnum.MainFrame, false);
                if ((!String.IsNullOrEmpty(ParentControlName)) && ParentControlName.Equals("WrongPollLocationViewModel"))
                {
                    _navigation.NavigateTo<WrongPollLocationViewModel>(NavigationFrameEnum.ModalFrame, false, withVerticalEffect: true);

                }
                _navigation.GoBack();
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string>();
                logProps.Add("Action", "SendTextViewModel.Cancel");
                _essLogger.LogError(ex, logProps);
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }

        private void Validate()
        {
            CellPhoneNumber.IsInvalid = !ValidatePhoneNumber();

            RaisePropertyChanged("IsValid");
        }

        private bool ValidatePhoneNumber()
        {
            var hasError = false;

            // Cleanse
            var phoneNumber = PhoneNumber;

            // Check length
            if (phoneNumber.Length != 10)
            {
                hasError = true;
            }

            return !hasError;
        }

        private string CleanPhoneNumber(string phoneNumber)
        {
            return phoneNumber
                .Replace("(", "")
                .Replace(")", "")
                .Replace("-", "")
                .Replace(" ", "")
                .Trim();
        }

        private void Send()
        {
            try
            {
                _auditLogFacade.AddToAuditLog(_pageName, $"'{SendLabel}' button was activated.");
                Validate();

                if (!IsValid)
                    return;

                var success = _textMessagingFacade.SendMessage(PhoneNumber, TextMessage);

                _messenger.Send(new StatusBarMessage(isVisible: true));

                _navigation.NavigateTo<DashboardViewModel>(NavigationFrameEnum.MainFrame, false);
                if ((!String.IsNullOrEmpty(ParentControlName)) && ParentControlName.Equals("WrongPollLocationViewModel"))
                {
                    _navigation.NavigateTo<WrongPollLocationViewModel>(NavigationFrameEnum.ModalFrame, false, withVerticalEffect: true);

                }
                _navigation.GoBack();

                if (success)
                {
                    _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Success, "Text Message Sent"));
                }
                else
                {
                    _navigation.NavigateTo<SendTextErrorViewModel>(NavigationFrameEnum.ModalFrame, false, withVerticalEffect: true);
                }
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string>();
                logProps.Add("Action", "SendTextViewModel.Send");
                _essLogger.LogError(ex, logProps);
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }
    }
}
