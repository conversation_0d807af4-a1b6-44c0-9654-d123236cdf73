using ESS.Pollbook.Core.Commands;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.Model.HelpCenter;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Facade.HelpCenter;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.Command;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;

namespace ESS.Pollbook.ViewModel.HelpCenter
{
    public class HelpCenterViewModel : ViewModelBase
    {
        private readonly IFrameNavigationService _navigation;
        private readonly IHelpCenterFacade _helpCenterFacade;
        private readonly IAuditLogFacade _auditLogFacade;
        private readonly IEssLogger _essLogger;

        private string PageName => _navigation.PageName(this.GetType().FullName);

        public static string PageTitle => "Help Center";
        public static string HelpUnavailable => "Help Documents are unavailable";
        public static string ContactInfo => "Please contact your administrator.";

        public ICommandAsync<HelpCenterItem> ItemSelected => new CommandAsync<HelpCenterItem>(GoToContentView);

        public Visibility NoHelpVisible => HelpCenterItems.Any() ? Visibility.Collapsed : Visibility.Visible;

        public Visibility HelpCenterVisible => HelpCenterItems.Any() ? Visibility.Visible : Visibility.Collapsed;

        public ObservableCollection<HelpCenterItem> HelpCenterItems { get; set; }

        private bool _hideSpinner;
        public bool HideSpinner
        {
            get => _hideSpinner;
            set => Set(ref _hideSpinner, value);
        }

        public HelpCenterViewModel(IFrameNavigationService navigation, IHelpCenterFacade helpCenterFacade, IAuditLogFacade auditLogFacade, IEssLogger essLogger)
        {
            _navigation = navigation;
            _helpCenterFacade = helpCenterFacade;
            _auditLogFacade = auditLogFacade;
            _essLogger = essLogger;

            HelpCenterItems = new ObservableCollection<HelpCenterItem>();
        }

        public async Task PageIsLoaded()
        {
            try
            {
                HideSpinner = false;
                await _helpCenterFacade.GetHelpUpdatesAsync();

                var items = await _helpCenterFacade.GetHelpFileList().ConfigureAwait(false);
                HelpCenterItems =
                    new ObservableCollection<HelpCenterItem>(items.OrderBy(x => x.DisplayOrder)
                        .ThenByDescending(x => x.FileDateTime));

                RaisePropertyChanged(nameof(NoHelpVisible));
                RaisePropertyChanged(nameof(HelpCenterVisible));
                RaisePropertyChanged(nameof(HelpCenterItems));
            }
            catch (Exception ex)
            {
                Dictionary<string, string> logProps = new Dictionary<string, string>()
                {
                    {"Action", "HelpCenterViewModel.PageIsLoaded"}
                };
                _essLogger.LogError(ex, logProps);
            }
            finally
            {
                HideSpinner = true;
            }
        }

        private async Task GoToContentView(object param)
        {
            HelpCenterItem item = (HelpCenterItem)param;
            if (item != null)
            {
                await _auditLogFacade.AddToAuditLogAsync(PageName, $"'{item.Title}' button was activated.");

                _navigation.NavigateTo<HelpCenterContentViewModel>(item, NavigationFrameEnum.ContextFrame);
            }
        }
    }
}
