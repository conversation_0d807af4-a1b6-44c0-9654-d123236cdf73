using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Model.HelpCenter;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.HelpCenter;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.CommandWpf;
using System;
using System.IO;
using System.Windows.Input;

namespace ESS.Pollbook.ViewModel.HelpCenter
{
    public class HelpCenterContentViewModel : ViewModelBase
    {
        private readonly IFrameNavigationService _navigation;
        private readonly IHelpCenterFacade _helpCenterFacade;
        private HelpCenterItem _item;

        public string BackLabel => UIText.Back;

        public string Title => (_item != null) ? _item.Title : string.Empty;

        private string _helpText;
        public string HelpText
        {
            get => _helpText;
            set => Set(ref _helpText, value);
        }

        public Uri Source { get; set; }

        public ICommand BackCommand { get; set; }

        public HelpCenterContentViewModel(IFrameNavigationService navigation, IHelpCenterFacade helpCenterFacade)
        {
            _navigation = navigation;
            _helpCenterFacade = helpCenterFacade;

            BackCommand = new RelayCommand(Back);
        }

        public void PageIsLoaded()
        {
            _item = _navigation.Parameter as HelpCenterItem;
            if (_item != null)
                Source = GetUri();

            RaisePropertyChanged(nameof(Title));
        }

        public Uri GetUri()
        {
            if (_item != null)
            {
                var filePath = Path.Combine(_helpCenterFacade.GetHelpCenterFolder(), _item.FileName);
                return new Uri(filePath);
            }

            return null;
        }

        private void Back()
        {
            _navigation.GoBack();
        }
    }
}
