using ESS.Pollbook.Core.Commands;
using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.Model;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Facade.Device;
using ESS.Pollbook.Facade.PrecinctSplit;
using ESS.Pollbook.Facade.Reports;
using ESS.Pollbook.Facade.VoterExport;
using ESS.Pollbook.Core.Messaging;
using ESS.Pollbook.ViewModel.VoterExport;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.Command;
using GalaSoft.MvvmLight.Messaging;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using System.Timers;
using System.Windows.Input;

namespace ESS.Pollbook.ViewModel.Reports
{
    public class PollingPlaceVoterListReportViewModel : ViewModelBase
    {
        private const int ButtonDisableTime = 2000; // 2 seconds

        private readonly IPollingPlaceVoterListFacade _pollingPlaceVoterListFacade;
        private readonly IPrecinctSplitFacade _precinctSplitFacade;
        private readonly IVoterExportFacade _voterExportFacade;
        private readonly IFrameNavigationService _navigation;
        private readonly IMessenger _messenger;
        private readonly CommaFormatter _commaFormatter;
        private readonly IEssLogger _essLogger;
        private readonly IAuditLogFacade _auditLogFacade;
        private readonly IPrinterFacade _printerFacade;

        private readonly string _pageName;

        public ICommand BackCommand => new RelayCommand(MonitorPoll);
        public ICommand ExportCommand => new RelayCommand(Export);
        public ICommand PrintCommand => new RelayCommand(Print);

        public string BackLabel => UIText.Back;
        public string ExportLabel => UIText.Export;
        public string PrintLabel => UIText.Print;

        private System.Timers.Timer _enablementTimer;
        private DateTime _currentDate;
        private List<PrecinctSplitDto> _precincts;
        private PollingPlaceVoterListRequest _request;
        private bool _isLoading = true;
        private NotifyTaskCompletion<bool> VoterExportResultsCompletion = new NotifyTaskCompletion<bool>();
        private CancellationTokenSource _cancellationTokenSource;

        #region Public Properties
        public string ReportDate => $"{_currentDate:MM-dd-yyyy hh:mm tt}";
        public string PrecinctLabel => DefinedText.PollbookDefinedTextDtos.FirstOrDefault(n => n.PollbookDefinedTextLanguage.Equals("English") && n.PollbookDefinedTextName.Equals("Precinct"))?.PollbookDefinedTextValue;
        public string GroupByPrecinctLabel => $"Group By {PrecinctLabel}";
        public string FilterByPrecinctLabel => $"Filter By {PrecinctLabel}";

        private bool _printEnabled;
        public bool PrintEnabled
        {
            get => _printEnabled;
            set => Set(ref _printEnabled, value);
        }

        public bool ExportEnabled => true;

        public bool PrintVisible => SystemConfiguration.ElectionConfiguration.PrintingEnabled;

        private List<PollingPlaceVoterListItemDto> _voterList;
        public List<PollingPlaceVoterListItemDto> VoterList
        {
            get => _voterList;
            set => Set(ref _voterList, value);
        }

        private VirtualList<PollingPlaceVoterListItemDto> _virtualVoterList;
        public VirtualList<PollingPlaceVoterListItemDto> VirtualVoterList
        {
            get =>_virtualVoterList;
            set => Set(ref _virtualVoterList, value);
        }

        private bool _isGroupByPrecinct;
        public bool IsGroupByPrecinct
        {
            get => _isGroupByPrecinct;
            set
            {
                if (_isGroupByPrecinct == value) return;
                Set(ref _isGroupByPrecinct, value);

                if (!_isLoading)
                    LoadPollingPlaceVoterList();
            }
        }

        public List<PrecinctSplitDto> Precincts
        {
            get => _precincts;
            set => Set(ref _precincts, value);
        }
        private PrecinctSplitDto _selectedPrecinct;

        public PrecinctSplitDto SelectedPrecinct
        {
            get => _selectedPrecinct;
            set
            {
                if (value == null) return;
                Set(ref _selectedPrecinct, value);

                if (!_isLoading)
                    LoadPollingPlaceVoterList();
            }
        }

        public string RecordCount
        {
            get
            {
                if (VoterList != null)
                {
                    return $"{_commaFormatter.FormatString(VoterList.Count)} " + (VoterList.Count == 1 ? "Voter" : "Voters");
                }

                return "0 Voters";
            }
        }

        private bool _loadingResults;
        public bool LoadingResults
        {
            get => _loadingResults;
            set => Set(ref _loadingResults, value);
        }

        #endregion

        public PollingPlaceVoterListReportViewModel(IFrameNavigationService navigationService,
                IPollingPlaceVoterListFacade pollingPlaceVoterListFacade,
                IPrecinctSplitFacade precinctSplitFacade,
                IVoterExportFacade voterExportFacade,
                IMessenger messengerService,
                CommaFormatter commaFormatter,
                IEssLogger essLogger,
                IAuditLogFacade auditLogFacade,
                IPrinterFacade printerFacade)
        {
            _navigation = navigationService;
            _pollingPlaceVoterListFacade = pollingPlaceVoterListFacade;
            _precinctSplitFacade = precinctSplitFacade;
            _voterExportFacade = voterExportFacade;
            _messenger = messengerService;
            _commaFormatter = commaFormatter;
            _essLogger = essLogger;
            _auditLogFacade = auditLogFacade;
            _printerFacade = printerFacade;

            _pageName = _navigation.PageName(this.GetType().FullName);

            _messenger.Register<LoadVoterDefaultTimeFilterMessage>(this, LoadVoterDefaultTimeFilterMessage);

            VoterExportResultsCompletion.PropertyChanged += VoterExportResultsCompletion_PropertyChanged;

            Load();
        }

        private void VoterExportResultsCompletion_PropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName.Equals("IsFaulted"))
            {
                _navigation.NavigateTo<VoterExportFailedViewModel>(NavigationFrameEnum.ContextFrame);
                return;
            }

            if (!e.PropertyName.Equals("Result") || VoterExportResultsCompletion == null)
            {
                return;
            }

            var appliedIncrementalUpdatesSuccessfully = VoterExportResultsCompletion.Result;
            if (appliedIncrementalUpdatesSuccessfully)
            {
                _navigation.NavigateTo<VoterExportLoadedViewModel>(NavigationFrameEnum.ContextFrame);
            }
            else
            {
                _navigation.NavigateTo<VoterExportFailedViewModel>(NavigationFrameEnum.ContextFrame);
            }
        }

        private void Clear()
        {
            _isGroupByPrecinct = false;
            RaisePropertyChanged(nameof(IsGroupByPrecinct));
        }

        private void ClearBeforeLeavePage()
        {
            Clear();
            _selectedPrecinct = Precincts[0];

            _isLoading = false;
        }

        private void Load()
        {
            if (!_isLoading)
            {
                _isLoading = true;

                ResetReportParameters();

                _isLoading = false;
            }
        }

        private void LoadPrecincts()
        {
            if (LoggedInPollplaceInfo.LoggedInPollPlace != null)
            {
                _precincts = Task.Run(async () => await _precinctSplitFacade.GetPrecinctSplitsNameByPollingPlaceAndSearchTerm(LoggedInPollplaceInfo.LoggedInPollPlace.PollingPlaceId, string.Empty))
                    .Result.OrderBy(x => x.PrecinctSplitName).ToList() ?? new List<PrecinctSplitDto>();

                _precincts.Insert(0, new PrecinctSplitDto
                {
                    PrecinctSplitId = 0,
                    PrecinctSplitName = "All",
                    PrecinctSplitSourcePrecinctSplitKey = null
                });

                Precincts = _precincts;

                SelectedPrecinct = _precincts[0];
            }
        }

        private void MonitorPoll()
        {
            try
            {
                _auditLogFacade.AddToAuditLog(_pageName, $"'{BackLabel}' button was activated.");
                _loadingResults = false;

                ClearBeforeLeavePage();

                _navigation.GoBackTo<MonitorPollViewModel>(NavigationFrameEnum.ContextFrame);
            }
            catch (Exception ex)
            {
                _essLogger.LogError(ex,
                    new Dictionary<string, string>
                        { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }

        private void Export()
        {
            try
            {
                _auditLogFacade.AddToAuditLog(_pageName, $"'{ExportLabel}' button was activated.");
                _messenger.Send(new VoterExportMessage(VoterExportMessage.VoterExportBackPage.PollingPlaceVoterListReport));

                _navigation.NavigateTo<VoterExportLoadingViewModel>(NavigationFrameEnum.ContextFrame);

                VoterExportResultsCompletion.ExecuteTask(_voterExportFacade.ExportPollingPlaceVoterListAsync(VoterList));
            }
            catch (Exception ex)
            {
                _essLogger.LogError(ex,
                    new Dictionary<string, string>
                        { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }

        private void Print()
        {
            try
            {
                _auditLogFacade.AddToAuditLog(_pageName, $"'{PrintLabel}' button was activated.");
                PrintEnabled = false;

                _enablementTimer = new System.Timers.Timer(ButtonDisableTime);
                _enablementTimer.Elapsed += EnablementTimerOnElapsed;
                _enablementTimer.Enabled = true;
                _enablementTimer.Start();

                var voterList = new VoterList()
                {
                    PollingPlaceVoterListItemDtos = VoterList,
                    Timestamp = ReportDate,
                    HasPollPlaceText = _selectedPrecinct.PrecinctSplitId == 0
                };
                var printResponse = _pollingPlaceVoterListFacade.PrintPollingPlaceVoterList(voterList);

                if (printResponse == null || !string.IsNullOrEmpty(printResponse.PrinterErrorMessage))
                {
                    _navigation.NavigateTo<PrintPollingPlaceVoterListFailedViewModel>(voterList, NavigationFrameEnum.ContextFrame);
                }
            }
            catch (Exception ex)
            {
                _essLogger.LogError(ex,
                    new Dictionary<string, string>
                        { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }

        private void EnablementTimerOnElapsed(object sender, ElapsedEventArgs e)
        {
            PrintEnabled = true;
            _enablementTimer.Stop();
            _enablementTimer.Elapsed -= EnablementTimerOnElapsed;
            _enablementTimer.Enabled = false;
        }

        private void LoadPollingPlaceVoterList()
        {
            try
            {
                _cancellationTokenSource = new CancellationTokenSource();
                Task.Run(async () =>
                {
                    const int MaxRetries = 30;
                    LoadingResults = true;

                    if (SelectedPrecinct == null)
                    {
                        _selectedPrecinct = Precincts[0];
                        RaisePropertyChanged(nameof(SelectedPrecinct));
                    }

                    _request = new PollingPlaceVoterListRequest()
                    {
                        PollingPlaceId = LoggedInPollplaceInfo.LoggedInPollPlace.PollingPlaceId,
                        GroupByPrecinct = _isGroupByPrecinct,
                        PrecinctSplitId = _selectedPrecinct.PrecinctSplitId
                    };

                    PollingPlaceVoterListResponse result = null;

                    //This can likely be removed for 7.2.9.0 keeping here to limit the scope of changes
                    for (var tries = 0; tries < MaxRetries; tries++)
                    {
                        try
                        {
                            if (_cancellationTokenSource.IsCancellationRequested)
                            {
                                LoadingResults = false;
                                return;
                            }
                            result = await _pollingPlaceVoterListFacade.GetPollingPlaceVoterList(_request);
                            break;
                        }
                        catch (Exception ex)
                        {
                            _essLogger.LogError(ex,
                            new Dictionary<string, string>
                                { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
                            await Task.Delay(500, _cancellationTokenSource.Token);
                        }
                    }

                    if (result != null)
                    {
                        VoterList = result.VoterDtoResults.ToList();
                        int lineNumber = 0;

                        foreach (var item in VoterList)
                        {
                            if (_cancellationTokenSource.IsCancellationRequested)
                            {
                                LoadingResults = false;
                                return;
                            }
                            item.LineNumber = ++lineNumber;

                            switch (item.AbsenteeStatus)
                            {
                                case "Absentee Issued":
                                    item.AbsenteeStatus = "ABS I";
                                    break;
                                case "Absentee Returned":
                                    item.AbsenteeStatus = "ABS R";
                                    break;
                                case "Eligible":
                                    item.AbsenteeStatus = "NO ABS";
                                    break;
                                case "Early Ballot Issued":
                                    item.AbsenteeStatus = "EV";
                                    break;
                                default:
                                    break;
                            }
                        }

                        VirtualVoterList = new VirtualList<PollingPlaceVoterListItemDto>(VoterList);

                        RaisePropertyChanged(nameof(RecordCount));
                    }

                    LoadingResults = false;
                }, _cancellationTokenSource.Token);
            }
            catch (Exception ex)
            {
                _essLogger.LogError(ex,
                    new Dictionary<string, string>
                        { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
            }
        }

        private void LoadVoterDefaultTimeFilterMessage(LoadVoterDefaultTimeFilterMessage msg)
        {
            _essLogger.LogDebug("PollingPlaceVoterListViewModel.LoadVoterDefaultTimeFilterMessage... rcvd msg");

            try
            {
                LoadPrecincts();
                ClearBeforeLeavePage();
                LoadPollingPlaceVoterList();
            }
            catch (Exception ex)
            {
                _essLogger.LogError(ex,
                    new Dictionary<string, string>
                        { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
            }
        }

        private void ResetReportParameters()
        {
            try
            {
                IsGroupByPrecinct = false;

                if (_precincts == null)
                {
                    LoadPrecincts();
                }
                else
                {
                    SelectedPrecinct = Precincts[0];
                }
            }
            catch (Exception ex)
            {
                _essLogger.LogError(ex,
                    new Dictionary<string, string>
                        { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
            }
        }

        public void PageIsLoaded()
        {
            PrintResponse pr = _printerFacade.GetPrinterInformationFromConfiguration(SystemConfiguration.ElectionConfiguration.SelectedExpressPollPrinterId);
            PrintEnabled = pr != null && !pr.IsLabelPaperType;

            _currentDate = DateTime.Now;
            RaisePropertyChanged(nameof(ReportDate));
        }

        public void PageUnloaded()
        {
            _isLoading = true;
            _cancellationTokenSource?.Cancel();
        }
    }
}
