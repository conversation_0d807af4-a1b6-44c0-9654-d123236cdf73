using ESS.Pollbook.Core.Commands;
using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.Model;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Facade.Device;
using ESS.Pollbook.Facade.Reports;
using ESS.Pollbook.Facade.VoterExport;
using ESS.Pollbook.ViewModel.Maintenance;
using ESS.Pollbook.Core.Messaging;
using ESS.Pollbook.ViewModel.VoterExport;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.Command;
using GalaSoft.MvvmLight.Messaging;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using System.Timers;
using System.Windows.Input;

namespace ESS.Pollbook.ViewModel.Reports
{
    public class BallotReissuedReportViewModel : ViewModelBase
    {
        private const int ButtonDisableTime = 2000; // 2 seconds
        private const int FirstStartHour = 0;
        private const int LastStartHour = 23;
        private const int FirstEndHour = 1;
        private const int LastEndHour = 24; // This means the very last moment of the day, approximately 11:59:59 PM

        private readonly IFrameNavigationService _navigation;
        private readonly IMessenger _messenger;
        private readonly IBallotReissuedListFacade _ballotReissuedListFacade;
        private readonly IVoterExportFacade _voterExport;
        private readonly CommaFormatter _commaFormatter;
        private readonly IEssLogger _essLogger;
        private readonly IAuditLogFacade _auditLogFacade;
        private readonly IPrinterFacade _printerFacade;

        private readonly string _pageName;

        private BallotReissuedListRequest _request;
        private DateTime _currentDate;

        private System.Timers.Timer _enablementTimer;
        private HashSet<string> _voterReissuedCount = new HashSet<string>();
        private bool _isLoading;

        public ICommand BackCommand => new RelayCommand(MonitorPoll);
        public ICommand PrintCommand => new RelayCommand(Print);
        public ICommand ExportCommand => new RelayCommand(Export);
        public ICommand ClearAllCommand => new RelayCommand(ClearAll);
        public ICommand FilterByDateCommand => new RelayCommand(FilterByDate);

        public string BackLabel => UIText.Back;
        public string PrintLabel => UIText.Print;
        public string ExportLabel => UIText.Export;
        public string ClearLabel => UIText.Clear;
        public string SetLabel => UIText.Set;

        public NotifyTaskCompletion<bool> VoterExportResultsCompletion = new NotifyTaskCompletion<bool>();

        public string ReportDate => $"{_currentDate:MM-dd-yyyy hh:mm tt}";

        private List<BallotReissuedListItemDto> _ballotReissuedList;
        public List<BallotReissuedListItemDto> BallotReissuedList
        {
            get => _ballotReissuedList;
            set => Set(ref _ballotReissuedList, value);
        }

        private VirtualList<BallotReissuedListItemDto> _virtualBallotReissuedList;
        public VirtualList<BallotReissuedListItemDto> VirtualBallotReissuedList
        {
            get => _virtualBallotReissuedList;
            set => Set(ref _virtualBallotReissuedList, value);
        }

        private bool _loadingResults;
        public bool LoadingResults
        {
            get => _loadingResults;
            set => Set(ref _loadingResults, value);
        }

        public string VoterCount
        {
            get
            {
                if (_voterReissuedCount != null)
                {
                    return $"{_commaFormatter.FormatString(_voterReissuedCount.Count)} " + (_voterReissuedCount.Count == 1 ? "Voter" : "Voters");
                }

                return "0 Voters";
            }
        }

        public string ReissuedBallotCount
        {
            get
            {
                if (BallotReissuedList != null)
                {
                    return $"{_commaFormatter.FormatString(BallotReissuedList.Count)} " + (BallotReissuedList.Count == 1 ? "Reissued Ballot" : "Reissued Ballots");
                }

                return "0 Voters";
            }
        }

        private bool _isGroupByVoter;
        public bool IsGroupByVoter
        {
            get { return _isGroupByVoter; }
            set
            {
                if (_isGroupByVoter != value)
                {
                    _isGroupByVoter = value;
                    RaisePropertyChanged("IsGroupByVoter");

                    if (!_isLoading)
                        LoadBallotReissueList();
                }
            }
        }

        private List<string> _devices;
        public List<string> Devices
        {
            get => _devices;
            set => Set(ref _devices, value);
        }

        private string _selectedDevice;
        public string SelectedDevice
        {
            get => _selectedDevice;
            set
            {
                Set(ref _selectedDevice, value);

                if (!LoadingResults)
                    LoadBallotReissueList();
            }
        }
        public ObservableCollection<TimeUnit> Months
        {
            get;
            private set;
        }

        private ObservableCollection<TimeUnit> _days;
        public ObservableCollection<TimeUnit> Days
        {
            get => _days;
            set => Set(ref _days, value);
        }
        public ObservableCollection<TimeUnit> Years
        {
            get;
            private set;
        }
        public ObservableCollection<TimeUnit> TimeStart
        {
            get;
            private set;
        }
        public ObservableCollection<TimeUnit> TimeEnd
        {
            get;
            private set;
        }

        private TimeUnit _selectedMonth;
        public TimeUnit SelectedMonth
        {
            get => _selectedMonth;
            set
            {
                Set(ref _selectedMonth, value);
                OnDateTimeChanged();
            }
        }

        private TimeUnit _selectedDay;
        public TimeUnit SelectedDay
        {
            get => _selectedDay;
            set => Set(ref _selectedDay, value);
        }

        private TimeUnit _selectedYear;
        public TimeUnit SelectedYear
        {
            get => _selectedYear;
            set
            {
                Set(ref _selectedYear, value);
                OnDateTimeChanged();
            }
        }

        private TimeUnit _selectedTimeStart;
        public TimeUnit SelectedTimeStart
        {
            get => _selectedTimeStart;
            set => Set(ref _selectedTimeStart, value);
        }

        private TimeUnit _selectedTimeEnd;
        public TimeUnit SelectedTimeEnd
        {
            get => _selectedTimeEnd;
            set => Set(ref _selectedTimeEnd, value);
        }

        private bool _printEnabled;
        public bool PrintEnabled
        {
            get => _printEnabled;
            set => Set(ref _printEnabled, value);
        }

        public bool PrintVisible => SystemConfiguration.ElectionConfiguration.PrintingEnabled;

        private bool _exportEnabled = true;
        public bool ExportEnabled
        {
            get => _exportEnabled;
            set => Set(ref _exportEnabled, value);
        }

        public string PartyLabel => DefinedText.PollbookDefinedTextDtos.FirstOrDefault(n => n.PollbookDefinedTextLanguage.Equals("English") && n.PollbookDefinedTextName.Equals("Party"))?.PollbookDefinedTextValue;
        public string PrecinctLabel => DefinedText.PollbookDefinedTextDtos.FirstOrDefault(n => n.PollbookDefinedTextLanguage.Equals("English") && n.PollbookDefinedTextName.Equals("Precinct"))?.PollbookDefinedTextValue;

        private CancellationTokenSource _cancellationTokenSource;
        #region Public Methods
        public BallotReissuedReportViewModel(IFrameNavigationService navigationService,
                                            IMessenger messengerService,
                                            IBallotReissuedListFacade ballotReissuedListFacade,
                                            IVoterExportFacade voterExport,
                                            CommaFormatter commaFormatter,
                                            IEssLogger essLogger,
                                            IAuditLogFacade auditLogFacade,
                                            IPrinterFacade printerFacade)
        {
            _navigation = navigationService;
            _messenger = messengerService;
            _ballotReissuedListFacade = ballotReissuedListFacade;
            _voterExport = voterExport;
            _commaFormatter = commaFormatter;
            _essLogger = essLogger;
            _auditLogFacade = auditLogFacade;
            _printerFacade = printerFacade;
            _pageName = _navigation.PageName(this.GetType().FullName);


            _messenger.Register<LoadBallotDefaultTimeFilterMessage>(this, LoadBallotDefaultTimeFilterMessage);

            VoterExportResultsCompletion.PropertyChanged += VoterExportResultsCompletion_PropertyChanged;

            Load();
        }

        public void PageIsLoaded()
        {
            PrintResponse pr = _printerFacade.GetPrinterInformationFromConfiguration(SystemConfiguration.ElectionConfiguration.SelectedExpressPollPrinterId);
            PrintEnabled = pr != null && !pr.IsLabelPaperType;
            _currentDate = DateTime.Now;
            RaisePropertyChanged("ReportDate");
            RaisePropertyChanged();
        }

        public void PageUnloaded()
        {
            _cancellationTokenSource?.Cancel();
        }

        public void Load()
        {
            if (!_isLoading)
            {
                _isLoading = true;
                ResetReportParameters();
                _isLoading = false;
            }
        }
        #endregion

        #region Private Methods

        private void MonitorPoll()
        {
            try
            {
                _auditLogFacade.AddToAuditLog(_pageName, $"'{BackLabel}' button was activated.");
                _loadingResults = false;
                ClearBeforeLeavePage();
                _navigation.GoBackTo<MonitorPollViewModel>(NavigationFrameEnum.ContextFrame);
            }
            catch (Exception ex)
            {
                _essLogger.LogError(ex,
                    new Dictionary<string, string>
                        { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }

        private void ClearAll()
        {
            try
            {
                _auditLogFacade.AddToAuditLog(_pageName, $"'{ClearLabel}' button was activated.");
                Clear();
                LoadBallotReissueList();
            }
            catch (Exception ex)
            {
                _essLogger.LogError(ex,
                    new Dictionary<string, string>
                        { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }

        private void Print()
        {
            try
            {
                _auditLogFacade.AddToAuditLog(_pageName, $"'{PrintLabel}' button was activated.");
                PrintEnabled = false;

                _enablementTimer = new System.Timers.Timer(ButtonDisableTime);
                _enablementTimer.Elapsed += EnablementTimerOnElapsed;
                _enablementTimer.Enabled = true;
                _enablementTimer.Start();

                PrintResponse printResponse = null;
                var ballotReissuedListCount = new BallotReissuedListCount()
                {
                    BallotReissuedListItems = BallotReissuedList,
                    VoterCount = _voterReissuedCount.Count,
                    Timestamp = ReportDate
                };
                printResponse = _ballotReissuedListFacade.PrintBallotReissuedList(ballotReissuedListCount);
                if (printResponse == null || !string.IsNullOrEmpty(printResponse.PrinterErrorMessage))
                {
                    _navigation.NavigateTo<PrintBallotReissuedListFailedViewModel>(ballotReissuedListCount, NavigationFrameEnum.ContextFrame);
                }
            }
            catch (Exception ex)
            {
                _essLogger.LogError(ex,
                    new Dictionary<string, string>
                        { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }

        private void Export() //work on this in the end
        {
            try
            {
                _auditLogFacade.AddToAuditLog(_pageName, $"'{ExportLabel}' button was activated.");
                _messenger.Send(new VoterExportMessage(VoterExportMessage.VoterExportBackPage.BallotReissueReport));

                _navigation.NavigateTo<VoterExportLoadingViewModel>(NavigationFrameEnum.ContextFrame);
                var ballotReissuedListCount = new BallotReissuedListCount()
                {
                    BallotReissuedListItems = BallotReissuedList,
                    VoterCount = _voterReissuedCount.Count
                };
                VoterExportResultsCompletion.ExecuteTask(_voterExport.ExportBallotReissueListAsync(ballotReissuedListCount));
            }
            catch (Exception ex)
            {
                _essLogger.LogError(ex,
                    new Dictionary<string, string>
                        { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }

        private void FilterByDate()
        {
            try
            {
                _auditLogFacade.AddToAuditLog(_pageName, $"'{SetLabel}' button was activated.");
                LoadBallotReissueList();
            }
            catch (Exception ex)
            {
                _essLogger.LogError(ex,
                    new Dictionary<string, string>
                        { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }

        private void VoterExportResultsCompletion_PropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            try
            {
                _messenger.Send(new VoterExportMessage(VoterExportMessage.VoterExportBackPage.BallotReissueReport));

                if (e.PropertyName.Equals("IsFaulted"))
                {
                    _navigation.NavigateTo<VoterExportFailedViewModel>(NavigationFrameEnum.ContextFrame);
                    return;
                }

                if (!e.PropertyName.Equals("Result") || VoterExportResultsCompletion == null)
                {
                    return;
                }

                var appliedIncrementalUpdatesSuccessfully = VoterExportResultsCompletion.Result;
                if (appliedIncrementalUpdatesSuccessfully)
                {
                    _navigation.NavigateTo<VoterExportLoadedViewModel>(NavigationFrameEnum.ContextFrame);
                }
                else
                {
                    _navigation.NavigateTo<VoterExportFailedViewModel>(NavigationFrameEnum.ContextFrame);
                }
            }
            catch (Exception ex)
            {
                _essLogger.LogError(ex,
                    new Dictionary<string, string>
                        { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
            }
        }

        private void ResetReportParameters()
        {
            _currentDate = DateTime.Now;
            RaisePropertyChanged("ReportDate");

            _isGroupByVoter = false;
            RaisePropertyChanged("IsGroupByVoter");
        }

        private void ClearBeforeLeavePage()
        {
            Clear();
            _isLoading = false;
        }

        private void EnablementTimerOnElapsed(object sender, ElapsedEventArgs e)
        {
            PrintEnabled = true;
            _enablementTimer.Stop();
            _enablementTimer.Elapsed -= EnablementTimerOnElapsed;
            _enablementTimer.Enabled = false;
        }

        private void Clear()
        {
            SelectedYear = null;
            SelectedDay = null;
            SelectedMonth = null;
            SelectedTimeStart = null;
            SelectedTimeEnd = null;
            _isGroupByVoter = false;
            ClearFooterCount();
        }

        private void ClearFooterCount()
        {
            _voterReissuedCount?.Clear();
            BallotReissuedList?.Clear();

            RaisePropertyChanged("VoterCount");
            RaisePropertyChanged("ReissuedBallotCount");
        }

        private void LoadBallotDefaultTimeFilterMessage(LoadBallotDefaultTimeFilterMessage msg)
        {
            _essLogger.LogDebug("BallotReissueReportViewModel.LoadDefaultTimeFilterMessage ... rcvd msg");

            try
            {
                InitTimeFilter();
                LoadDevices();
                LoadBallotReissueList();
            }
            catch (Exception ex)
            {
                _essLogger.LogError(ex,
                    new Dictionary<string, string>
                        { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
            }
        }

        private void InitTimeFilter()
        {
            LoadTimeUnits();
            ClearBeforeLeavePage();
        }

        private void LoadDevices()
        {
            var devices = Task.Run(async () => await _ballotReissuedListFacade.GetDevicesListTask(LoggedInPollplaceInfo.LoggedInPollPlace.PollingPlaceId)).Result;

            _devices = new List<string>();
            _devices.Add("All Devices");
            _devices.Add("This Device");

            foreach (var device in devices)
            {
                if ((!string.IsNullOrEmpty(device)) && !device.Equals(SystemDetails.MachineName))
                    _devices.Add(device);
            }
        }

        private void LoadBallotReissueList()
        {
            try
            {
                _cancellationTokenSource = new CancellationTokenSource();
                Task.Run(async () =>
                {
                    const int MaxRetries = 30;
                    LoadingResults = true;
                    ClearFooterCount();

                    if (SelectedDevice == null)
                    {
                        SelectedDevice = Devices.First();
                    }

                    DateTime? start = null;
                    DateTime? end = null;
                    if (SelectedMonth != null && SelectedDay != null && SelectedYear != null)
                    {
                        if (SelectedTimeStart != null && SelectedTimeStart.UnitNumber != -1 &&
                            SelectedMonth.UnitNumber != -1 && SelectedDay.UnitNumber != -1 && SelectedYear.UnitNumber != -1)
                        {
                            var startDatetime = new DateTime(SelectedYear.UnitNumber, SelectedMonth.UnitNumber,
                                SelectedDay.UnitNumber, SelectedTimeStart.UnitNumber, 0, 0);
                            start = TimeZoneInfo.ConvertTimeToUtc(startDatetime, TimeZoneInfo.Local);
                        }
                        else
                        {
                            var startDatetime = new DateTime(SelectedYear.UnitNumber, SelectedMonth.UnitNumber,
                                SelectedDay.UnitNumber, FirstStartHour, 0, 0);
                            start = TimeZoneInfo.ConvertTimeToUtc(startDatetime, TimeZoneInfo.Local);
                        }

                        if (SelectedTimeEnd != null && SelectedTimeStart.UnitNumber != -1 &&
                            SelectedMonth.UnitNumber != -1 && SelectedDay.UnitNumber != -1 && SelectedYear.UnitNumber != -1)
                        {
                            DateTime endDatetime;
                            if (SelectedTimeEnd.UnitNumber == LastEndHour)
                            {
                                endDatetime = new DateTime(SelectedYear.UnitNumber, SelectedMonth.UnitNumber,
                                    SelectedDay.UnitNumber, 23, 59, 59);
                            }
                            else
                            {
                                endDatetime = new DateTime(SelectedYear.UnitNumber, SelectedMonth.UnitNumber,
                                    SelectedDay.UnitNumber, SelectedTimeEnd.UnitNumber, 0, 0);
                            }

                            end = TimeZoneInfo.ConvertTimeToUtc(endDatetime, TimeZoneInfo.Local);
                        }
                        else
                        {
                            var endDatetime = new DateTime(SelectedYear.UnitNumber, SelectedMonth.UnitNumber,
                                SelectedDay.UnitNumber, 23, 59, 59);
                            end = TimeZoneInfo.ConvertTimeToUtc(endDatetime, TimeZoneInfo.Local);
                        }
                    }

                    _request = new BallotReissuedListRequest()
                    {
                        PollingPlaceId = LoggedInPollplaceInfo.LoggedInPollPlace.PollingPlaceId,
                        GroupByVoter = _isGroupByVoter,
                        DeviceFilter = SelectedDevice,
                        FilterStartDate = start,
                        FilterEndDate = end
                    };

                    BallotReissuedLimitResponse result = null;
                    //This can likely be removed for 7.2.9.0 keeping here to limit the scope of changes
                    for (int tries = 0; tries < MaxRetries; tries++)
                    {
                        try
                        {
                            if (_cancellationTokenSource.IsCancellationRequested)
                            {
                                LoadingResults = false;
                                return;
                            }

                            result = await _ballotReissuedListFacade.GetBallotReissuedList(_request);
                            break;
                        }
                        catch (Exception ex)
                        {
                            _essLogger.LogError(ex,
                                new Dictionary<string, string>
                                    { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
                            await Task.Delay(500, _cancellationTokenSource.Token);
                        }
                    }

                    if (result != null)
                    {
                        // overall list
                        BallotReissuedList = result.BallotReissuedListItemDtos.ToList();

                        int lineNumber = 0;
                        foreach (var item in BallotReissuedList)
                        {
                            if (_cancellationTokenSource.IsCancellationRequested)
                            {
                                LoadingResults = false;
                                return;
                            }
                            item.LineNumber = ++lineNumber;
                            _voterReissuedCount.Add(item.VoterKey);
                        }

                        VirtualBallotReissuedList = new VirtualList<BallotReissuedListItemDto>(BallotReissuedList);

                    }

                    RaisePropertyChanged("VoterCount");
                    RaisePropertyChanged("ReissuedBallotCount");
                    LoadingResults = false;
                }, _cancellationTokenSource.Token);
            }
            catch (Exception ex)
            {
                _essLogger.LogError(ex,
                    new Dictionary<string, string>
                        { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
            }
        }

        private void LoadTimeUnits()
        {
            Months = new ObservableCollection<TimeUnit>(TimeUnit.GetMonths());
            Years = new ObservableCollection<TimeUnit>(TimeUnit.GetYears(DateTime.Now.Year, DateTime.Now.Year - 10).OrderByDescending(x => x.UnitNumber));
            TimeStart = new ObservableCollection<TimeUnit>(TimeUnit.GetTimeRange(FirstStartHour, LastStartHour));
            TimeEnd = new ObservableCollection<TimeUnit>(TimeUnit.GetTimeRange(FirstEndHour, LastEndHour));

            SelectedMonth = Months.Where(m => m.UnitNumber == DateTime.Now.Month).FirstOrDefault();
            SelectedYear = Years.Where(y => y.UnitNumber == DateTime.Now.Year).FirstOrDefault();
            SelectedDay = Days.Where(d => d.UnitNumber == DateTime.Now.Day).FirstOrDefault();
            SelectedTimeStart = TimeStart.Where(h => h.UnitNumber == FirstStartHour).FirstOrDefault();
            SelectedTimeEnd = TimeEnd.Where(m => m.UnitNumber == LastEndHour).FirstOrDefault();
        }

        private void OnDateTimeChanged()
        {
            if (SelectedMonth != null && SelectedYear != null)
            {
                int selectedDay = -1;
                if (SelectedDay != null)
                {
                    selectedDay = SelectedDay.UnitNumber;
                }

                Days = new ObservableCollection<TimeUnit>(TimeUnit.GetDaysAvailableForGivenMonthAndYear(SelectedMonth.UnitNumber, SelectedYear.UnitNumber));

                int correctDayNumber;
                if (selectedDay == -1 || selectedDay > Days.Last().UnitNumber)
                {
                    //We reset the day only if it wasn´t previously selected OR if the selected day is greater than the greatest day for the current month and year (consider leap years)
                    correctDayNumber = 1;
                }
                else
                {
                    //Otherwise we keep the selected day
                    correctDayNumber = selectedDay;
                }

                SelectedDay = Days.Where(d => d.UnitNumber == correctDayNumber).FirstOrDefault();
            }
        }
        #endregion
    }
}
