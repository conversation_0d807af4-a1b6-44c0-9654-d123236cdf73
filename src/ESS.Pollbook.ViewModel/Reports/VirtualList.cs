using System;
using System.Collections;
using System.Collections.Generic;

namespace ESS.Pollbook.ViewModel.Reports
{
    /// <summary>
    /// Virtual lists are lists from which the content is loaded on demand.
    /// </summary>
    /// <remarks>
    /// Use visual lists if it is expensive to populate the list and only
    /// a subset of the list's elements are used. The virtual list uses an
    /// object generator to populate the list on demand.
    /// </remarks>
    /// <typeparam name="T">Objects that are stored inside the list.</typeparam>
    public class VirtualList<T> : IList<T>, IList where T : class
    {
        #region Internal attributes
        /// <summary>
        /// Object that is used to generate the requested objects.
        /// </summary>
        /// <remarks>
        /// This object can also hold a IMultipleObjectGenerator reference.
        /// </remarks>
        private readonly List<T> _listOfItems;

        /// <summary>
        /// Internal array that holds the cached items.
        /// </summary>
        private readonly T[] _cachedItems;

        #endregion

        #region Constructor

        /// <summary>
        /// Create the virtual list.
        /// </summary>
        /// <param name="listOfItems"></param>
        public VirtualList(List<T> listOfItems)
        {
            // Determine the number of items
            int maxItems = listOfItems.Count;

            // Save generator and items
            _listOfItems = listOfItems;
            _cachedItems = new T[maxItems];
        }
        #endregion

        #region IList<T> Members
        public int IndexOf(T item)
        {
            return _listOfItems.IndexOf(item);
        }

        public void Insert(int index, T item)
        {
            // VirtualList is a read-only collection.
        }

        public void RemoveAt(int index)
        {
            // VirtualList is a read-only collection.
        }

        public T this[int index]
        {
            get
            {
                // Cache item if it isn't cached already
                if (!IsItemCached(index))
                    CacheItem(index);

                // Return the cached object
                return _cachedItems[index];
            }
            set
            {
                // VirtualList is a read-only collection.
            }
        }
        #endregion

        #region ICollection<T> Members
        public void Add(T item)
        {
            // VirtualList is a read-only collection.
        }

        public void Clear()
        {
            // VirtualList is a read-only collection.
        }

        public bool Contains(T item)
        {
            return IndexOf(item) != -1;
        }

        public void CopyTo(T[] array, int arrayIndex)
        {
            _cachedItems.CopyTo(array, arrayIndex);
        }

        public int Count
        {
            get { return _cachedItems.Length; }
        }

        public bool IsReadOnly
        {
            get { return true; }
        }

        public bool Remove(T item)
        {
            return false;
        }
        #endregion

        #region IEnumerable<T> Members
        public IEnumerator<T> GetEnumerator()
        {
            return new VirtualEnumerator(this);
        }
        #endregion

        #region IList Members
        public int Add(object value)
        {
            return -1;
        }

        public bool Contains(object value)
        {
            return false;
        }

        public int IndexOf(object value)
        {
            try
            {
                int items = _cachedItems.Length;
                for (int index = 0; index < items; ++index)
                {
                    // Check if item is found
                    if (_cachedItems[index] != null)
                        if (_cachedItems[index].Equals(value))
                            return index;
                }

                // Item not found
                return -1;
            }
            catch (Exception)
            {
                return -1;
            }
        }

        public void Insert(int index, object value)
        {
            // VirtualList is a read-only collection.
        }

        public bool IsFixedSize
        {
            get { return true; }
        }

        public void Remove(object value)
        {
            // VirtualList is a read-only collection.
        }

        object IList.this[int index]
        {
            get { return this[index]; }
            set
            {
                // VirtualList is a read-only collection.
            }
        }
        #endregion

        #region ICollection Members
        public void CopyTo(Array array, int index)
        {
            _cachedItems.CopyTo(array, index);
        }

        public bool IsSynchronized
        {
            get { return false; }
        }

        public object SyncRoot
        {
            get { return this; }
        }
        #endregion

        #region IEnumerable Members
        IEnumerator IEnumerable.GetEnumerator()
        {
            return new VirtualEnumerator(this);
        }
        #endregion

        #region Internal helper methods required for caching

        private bool IsItemCached(int index)
        {
            // If the object is NULL, then it is empty
            try
            {
                return _cachedItems[index] != null;
            }
            catch
            {
                return false;
            }
        }

        #endregion

        public void CacheItem(int index)
        {
            // Obtain only a single object
            try
            {
                _cachedItems[index] = _listOfItems[index];
            }
            catch
            {
                // Failed to cache the item, presumably because of an index out of range. So it simply does not get cached.
            }
        }

        #region Internal IEnumerator implementation
        private class VirtualEnumerator : IEnumerator<T>
        {
            private readonly VirtualList<T> _collection;
            private int _cursor;

            public VirtualEnumerator(VirtualList<T> collection)
            {
                _collection = collection;
                _cursor = 0;
            }

            public T Current
            {
                get { return _collection[_cursor]; }
            }

            object IEnumerator.Current
            {
                get { return Current; }
            }

            public bool MoveNext()
            {
                // Check if we are behind
                if (_cursor == _collection.Count)
                    return false;

                if (_collection.Count > 1)
                    // Increment cursor
                    ++_cursor;
                return true;
            }

            public void Reset()
            {
                // Reset cursor
                _cursor = 0;
            }

            public void Dispose()
            {
                // NOP
            }
        }
        #endregion
    }
}
