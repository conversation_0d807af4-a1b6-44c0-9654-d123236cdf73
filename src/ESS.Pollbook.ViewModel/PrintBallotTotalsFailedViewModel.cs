using ESS.Pollbook.Core;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.NavigationParameters;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Facade.Reports;
using GalaSoft.MvvmLight.Messaging;
using System;
using System.Collections.Generic;

namespace ESS.Pollbook.ViewModel
{
    public class PrintBallotTotalsFailedViewModel : PrintFailedViewModelBase
    {
        private readonly IBallotTotalsFacade _ballotTotalsFacade;
        private readonly IAuditLogFacade _auditLogFacade;
        private readonly string _pageName;

        public PrintBallotTotalsFailedViewModel(IFrameNavigationService navigationService,
            IMessenger messengerService,
            IEssLogger essLogger,
            IBallotTotalsFacade ballotTotalsFacade,
            IAuditLogFacade auditLogFacade)
            : base(navigationService, messengerService, essLogger, auditLogFacade)
        {
            _ballotTotalsFacade = ballotTotalsFacade;
            _auditLogFacade = auditLogFacade;

            _pageName = _navigation.PageName(this.GetType().FullName);
        }

        protected override void Retry()
        {
            try
            {
                _auditLogFacade.AddToAuditLog(_pageName, $"'{RetryLabel}' button was activated.");
                var ballotTotalsReportData = (BallotTotalsReportParameter)(_navigation.Parameter);
                var printResponse = _ballotTotalsFacade.PrintBallotTotals(ballotTotalsReportData);
                if (printResponse != null &&
                    printResponse.PrinterErrorMessage != PrinterErrorMessages.PrinterNotConnected &&
                    printResponse.PrinterErrorMessage != PrinterErrorMessages.PrinterNotSelected &&
                    printResponse.PrinterErrorMessage != PrinterErrorMessages.PrinterOffline)
                {
                    _navigation.GoBack();
                }
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string>();
                logProps.Add("Action", "PrintBallotTotalsFailedViewModel.Retry");
                _essLogger.LogError(ex, logProps);
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }
    }
}
