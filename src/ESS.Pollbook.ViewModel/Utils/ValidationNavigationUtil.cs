using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Facade.Device;
using ESS.Pollbook.Facade.Maintenance;
using ESS.Pollbook.OAuth;
using ESS.Pollbook.ViewModel.Maintenance;
using ESS.Pollbook.Core.Messaging;
using GalaSoft.MvvmLight.Messaging;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using ESS.Pollbook.Core.CustomExceptions;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.StaticValues;

namespace ESS.Pollbook.ViewModel.Utils
{
   public class ValidationNavigationUtil : IValidationNavigationUtil
   {
      private readonly IFrameNavigationService _navigation;
      private readonly IMessenger _messenger;
      private readonly IEssLogger _essLogger;
      private readonly IDeviceFacade _deviceFacade;
      private readonly ILoadElectionFacade _loadElectionFacade;
      private bool _isFromManageElectionViewModel;
      private bool _showInvalidElection;
      private Type _targetViewModel;

      public ValidationNavigationUtil(IFrameNavigationService navigation, IMessenger messenger, IEssLogger essLogger, ILoadElectionFacade loadElectionFacade, IDeviceFacade deviceFacade)
      {
         _navigation = navigation;
         _messenger = messenger;
         _essLogger = essLogger;
         _loadElectionFacade = loadElectionFacade;
         _deviceFacade = deviceFacade;
      }

      public bool DoValidation(bool isFromManageElectionViewModel, Type targetViewModel = null)
      {
         _isFromManageElectionViewModel = isFromManageElectionViewModel;
         _targetViewModel = targetViewModel ?? typeof(LoginViewModel);

         var deviceValidation = new DeviceValidation();
         deviceValidation.ValidateDevice(_essLogger);

         switch (deviceValidation.DeviceStatus)
         {
            case DeviceStatuses.Unknown:
               return ValidateElection(deviceValidation);
            case DeviceStatuses.AccessDenied:
            case DeviceStatuses.Pending:
               return ValidateDevice(deviceValidation);
            default:
               return ValidateElection(deviceValidation);
         }
      }

      private bool ValidateDevice(DeviceValidation deviceValidation)
      {
         if (!deviceValidation.DeviceStatusesDisabled)
            return true;

         _showInvalidElection = false;
         const string msg = "Please contact your administrator to activate this device.";

         NavToViewModel(deviceValidation, msg, _showInvalidElection, _isFromManageElectionViewModel);

         return false;
      }

      private bool ValidateElection(DeviceValidation deviceValidation)
      {
         if (deviceValidation.IsElectionValid)
            return true;

         _showInvalidElection = true;
         var msg = deviceValidation.DeviceStatusNotFound ? "Please contact your administrator the database is invalid."
             : "Please contact your administrator for a new election database.";

         NavToViewModel(deviceValidation, msg, _showInvalidElection, _isFromManageElectionViewModel);

         return false;
      }

      public void NavigateToLockScreen()
      {
         const string msg = "Please contact your administrator to activate this device.";
         Application.Current.Dispatcher.Invoke(
             new ThreadStart(delegate
                 {
                    var m = new DeviceLockedMessage(message: msg, isElectionValid: true, showInvalidElection: _showInvalidElection, targetViewModel: typeof(LoginViewModel), isFromManageElection: _isFromManageElectionViewModel);
                    _messenger.Send(new StatusBarMessage(isVisible: false));
                    _navigation.NavigateTo<DeviceLockedViewModel>(m);
                 }
             ));
      }

      public async Task NavigateTo(Type targetViewModel)
      {
         _targetViewModel = targetViewModel;
         if (CheckTargetType(_targetViewModel, typeof(LoadElectionByUSBViewModel)))
         {
            _messenger.Send(new PQCLoadingMessage { Message = "Loading Election..." });
            await DownloadUsbFilesAsync();
         }
         else if (CheckTargetType(_targetViewModel, typeof(ConfigureElectionLandingViewModel)))
         {
            NavigateToConfigureElection();
         }
         else if (CheckTargetType(_targetViewModel, typeof(ManageElectionViewModel)))
         {
            NavigateToManageElection();
         }
         else if (CheckTargetType(_targetViewModel, typeof(LoginViewModel)))
         {
            NavigateToLogin();
         }
      }

      public bool CheckTargetType(Type originalType, Type testType)
      {
         return originalType.FullName == testType.FullName;
      }

      private void NavigateToConfigureElection()
      {
         _messenger.Send(new StatusBarMessage(isElectionInfoVisible: false, isSystemInfoVisible: true));
         _navigation.GoBackTo<MaintenanceViewModel>();
         _navigation.NavigateTo<ConfigureElectionLandingViewModel>(NavigationFrameEnum.ContextFrame, withVerticalEffect: true);
         _navigation.CloseModalDialogWindow();
      }

      private void NavigateToLogin()
      {
         _messenger.Send(new StatusBarMessage(isVisible: false));
         _messenger.Send(new VerifyPollPlaceMessage());
         _navigation.NavigateTo<LoginViewModel>();
      }

      private void NavigateToManageElection()
      {
         _messenger.Send(new StatusBarMessage(isElectionInfoVisible: false, isSystemInfoVisible: true));
         _navigation.GoBackTo<MaintenanceViewModel>();
         _navigation.NavigateTo<ManageElectionViewModel>(NavigationFrameEnum.ContextFrame, withVerticalEffect: true);
         _navigation.CloseModalDialogWindow();
      }

      private void NavToViewModel(DeviceValidation deviceValidation, string msg, bool showInvalidElection, bool isFromManageElectionViewModel)
      {
         Application.Current.Dispatcher.Invoke(
             new ThreadStart(delegate
                 {
                    var m = new DeviceLockedMessage(message: msg, isElectionValid: deviceValidation.IsElectionValid, showInvalidElection: showInvalidElection, isFromManageElection: isFromManageElectionViewModel, targetViewModel: _targetViewModel ?? typeof(LoginViewModel));
                    _messenger.Send(new StatusBarMessage(isVisible: false));
                    _navigation.NavigateTo<DeviceLockedViewModel>(m);
                 }
             ));
      }

      private async Task DownloadUsbFilesAsync()
      {
         _essLogger.LogInformation("Starting load election from USB");

         _navigation.CloseModalDialogWindow();
         _navigation.NavigateTo<ElectionLoadingViewModel>(NavigationFrameEnum.ContextFrame);

         await Task.Delay(2000);

         _messenger.Send(new PQCLoadingMessage() { Message = "Loading Election from USB..." });

         try
         {
            var loadResponse = await _loadElectionFacade.LoadElectionFromUSBAsync(SystemDetails.PQCPassword);
            if (loadResponse.IsSuccess)
            {
               // clear local test mode variable
               _deviceFacade.SaveTestMode(false);

               _loadElectionFacade.ClearLogs();
               _loadElectionFacade.ClearQueues();

               _navigation.NavigateTo<ElectionLoadedViewModel>(NavigationFrameEnum.ContextFrame);
            }
            else
            {
               var actionFailed = new ActionFailedDto()
               {
                  Title = "Load Election Failed",
                  Message = "Load Election Failed: Please remove the USB and plug it in and try again."
               };

               if (loadResponse.LocalException.GetType() == typeof(LoadElectionException))
               {
                  var loadFileTest = loadResponse.LocalException.Message.Contains("EXPRESSPOLL_LOAD");
                  actionFailed = CreateActionFailedForLoadElection(((LoadElectionException)loadResponse.LocalException).Code, loadFileTest);
               }

               _navigation.NavigateTo<ElectionLoadingFailedViewModel>(actionFailed, NavigationFrameEnum.ContextFrame);
            }
         }
         catch (Exception ex)
         {
            var logProps = new Dictionary<string, string> { { "Action", "DownloadUsbElectionWorkerAsync" } };
            _essLogger.LogError(ex, logProps);
         }
      }

      private ActionFailedDto CreateActionFailedForLoadElection(LoadElectionCode code, bool isExpresspollLoadTest)
      {
         var action = new ActionFailedDto()
         {
            Title="Load Election Failed", 
            Message = "Load Election Failed: Please remove the USB and plug it in and try again"
         };

         switch (code)
         {
            case LoadElectionCode.ZPFFTE:
            case LoadElectionCode.CNFC:
            case LoadElectionCode.DBFNF:
               return new ActionFailedDto()
               {
                  Title = UIText.LoadElectionFailedTitle,
                  Message = UIText.LoadElectionMessageFailed
               };

            case LoadElectionCode.DBINV:
            case LoadElectionCode.ZPFINV:
               return new ActionFailedDto()
               {
                  Title = UIText.LoadElectionFailedTitle,
                  Message = isExpresspollLoadTest ? UIText.LoadElectionMessageIncorrectPQC : UIText.LoadElectionMessageMismatchPQC
               };
         }

         return action;
      }
   }
}
