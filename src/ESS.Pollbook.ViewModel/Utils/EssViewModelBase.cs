using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Facade.AuditLog;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.Messaging;
using System.Windows;

namespace ESS.Pollbook.ViewModel.Utils
{
	public class EssViewModelBase : ViewModelBase
	{
		protected EssViewModelBase(IFrameNavigationService navigation, IMessenger messenger, IEssLogger essLogger, IAuditLogFacade auditLogFacade)
        {
            EssNav = navigation;
            EssMsg = messenger;
            EssLog = essLogger;
            EssAdt = auditLogFacade;
        }

        protected IFrameNavigationService EssNav { get; }

        protected IMessenger EssMsg { get; }

        protected IEssLogger EssLog { get; }

        protected IAuditLogFacade EssAdt { get; }

        protected string PageName => EssNav == null ? string.Empty : EssNav.PageName(GetType().FullName);

        /// <summary>
        /// This is currently used in one place as a means to support workflows and decisions based on
        /// navigation.  The issue at hand is that generics are compile time.  The derived class needs
        /// to pass itself in.  We can get the type of this.GetType() at runtime but that can not be
        /// used.  This needs to be thought out further.  Possibly this is removed and placed inside the
        /// view model as a special case.
        /// </summary>
        protected void NavigateHere<T>(object parameter, NavigationFrameEnum navigationFrame = NavigationFrameEnum.MainFrame, bool registerHistory = true, bool isRootPage = false, bool withVerticalEffect = false, bool suppressHorizontalEffect = true, bool useThreadSleep = false)
	        where T : ViewModelBase
        {
	        OnUnLoad(this, new RoutedEventArgs());
	        EssNav?.NavigateTo<T>(parameter, navigationFrame, registerHistory, isRootPage, withVerticalEffect, suppressHorizontalEffect, useThreadSleep); // make things happy
	        OnLoad(this, new RoutedEventArgs());
        }

        /// <summary>
        /// The parameter can not be part of the base simply because there is the chance
        /// that a view model might get multiple types of parameters.
        /// </summary>
        /// <typeparam name="TP"></typeparam>
        /// <returns></returns>
        protected TP GetNavParameter<TP>()
        {
	        if (EssNav?.Parameter is TP dto)
		        return dto;
	        return default;
        }

        /// <summary>
        /// Ran only the first time the view model is loaded, and will not run until Unload has fired.
        /// Occurs after screen is displayed.
        /// The reason for "FirstLoaded" is when the NavigateTo sends something here.  But in the case when
        /// the screen needs to Navigate
        /// </summary>
        protected event RoutedEventHandler ViewModelFirstLoaded;

        /// <summary>
        /// Ran each time the View model is loaded.  For example, a Navigate to an existing instantiated view model
        /// </summary>
        protected event RoutedEventHandler ViewModelLoaded;

        /// <summary>
        /// When WPF event does an unload. ViewModelFirstLoaded will now fire again.
        /// </summary>
        protected event RoutedEventHandler ViewModelUnloaded;

        /// <summary>
        /// Internal book to make sure FirstLoaded is not called again.
        /// </summary>
        private bool ViewModelFirstLoadCalled { get; set; }

        /// <summary>
        /// This is assigned in the View as the OnLoad Event
        /// Example:  Loaded += ((TheNameOfThisViewModel)DataContext).OnLoad;
        /// </summary>
        public void OnLoad(object sender, RoutedEventArgs e)
        {
            if (!ViewModelFirstLoadCalled && ViewModelFirstLoaded != null)
                ViewModelFirstLoaded(sender, e);
            ViewModelFirstLoadCalled = true;
            ViewModelLoaded?.Invoke(sender, e);
        }

        /// <summary>
        /// This is assigned in the View as the Unload Event
        /// Example:  Unloaded += ((TheNameOfThisViewModel)DataContext).UnLoad;
        /// </summary>
        public void OnUnLoad(object sender, RoutedEventArgs e)
        {
            ViewModelUnloaded?.Invoke(sender, e);
            ViewModelFirstLoadCalled = false;
        }
    }
}
