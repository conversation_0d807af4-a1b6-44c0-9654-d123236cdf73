using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Core.Messaging;
using ESS.Pollbook.ViewModel.Reports;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.Command;
using GalaSoft.MvvmLight.Messaging;
using System;
using System.Collections.Generic;
using System.Windows.Input;

namespace ESS.Pollbook.ViewModel.VoterExport
{
    public class VoterExportLoadedViewModel : ViewModelBase
    {
        private readonly IFrameNavigationService _navigation;
        private readonly IMessenger _messenger;
        private readonly IEssLogger _essLogger;
        private readonly IAuditLogFacade _auditLogFacade;

        private string PageName => _navigation.PageName(GetType().FullName);

        public ICommand BackCommand => new RelayCommand(Back);

        public string BackLabel => UIText.Back;

        private PollPlaceDto _pollPlaceDto;
        private VoterExportMessage.VoterExportBackPage _backPage;

        private string _successText;
        public string SuccessText
        {
            get => _successText;
            set => Set(ref _successText, value);
        }

        public VoterExportLoadedViewModel(IFrameNavigationService navigationService, IMessenger messengerService, IEssLogger essLogger, IAuditLogFacade auditLogFacade)
        {
            _navigation = navigationService;
            _messenger = messengerService;
            _essLogger = essLogger;
            _auditLogFacade = auditLogFacade;

            _pollPlaceDto = (PollPlaceDto)navigationService.Parameter;

            _messenger.Register<VoterExportMessage>(this, MessageHandler);
        }

        private void Back()
        {
            try
            {
                _auditLogFacade.AddToAuditLog(PageName, $"'{BackLabel}' button was activated.");


                switch (_backPage)
                {
                    case VoterExportMessage.VoterExportBackPage.FullVotedListReport:
                        _messenger.Send(new StatusBarMessage(isVisible: true));
                        _navigation.NavigateTo<FullVotedListReportViewModel>(NavigationFrameEnum.ContextFrame);
                        break;

                    case VoterExportMessage.VoterExportBackPage.PollingPlaceVoterListReport:
                        _messenger.Send(new StatusBarMessage(isVisible: true));
                        _navigation.NavigateTo<PollingPlaceVoterListReportViewModel>(NavigationFrameEnum.ContextFrame);
                        break;

                    case VoterExportMessage.VoterExportBackPage.BallotReissueReport:
                        _messenger.Send(new StatusBarMessage(isVisible: true));
                        _navigation.NavigateTo<BallotReissuedReportViewModel>(NavigationFrameEnum.ContextFrame);
                        break;

                    case VoterExportMessage.VoterExportBackPage.SpoiledBallotReport:
                        _messenger.Send(new StatusBarMessage(isVisible: true));
                        _navigation.NavigateTo<SpoiledBallotReportViewModel>(_pollPlaceDto, NavigationFrameEnum.ContextFrame);
                        break;

                    case VoterExportMessage.VoterExportBackPage.VerifySoftware:
                        _messenger.Send(new StatusBarMessage(isElectionInfoVisible: false, isSystemInfoVisible: true));
                        _navigation.NavigateTo<VerifySoftwareViewModel>(NavigationFrameEnum.ContextFrame, withVerticalEffect: true);
                        break;

                    default:
                        break;
                }
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string> { { "Action", "VoterExportLoadedViewModel.Back" } };
                _essLogger.LogError(ex, logProps);
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }

        private void MessageHandler(VoterExportMessage msg)
        {
            _backPage = msg.BackPage;

            switch (_backPage)
            {
                case VoterExportMessage.VoterExportBackPage.FullVotedListReport:
                    SuccessText = "Voted List Exported Successfully";
                    break;

                case VoterExportMessage.VoterExportBackPage.PollingPlaceVoterListReport:
                    SuccessText = "Voter List Exported Successfully";
                    break;

                case VoterExportMessage.VoterExportBackPage.BallotReissueReport:
                    SuccessText = "Ballot Reissued Report Exported Successfully";
                    break;

                case VoterExportMessage.VoterExportBackPage.SpoiledBallotReport:
                    SuccessText = "Spoiled Ballot Report Exported Successfully";
                    break;

                case VoterExportMessage.VoterExportBackPage.VerifySoftware:
                    SuccessText = "Software Verification Exported Successfully";
                    break;

                default:
                    break;
            }
        }
    }
}
