using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Core.Messaging;
using ESS.Pollbook.ViewModel.Voter;
using ESS.Pollbook.ViewModel.VoterSearch;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.Command;
using GalaSoft.MvvmLight.Messaging;
using System;
using System.Collections.Generic;
using System.Windows.Input;

namespace ESS.Pollbook.ViewModel.VerifyVoterId
{
    public class VerifyVoterIdExemptViewModel : ViewModelBase
    {
        private readonly IEssLogger _essLogger;
        private readonly IFrameNavigationService _navigation;
        private readonly IMessenger _messenger;
        private readonly IAuditLogFacade _auditLogFacade;

        private string PageName => _navigation.PageName(this.GetType().FullName);

        public ICommand BackCommand => new RelayCommand(Back);
        public ICommand NextCommand => new RelayCommand(Next);

        public string BackLabel => UIText.Back;
        public string NextLabel => UIText.Next;

        private bool _nextEnabled;
        private VoterDto _voter;

        public string PageTitle => VerifyVoterIdValues.ExemptTitle;
        public string LabelContent => VerifyVoterIdValues.ExemptContent;

        private int _toggle = -1;

        public bool ToggleNo
        {
            get => _toggle == 0;
            set
            {
                _toggle = 0;
                NextEnabled = true;
                RaisePropertyChanged();
                RaisePropertyChanged(nameof(ToggleYes));
            }
        }

        public bool ToggleYes
        {
            get => _toggle == 1;
            set
            {
                _toggle = 1;
                NextEnabled = true;
                RaisePropertyChanged();
                RaisePropertyChanged(nameof(ToggleNo));
            }
        }

        public bool NextEnabled
        {
            get => _nextEnabled;
            set
            {
                if (Set(ref _nextEnabled, value))
                    RaisePropertyChanged();
            }
        }


        #region Constructors
        public VerifyVoterIdExemptViewModel(
            IMessenger messenger,
            IFrameNavigationService navigation,
            IEssLogger essLogger,
            IAuditLogFacade auditLogFacade)
        {
            _messenger = messenger;
            _navigation = navigation;
            _essLogger = essLogger;
            _auditLogFacade = auditLogFacade;
            _messenger.Register<VoterSelectedMessage>(this, VoterSelected);
            _messenger.Register<VerifyVoterIdViewMessage>(this, MessageHandler);
        }
        #endregion

        #region Public Methods
        public void PageInitialized()
        {
            Form_Reset();
        }
        #endregion

        #region Private Methods
        private void Form_Reset()
        {
            _toggle = -1;
            NextEnabled = false;
            RaisePropertyChanged(nameof(ToggleYes));
            RaisePropertyChanged(nameof(ToggleNo));
            RaisePropertyChanged(nameof(NextEnabled));
        }

        private void VoterSelected(VoterSelectedMessage msg)
        {
            if (SystemConfiguration.ElectionConfiguration.EnableTexasWorkflow)
            {
                _voter = msg.Voter;
            }
        }

        private void MessageHandler(VerifyVoterIdViewMessage msg)
        {
            if (msg.Reset)
                Form_Reset();
        }

        private void Next()
        {
            try
            {
                _auditLogFacade.AddToAuditLog(PageName, $"'{NextLabel}' button was activated.");
                _messenger.Send(new VerifyVoterIdMessage { ExemptSelection = _toggle == 1 });

                if (ToggleNo)
                    _navigation.NavigateTo<VerifyVoterIdListAViewModel>(_voter, NavigationFrameEnum.ContextFrame, suppressHorizontalEffect: true);
                else if (ToggleYes)
                    NavigateToVoterViewModel(IssueBallotHelper.Voter.IsPreviouslyVoted);
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string> { { "Action", "VerifyVoterIdListAViewModel.Next" } };
                _essLogger.LogError(ex, logProps);
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }

        private void NavigateToVoterViewModel(bool isProvisional)
        {
            _messenger.Send(new VerifyVoterIdBallotTypeMessage(isProvisional: isProvisional));
            _navigation.NavigateTo<VoterViewModel>(_voter, NavigationFrameEnum.ContextFrame);
        }

        private void Back()
        {
            try
            {
                _auditLogFacade.AddToAuditLog(PageName, $"'{BackLabel}' button was activated.");
                _messenger.Send(new VoterSearchMessage());

                _navigation.NavigateTo<DashboardViewModel>(NavigationFrameEnum.MainFrame, false);
                _messenger.Send(new StatusBarMessage(isVisible: true));
                _navigation.GoBack();
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string>
                {
                    { "Action", "VerifyVoterIdListAViewModel.Back" }
                };
                _essLogger.LogError(ex, logProps);
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }
    }
    #endregion
}