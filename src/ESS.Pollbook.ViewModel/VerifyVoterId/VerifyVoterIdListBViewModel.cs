using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.Model.VoterEligibility;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.ViewModel.Affidavit;
using ESS.Pollbook.Core.Messaging;
using ESS.Pollbook.ViewModel.Voter;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.Command;
using GalaSoft.MvvmLight.Messaging;
using System;
using System.Collections.Generic;
using System.Windows.Input;

namespace ESS.Pollbook.ViewModel.VerifyVoterId
{
    public class VerifyVoterIdListBViewModel : ViewModelBase
    {
        private readonly IFrameNavigationService _navigation;
        private readonly IMessenger _messenger;
        private readonly IEssLogger _essLogger;
        private readonly IAuditLogFacade _auditLogFacade;

        private readonly string _pageName;

        private bool _nextEnabled;
        private VoterDto _voter;
        private EligibilityCalculationResult _result;

        public ICommand BackCommand => new RelayCommand(Back);
        public ICommand NextCommand => new RelayCommand(Next);

        public string BackLabel => UIText.Back;
        public string NextLabel => UIText.Next;

        public string PageTitle => VerifyVoterIdValues.ListBTitle;

        public string LabelContent => VerifyVoterIdValues.ListBContent;

        private int _toggle = -1;

        public bool ToggleNo
        {
            get => _toggle == 0;
            set
            {
                _toggle = 0;
                NextEnabled = true;
                RaisePropertyChanged();
                RaisePropertyChanged(nameof(ToggleYes));
            }
        }

        public bool ToggleYes
        {
            get => _toggle == 1;
            set
            {
                _toggle = 1;
                NextEnabled = true;
                RaisePropertyChanged();
                RaisePropertyChanged(nameof(ToggleNo));
            }
        }

        public bool NextEnabled
        {
            get => _nextEnabled;
            set
            {
                if (Set(ref _nextEnabled, value))
                    RaisePropertyChanged();
            }
        }


        #region Constructors
        public VerifyVoterIdListBViewModel(IMessenger messenger, IFrameNavigationService navigation, IEssLogger essLogger, IAuditLogFacade auditLogFacade)
        {
            _messenger = messenger;
            _navigation = navigation;
            _essLogger = essLogger;
            _auditLogFacade = auditLogFacade;
            _pageName = _navigation.PageName(this.GetType().FullName);
            _messenger.Register<VoterSelectedMessage>(this, VoterSelected);
            _messenger.Register<VerifyVoterIdViewMessage>(this, MessageHandler);
        }
        #endregion

        #region Public Methods
        public void PageInitialized()
        {
            Form_Reset();
        }
        #endregion

        #region Private Methods
        private void Form_Reset()
        {
            _toggle = -1;
            NextEnabled = false;
            RaisePropertyChanged(nameof(ToggleYes));
            RaisePropertyChanged(nameof(ToggleNo));
            RaisePropertyChanged(nameof(NextEnabled));
        }

        private void VoterSelected(VoterSelectedMessage msg)
        {
            if (SystemConfiguration.ElectionConfiguration.EnableTexasWorkflow)
            {
                _voter = msg.Voter;
                _result = msg.CalculationResult;
            }
        }

        private void MessageHandler(VerifyVoterIdViewMessage msg)
        {
            Form_Reset();
        }

        private void Next()
        {
            try
            {
                _auditLogFacade.AddToAuditLog(_pageName, $"'{NextLabel}' button was activated.");
                _messenger.Send(new VerifyVoterIdMessage { ListBSelection = _toggle == 1 });

                if (ToggleNo)
                {
                    _messenger.Send(new VoterSelectedMessage(_voter, _result));
                    _messenger.Send(new VerifyVoterIdBallotTypeMessage(isProvisional: true));
                    _navigation.NavigateTo<VoterViewModel>(_voter, NavigationFrameEnum.ContextFrame, suppressHorizontalEffect: true);
                }
                else if (ToggleYes)
                {
                    _navigation.NavigateTo<VoterReasonableImpedimentViewModel>(NavigationFrameEnum.ContextFrame, suppressHorizontalEffect: true);
                }
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string> { { "Action", "VerifyVoterIdListBViewModel.Next" } };
                _essLogger.LogError(ex, logProps);
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }

        private void Back()
        {
            try
            {
                _auditLogFacade.AddToAuditLog(_pageName, $"'{BackLabel}' button was activated.");
                _navigation.GoBack();
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string> { { "Action", "VerifyVoterIdListBViewModel.Back" } };
                _essLogger.LogError(ex, logProps);
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }
        #endregion
    }
}
