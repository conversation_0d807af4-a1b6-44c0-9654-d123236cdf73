using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Facade.PollbookDefinedText;
using ESS.Pollbook.ViewModel.Affidavit;
using ESS.Pollbook.Core.Messaging;
using ESS.Pollbook.ViewModel.VoterSignature;
using ESS.Pollbook.ViewModel.VoterVerification;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.Command;
using GalaSoft.MvvmLight.Messaging;
using System;
using System.Collections.Generic;
using System.Windows.Input;

namespace ESS.Pollbook.ViewModel.VerifyVoterId
{
    public class VerifyVoterRegistrationForNotInRosterViewModel : ViewModelBase
    {
        private readonly IEssLogger _essLogger;
        private readonly IFrameNavigationService _navigation;
        private readonly IMessenger _messenger;
        private readonly IAuditLogFacade _auditLogFacade;
        private readonly IPollbookDefinedTextFacade _definedTextFacade;

        private string PageName => _navigation.PageName(this.GetType().FullName);

        public ICommand BackCommand => new RelayCommand(Back);

        public ICommand NextCommand => new RelayCommand(Next);

        public string BackLabel => UIText.Back;
        public string NextLabel => UIText.Next;

        private bool _nextEnabled;

        public string PageTitle => VerifyVoterIdValues.VRCertTitle;

        public string LabelContent => _definedTextFacade.GetPollbookDefinedTextForLanguage("VerifyNotInRoster", "English");

        private int _toggle = -1;

        public bool ToggleNo
        {
            get => _toggle == 0;
            set
            {
                _toggle = 0;
                NextEnabled = true;
                RaisePropertyChanged();
                RaisePropertyChanged(nameof(ToggleYes));
            }
        }

        public bool ToggleYes
        {
            get => _toggle == 1;
            set
            {
                _toggle = 1;
                NextEnabled = true;
                RaisePropertyChanged();
                RaisePropertyChanged(nameof(ToggleNo));
            }
        }

        public bool NextEnabled
        {
            get => _nextEnabled;
            set
            {
                if (Set(ref _nextEnabled, value))
                    RaisePropertyChanged();
            }
        }


        #region Constructors
        public VerifyVoterRegistrationForNotInRosterViewModel(
            IMessenger messenger,
            IFrameNavigationService navigation,
            IEssLogger essLogger,
            IAuditLogFacade auditLogFacade,
            IPollbookDefinedTextFacade definedTextFacade)
        {
            _messenger = messenger;
            _navigation = navigation;
            _essLogger = essLogger;
            _auditLogFacade = auditLogFacade;
            _definedTextFacade = definedTextFacade;

            _messenger.Register<VerifyVoterIdViewMessage>(this, MessageHandler);
        }
        #endregion

        #region Public Methods
        public void PageInitialized()
        {
            Form_Reset();
        }
        #endregion

        #region Private Methods
        private void Form_Reset()
        {
            _toggle = -1;
            NextEnabled = false;
            RaisePropertyChanged(nameof(ToggleYes));
            RaisePropertyChanged(nameof(ToggleNo));
            RaisePropertyChanged(nameof(NextEnabled));
        }

        private void MessageHandler(VerifyVoterIdViewMessage msg)
        {
            if (msg.Reset)
                Form_Reset();
        }

        private void Next()
        {
            try
            {
                _auditLogFacade.AddToAuditLog(PageName, $"'{NextLabel}' button was activated.");
                _messenger.Send(new VerifyVoterIdMessage { NIR_VR_Verified = _toggle == 1 });

                if (ToggleNo)
                {
                    _navigation.NavigateTo<AffidavitTemplateViewModel>(NavigationFrameEnum.ContextFrame, suppressHorizontalEffect: true);
                }
                else
                {
                    IssueBallotHelper.RemoveAffidavitByAffidavitTypeName("NotInList");

                    if (IssueBallotHelper.Voter.SelectedAffidavits.Count > 0)
                    {
                        _messenger.Send(new AffidavitsMessage(IssueBallotHelper.Voter.SelectedAffidavits));
                        _navigation.NavigateTo<AffidavitTemplateViewModel>(NavigationFrameEnum.ContextFrame, suppressHorizontalEffect: true);
                    }
                    else
                        _navigation.NavigateTo<VoterSignatureViewModel>(NavigationFrameEnum.ContextFrame, suppressHorizontalEffect: true);
                }
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string>
                {
                    { "Action", "VerifyVoterRegistrationForNotInRosterViewModel.Next" }
                };
                _essLogger.LogError(ex, logProps);
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }

        private void Back()
        {
            try
            {
                _auditLogFacade.AddToAuditLog(PageName, $"'{BackLabel}' button was activated.");
                _messenger.Send(new VoterSearchMessage());

                _navigation.NavigateTo<DashboardViewModel>(NavigationFrameEnum.MainFrame, false);
                _messenger.Send(new StatusBarMessage(isVisible: true));
                _navigation.GoBackTo<VoterVerificationViewModel>(NavigationFrameEnum.ContextFrame);
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string>
                {
                    { "Action", "VerifyVoterRegistrationForNotInRosterViewModel.Back" }
                };
                _essLogger.LogError(ex, logProps);
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }
    }
    #endregion
}