using ESS.ELLEGO.ServiceBus.Core.Interfaces;
using ESS.Pollbook.Components.Repository.PollbookTransaction;
using ESS.Pollbook.Core.Commands;
using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.Messaging;
using ESS.Pollbook.Core.Model;
using ESS.Pollbook.Core.NavigationParameters;
using ESS.Pollbook.Core.SignatureHandler;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Core.Storage;
using ESS.Pollbook.Core.Utilities;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Facade.Configuration;
using ESS.Pollbook.Facade.IncrementalUpdates;
using ESS.Pollbook.Facade.Maintenance;
using ESS.Pollbook.Facade.RegionalResults;
using ESS.Pollbook.Hardware.Storage;
using ESS.Pollbook.ViewModel.HelpCenter;
using ESS.Pollbook.ViewModel.IncrementalUpdates;
using ESS.Pollbook.ViewModel.Pollworker;
using ESS.Pollbook.ViewModel.RegionalResults;
using ESS.Pollbook.ViewModel.Supervisor;
using ESS.Pollbook.ViewModel.Utils;
using ESS.Pollbook.ViewModel.WaitTime;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.Command;
using GalaSoft.MvvmLight.Messaging;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Threading;

namespace ESS.Pollbook.ViewModel
{
    public class OptionsViewModel : ViewModelBase
    {
        private readonly IFrameNavigationService _navigation;
        private readonly IMessenger _messenger;
        private readonly IIncrementalUpdatesFacade _incrementalUpdatesFacade;
        private readonly IAuthorizeRegionalResultsUserFacade _authorizeRegionalResultsUserFacade;
        private readonly IEssServiceBus _essServiceBus;
        private readonly IPollbookTransactionRepository _pollbookTransactionRepository;
        private readonly IMaintenanceFacade _maintenanceFacade;
        private readonly IEssLogger _essLogger;
        private readonly IAuditLogFacade _auditLogFacade;
        private readonly IPollbookConfigurationFacade _configurationFacade;
        private readonly string _pageName;

        public ICommandAsync IncrementalUpdateCommand => new CommandAsync(IncrementalUpdateAsync);
        public ICommandAsync ConfigurationUpdateCommand => new CommandAsync(ConfigurationUpdate);
        public ICommand PollWorkerManagementCommand => new RelayCommand(PollworkerManagement);
        public ICommandAsync RegionalResultsCommand => new CommandAsync(RegionalResults);
        public ICommand ToggleShowP2PCommand => new RelayCommand(ToggleShowPeer2Peer);
        public ICommand ToggleShowSystemInfoCommand => new RelayCommand(ToggleShowSystemInfo);
        public ICommand ToggleShowSignedInUserCommand => new RelayCommand(ToggleShowSignedInUser);
        public ICommand HelpCenterCommand => new RelayCommand(OpenHelpCenter);
        public ICommand ToggleShowHostConnectedCommand => new RelayCommand(ToggleShowHostConnected);
        public ICommand WaitTimeCommand => new RelayCommand(NavToWaitTime);
        public ICommand SignOutCommand => new RelayCommand(SignOut);
        public ICommand ToggleShowPollPlaceManagementCommand => new RelayCommand(ToggleShowPollPlaceManagement);

        public static string IncrementalUpdatesLabel => UIText.IncrementalUpdates;
        public static string ConfigurationUpdateLabel => UIText.ConfigurationUpdates;

        public static string SignOutLabel => UIText.SignOut;
        public static string WaitTimeLabel => UIText.WaitTime;
        public static string PollWorkerManagementLabel => UIText.PollWorkerManagement;
        public static string RegionalResultsLabel => UIText.RegionalResults;
        public static string HelpCenterLabel => UIText.HelpCenter;
        public string LastUpdatedStatus { get; private set; }

        public static string LastConfigurationUpdateInfo => !string.IsNullOrEmpty(ElectionSettings.ConfigurationVersion)
            ? $"Last Applied: {ElectionSettings.ConfigurationVersion}"
            : "No Updates Applied";

        private string _sdCardMessage;
        public string SdCardMessage
        {
            get => _sdCardMessage;
            private set => Set(ref _sdCardMessage, value);
        }

        public static string SignedInUser1 => CurrentUserInfo.LoggedInUser.Username;

        public static string SignedInUser2 => CurrentUserInfo.LoggedInUser2?.Username ?? string.Empty;

        public static bool DualUserSignOn => SystemConfiguration.ElectionConfiguration.DualLogin;

        public static bool WaitTimeEnabled => SystemConfiguration.ElectionConfiguration.WaitTimeEnabled;

        private string _waitTimeTokenText;
        public string WaitTimeTokenText
        {
            get => _waitTimeTokenText;
            private set => Set(ref _waitTimeTokenText, value);
        }

        private string _waitTimeMinutesText;
        public string WaitTimeMinutesText
        {
            get => _waitTimeMinutesText;
            private set => Set(ref _waitTimeMinutesText, value);
        }

        private bool _isHostDisconnected;

        public bool IsHostDisconnected
        {
            set => Set(ref _isHostDisconnected, value);
            get => _isHostDisconnected;
        }

        private string _peerToPeerMessage;
        public string PeerToPeerMessage
        {
            get => _peerToPeerMessage;
            set => Set(ref _peerToPeerMessage, value);
        }

        private ObservableCollection<Node> _nodes = new ObservableCollection<Node>();

        public ObservableCollection<Node> Nodes
        {
            get => _nodes;

            set => Set(ref _nodes, value);
        }

        #region SystemInfo
        public static string SystemInfoLabel => "System Information";

        private bool _showSystemInfo;
        public bool ShowSystemInfo
        {
            get => _showSystemInfo;
            private set => Set(ref _showSystemInfo, value);
        }

        private int _rotateChevronSystemInfo;
        public int RotateChevronSystemInfo
        {
            get => _rotateChevronSystemInfo;
            private set => Set(ref _rotateChevronSystemInfo, value);
        }

        private void ToggleShowSystemInfo()
        {
            ShowSystemInfo = !ShowSystemInfo;
            RotateChevronSystemInfo = (ShowSystemInfo) ? 90 : -90;
            RaisePropertyChanged(nameof(ShowSystemInfo));
            RaisePropertyChanged(nameof(RotateChevronSystemInfo));
        }
        #endregion // SystemInfo

        #region SignedInUser
        public static string SignedInUserMessage => SystemConfiguration.ElectionConfiguration.DualLogin ? "Signed in Users" : "Signed in User";

        private bool _showSignedInUser;
        public bool ShowSignedInUser
        {
            get => _showSignedInUser;
            private set => Set(ref _showSignedInUser, value);
        }

        private int _rotateChevronSignedInUser;
        public int RotateChevronSignedInUser
        {
            get => _rotateChevronSignedInUser;
            private set => Set(ref _rotateChevronSignedInUser, value);
        }

        private void ToggleShowSignedInUser()
        {
            ShowSignedInUser = !ShowSignedInUser;

            RotateChevronSignedInUser = (ShowSignedInUser) ? 90 : -90;
            RaisePropertyChanged(nameof(ShowSignedInUser));
            RaisePropertyChanged(nameof(RotateChevronSignedInUser));
        }
        #endregion //SignedInUser

        #region Peer2Peer
        private bool _peer2PeerEnabled;
        public bool Peer2PeerEnabled
        {
            get => _peer2PeerEnabled;
            set => Set(ref _peer2PeerEnabled, value);
        }

        private bool _showPeer2Peer;
        public bool ShowPeer2Peer
        {
            get => _showPeer2Peer;
            private set => Set(ref _showPeer2Peer, value);
        }

        private int _rotateChevronPeer2Peer;
        public int RotateChevronPeer2Peer
        {
            get => _rotateChevronPeer2Peer;
            private set => Set(ref _rotateChevronPeer2Peer, value);
        }

        private void ToggleShowPeer2Peer()
        {
            ShowPeer2Peer = !ShowPeer2Peer && SystemConfiguration.ElectionConfiguration.PeerSync;

            RotateChevronPeer2Peer = (ShowPeer2Peer) ? 90 : -90;
        }
        #endregion // SystemInfo

        private bool _isHostConnected;
        public bool IsHostConnected
        {
            get => _isHostConnected;
            set => Set(ref _isHostConnected, value);
        }

        #region ShowHostConnected
        private bool _showHostConnected;
        public bool ShowHostConnected
        {
            get => _showHostConnected;
            set => Set(ref _showHostConnected, value);
        }

        private int _rotateChevronHostConnected;
        public int RotateChevronHostConnected
        {
            get => _rotateChevronHostConnected;
            private set => Set(ref _rotateChevronHostConnected, value);
        }

        private void ToggleShowHostConnected()
        {
            ShowHostConnected = !ShowHostConnected;

            RotateChevronHostConnected = (ShowHostConnected) ? 90 : -90;
            RaisePropertyChanged(nameof(ShowHostConnected));
            RaisePropertyChanged(nameof(RotateChevronHostConnected));
        }
        #endregion

        #region ShowPollPlaceManagement

        private bool _enablePollPlaceManagementControl;

        public bool EnablePollPlaceManagementControl
        {
            get => _enablePollPlaceManagementControl;
            set => Set(ref _enablePollPlaceManagementControl, value);
        }
        public static string PollPlaceManagementLabel => "Poll Place Management";

        private bool _showPollPlaceManagement;
        public bool ShowPollPlaceManagement
        {
            get => _showPollPlaceManagement;
            set => Set(ref _showPollPlaceManagement, value);
        }

        private int _rotateChevronPollPlaceManagement;
        public int RotateChevronPollPlaceManagement
        {
            get => _rotateChevronPollPlaceManagement;
            private set => Set(ref _rotateChevronPollPlaceManagement, value);
        }
        private void ToggleShowPollPlaceManagement()
        {
            ShowPollPlaceManagement = !ShowPollPlaceManagement;

            RotateChevronPollPlaceManagement = (ShowPollPlaceManagement) ? 90 : -90;
            RaisePropertyChanged(nameof(ShowPollPlaceManagement));
            RaisePropertyChanged(nameof(RotateChevronPollPlaceManagement));
        }
        #endregion // ShowPollPlaceManagement

        public bool HasNodes => _nodes.Count > 0;

        #region HostConnected
        private string _hostConnectedMessage;
        public string HostConnectedMessage
        {
            get => _hostConnectedMessage;
            private set => Set(ref _hostConnectedMessage, value);
        }
        #endregion HostConnected

        public static string DeviceId => $"Device Name: {SystemDetails.MachineName}";

        public static string SerialNumber => $"Serial Number: {SystemDetails.DeviceSerialNumber}";
        public static string DbVersion =>
            $"Database Version: {DateTimeHelper.GetDisplayDateTime(SystemConfiguration.ElectionConfiguration.VoterDataCreateDate)}";

        public static string AppVersion => $"App Version: {SystemDetails.VersionNumber} ";

        public string Ssid => $"SSID: {_maintenanceFacade.GetNetworkSsid()}";

        public static bool EnablePollworkerManagement => SystemConfiguration.ElectionConfiguration.EnablePollWorkerManagement;
        public static bool EnableRegionalResults => SystemConfiguration.ElectionConfiguration.EnableRegionalResults;

        public static bool EnableHelpCenter => SystemConfiguration.ElectionConfiguration.EnableHelpCenter;
        public static bool ShowWaitTime => SystemConfiguration.ElectionConfiguration.WaitTimeEnabled;

        public OptionsViewModel(IFrameNavigationService navigationService,
                                IMessenger messengerService,
                                IIncrementalUpdatesFacade incrementalUpdatesFacade,
                                IEssLogger essLogger,
                                IPollbookTransactionRepository pollbookTransactionRepository,
                                IMaintenanceFacade maintenanceFacade,
                                IEssServiceBus essServiceBus,
                                IAuditLogFacade auditLogFacade,
                                IPollbookConfigurationFacade configurationFacade,
                                IAuthorizeRegionalResultsUserFacade authorizeRegionalResultsUserFacade)

        {
            _navigation = navigationService;
            _messenger = messengerService;
            _incrementalUpdatesFacade = incrementalUpdatesFacade;
            _essLogger = essLogger;
            _pollbookTransactionRepository = pollbookTransactionRepository;
            _maintenanceFacade = maintenanceFacade;
            _essServiceBus = essServiceBus;
            _auditLogFacade = auditLogFacade;
            _configurationFacade = configurationFacade;
            _authorizeRegionalResultsUserFacade = authorizeRegionalResultsUserFacade;

            _pageName = _navigation.PageName(this.GetType().FullName);
            _essServiceBus.NodeAdded += AddNewNode;
            _essServiceBus.NodeRemoved += RemoveNode;
            _essServiceBus.NodesCleared += NodesCleared;
            SystemDetails.HostSlowChanged += SystemDetails_HostSlowChanged;
            SystemDetails.HostConnectedChanged += HostConnectedChanged;

            _messenger.Register<SdCardChangedMessage>(this, SetSdCardMessage);
            _messenger.Register<OfflineIncrementalUpdateMessage>(this, OfflineIncrementalUpdatesHandler);
        }

        private async Task ConfigurationUpdate()
        {
            try
            {
                await _auditLogFacade.AddToAuditLogAsync(_pageName, $"'{ConfigurationUpdateLabel}' button was activated.");
                var modulesMsg = new ModuleTypesMessage(nameof(ModulesEnum.ExpressPoll));
                _messenger.Send(modulesMsg);
                HostAuthorizationMonitor.DelayChanges = false;
                _navigation.NavigateTo<ApplyingConfigurationUpdatesViewModel>(NavigationFrameEnum.ContextFrame, withVerticalEffect: true);
                var result = await _configurationFacade.CheckAndApplyConfigurationsUpdatesAsync();
                if (result == -1)
                {
                    _navigation.NavigateTo<ApplyingConfigurationUpdatesFailedViewModel>(NavigationFrameEnum.ContextFrame);
                }
                else
                {
                    _navigation.NavigateTo<ApplyingConfigurationUpdatesDoneViewModel>(result, NavigationFrameEnum.ContextFrame);
                }
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string>
                    { { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } };
                _essLogger.LogError(ex, logProps);
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }

        private void HostConnectedChanged(object sender, EventArgs e)
        {
            UpdateDisplayData();
        }

        private void SystemDetails_HostSlowChanged(object sender, EventArgs e)
        {
            UpdateDisplayData();
        }

        private void SignOut()
        {
            try
            {
                _auditLogFacade.AddToAuditLog(_pageName, $"'{SignOutLabel}' button was activated.");
                if (SystemConfiguration.ElectionConfiguration.DualLogout)
                {
                    if (SystemConfiguration.ElectionConfiguration.DualLogin)
                    {
                        _navigation.NavigateTo<DualLogoutViewModel>(NavigationFrameEnum.ModalDialogFrame, false, withVerticalEffect: true);
                        return;
                    }
                    else
                    {
                        _essLogger.LogError("Configuration error - dual logout is enabled, but dual login is not");
                    }
                }

                var signOutDto = new SignOutDto()
                {
                    PollPlace = LoggedInPollplaceInfo.LoggedInPollPlace,
                    Title = "Sign out of ExpressPoll?",
                    Message = "By selecting yes, you will be signed out."
                };

                _navigation.NavigateTo<SignOutViewModel>(signOutDto, NavigationFrameEnum.ModalFrame, false, withVerticalEffect: true);
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string>
                    { { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } };
                _essLogger.LogError(ex, logProps);
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }

        private void PollworkerManagement()
        {
            try
            {
                _auditLogFacade.AddToAuditLog(_pageName, $"'{PollWorkerManagementLabel}' button was activated.");
                if (SystemConfiguration.PollworkerConfiguration.EnableManagementPassword)
                {
                    SupervisorPwEntryParameter navParameter = new SupervisorPwEntryParameter()
                    {
                        TargetType = SupervisorPwEntryTargetType.PollworkerManagement
                    };
                    _navigation.NavigateTo<SupervisorPwEntryViewModel>(navParameter, NavigationFrameEnum.ModalDialogFrame, false);
                }
                else
                {
                    _navigation.NavigateTo<PollworkerManagementViewModel>(suppressHorizontalEffect: true);
                }
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string>
                    { { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } };
                _essLogger.LogError(ex, logProps);
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }

        private async Task RegionalResults()
        {
            try
            {
                await _auditLogFacade.AddToAuditLogAsync(_pageName, "RegionalResults button was activated.");
                switch (SystemConfiguration.ElectionConfiguration.EnableRegionalResults)
                {
                    case true when SystemConfiguration.ElectionConfiguration.EnableRegionalResultsCred:
                        _navigation.NavigateTo<RegionalResultsPasswordViewModel>(NavigationFrameEnum.ModalDialogFrame, false);
                        break;
                    case true:
                        _navigation.NavigateTo<RegionalResultsLoadingViewModel>(NavigationFrameEnum.ModalDialogFrame, false, withVerticalEffect: true);
                        break;
                }
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string> { { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } };
                _essLogger.LogError(ex, logProps);
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }

        private void NavToWaitTime()
        {
            try
            {
                _auditLogFacade.AddToAuditLog(_pageName, $"'{WaitTimeLabel}' button was activated.");
                _navigation.NavigateTo<WaitTimeViewModel>(NavigationFrameEnum.ContextFrame);
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string>
                    { { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } };
                _essLogger.LogError(ex, logProps);
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }

        private void OpenHelpCenter()
        {
            try
            {
                _auditLogFacade.AddToAuditLog(_pageName, $"'{HelpCenterLabel}' button was activated.");
                _navigation.NavigateTo<HelpCenterViewModel>(NavigationFrameEnum.ContextFrame);
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string>
                    { { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } };
                _essLogger.LogError(ex, logProps);
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }

        public async Task PageIsLoadedAsync()
        {
            try
            {
                IsHostConnected = SystemConfiguration.ElectionConfiguration.HostSync;
                Peer2PeerEnabled = SystemConfiguration.ElectionConfiguration.PeerSync;
                EnablePollPlaceManagementControl = ShowWaitTime || EnableHelpCenter || EnablePollworkerManagement || EnableRegionalResults;

                RotateChevronSystemInfo = ShowSystemInfo ? 90 : -90;
                RotateChevronSignedInUser = ShowSignedInUser ? 90 : -90;
                RotateChevronPeer2Peer = ShowPeer2Peer ? 90 : -90;
                RotateChevronPollPlaceManagement = ShowPollPlaceManagement ? 90 : -90;
                RotateChevronHostConnected = ShowHostConnected ? 90 : -90;

                RaisePropertyChanged(nameof(ShowSystemInfo));
                RaisePropertyChanged(nameof(ShowPeer2Peer));
                RaisePropertyChanged(nameof(ShowHostConnected));
                RaisePropertyChanged(nameof(ShowPollPlaceManagement));

                SetSdCardMessage(null);
                SetPeerToPeerMessage();
                UpdateDisplayData();
                if (SystemConfiguration.ElectionConfiguration.WaitTimeEnabled)
                {
                    await SetWaitTime();
                }
                await GetLastIncrementalUpdateAsync();
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string>
                    { { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } };
                _essLogger.LogError(ex, logProps);
            }
        }

        private void SetSdCardMessage(SdCardChangedMessage message)
        {
            SdCardMessage = SDCard.IsSdCardMounted ? "SD Card Connected" : "SD Card Not Connected";
        }

        private async Task SetWaitTime()
        {
            var lastWaitTimeTransaction = await _pollbookTransactionRepository.GetLastWaitTimeTransaction();
            var lastWaitTimeMinutes = await _pollbookTransactionRepository.GetLastWaitTimeMinutes();

            if (lastWaitTimeMinutes == null)
            {
                LoggedInPollplaceInfo.WaitTime = null;
            }
            else
            {
                LoggedInPollplaceInfo.WaitTime = new TimeSpan(0, (int)lastWaitTimeMinutes, 0);
            }

            bool haveActiveToken = lastWaitTimeTransaction.TransactionSubtype == "WaitTimeGenerated" && lastWaitTimeTransaction.Token != 0;
            string waitTimeTokenString = haveActiveToken ? lastWaitTimeTransaction.Token.ToString() : "- -";
            string waitTimeMinutesString = lastWaitTimeMinutes == null ? "- -" : LoggedInPollplaceInfo.WaitTimeDisplay;

            WaitTimeTokenText = $"Active Token: {waitTimeTokenString}";
            WaitTimeMinutesText = $"Wait Time: {waitTimeMinutesString}";
        }

        private void SetPeerToPeerMessage()
        {
            PeerToPeerMessage = (_nodes.Count > 0) ? "Peer to Peer Connected" : "Peer to Peer Not Connected";
            SystemDetails.IsPeerConnected = _nodes.Count > 0;
            _messenger.Send(new PeerToPeerConnectedStatusMessage());
        }

        private void UpdateDisplayData()
        {
            HostConnectedMessage = (SystemDetails.IsHostConnected && !SystemDetails.IsHostSlow) ? "Connected to Server" : "Not Connected to Server";
            IsHostDisconnected = !SystemDetails.IsHostConnected || SystemDetails.IsHostSlow;
        }

        private async Task IncrementalUpdateAsync()
        {
            try
            {
                await _auditLogFacade.AddToAuditLogAsync(_pageName, $"'{IncrementalUpdatesLabel}' button was activated.");
                var modulesMsg = new ModuleTypesMessage(nameof(ModulesEnum.ExpressPoll));
                _messenger.Send(modulesMsg);

                _navigation.NavigateTo<IncrementalUpdatesLoadingViewModel>(NavigationFrameEnum.ContextFrame, withVerticalEffect: true);

                await IncrementalUsbWorkerAsync();
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string>
                    { { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } };
                _essLogger.LogError(ex, logProps);
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }

        private async Task IncrementalUsbWorkerAsync()
        {
            try
            {
                HostSyncDetails.IsIncrementalUpdateInProcess = true;
                var localResponse = await _incrementalUpdatesFacade.DownloadIncrementalUpdatesUsbAsync();

                if (!localResponse.IsSuccess)
                {
                    await Application.Current.Dispatcher.BeginInvoke(DispatcherPriority.Normal, new Action(() =>
                        _navigation.NavigateTo<IncrementalUpdatesFailedViewModel>(NavigationFrameEnum.ContextFrame)));
                }
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string>
                    { { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } };
                _essLogger.LogError(ex, logProps);
            }
            finally
            {
                HostSyncDetails.IsIncrementalUpdateInProcess = false;
            }
        }

        private async Task GetLastIncrementalUpdateAsync()
        {
            DateTime? lastDateTime = null;

            try
            {
                lastDateTime = await _incrementalUpdatesFacade.GetLastUpdateDateTimeAsync();
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string>
                    { { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } };
                _essLogger.LogError(ex, logProps);
            }

            LastUpdatedStatus = lastDateTime != null ? $"Last Sync: {DateTimeHelper.GetDisplayDateTime((DateTime)lastDateTime)}" : "No Updates Applied";

            RaisePropertyChanged(nameof(LastUpdatedStatus));
        }

        private void RemoveNode(string address)
        {
            try
            {
                var nodeInfo = address.Split(' ');
                var deviceName = nodeInfo[0];

                var nodeToRemove = _nodes.FirstOrDefault(x => x.DeviceName == deviceName);

                if (!_nodes.Contains(nodeToRemove)) return;

                Application.Current.Dispatcher.BeginInvoke(DispatcherPriority.Loaded, new Action(() =>
                {
                    if (!_nodes.Remove(nodeToRemove))
                    {
                        var logProps = new Dictionary<string, string>
                            { { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } };
                        _essLogger.LogInformation("_nodes.Remove failed", logProps);
                    }
                    RaisePropertyChanged(nameof(Nodes));
                    RaisePropertyChanged(nameof(HasNodes));
                    SetPeerToPeerMessage();
                }
                ));
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string>
                    { { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } };
                _essLogger.LogError(ex, logProps);
            }
        }

        private void NodesCleared()
        {
            try
            {
                Application.Current.Dispatcher.BeginInvoke(DispatcherPriority.Loaded, new Action(() =>
                {
                    _nodes.Clear();
                    RaisePropertyChanged(nameof(Nodes));
                    RaisePropertyChanged(nameof(HasNodes));
                    SetPeerToPeerMessage();
                }));
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string>
                    { { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } };
                _essLogger.LogError(ex, logProps);
            }
        }

        private void AddNewNode(string address)
        {
            try
            {
                var nodeInfo = address.Split(' ');
                if (nodeInfo.Length < 2)
                {
                    _essLogger.LogError("OptionsViewModel received new node to add without name and address"); // Should never happen
                    return;
                }

                var nodeName = nodeInfo[0];

                if (nodeName == SystemDetails.MachineName) return; // Never add our own device name to the node list

                Application.Current.Dispatcher.BeginInvoke(DispatcherPriority.Loaded, new Action(() =>
                {
                    if (!_nodes.Any(n => n.DeviceName.Equals(nodeName))) // Don't add a node that is already there
                    {
                        var newNode = new Node { DeviceName = nodeName };
                        _nodes.Add(newNode);
                        _nodes.Sort(x => x.DisplayName);
                        RaisePropertyChanged(nameof(Nodes));
                        RaisePropertyChanged(nameof(HasNodes));
                        SetPeerToPeerMessage();
                    }
                }));
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string>
                    { { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } };
                _essLogger.LogError(ex, logProps);
            }
        }

        private async void OfflineIncrementalUpdatesHandler(OfflineIncrementalUpdateMessage msg)
        {
            var localResponse = msg.Response;

            try
            {
                if (localResponse.IsSuccess)
                {
                    await Application.Current.Dispatcher.BeginInvoke(DispatcherPriority.Normal, new Action(() =>
                        _navigation.NavigateTo<IncrementalUpdatesLoadedViewModel>(NavigationFrameEnum.ContextFrame)));

                    ZipStreamHandler.LoadSignatureZip(StorageLocator.DefaultDbLocation,
                        _essLogger); //Reload any new signature zip files that may have come down

                    _messenger.Send(new UpdateSystemStatsMessage());
                }
                else
                {
                    await Application.Current.Dispatcher.BeginInvoke(DispatcherPriority.Normal, new Action(() =>
                        _navigation.NavigateTo<IncrementalUpdatesFailedViewModel>(NavigationFrameEnum.ContextFrame)));
                }
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string>
                    { { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } };
                _essLogger.LogError(ex, logProps);
            }
        }
    }
}
