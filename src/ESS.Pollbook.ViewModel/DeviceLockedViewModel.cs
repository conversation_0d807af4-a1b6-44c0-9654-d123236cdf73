using ESS.Pollbook.Core.Commands;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Core.Storage;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Facade.PQC;
using ESS.Pollbook.Facade.User;
using ESS.Pollbook.OAuth;
using ESS.Pollbook.ViewModel.Maintenance;
using ESS.Pollbook.Core.Messaging;
using ESS.Pollbook.ViewModel.SystemControl;
using ESS.Pollbook.ViewModel.SystemStats;
using ESS.Pollbook.ViewModel.Utils;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.Command;
using GalaSoft.MvvmLight.Messaging;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Net.NetworkInformation;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;

namespace ESS.Pollbook.ViewModel
{
	public class DeviceLockedViewModel : ViewModelBase, IDisposable
	{
		private const int MinimumDisplayTime = 3000;
		private SemaphoreSlim _semaphore = new SemaphoreSlim(1, 1);
		private bool _disposed;

		private readonly IAuditLogFacade _auditLogFacade;
        private readonly IEssLogger _essLogger;
        private readonly IMessenger _messenger;
        private readonly IFrameNavigationService _navigation;
        private readonly IPQCFacade _pqcFacade;
        private readonly IValidationNavigationUtil _validationNavigationUtil;
        private readonly IUserFacade _userFacade;

        private string _deviceInformation;

        private bool _electionInfoVisible;

        private bool _isFromManageElection;

        private bool _isRetryButtonVisible;

        private bool _isRetryMessageVisible;

        private string _lockedMessage;

        private string _lockedTitle;

        private string _retryMessage;
        private Type _targetViewModel;

        private string PageName => _navigation.PageName(GetType().FullName);

        public ICommand DeviceLockedShutdownCommand => new RelayCommand(ShutdownDialogue);

        public ICommandAsync RetryCommand => new CommandAsync(RetryDeviceApprovalCommandAsync);

        public ICommand BackCommand => new RelayCommand(Back);

        public static string ShutDownLabel => UIText.ShutDown;

        public static string BackLabel => UIText.Back;

        private static string RetryLabel => UIText.Retry;

        public string LockedTitle
        {
	        get => _lockedTitle;
	        private set => Set(ref _lockedTitle, value);
        }

        public SystemStatsViewModel SystemStats { get; }

        public bool IsNetWorkAttached => NetworkInterface.GetIsNetworkAvailable();

        public string DeviceInformation
        {
            private set => Set(ref _deviceInformation, value);
            get => _deviceInformation;
        }

        public static string LaunchInformation =>
            string.Format($"Version: {AppVersion} Device Name: {SystemDetails.MachineName}");

        private static string AppVersion => SystemDetails.VersionNumber;

        public bool IsRetryButtonVisible
        {
            get => _isRetryButtonVisible;
            private set => Set(ref _isRetryButtonVisible, value);
        }

        public string LockedMessage
        {
            get => _lockedMessage;
            private set => Set(ref _lockedMessage, value);
        }

        public bool IsRetryMessageVisible
        {
	        get => _isRetryMessageVisible;
	        set => Set(ref _isRetryMessageVisible, value);
        }

        public string RetryMessage
        {
            get => _retryMessage;
            private set => Set(ref _retryMessage, value);
        }

        public bool ElectionInfoVisible
        {
            set => Set(ref _electionInfoVisible, value);
            get => _electionInfoVisible;
        }

        public DeviceLockedViewModel(IFrameNavigationService navigationService, IMessenger messengerService,
	        IEssLogger essLogger, IAuditLogFacade auditLogFacade, IValidationNavigationUtil validationNavigationUtil,
	        IPQCFacade pqcFacade, IUserFacade users)
        {
	        _navigation = navigationService;
	        _messenger = messengerService;
	        _essLogger = essLogger;
	        _auditLogFacade = auditLogFacade;
	        _validationNavigationUtil = validationNavigationUtil;
	        _pqcFacade = pqcFacade;
	        _userFacade = users;

	        NetworkChange.NetworkAvailabilityChanged += NetworkChange_NetworkAvailabilityChanged;

	        if (Application.Current.MainWindow != null)
		        SystemStats = ((MainWindowViewModel)Application.Current.MainWindow.DataContext)?.SystemStats;
        }

        public void PageLoaded()
        {
	        if (_navigation.Parameter is DeviceLockedMessage message)
		        DeviceLockedMessageHandler(message);
        }

        private void DeviceLockedMessageHandler(DeviceLockedMessage message)
        {
            //initialize these here in case of subsequent uses
            RetryMessage = string.Empty;
            IsRetryMessageVisible = false;

            _isFromManageElection = message.IsFromManageElection;
            _targetViewModel = message.TargetViewModel;

            Thread.Sleep(100);

            if (!message.ShowInvalidElection)
            {
                DeviceInformation =
                    $"Version: {AppVersion}  Device Name: {SystemDetails.MachineName}\nSerial Number: {SystemDetails.DeviceSerialNumber}";
                ElectionInfoVisible = false;
                IsRetryButtonVisible = true;
                LockedTitle = "Access Denied";
            }
            else
            {
                //currently election date - they asked for database date - need to verify what's wanted here.
                DeviceInformation =
	                $"Election Name: {SystemConfiguration.ElectionConfiguration.ElectionName}\nDatabase: {SystemConfiguration.ElectionConfiguration.VoterDataCreateDate:MM-dd-yyyy hh:mm tt}";
                ElectionInfoVisible = true;
                IsRetryButtonVisible = false;
                LockedTitle = "Invalid Election";
            }

            LockedMessage = message.Message;
        }

        private void NetworkChange_NetworkAvailabilityChanged(object sender, NetworkAvailabilityEventArgs e)
        {
            RaisePropertyChanged(nameof(IsNetWorkAttached));
        }

        private void ShutdownDialogue()
        {
            try
            {
                _auditLogFacade.AddToAuditLog(PageName, $"'{ShutDownLabel}' button was activated.");
                var shutdownDto = new ShutDownDto
                {
	                Title = "Are you sure you want to shut down this device?",
	                Message = string.Empty
                };

                _navigation.NavigateTo<ShutDownViewModel>(shutdownDto, NavigationFrameEnum.ModalFrame, false,
                    withVerticalEffect: true);
            }
            catch (Exception ex)
            {
	            _essLogger.LogError(ex,
		            new Dictionary<string, string>
			            { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
	            _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error,
		            SystemDetails.GenericErrorMessage));
            }
        }

        private void Back()
        {
            SystemDetails.IsPQCVerified = false;
            if (_isFromManageElection)
            {
                _navigation.NavigateTo<ManageElectionViewModel>();
                return;
            }

            _navigation.NavigateTo<LaunchViewModel>();
        }

        private async Task RetryDeviceApprovalCommandAsync()
        {
	        if (!await _semaphore.WaitAsync(0))
		        return;

	        var lastAttempt = DateTime.Now; // establish one lastAttempt datetime for log sync.

	        try
	        {
		        await _auditLogFacade.AddToAuditLogAsync(PageName, $"'{RetryLabel}' button was activated.");
		        //validate device. We're either new, and need to enter a pending state while the device is authorized
		        //in Connect, or the device is recognized and approved, or the device is denied access.

		        var deviceValidation = new DeviceValidation();
		        deviceValidation.ValidateDevice(_essLogger);

		        if (deviceValidation.DeviceStatusesDisabled)
		        {
			        RetryMessage = $"Last Attempt: {lastAttempt}";
			        IsRetryMessageVisible = true;
			        DeviceInformation =
				        $"Version: {AppVersion}  Device Name: {SystemDetails.MachineName}\nSerial Number: {SystemDetails.DeviceSerialNumber}";
			        ElectionInfoVisible = false;
			        IsRetryButtonVisible = true;
			        LockedTitle = "Access Denied";
		        }
		        else if (!deviceValidation.IsElectionValid)
		        {
			        RetryMessage = string.Empty;
			        IsRetryMessageVisible = false;
			        DeviceInformation =
				        $"Election Name: {SystemConfiguration.ElectionConfiguration.ElectionName}\nDatabase: {SystemConfiguration.ElectionConfiguration.VoterDataCreateDate:MM-dd-yyyy hh:mm tt}";
			        ElectionInfoVisible = true;
			        IsRetryButtonVisible = false;
			        LockedTitle = "Invalid Election";
			        SystemDetails.IsPQCVerified = false;
			        LockedMessage = deviceValidation.GetValidationMessage();
		        }
		        else
		        {
			        // On Reactivation (which is a host connected call) we need to
			        // reapply the users. Checking if host connected is rhetorical, but
			        // let's just make sure.
			        if (SystemDetails.IsHostConnected)
				        await _userFacade.GetUserUpdatesAsync();

			        //successful retry - see if we have an SD Card mounted and sync transactions if so.
			        if (SDCard.IsSdCardMounted)
			        {
				        _navigation.NavigateTo<SdCardCopyingViewModel>(NavigationFrameEnum.ModalFrame);
				        await SynchronizeTransactions();
			        }

			        _navigation.CloseModalWindow();
			        _navigation.GoBack();
			        _messenger.Send(new StatusBarMessage(false, true));
			        await _validationNavigationUtil.NavigateTo(_targetViewModel);
		        }
	        }
	        catch (Exception ex)
	        {
		        IsRetryMessageVisible = true;
		        RetryMessage =
			        $"Retry Failed.\r\n{lastAttempt.ToShortDateString()} {lastAttempt.ToShortTimeString()}";
		        _essLogger.LogError(ex,
			        new Dictionary<string, string>
				        { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
		        _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error,
			        SystemDetails.GenericErrorMessage));
	        }
	        finally
	        {
		        _semaphore.Release();
	        }
        }

        private async Task SynchronizeTransactions()
        {
            var sw = new Stopwatch();
            try
            {
                sw.Start();
                await _pqcFacade.SyncSecondaryTransactionLogAsync(SystemDetails.PQCPassword);
            }
            catch (Exception ex)
            {
	            _essLogger.LogError(ex,
		            new Dictionary<string, string>
			            { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
            }

            //apply delay on closing the modal if the modal has been display for less than the minimum display time value.
            var ms = sw.ElapsedMilliseconds;
            if (ms < MinimumDisplayTime)
                await Task.Delay(Convert.ToInt16(MinimumDisplayTime - ms));
            _navigation.CloseModalWindow(true);
        }


        public void Dispose()
        {
	        Dispose(true);
	        GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
	        if (_disposed)
		        return;

	        if (disposing)
	        {
		        _semaphore?.Dispose();
		        _semaphore = null;
	        }

	        _disposed = true;
        }
	}
}