using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.PollbookDefinedText;
using ESS.Pollbook.Core.Messaging;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.Messaging;

namespace ESS.Pollbook.ViewModel
{
    public class SignatureCanvasViewModel : ViewModelBase
    {
        private readonly IPollbookDefinedTextFacade _pollbookDefinedTextFacade;

        public string ClearSignatureLabel => UIText.ClearSignature;

        public string SelectedLanguage
        {
            get
            {
                return _pollbookDefinedTextFacade.GetPollbookDefinedTextCurrentLanguage();
            }
            set
            {
                _pollbookDefinedTextFacade.SetPollbookDefinedTextCurrentLanguage(value);

                RaisePropertyChanged();
                RaisePropertyChanged("SignatureHeaderText");
            }
        }

        public SignatureCanvasViewModel (IPollbookDefinedTextFacade pollbookDefinedTextFacade,
            IMessenger messengerService)
        {
            _pollbookDefinedTextFacade = pollbookDefinedTextFacade;

            messengerService.Register<LanguageSelectedMessage>(this, SetLanguage);
        }

        private void SetLanguage(LanguageSelectedMessage input)
        {
            SelectedLanguage = input.Language;
        }
    }
}
