using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.Model.Transactions;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.ViewModel.Maintenance;
using ESS.Pollbook.Core.Messaging;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.Command;
using GalaSoft.MvvmLight.Messaging;
using System;
using System.Collections.Generic;
using System.Windows.Input;

namespace ESS.Pollbook.ViewModel.IncrementalUpdates
{
    public class IncrementalUpdatesFailedViewModel : ViewModelBase
    {
        private readonly IFrameNavigationService _navigation;
        private readonly IMessenger _messenger;
        private readonly IEssLogger _essLogger;
        private readonly IAuditLogFacade _auditLogFacade;

        private string PageName => _navigation.PageName(this.GetType().FullName);

        public ICommand GoBackCommand => new RelayCommand(GoBack);

        public string BackLabel => UIText.Back;

        public string CameFrom { get; set; }

        public LocalResponse LocalResponse { get; set; }

        public string FailedMsg => LocalResponse?.LocalException?.Message ?? "No message received.";

        public string IncrementalUpdatesFailed => UIText.IncrementalUpdatesFailed;


        public IncrementalUpdatesFailedViewModel(IFrameNavigationService navigationService, IMessenger messengerService, IEssLogger essLogger, IAuditLogFacade auditLogFacade)
        {
            _navigation = navigationService;
            _messenger = messengerService;
            _essLogger = essLogger;
            _auditLogFacade = auditLogFacade;

            _messenger.Register<ModuleTypesMessage>(this, ModuleTypesMessage);
            _messenger.Register<LocalResponseMessage>(this, LocalResponseMessage);
        }

        private void GoBack()
        {
            try
            {
                _auditLogFacade.AddToAuditLog(PageName, $"'{BackLabel}' button was activated.");
                if (CameFrom.Equals(ModulesEnum.ExpressPoll.ToString(), StringComparison.CurrentCultureIgnoreCase))
                {
                    _messenger.Send(new StatusBarMessage(isVisible: true));
                    _navigation.NavigateTo<DashboardViewModel>();
                }
                else if (CameFrom.Equals(ModulesEnum.Maintainance.ToString(), StringComparison.CurrentCultureIgnoreCase))
                {
                    _messenger.Send(new StatusBarMessage(isElectionInfoVisible: false, isSystemInfoVisible: true));
                    _navigation.GoBackTo<SelectTaskTypeViewModel>(NavigationFrameEnum.ContextFrame);
                }
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string>();
                logProps.Add("Action", "IncrementalUpdatesFailedViewModel.GoBack");
                _essLogger.LogError(ex, logProps);
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }

        private void ModuleTypesMessage(ModuleTypesMessage message)
        {
            CameFrom = message.ModuleName;
        }

        private void LocalResponseMessage(LocalResponseMessage message)
        {
            LocalResponse = message.LocalResponse;
        }
    }
}
