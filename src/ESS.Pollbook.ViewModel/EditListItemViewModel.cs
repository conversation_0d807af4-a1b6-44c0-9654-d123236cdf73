using GalaSoft.MvvmLight;
using System;
using System.Collections.Generic;
using System.Linq;

namespace ESS.Pollbook.ViewModel
{
    public class EditListItemViewModel<T> : ViewModelBase where T : class
    {
        private T _model;

        public Func<string, string> ModelValidation;

        public Func<T, object> Property;

        public string Label { get; set; }

        private string _value;
        private string _errorText;
        private bool _isInvalid;
        private KeyValuePair<string, string> _selectedPossibleValue;
        private List<KeyValuePair<string, string>> _possibleValues;

        public string Value
        {
            get { return _value; }
            set
            {
                if (_value != value
                    && (string.IsNullOrEmpty(value) || value == "(   )    -    "))
                {
                    IsInvalid = false;
                }

                _value = value;

                if (ModelValidation != null)
                {
                    _errorText = ModelValidation.Invoke(_value);
                    IsInvalid = !string.IsNullOrEmpty(_errorText);
                }

                RaisePropertyChanged();

                if (ModelValidation != null)
                {
                    RaisePropertyChanged("ErrorText");
                    RaisePropertyChanged("IsInvalid");
                }
            }
        }

        public bool IsInvalid
        {
            get
            {
                return _isInvalid;
            }
            set
            {
                _isInvalid = value;

                RaisePropertyChanged();
            }
        }

        public KeyValuePair<string, string> SelectedPossibleValue
        {
            get { return _selectedPossibleValue; }
            set
            {
                _selectedPossibleValue = value;
                _value = _selectedPossibleValue.Key;

                RaisePropertyChanged();
                RaisePropertyChanged("Value");
            }
        }

        public List<KeyValuePair<string, string>> PossibleValues
        {
            get { return _possibleValues; }
            set
            {
                _possibleValues = value;
                RaisePropertyChanged();
            }
        }

        public T Model
        {
            get { return _model; }
            set
            {
                _model = value;
                _value = Property.Invoke(_model)?.ToString();

                if (_possibleValues != null)
                {
                    _selectedPossibleValue = _possibleValues.Where(x => x.Key == _value).FirstOrDefault();
                }

                RaisePropertyChanged();
                RaisePropertyChanged("Value");
                RaisePropertyChanged("SelectedPossibleValue");
            }
        }

        public string ErrorText
        {
            get
            {
                return _errorText;
            }
            set
            {
                _errorText = value;
                RaisePropertyChanged();
            }
        }

        public bool IsOptional { get; set; }

        public bool IsTextEnabled { get; set; }

        public EditListItemViewModel(T model, Func<T, object> modelProperty, bool isEnabled = true, Func<string, string> modelValidation = null)
        {
            Property = modelProperty;
            Model = model;
            ModelValidation = modelValidation;

            IsTextEnabled = isEnabled;
            IsInvalid = false;
        }
    }
}
