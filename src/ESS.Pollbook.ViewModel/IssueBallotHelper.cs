using System;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.Party;
using ESS.Pollbook.Core.Messaging;
using ESS.Pollbook.ViewModel.SelectBallotType;
using ESS.Pollbook.ViewModel.SelectParty;
using ESS.Pollbook.ViewModel.VoterBallot;
using GalaSoft.MvvmLight.Messaging;
using System.Linq;
using System.Threading.Tasks;
using ESS.Pollbook.ViewModel.Utils;

namespace ESS.Pollbook.ViewModel {
    public static class IssueBallotHelper {

        public static VoterDto Voter { get; set; }

        public static bool IsReissuingBallot { get; set; }

        private static BallotType _ballotType = BallotType.Unknown;

        public static BallotType SelectedBallotType {
            get {
                if (_ballotType != BallotType.Unknown) {
                    // Ballot type is known, so return it
                    return _ballotType;
                }

                var config = SystemConfiguration.ElectionConfiguration;
                var isEarlyVote = LoggedInPollplaceInfo.IsEarlyVotingPollPlace;

                if (config.ExpressVoteEnabled(isEarlyVote, Voter.VoterBallotDto.IsProvisional) && config.PaperBallotEnabled(isEarlyVote, Voter.VoterBallotDto.IsProvisional)) {
                    //could be either paper or BOD so unknown
                    _ballotType = BallotType.Unknown;
                } else if (config.ExpressVoteEnabled(isEarlyVote, Voter.VoterBallotDto.IsProvisional)) {
                    _ballotType = SystemConfiguration.ElectionConfiguration.DacEnabled ? BallotType.DAC : BallotType.ExpressVote;
                } else if (config.PaperBallotEnabled(isEarlyVote, Voter.VoterBallotDto.IsProvisional)) {
                    _ballotType = BallotType.Paper;
                } else {
                    _ballotType = BallotType.Unknown;
                }

                return _ballotType;
            }
            set => _ballotType = value;
        }

        public static async Task NavigateToNextPageAsync(IFrameNavigationService navigation, IPartyFacade partyService, IMessenger messenger, IPrintBallotUtil printBallotUtil) {
            var electionType = (ElectionTypeEnum)Enum.Parse(typeof(ElectionTypeEnum), SystemConfiguration.ElectionConfiguration.ElectionType, true);

            switch (electionType) {
                case ElectionTypeEnum.OpenPrimary:
                case ElectionTypeEnum.MixedPrimary:
                    messenger.Send(new ResetPartiesMessage());
                    navigation.NavigateTo<SelectPartyViewModel>(NavigationFrameEnum.ContextFrame, suppressHorizontalEffect: true);
                    break;

                case ElectionTypeEnum.ClosedPrimary:
                case ElectionTypeEnum.General:

                    Voter.PrecinctParty = (await partyService.GetPrecinctParties(Voter.PrecinctSplitId))
                        .FirstOrDefault(x => x.BallotStyleId == Voter.VoterBallotDto.BallotStyleId);

                    if (Voter.VoterBallotDto.IsProvisional) {
                        messenger.Send(new ResetPartiesMessage());
                        navigation.NavigateTo<SelectPartyViewModel>(NavigationFrameEnum.ContextFrame, suppressHorizontalEffect: true);
                    } else {
                        DefinedText.ResetPollbookDefinedTextCurrentLanguage();
                        if (SelectedBallotType == BallotType.Unknown) {
                            navigation.NavigateTo<SelectBallotTypeViewModel>(NavigationFrameEnum.ContextFrame, suppressHorizontalEffect: true);
                        } else {
                            Voter.NeedExpressVoteCard = SelectedBallotType == BallotType.ExpressVote;
                            switch (SelectedBallotType) {
                                case BallotType.Paper:
                                    await printBallotUtil.PrintBallotAsync(Voter);
                                    break;

                                case BallotType.DAC:
                                    if (SystemConfiguration.ElectionConfiguration.ExpressVotePrintingEnabled(
                                            LoggedInPollplaceInfo.IsEarlyVotingPollPlace,
                                            IssueBallotHelper.Voter.VoterBallotDto.IsProvisional))
                                    {
                                        navigation.NavigateTo<ActivationCardViewModel>(NavigationFrameEnum.ContextFrame, false, suppressHorizontalEffect: true);
                                        messenger.Send(new RegisterSmartCardEventsMessage());
                                        break;
                                    }
                                    messenger.Send(new IssueBallotMessage());
                                    navigation.NavigateTo<VoterIssueBallotViewModel>(NavigationFrameEnum.ContextFrame, false, suppressHorizontalEffect: true);
                                    break;

                                default:
                                    if (SystemConfiguration.ElectionConfiguration.ExpressVoteEnabled(
                                            LoggedInPollplaceInfo.IsEarlyVotingPollPlace,
                                            Voter.VoterBallotDto.IsProvisional) &&
                                        SystemConfiguration.ElectionConfiguration.ExpressVotePrintingEnabled(
                                            LoggedInPollplaceInfo.IsEarlyVotingPollPlace,
                                            Voter.VoterBallotDto.IsProvisional))
                                    {
                                        Voter.NeedExpressVoteCard = true;
                                        messenger.Send(new ExpressVoteCardActivationMessage());

                                        navigation.NavigateTo<ExpressVoteActivationCardViewModel>(
                                            NavigationFrameEnum.ContextFrame, false, suppressHorizontalEffect: true);
                                    }
                                    else
                                    {
                                        messenger.Send(new IssueBallotMessage());
                                        navigation.NavigateTo<VoterIssueBallotViewModel>(NavigationFrameEnum.ContextFrame, false, suppressHorizontalEffect: true);
                                    }
                                    break;
                            }
                        }
                    }
                    break;
            }
        }

        public static void RemoveAffidavitByAffidavitTypeName(string affidavitTypeName) {
            Voter.SelectedAffidavits.RemoveAll(a => a.Affidavit_Type_Name == affidavitTypeName);
        }
    }
}