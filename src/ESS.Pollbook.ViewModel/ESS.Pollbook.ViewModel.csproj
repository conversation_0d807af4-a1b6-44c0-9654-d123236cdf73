<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
	  <TargetFramework>net48</TargetFramework>
	  <Authors>Election Systems and Software</Authors>
	  <Company>Election Systems and Software</Company>
	  <Copyright>Election Systems and Software</Copyright>
	  <AssemblyVersion>7.2.9.0</AssemblyVersion>
	  <FileVersion>7.2.9.0</FileVersion>
	  <CodeAnalysisRuleSet>..\.sonarlint\express-poll-branchescsharp.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="LoadForms\**" />
    <Compile Remove="VoterEligibility\**" />
    <EmbeddedResource Remove="LoadForms\**" />
    <EmbeddedResource Remove="VoterEligibility\**" />
    <None Remove="LoadForms\**" />
    <None Remove="VoterEligibility\**" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="ConnectTicketCreateViewModel.cs" />
    <Compile Remove="ConnectTicketViewModel.cs" />
    <Compile Remove="PDCViewModel.cs" />
    <Compile Remove="Test.cs" />
    <Compile Remove="VoterSearch\VoterSearchViewModelOLD.cs" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="BarCodeAssemblies\code128.ttf" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="BarCodeAssemblies\code128.ttf">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="LibUsbDotNet" Version="2.2.29" />
    <PackageReference Include="ManagedNativeWifi" Version="2.7.1" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Client" Version="8.0.14" />
    <PackageReference Include="SSH.NET" Version="2024.2.0" />
    <PackageReference Include="System.Text.Json" Version="8.0.5" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ESS.ELLEGO.Rest\ESS.ELLEGO.Rest.csproj" />
    <ProjectReference Include="..\ESS.ELLEGO.ServiceBus.Core\ESS.ELLEGO.ServiceBus.Core.csproj" />
    <ProjectReference Include="..\ESS.Pollbook.Core\ESS.Pollbook.Core.csproj" />
    <ProjectReference Include="..\ESS.Pollbook.DataAccess\ESS.Pollbook.DataAccess.csproj" />
    <ProjectReference Include="..\ESS.Pollbook.Facade\ESS.Pollbook.Facade.csproj" />
    <ProjectReference Include="..\ESS.Pollbook.OAuth\ESS.Pollbook.OAuth.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="Microsoft.PointOfService">
      <HintPath>BarCodeAssemblies\Microsoft.PointOfService.dll</HintPath>
    </Reference>
    <Reference Include="PresentationCore">
      <HintPath>..\..\..\..\..\..\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\PresentationCore.dll</HintPath>
    </Reference>
    <Reference Include="PresentationFramework">
      <HintPath>..\..\..\..\..\..\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\PresentationFramework.dll</HintPath>
    </Reference>
    <Reference Include="System.Management" />
    <Reference Include="System.Printing">
      <HintPath>..\..\..\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Printing.dll</HintPath>
    </Reference>
    <Reference Include="WindowsBase">
      <HintPath>..\..\..\..\..\..\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\WindowsBase.dll</HintPath>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Compile Update="PDFResources.Designer.cs">
      <DependentUpon>PDFResources.resx</DependentUpon>
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="PDFResources.resx">
      <LastGenOutput>PDFResources.Designer.cs</LastGenOutput>
      <Generator>PublicResXFileCodeGenerator</Generator>
    </EmbeddedResource>
  </ItemGroup>

  <ItemGroup>
    <None Update="Resources\BOD6400-BODEX415_T1_85x11.dat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Resources\BOD6400-BODEX415_T1_85x14.dat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Resources\BOD6400-BODEX415_T1_85x17.dat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Resources\BOD6400-BODEX415_T1_85x19.dat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Resources\BOD6400_T1_85x11.dat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Resources\BOD6400_T1_85x14.dat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Resources\BOD6400_T1_85x17.dat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Resources\BOD6400_T1_85x19.dat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Resources\BOD9310_T1_85x11.dat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Resources\BOD9310_T1_85x14.dat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Resources\BOD9310_T2_85x11.dat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Resources\BOD9310_T2_85x14.dat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Resources\BOD9310_T2_85x17.dat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Resources\BOD9310_T2_85x19.dat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>