using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Facade.Pollworker;
using ESS.Pollbook.Core.Messaging;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.Command;
using GalaSoft.MvvmLight.Messaging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;

namespace ESS.Pollbook.ViewModel.Pollworker.Search
{
	public sealed class PollworkerClockOutReasonViewModel : ViewModelBase
	{
        private readonly IFrameNavigationService _navigation;
        private readonly IMessenger _messenger;
        private readonly IEssLogger _essLogger;
        private readonly IAuditLogFacade _auditLogFacade;
        private readonly IPollworkerFacade _pollworkerFacade;

        public ICommand BackCommand { get; }

        public ICommand NextCommand { get; }

        private TimeCardDto _timeCard;
        private PollworkerDto _pollworker;

        public static string ClockOutLabel => PollworkerHelper.GetClockPromptLabel(PollworkerStatus.ClockedIn).ToLower();

        public static string PageTitle => $"Poll Worker is {ClockOutLabel}.";
        public static string PageInstructions => $"To continue {ClockOutLabel}, please select a reason.";
        public static string BackLabel => UIText.Back;
        public static string NextLabel => UIText.Next;

        public string RbOne { get; private set; }
        public string RbTwo { get; private set; }
        public string RbThree { get; private set; }
        public string RbFour { get; private set; }

        public Visibility RbOneVisibility { get; set; }
        public Visibility RbTwoVisibility { get; set; }
        public Visibility RbThreeVisibility { get; set; }
        public Visibility RbFourVisibility { get; set; }

        private bool[] _modeArray;

        public bool[] ModeArray
        {
	        get => _modeArray;
	        private set => Set(ref _modeArray, value);
        }

        private int SelectedMode => Array.IndexOf(ModeArray, true);

        private bool _isNextButtonEnabled;
        public bool IsNextButtonEnabled
        {
            get => _isNextButtonEnabled;
            set => Set(ref _isNextButtonEnabled, value);
        }

        public PollworkerClockOutReasonViewModel(IFrameNavigationService navigation,
            IMessenger messenger,
            IEssLogger essLogger,
            IAuditLogFacade auditLogFacade,
            IPollworkerFacade pollworkerFacade)
        {
            _navigation = navigation;
            _essLogger = essLogger;
            _messenger = messenger;
            _auditLogFacade = auditLogFacade;
            _pollworkerFacade = pollworkerFacade;

            BackCommand = new RelayCommand(Back);
            NextCommand = new RelayCommand(Next);

            LoadActivities();
        }

        public async Task OnPageLoaded()
        {
            IsNextButtonEnabled = false;
            ModeArray = new[] { false, false, false, false };

            if (_navigation.Parameter is TimeCardDto timeCard)
            {
	            _timeCard = timeCard;
	            _pollworker = await _pollworkerFacade.GetPollworkerAsync(timeCard.PollworkerGUID);
            }
        }

        private void LoadActivities()
        {
            RbOneVisibility = Visibility.Hidden;
            RbTwoVisibility = Visibility.Hidden;
            RbThreeVisibility = Visibility.Hidden;
            RbFourVisibility = Visibility.Hidden;

            var activities = Task.Run(async () => await _pollworkerFacade.GetActivitiesAsync(false)).Result.ToList();

            for (int i = 0; i < activities.Count; i++)
            {
                var content = activities[i].Name;
                switch (i)
                {
	                case 0:
		                RbOne = content;
		                RbOneVisibility = Visibility.Visible;
		                break;
	                case 1:
		                RbTwo = content;
		                RbTwoVisibility = Visibility.Visible;
		                break;
	                case 2:
		                RbThree = content;
		                RbThreeVisibility = Visibility.Visible;
		                break;
	                case 3:
		                RbFour = content;
		                RbFourVisibility = Visibility.Visible;
		                break;
	                default:
		                // we have more options than we have spots?
		                _essLogger.LogError("PollworkerClockOutReason: # of reasons exceed the # of radio buttons shown.");
		                break;
                }
            }
        }

        private void Back()
        {
            try
            {
	            _auditLogFacade.AddToAuditLog(_navigation.PageName(GetType().FullName), $"'{BackLabel}' button was activated.");
	            _messenger.Send(new StatusBarMessage(isVisible: true));
                _navigation.NavigateTo<PollworkerDetailViewModel>(_pollworker, NavigationFrameEnum.ContextFrame);
            }
            catch (Exception ex)
            {
	            _essLogger.LogError(ex, new Dictionary<string, string> { { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } });
	            _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }

        private void Next()
        {
            try
            {
	            _auditLogFacade.AddToAuditLog(_navigation.PageName(GetType().FullName), $"'{NextLabel}' button was activated.");
	            _timeCard.EndTime = DateTime.UtcNow;
	            _timeCard.EndTimeActivityId = SelectedMode + 1;
                var successful = Task.Run(async () => await _pollworkerFacade.SaveTimeCardAsync(_timeCard)).Result;
                if (!successful)
	                return;

                _pollworker.LastEndTime = _timeCard.EndTime;
                _pollworker.LastStartTime = null;
                _pollworker.LastTimeCardGuid = _timeCard.TimeCardGuid;

                _navigation.NavigateTo<PollworkerActionConfirmationViewModel>(new PollworkerActionConfirmationDto()
	                {
		                ConfirmationText = $"{_pollworker.LastName}, {_pollworker.FirstName} has {PollworkerHelper.GetStatusNameLabel(PollworkerStatus.ClockedOut).ToLower()}.",
		                ConfirmationInstructions = _timeCard.EndTime.Value.ToLocalTime().ToString("MM/dd/yyyy hh:mm tt"),
		                Destination = nameof(PollworkerManagementViewModel),
		                NavigationParameter = _pollworker
	                },
	                NavigationFrameEnum.ContextFrame);
            }
            catch (Exception ex)
            {
	            _essLogger.LogError(ex, new Dictionary<string, string> { { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } });
	            _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }

        public void rbg_Checked(object sender, RoutedEventArgs e)
        {
	        IsNextButtonEnabled = ModeArray.Any(x => x);
        }
    }
}
