using ESS.Pollbook.Core.Commands;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.Model;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Facade.Pollworker;
using ESS.Pollbook.Core.Messaging;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.Command;
using GalaSoft.MvvmLight.Messaging;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using System.Windows.Input;
using System.Globalization;

namespace ESS.Pollbook.ViewModel.Pollworker.Search
{
	public sealed class PollworkerSearchViewModel : ViewModelBase
	{
        #region properties
        private readonly IFrameNavigationService _navigation;
        private readonly IMessenger _messenger;
        private readonly IEssLogger _essLogger;
        private readonly IAuditLogFacade _auditLogFacade;
        private readonly IPollworkerFacade _pollworkerFacade;

        private readonly NotifyTaskCompletion<PollworkerSearchResponse> PollworkerSearchTaskResults = new NotifyTaskCompletion<PollworkerSearchResponse>();

        public delegate void OnLastNameFocusHandler();

        public event OnLastNameFocusHandler LastNameFocus;

        public delegate void OnSearchSetEventHandler();
        public event OnSearchSetEventHandler OnSearchSet;

        private readonly string _pageName;

        private const string DateFormat = "MMddyyyy";

        private List<PollworkerDto> _pollworkers;

        public List<PollworkerDto> Pollworkers
        {
            get => _pollworkers;
            private set
            {
                Set(ref _pollworkers, value);
                if (_pollworkers == null || _pollworkers?.Count == 0)
                    PollworkerSearchResultStatus = PollworkerSearchResultStatusEnum.NoResults;
                else
                    PollworkerSearchResultStatus = _pollworkers?.Count == 1 ? PollworkerSearchResultStatusEnum.Result : PollworkerSearchResultStatusEnum.Results; // Result vs Results
            }
        }

        private bool _hasSearched;
        private bool _initialLoad;

        public ICommand ClearCommand => new RelayCommand(ResetSearch);

        public ICommand BackCommand => new RelayCommand(GoBack);

        public ICommand SelectPollworkerCommand => new RelayCommand<object>(async (param) => await PollworkerSelected(param));

        public ICommand AddPollworkerCommand => new RelayCommand(AddPollworker);

        public string ClearAllLabel => UIText.ClearAll;

        public string BackLabel => UIText.Back;
        private string PrecinctLabel => DefinedText.PollbookDefinedTextDtos.Find(n => n.PollbookDefinedTextLanguage.Equals("English") && n.PollbookDefinedTextName.Equals("Precinct"))?.PollbookDefinedTextValue;
        public string ByPrecinctLabel => $"By {PrecinctLabel}";

        public string CountyLabel => DefinedText.PollbookDefinedTextDtos.Find(n => n.PollbookDefinedTextLanguage.Equals("English") && n.PollbookDefinedTextName.Equals("County"))?.PollbookDefinedTextValue;
        public string ByCountyLabel => $"By {CountyLabel}";

        public string Title => "Matching Results";

        public delegate void FocusLastNameEventHandler();
        public event FocusLastNameEventHandler FocusLastName; // used in code behind

        private bool _filterChanging;

        private bool _isLoading;
        public bool IsLoading
        {
            get => _isLoading;
            set
            {
                Set(ref _isLoading, value);
                RaisePropertyChanged(nameof(DisplaySearchResults));
                RaisePropertyChanged(nameof(ShowAddPollworker));
            }
        }

        public bool ShowAddPollworker =>
            (PollworkerSearchResultStatus == PollworkerSearchResultStatusEnum.NoResults ||
             PollworkerSearchResultStatus == PollworkerSearchResultStatusEnum.Result ||
             PollworkerSearchResultStatus == PollworkerSearchResultStatusEnum.Results) &&
            _hasSearched &&
            !IsLoading;

        public bool DisplaySearchResults
        {
            get
            {
                if (IsLoading)
                    return false;

                return LastNameSearchTerm?.Length > 0 || FirstNameSearchTerm?.Length > 0 || DobSearchTerm?.Length == 4 || DobSearchTerm?.Length == 8 || SearchFilter == SearchFilterEnum.PollingPlaceId;
            }
        }

        private PollworkerSearchResultStatusEnum _pollworkerSearchResultStatus = PollworkerSearchResultStatusEnum.Initial;

        public PollworkerSearchResultStatusEnum PollworkerSearchResultStatus
        {
            get => _pollworkerSearchResultStatus;
            set => Set(ref _pollworkerSearchResultStatus, value);
        }

        private string _lastNameSearchTerm = string.Empty;
        public string LastNameSearchTerm
        {
            get => _lastNameSearchTerm;
            set
            {
                try
                {
                    Set(ref _lastNameSearchTerm, value);
                    RaisePropertyChanged(nameof(DisplaySearchResults));
                    PollworkerSearch();

                    if (!string.IsNullOrEmpty(_lastNameSearchTerm))
                        _auditLogFacade.AddToAuditLog(_pageName, $"Entered Last Name search term: '{_lastNameSearchTerm}'.");
                }
                catch (Exception ex)
                {
                    _essLogger.LogError(ex, new Dictionary<string, string> { { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } });
                    _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
                }
            }
        }

        private string _firstNameSearchTerm = string.Empty;
        public string FirstNameSearchTerm
        {
            get => _firstNameSearchTerm;
            set
            {
                try
                {
                    Set(ref _firstNameSearchTerm, value);
                    RaisePropertyChanged(nameof(DisplaySearchResults));
                    PollworkerSearch();

                    if (!string.IsNullOrEmpty(_firstNameSearchTerm))
                        _auditLogFacade.AddToAuditLog(_pageName, $"Entered First Name search term: '{_firstNameSearchTerm}'.");
                }
                catch (Exception ex)
                {
	                _essLogger.LogError(ex, new Dictionary<string, string> { { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } });
	                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
                }
            }
        }

        private string _dobSearchTerm = string.Empty;

        public string DobSearchTerm
        {
            get => _dobSearchTerm;
            set
            {
                try
                {
                    //filter out mask
                    value = value?.Replace("-", "").Replace(" ", "");
                    if (Set(ref _dobSearchTerm, value) && string.IsNullOrEmpty(_dobSearchTerm))
                    {
                        _auditLogFacade.AddToAuditLog(_pageName, $"Entered Date Of Birth search term: '{_dobSearchTerm}'.");
                        PollworkerSearch();
                        return;
                    }

                    if (_dobSearchTerm.Length < 8)
                        return;

                    PollworkerSearch();
                    RaisePropertyChanged(nameof(DisplaySearchResults));
                }
                catch (Exception ex)
                {
	                _essLogger.LogError(ex, new Dictionary<string, string> { { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } });
	                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
                }
            }
        }

        private SearchFilterEnum _searchFilter = SearchFilterEnum.PollingPlaceId;

        public SearchFilterEnum SearchFilter
        {
            get => _searchFilter;
            set
            {
                try
                {
                    if (_filterChanging || _searchFilter == value)
                        return;

                    _filterChanging = true;
                    Set(ref _searchFilter, value);
                    RaisePropertyChanged(nameof(DisplaySearchResults));
                    PollworkerSearch();
                    _filterChanging = false;
                }
                catch (Exception ex)
                {
                    _essLogger.LogError(ex, new Dictionary<string, string> { { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } });
                    _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
                }
            }
        }
        #endregion properties

        public PollworkerSearchViewModel(IFrameNavigationService navigationService,
            IMessenger messengerService,
            IEssLogger essLogger,
            IAuditLogFacade auditLogFacade,
            IPollworkerFacade pollworkerFacade)
        {
            _navigation = navigationService;
            _messenger = messengerService;
            _essLogger = essLogger;
            _auditLogFacade = auditLogFacade;
            _pollworkerFacade = pollworkerFacade;

            _pageName = _navigation?.PageName(GetType().FullName);

            PollworkerSearchTaskResults.PropertyChanged += OnPollworkerSearchTaskCompletion;

            _hasSearched = false;
        }

        public void PollworkerSearch_Loaded()
        {
            if (_navigation.Parameter as bool? == true)
                ClearFields();

            LastNameFocus?.Invoke();
            PollworkerSearch();
        }

        private void PollworkerSearch()
        {
            if (_initialLoad)
                return;

            try
            {
                DateTime? dob = null;

                if (!string.IsNullOrWhiteSpace(_dobSearchTerm) && _dobSearchTerm.Length == 8)
                {
                    if (_dobSearchTerm != "00000000" && DateTime.TryParseExact(_dobSearchTerm, DateFormat,
                            CultureInfo.InvariantCulture,
                            DateTimeStyles.None,
                            out var temp))
                    {
                        dob = temp;
                    }
                    else
                    {
                        _essLogger.LogWarning("User entered an invalid DOB - using a future date as a fallback.");

                        //Hack! User have entered the complete DOB, but invalid in terms of month / day. 
                        // In this case, we need to show "No result" instead of not triggering the search. So trigger the search with 
                        // Future date as DOB, so you will get No Result always. 
                        dob = DateTime.Now.AddYears(1);
                    }
                }

                _essLogger.LogInformation("NameSearchTask Initiated");

                var doPollPlaceSearch = SearchFilter == SearchFilterEnum.PollingPlaceId;

                if ((!string.IsNullOrEmpty(FirstNameSearchTerm) && FirstNameSearchTerm?.Length > 0) ||
                    (!string.IsNullOrEmpty(LastNameSearchTerm) && LastNameSearchTerm?.Length > 0) ||
                    dob != null ||
                    doPollPlaceSearch)
                {
                    _essLogger.LogInformation($"PollworkerSearch request LastName  : {LastNameSearchTerm}");
                    _essLogger.LogInformation($"PollworkerSearch request FirstName : {FirstNameSearchTerm}");
                    _essLogger.LogInformation($"PollworkerSearch request DOB       : {dob?.ToString()}");

                    PollworkerSearchResultStatus = PollworkerSearchResultStatusEnum.Searching;
                    IsLoading = true;
                    PollworkerSearchTaskResults.ExecuteTask(_pollworkerFacade.SearchPollworkerAsync(SearchFilter == SearchFilterEnum.PollingPlaceId, FirstNameSearchTerm, LastNameSearchTerm, dob));
                }
                else
                {
                    //clear "Add Pollworker" UI
                    Pollworkers = null;
                    _hasSearched = false;
                    RaisePropertyChanged(nameof(ShowAddPollworker));

                    //reset search result message
                    PollworkerSearchResultStatus = PollworkerSearchResultStatusEnum.Initial;
                }
            }
            catch (Exception ex)
            {
                _essLogger.LogError(ex, new Dictionary<string, string> { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }

        private void OnPollworkerSearchTaskCompletion(object sender, PropertyChangedEventArgs e)
        {
            try
            {
                if (!e.PropertyName.Equals("Result") || PollworkerSearchTaskResults.Result == null)
                {
                    IsLoading = false;
                    return;
                }

                // Search Terms
                Pollworkers = PollworkerSearchResponse.ConvertToPollworkerDto(PollworkerSearchTaskResults.Result.PollWorkersInfo).OrderBy(f => f.LastName).ThenBy(f => f.FirstName).ToList();
                PollworkerHelper.AddStatusDisplayNameToPollworkers(Pollworkers);
                _essLogger.LogInformation("Pollworker search Completed");

                _hasSearched = true;

                IsLoading = false;
                RaisePropertyChanged(nameof(DisplaySearchResults));
                RaisePropertyChanged(nameof(ShowAddPollworker));

                //reset scroll in code-behind
                OnSearchSet?.Invoke();
            }
            catch (Exception ex)
            {
	            _essLogger.LogError(ex, new Dictionary<string, string> { { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } });
	            _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }

        private void ClearFields()
        {
            _initialLoad = true;
            FirstNameSearchTerm = string.Empty;
            LastNameSearchTerm = string.Empty;
            DobSearchTerm = string.Empty;
            Pollworkers = null;

            //clear results title
            PollworkerSearchResultStatus = PollworkerSearchResultStatusEnum.Initial;

            //reset radio to polling place search
            SearchFilter = SearchFilterEnum.PollingPlaceId;

            _hasSearched = false;
            RaisePropertyChanged(nameof(ShowAddPollworker));
            _initialLoad = false;

            //reset scroll in code-behind
            OnSearchSet?.Invoke();
        }

        private void ResetSearch()
        {
            ClearFields();
            PollworkerSearch();
        }

        private void GoBack()
        {
            _navigation.NavigateTo<PollworkerManagementViewModel>(navigationFrame: NavigationFrameEnum.ContextFrame);
        }

        private async Task PollworkerSelected(object parameter)
        {
	        if (!(parameter is PollworkerDto pwDto))
		        return;

	        try
	        {
		        var pollworkerDto = await _pollworkerFacade.GetPollworkerAsync(pwDto.PollworkerGUID ?? Guid.Empty);
		        _navigation.NavigateTo<PollworkerDetailViewModel>(parameter: pollworkerDto,
			        navigationFrame: NavigationFrameEnum.ContextFrame);
		        _messenger.Send(new PollworkerDetailMessage() { TabIndex = 0 });
	        }
	        catch (Exception ex)
	        {
		        _essLogger.LogError(ex,
			        new Dictionary<string, string>
				        { { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } });
		        _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error,
			        SystemDetails.GenericErrorMessage));
	        }
        }

        private void AddPollworker()
        {
            _navigation.NavigateTo<PollworkerAddEditViewModel>(navigationFrame: NavigationFrameEnum.ContextFrame);
        }
    }
}
