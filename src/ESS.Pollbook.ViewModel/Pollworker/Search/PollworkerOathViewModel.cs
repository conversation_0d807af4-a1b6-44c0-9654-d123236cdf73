using ESS.Pollbook.Core.Commands;
using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Facade.PollbookDefinedText;
using ESS.Pollbook.Facade.Pollworker;
using ESS.Pollbook.Core.Messaging;
using ESS.Pollbook.ViewModel.Utils;
using GalaSoft.MvvmLight.Command;
using GalaSoft.MvvmLight.Messaging;
using System;
using System.Collections.Generic;
using System.Reflection;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;

namespace ESS.Pollbook.ViewModel.Pollworker.Search
{
	public sealed class PollworkerOathViewModel : EssViewModelBase
	{
        private readonly IPollworkerFacade _pollworkerFacade;
        private readonly IPollbookDefinedTextFacade _pollbookDefinedTextFacade;

        private PollworkerDto _pollworker;
        private bool _fromAddPollworker;

        public ICommand BackCommand => new RelayCommand(Back);

        public ICommand SaveCommand => new CommandAsync<object>(SaveAsync);

        public string PageTitle => "Please sign the oath to proceed.";

        public string PollworkerOath => _pollbookDefinedTextFacade.GetPollbookDefinedTextForLanguage("Pollworker_Oath", "English");

        public static string BackButtonLabel => UIText.Back;

        public static string SaveButtonLabel => UIText.OK;

        public static string ClearSignature => UIText.ClearSignature;

        private bool _hasStrokes;
        public bool HasStrokes
        {
            get => _hasStrokes;
            set
            {
                Set(ref _hasStrokes, value);
                RaisePropertyChanged(nameof(EnableSave));
            }
        }

        public bool EnableSave => HasStrokes;

        public PollworkerOathViewModel(IFrameNavigationService navigation, IMessenger messenger, IEssLogger essLogger, IAuditLogFacade auditLogFacade, IPollworkerFacade pollworkerFacade, IPollbookDefinedTextFacade pollbookDefinedTextFacade) :
            base(navigation, messenger, essLogger, auditLogFacade)
        {
            _pollworkerFacade = pollworkerFacade;
            _pollbookDefinedTextFacade = pollbookDefinedTextFacade;

            ViewModelFirstLoaded += FirstLoad;
            ViewModelUnloaded += UnLoad;
        }

        private void AddMessageHandler(PollworkerAddMessage msg)
        {
            _fromAddPollworker = true;
        }

        private void FirstLoad(object sender, RoutedEventArgs e)
        {
	        EssMsg.Register<PollworkerAddMessage>(this, AddMessageHandler);
	        HasStrokes = false;
	        _pollworker = GetNavParameter<PollworkerDto>();
        }

        private void UnLoad(object sender, RoutedEventArgs e)
        {
            HasStrokes = false;
        }

        private void Back()
        {
            try
            {
                EssAdt.AddToAuditLog(PageName, $"'{BackButtonLabel}' button was activated.");
                EssMsg.Send(new StatusBarMessage(isVisible: true));
                EssNav.RemoveHistory(1);

                if (SystemConfiguration.PollworkerConfiguration.EnableConfirmation)
	                EssNav.NavigateTo<PollworkerJobConfirmationViewModel>(_pollworker, NavigationFrameEnum.ContextFrame);
                else
	                EssNav.NavigateTo<PollworkerDetailViewModel>(_pollworker, NavigationFrameEnum.ContextFrame);

            }
            catch (Exception ex)
            {
                EssLog.LogError(ex, new Dictionary<string, string> { { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } });
                EssMsg.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }

        private async Task SaveAsync(object parameter)
        {
            try
            {
                HasStrokes = false;
	            await EssAdt.AddToAuditLogAsync(PageName, $"'{SaveButtonLabel}' button was activated.");

	            if (!(parameter is InkCanvas inkCanvas))
	                throw new ArgumentException("Parameter was not type of InkCanvis");

                var rtb = new RenderTargetBitmap((int)inkCanvas.ActualWidth, (int)inkCanvas.ActualHeight, 96d, 96d, PixelFormats.Default);
                rtb.Render(inkCanvas);

                var encoder = new PngBitmapEncoder();
                encoder.Frames.Add(BitmapFrame.Create(rtb));
                var signature = Conversion.ConvertToGray(encoder, Conversion.GrayPixelFormatOptions.BlackWhite, Conversion.DefaultPixelWidth);

                if (await _pollworkerFacade.SavePollworkOathAsync(_pollworker, signature))
                {
	                EssMsg.Send(new StatusBarMessage(isVisible: true));
                }

                ClockIn();
                HasStrokes = true;
            }
            catch (Exception ex)
            {
                EssLog.LogError(ex, new Dictionary<string, string> { { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } });
                EssMsg.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }

        private void ClockIn()
        {
	        try
	        {
		        TimeCardDto timeCard = new TimeCardDto()
		        {
			        TimeCardGuid = Guid.NewGuid(),
			        IsNewTimeCard = true,
			        PollworkerGUID = _pollworker.PollworkerGUID ?? Guid.Empty,
			        PollworkerTitleId = _pollworker.TitleID ?? 0,
			        PollingPlaceId = _pollworker.CurrentPollingPlaceID ??
			                         LoggedInPollplaceInfo.LoggedInPollPlace.PollingPlaceId,
			        StartTime = DateTime.UtcNow,
			        JudgeApprovedDateTime = null,
			        JudgePollworkerGUID = null
		        };

		        if (SystemConfiguration.PollworkerConfiguration.EnableConfirmation && _pollworker.CurrentPollingPlaceID.HasValue)
		        {
			        timeCard.PollingPlaceId = (int)_pollworker.CurrentPollingPlaceID;
		        }
		        else
		        {
			        timeCard.PollingPlaceId = LoggedInPollplaceInfo.LoggedInPollPlace.PollingPlaceId;
		        }

		        // Must be Sync.  Can't advance in Nav until we have values.
		        if (!Task.Run(async () => await _pollworkerFacade.SaveTimeCardAsync(timeCard)).Result)
			        return;

		        _pollworker.LastStartTime = timeCard.StartTime;
		        _pollworker.LastEndTime = null;
		        _pollworker.LastTimeCardGuid = timeCard.TimeCardGuid;

		        EssNav.NavigateTo<PollworkerActionConfirmationViewModel>(new PollworkerActionConfirmationDto()
		        {
			        ConfirmationText = $"{_pollworker.LastName}, {_pollworker.FirstName} has {PollworkerHelper.GetStatusNameLabel(PollworkerStatus.ClockedIn).ToLower()}.",
			        ConfirmationInstructions = timeCard.StartTime.ToLocalTime().ToString("MM/dd/yyyy hh:mm tt"),
			        Destination = nameof(PollworkerManagementViewModel),
			        NavigationParameter = _pollworker,
		        }, NavigationFrameEnum.ContextFrame);
	        }
	        catch (Exception ex)
	        {
		        EssLog.LogError(ex, new Dictionary<string, string> { { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } });
		        EssMsg.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
	        }
        }
	}
}
