using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Facade.Pollworker;
using ESS.Pollbook.Core.Messaging;
using ESS.Pollbook.ViewModel.Utils;
using GalaSoft.MvvmLight.Command;
using GalaSoft.MvvmLight.Messaging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using System.Web.UI.WebControls;
using System.Windows;
using System.Windows.Input;

namespace ESS.Pollbook.ViewModel.Pollworker.Search
{
	public sealed class PollworkerJobConfirmationViewModel : EssViewModelBase
	{
		private readonly IPollworkerFacade _pollworkerFacade;

		private PollworkerDto _pollworker;

		public ICommand BackCommand => new RelayCommand(Back);

        public ICommand NextCommand => new RelayCommand(Next);

        public static string PageTitle => "Please confirm Job Title and Poll Place.";
        public static string RoleLabel => UIText.JobTitle;
        public static string PollingPlaceLabel => UIText.PollPlace;
        public static string BackLabel => UIText.Back;
        public static string NextLabel => UIText.Next;

        private IEnumerable<ListItem> _jobTitles;
        public IEnumerable<ListItem> JobTitles
        {
            get => _jobTitles;
            set => Set(ref _jobTitles, value);
        }

        private int? _jobTitleSelected;
        public int? JobTitleSelected
        {
            get => _jobTitleSelected;
            set
            {
                if (Set(ref _jobTitleSelected, value))
                    RaisePropertyChanged(nameof(IsSaveEnabled));
            }
        }

        private string _jobTitleWatermark;

        public string JobTitleWatermark
        {
            get => _jobTitleWatermark;
            set => Set(ref _jobTitleWatermark, value);
        }

        private List<KeyNameLookup> _pollPlaces;

        public List<KeyNameLookup> PollPlaces
        {
            get => _pollPlaces;
            set => Set(ref _pollPlaces, value);
        }

        private KeyNameLookup _pollPlaceSelected;
        public KeyNameLookup PollPlaceSelected
        {
            get => _pollPlaceSelected;
            set
            {
	            if (!Set(ref _pollPlaceSelected, value))
		            return;

	            PollPlaceChanged();
	            RaisePropertyChanged(nameof(IsSaveEnabled));
            }
        }

        public bool IsSaveEnabled => JobTitleSelected != null && PollPlaceSelected != null;

        public PollworkerJobConfirmationViewModel(IFrameNavigationService navigation,
	        IMessenger messenger,
	        IEssLogger essLogger,
	        IAuditLogFacade auditLogFacade,
	        IPollworkerFacade pollworkerFacade) : base(navigation, messenger, essLogger, auditLogFacade)
        {
	        _pollworkerFacade = pollworkerFacade;
	        ViewModelFirstLoaded += FirstLoad;
	        ViewModelLoaded += Load;
        }

        private void FirstLoad(object sender, RoutedEventArgs e)
        {
	        _pollworker = GetNavParameter<PollworkerDto>();
        }

        private void Load(object sender, RoutedEventArgs e)
        {
	        JobTitleWatermark = UIText.Select;
	        JobTitleSelected = null;

	        if (_pollworker == null)
		        return;

	        PollPlaces = Task.Run(async () => await GetPollPlaces()).Result;
	        JobTitles = Task.Run(async () => await GetJobTitles()).Result;

	        JobTitleSelected = _pollworker.TitleID;
	        PollPlaceSelected = PollPlaces.First(p => p.Key == _pollworker.CurrentPollingPlaceID);

	        RaisePropertyChanged(nameof(JobTitles));
	        RaisePropertyChanged(nameof(PollPlaces));
	        RaisePropertyChanged(nameof(JobTitleSelected));
	        RaisePropertyChanged(nameof(PollPlaceSelected));
	        RaisePropertyChanged(nameof(IsSaveEnabled));
        }

        private void Back()
        {
	        EssAdt.AddToAuditLog(PageName, $"'{BackLabel}' button was activated.");
	        try
	        {
		        EssMsg.Send(new StatusBarMessage(isVisible: true));
				EssNav.RemoveHistory(1);
                EssNav.NavigateTo<PollworkerDetailViewModel>(_pollworker, NavigationFrameEnum.ContextFrame);
	        }
	        catch (Exception ex)
	        {
		        EssLog.LogError(ex, new Dictionary<string, string> { { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } });
		        EssMsg.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
	        }
        }

        private void Next()
        {
	        EssAdt.AddToAuditLog(PageName, $"'{NextLabel}' button was activated.");
	        try
	        {
		        if (JobTitleSelected != _pollworker.TitleID)
		        {
			        _pollworker.TitleID = JobTitleSelected;
			        _pollworker.JobTitleChanged = true;
		        }

		        if (PollPlaceSelected == null)
			        return;

		        if (PollPlaceSelected.Key != _pollworker.CurrentPollingPlaceID)
		        {
			        _pollworker.CurrentPollingPlaceID = PollPlaceSelected.Key;
			        _pollworker.PollPlaceChanged = true;
		        }

		        var result = Task.Run(() => _pollworkerFacade.SavePollworkerJobPrecinctConfirmationAsync(_pollworker));
		        if (result.Result)
			        EssMsg.Send(new StatusBarMessage(isVisible: true));

		        if ((!_pollworker.SignedOath.HasValue || !_pollworker.SignedOath.Value) &&
		            SystemConfiguration.PollworkerConfiguration.EnableOath)
		        {
			        EssNav.NavigateTo<PollworkerOathViewModel>(_pollworker, NavigationFrameEnum.ContextFrame);
			        return;
		        }

		        var timeCard = new TimeCardDto()
		        {
			        TimeCardGuid = Guid.NewGuid(),
			        IsNewTimeCard = true,
			        PollworkerGUID = _pollworker.PollworkerGUID ?? Guid.Empty,
			        PollworkerTitleId = _pollworker.TitleID ?? 0,
			        PollingPlaceId = _pollworker.CurrentPollingPlaceID ??
			                         LoggedInPollplaceInfo.LoggedInPollPlace.PollingPlaceId,
			        StartTime = DateTime.UtcNow,
			        JudgeApprovedDateTime = null,
			        JudgePollworkerGUID = null
		        };

		        // Must be Sync.  Can't advance in Nav until we have values.
		        if (!Task.Run(async () => await _pollworkerFacade.SaveTimeCardAsync(timeCard)).Result)
			        return;

		        _pollworker.LastStartTime = timeCard.StartTime;
		        _pollworker.LastEndTime = null;
		        _pollworker.LastTimeCardGuid = timeCard.TimeCardGuid;

		        EssNav.NavigateTo<PollworkerActionConfirmationViewModel>(new PollworkerActionConfirmationDto()
		        {
			        ConfirmationText = $"{_pollworker.LastName}, {_pollworker.FirstName} has {PollworkerHelper.GetStatusNameLabel(PollworkerStatus.ClockedIn).ToLower()}.",
			        ConfirmationInstructions = timeCard.StartTime.ToLocalTime().ToString("MM/dd/yyyy hh:mm tt"),
			        Destination = nameof(PollworkerManagementViewModel),
			        NavigationParameter = _pollworker,
		        }, NavigationFrameEnum.ContextFrame);

		        EssMsg.Send(new StatusBarMessage(isVisible: true));
	        }
	        catch (Exception ex)
	        {
		        EssLog.LogError(ex, new Dictionary<string, string> { { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } });
		        EssMsg.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
	        }
        }


        private async Task<List<KeyNameLookup>> GetPollPlaces()
        {
            var results = new List<KeyNameLookup>();
            if (_pollworker == null)
	            return results;

            var places = await _pollworkerFacade.GetPollPlacesAsync(false);
            results.Add(places.First(f => f.Key == LoggedInPollplaceInfo.LoggedInPollPlace.PollingPlaceId));

            if (_pollworker.CurrentPollingPlaceID != null &&
                _pollworker.CurrentPollingPlaceID != 0 &&
                _pollworker.CurrentPollingPlaceID != LoggedInPollplaceInfo.LoggedInPollPlace.PollingPlaceId)
            {
	            results.Add(places.First(f => f.Key == _pollworker.CurrentPollingPlaceID));
            }

            return results.OrderBy(f => f.Name).ToList();
        }

        private void PollPlaceChanged()
        {
            if (_pollworker == null || PollPlaceSelected == null)
                return;

            if (PollPlaceSelected.Key != _pollworker.CurrentPollingPlaceID)
            {
                _pollworker.CurrentPollingPlaceID = PollPlaceSelected.Key;
                JobTitleSelected = null;
            }

            JobTitles = Task.Run(async () => await GetJobTitles()).Result;
        }

        private async Task<IEnumerable<ListItem>> GetJobTitles()
        {
	        if (_pollworker == null)
		        return null;

	        var pollPlace = _pollworker.CurrentPollingPlaceID ?? LoggedInPollplaceInfo.LoggedInPollPlace.PollingPlaceId;

	        if (SystemConfiguration.PollworkerConfiguration.EnableSignInLimit)
	        {
		        //get our polling place title details
		        return await _pollworkerFacade.GetPollingPlaceTitlesWithUsageCounts(pollPlace);
	        }

	        //load available job titles without title limits
	        return (await _pollworkerFacade.GetTitlesAsync(false))
		        .Select(s => new ListItem(s.TitleName, s.TitleId.ToString(), enabled: true));
        }
	}
}