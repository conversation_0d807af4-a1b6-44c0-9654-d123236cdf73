using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Core.Messaging;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.Command;
using GalaSoft.MvvmLight.Messaging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Windows.Input;
using ESS.Pollbook.Facade.Pollworker;

namespace ESS.Pollbook.ViewModel.Pollworker.Search
{
	public sealed class PollworkerTimeHistoryDetailsViewModel : ViewModelBase
	{
        private readonly IFrameNavigationService _navigation;
        private readonly IMessenger _messenger;
        private readonly IEssLogger _essLogger;
        private readonly IAuditLogFacade _auditLogFacade;

        private readonly ICommand _backCommand;

        private readonly string _pageName;

        public static string PageTitle => "Poll Worker Time History Details";

        public static string ClockInHeader => PollworkerHelper.GetClockPromptLabel(PollworkerStatus.ClockedOut);
        public static string ClockOutHeader => PollworkerHelper.GetClockPromptLabel(PollworkerStatus.ClockedIn);
        public static string TotalTimeHeader => UIText.TotalTime;
        public static string PollingPlaceHeader => UIText.PollingPlace;
        public static string RoleHeader => UIText.Role;

        private List<TimeCardDto> _cards;

        public List<TimeCardDto> TimeCards
        {
            get => _cards;
            set => Set(ref _cards, value);
        }

        public ICommand BackCommand => _backCommand;

        public static string BackLabel => UIText.Back;

        private string _timeCardDate;

        public string TimeCardDate
        {
            get => _timeCardDate;
            set => Set(ref _timeCardDate, value);
        }

        private string _grandTotal;
        public string GrandTotal
        {
            get => _grandTotal;
            set => Set(ref _grandTotal, value);
        }

        public static bool IsSingleCheckInEnabled => SystemConfiguration.PollworkerConfiguration.CheckInWorkflow.Equals("Single");

        public PollworkerTimeHistoryDetailsViewModel(IFrameNavigationService navigation,
            IMessenger messenger,
            IEssLogger essLogger,
            IAuditLogFacade auditLogFacade)
        {
            _navigation = navigation;
            _essLogger = essLogger;
            _messenger = messenger;
            _auditLogFacade = auditLogFacade;

            _pageName = _navigation.PageName(this.GetType().FullName);

            _backCommand = new RelayCommand(Back);
        }

        public void PageOnLoaded()
        {
            var cards = (List<TimeCardDto>)_navigation.Parameter;
            if (cards == null)
	            return;

            TimeCards = cards;
            TimeCardDate = cards[0].StartTime.ToLocalTime().ToLongDateString();

            var gt = PollworkerHelper.SumTimeCards(cards.ToList());
            GrandTotal = $"{gt.Hours}:{gt.Minutes:00}";
        }

        private void Back()
        {
            try
            {
                _auditLogFacade.AddToAuditLog(_pageName, $"'{BackLabel}' button was activated.");
                _messenger.Send(new StatusBarMessage(isVisible: true));
                _navigation.GoBackTo<PollworkerDetailViewModel>(NavigationFrameEnum.ContextFrame);
                _messenger.Send(new PollworkerDetailMessage()
                {
                    TabIndex = 1
                });
            }
            catch (Exception ex)
            {
	            _essLogger.LogError(ex, new Dictionary<string, string> { { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } });
	            _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }
    }
}
