using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Facade.Pollworker;
using ESS.Pollbook.Core.Messaging;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.CommandWpf;
using GalaSoft.MvvmLight.Messaging;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using System.Windows.Input;

namespace ESS.Pollbook.ViewModel.Pollworker.TimeCard
{
	public sealed class PollworkerViewModel : ViewModelBase
	{
		private readonly IFrameNavigationService _navigation;
		private readonly IMessenger _messenger;
		private readonly IEssLogger _essLogger;
		private readonly IAuditLogFacade _auditLogFacade;
		private readonly IPollworkerFacade _pollworkerFacade;

		private string _pageName;

		private ICommand _approvalCommand;
		private ICommand _backCommand;
		private ICommand _selectPollworkerCommand;

		public ICommand ApprovalCommand => _approvalCommand;
		public ICommand BackCommand => _backCommand;
		public ICommand SelectPollworkerCommand => _selectPollworkerCommand;

		public static string PollingPlaceLabel => UIText.PollingPlace;
		public static string PollingPlace => PollworkerHelper.GetPollPlaceDisplayText(LoggedInPollplaceInfo.LoggedInPollPlace.PollingPlaceName);
		public static string DateLabel => UIText.Date;
		public static string CurrentDate => PollworkerHelper.CurrentDate;
		public static string ApproveLabel => UIText.Approve;
		public static string BackLabel => UIText.Back;

        public static bool IsSingleCheckInEnabled => SystemConfiguration.PollworkerConfiguration.CheckInWorkflow.Equals("Single");

		private int _pollingPlaceJudgeApprovalStatus;
		private bool _isLoading;

		// Observable not needed as no one is using the .CollectionChanged event
		private ObservableCollection<PollworkerDto> _pollworkersList;

		public ObservableCollection<PollworkerDto> PollworkersList
		{
			get => _pollworkersList;
			set => Set(ref _pollworkersList, value);
		}

		private PollworkerDto _selectedPollworker;

		public PollworkerDto PollworkerSelected
		{
			get => _selectedPollworker;
			set => Set(ref _selectedPollworker, value);
		}

		public bool ApprovalEnabled
		{
			get
			{
				if (_pollingPlaceJudgeApprovalStatus != 0 || IsSingleCheckInEnabled)
					return false;

				return PollworkersList?.Count > 0 && PollworkersList.All(p => p.Status == PollworkerStatus.ClockedOut);
			}
		}

		public PollworkerViewModel(IFrameNavigationService navigation,
			IMessenger messenger,
			IEssLogger essLogger,
			IAuditLogFacade auditLogFacade,
			IPollworkerFacade pollworkerFacade)
		{
			_navigation = navigation;
			_essLogger = essLogger;
			_messenger = messenger;
			_auditLogFacade = auditLogFacade;
			_pollworkerFacade = pollworkerFacade;

			_pageName = _navigation.PageName(this.GetType().FullName);

			_approvalCommand = new RelayCommand(Approved);
			_backCommand = new RelayCommand(Back);
			_selectPollworkerCommand = new RelayCommand(SelectPollworker);
		}

		private void Approved()
		{
			try
			{
				_auditLogFacade.AddToAuditLog(_pageName, $"'{ApproveLabel}' button was activated.");
				_navigation.NavigateTo<PollworkerApprovalViewModel>(NavigationFrameEnum.ContextFrame, suppressHorizontalEffect: true);
			}
			catch (Exception ex)
			{
				_essLogger.LogError(ex, new Dictionary<string, string> { { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } });
				_messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
			}
		}

		public async Task PageLoaded()
		{
			try
			{
				_isLoading = true;

				// clean the selection and list
				PollworkerSelected = null;
				PollworkersList = null;

				// get new list of pollworkers
				var pollworkers = (await _pollworkerFacade.GetPollworkersAsync(LoggedInPollplaceInfo.LoggedInPollPlace.PollingPlaceId, DateTime.UtcNow)).ToList();
                PollworkerHelper.AddStatusDisplayNameToPollworkers(pollworkers);

				PollworkersList = new ObservableCollection<PollworkerDto>(pollworkers.OrderBy(p => p.LastName).ThenBy(p => p.FirstName));
                _pollingPlaceJudgeApprovalStatus = await _pollworkerFacade.GetJudgeApprovalStatusForPollingPlace();

				RaisePropertyChanged(nameof(ApprovalEnabled));

				_isLoading = false;
			}
			catch (Exception ex)
			{
				_essLogger.LogError(ex, new Dictionary<string, string> { { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } });
				_messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
			}
		}

		private void Back()
		{
			try
			{
				_auditLogFacade.AddToAuditLog(_pageName, $"'{BackLabel}' button was activated.");
				_messenger.Send(new StatusBarMessage(isVisible: true));
				_navigation.GoBackTo<PollworkerManagementViewModel>();
			}
			catch (Exception ex)
			{
				_essLogger.LogError(ex, new Dictionary<string, string> { { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } });
				_messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
			}
		}

		private void SelectPollworker()
		{
			try
			{
				if (_isLoading)
					return;
				_auditLogFacade.AddToAuditLog(_pageName, $"'{PollworkerSelected?.FirstName} {PollworkerSelected?.LastName}' was activated.");
				_navigation.NavigateTo<PollworkerManageTimeViewModel>(PollworkerSelected, NavigationFrameEnum.ContextFrame);
			}
			catch (Exception ex)
			{
				_essLogger.LogError(ex, new Dictionary<string, string> { { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } });
				_messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
			}
		}
    }
}