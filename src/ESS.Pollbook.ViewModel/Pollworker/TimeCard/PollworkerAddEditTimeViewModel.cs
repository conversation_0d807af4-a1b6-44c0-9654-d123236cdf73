using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Facade.Pollworker;
using ESS.Pollbook.ViewModel.Maintenance;
using ESS.Pollbook.Core.Messaging;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.CommandWpf;
using GalaSoft.MvvmLight.Messaging;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using System.Windows.Input;

namespace ESS.Pollbook.ViewModel.Pollworker.TimeCard
{
	public sealed class PollworkerAddEditTimeViewModel : ViewModelBase
	{
        private readonly IFrameNavigationService _navigation;
        private readonly IMessenger _messenger;
        private readonly IEssLogger _essLogger;
        private readonly IAuditLogFacade _auditLogFacade;
        private readonly IPollworkerFacade _pollworkerFacade;

        private readonly string _pageName;
        private TimeCardDto _timeCard;
        private List<TimeCardDto> _timeCards;
        private int? _inHour;
        private int? _outHour;
        private PollworkerDto _pollworker;
        private bool _isInitializing;

        // This is the flag to avoid bursting validation message upon properties changed
        private bool _showValidationMessage = true;

        private string _pageTitle;
        public string PageTitle
        {
            get => _pageTitle;
            set => Set(ref _pageTitle, value);
        }
        public static string CancelLabel => UIText.Cancel;
        public static string SaveLabel => UIText.Save;
        public static string ClockInLabel => PollworkerHelper.GetClockPromptLabel(PollworkerStatus.ClockedOut);
        public static string ClockOutLabel =>
            PollworkerHelper.GetClockPromptLabel(PollworkerStatus.ClockedIn);

        public static bool IsSingleCheckInEnabled => SystemConfiguration.PollworkerConfiguration.CheckInWorkflow.Equals("Single");

        private ActionType _actionType;
        public ActionType ActionType
        {
            get => _actionType;
            set => Set(ref _actionType, value);
        }

        public ObservableCollection<TimeUnit> Hours
        {
            get;
            private set;
        }

        public ObservableCollection<TimeUnit> Minutes
        {
            get;
            private set;
        }
        private TimeUnit _selectedInHour;
        public TimeUnit SelectedInHour
        {
            get => _selectedInHour;
            set
            {
                Set(ref _selectedInHour, value);
                IsSaveEnabled = Validate();
            }
        }

        private TimeUnit _selectedInMinute;
        public TimeUnit SelectedInMinute
        {
            get => _selectedInMinute;
            set
            {
                Set(ref _selectedInMinute, value);
                IsSaveEnabled = Validate();
            }
        }
        private TimeUnit _selectedOutHour;
        public TimeUnit SelectedOutHour
        {
            get => _selectedOutHour;
            set
            {
                Set(ref _selectedOutHour, value);
                IsSaveEnabled = Validate();
            }
        }

        private TimeUnit _selectedOutMinute;
        public TimeUnit SelectedOutMinute
        {
            get => _selectedOutMinute;
            set
            {
                Set(ref _selectedOutMinute, value);
                IsSaveEnabled = Validate();
            }
        }

        private bool _isAMIn;
        public bool IsAMIn
        {
            get => _isAMIn;
            set
            {
                Set(ref _isAMIn, value);
                IsSaveEnabled = Validate();
            }
        }
        private bool _isAMOut;
        public bool IsAMOut
        {
            get => _isAMOut;
            set
            {
                Set(ref _isAMOut, value);
                IsSaveEnabled = Validate();
            }
        }

        private bool _isSaveEnabled;
        public bool IsSaveEnabled
        {
            get => _isSaveEnabled;
            set => Set(ref _isSaveEnabled, value);
        }

        private bool _clockOutEnableChecked;
        public bool ClockOutEnableChecked
        {
            get => _clockOutEnableChecked;
            set => Set(ref _clockOutEnableChecked, value);
        }

        public static string TimeCardDateTime => DateTime.UtcNow.ToLocalTime().ToLongDateString();

        public ICommand CancelCommand =>  new RelayCommand(Cancel);
        public ICommand SaveCommand => new RelayCommand(Save);
        public ICommand CheckEvent => new RelayCommand(CheckEventHandler);

        public PollworkerAddEditTimeViewModel(IFrameNavigationService navigation,
            IMessenger messenger,
            IEssLogger essLogger,
            IAuditLogFacade auditLogFacade,
            IPollworkerFacade pollworkerFacade)
        {
            _navigation = navigation;
            _essLogger = essLogger;
            _messenger = messenger;
            _auditLogFacade = auditLogFacade;
            _pollworkerFacade = pollworkerFacade;

            _pageName = _navigation.PageName(this.GetType().FullName);

            LoadTimeUnits();
        }
        private void LoadTimeUnits()
        {
            Hours = new ObservableCollection<TimeUnit>(TimeUnit.GetHours());
            Minutes = new ObservableCollection<TimeUnit>(TimeUnit.GetMinutes());
        }

        public void PageOnLoad()
        {
            try
            {
                _isInitializing = true;

                ClearTimes();

                _timeCard = _navigation.Parameter as TimeCardDto;
                if (_timeCard != null)
	                _pollworker = new PollworkerDto
	                {
		                PollworkerGUID = _timeCard.PollworkerGUID,
		                TitleID = _timeCard.PollworkerTitleId
	                };

                if (_timeCard?.IsNewTimeCard == true)
                {
	                ActionType = ActionType.Add;
	                IsSaveEnabled = false;
                }
                else if (_timeCard != null)
                {
	                ActionType = ActionType.Edit;
	                var ampm = _timeCard.StartTime.ToLocalTime().ToString("tt");
	                var hour = int.Parse(_timeCard.StartTime.ToLocalTime().ToString("hh"));
	                var minute = _timeCard.StartTime.ToLocalTime().Minute;

	                IsAMIn = ampm.Equals("AM");
	                SelectedInHour = Hours.FirstOrDefault(t => t.UnitNumber == hour);
	                SelectedInMinute = Minutes.FirstOrDefault(t => t.UnitNumber == minute);
	                var selected = SelectedInHour?.UnitNumber ?? 0;
	                _inHour = (IsAMIn ? 0 : 1) * 12 + (selected < 12 ? selected : 0);

	                if (_timeCard.EndTime != null)
	                {
		                ampm = _timeCard.EndTime.Value.ToLocalTime().ToString("tt");
		                hour = int.Parse(_timeCard.EndTime.Value.ToLocalTime().ToString("hh"));
		                minute = _timeCard.EndTime.Value.ToLocalTime().Minute;

		                IsAMOut = ampm.Equals("AM");
		                SelectedOutHour = Hours.FirstOrDefault(t => t.UnitNumber == hour);
		                SelectedOutMinute = Minutes.FirstOrDefault(t => t.UnitNumber == minute);
		                selected = SelectedOutHour?.UnitNumber ?? 0;
		                _outHour = (IsAMOut ? 0 : 1) * 12 + (selected < 12 ? selected : 0);
	                }

	                ClockOutEnableChecked = true;
                }
                else
                {
	                //do nothing with this
	                ActionType = ActionType.Hammer;
                }

                if (_timeCard != null)
                {
	                var cards = Task.Run(async () =>
		                await _pollworkerFacade.GetAllTimeCardsForPollworkerAndDateTimeAsync(_timeCard.PollworkerGUID,
			                DateTime.UtcNow.ToLocalTime()));
	                _timeCards = cards.Result != null ? cards.Result.ToList() : new List<TimeCardDto>();
                }

                PageTitle = $"{ActionType} Time";

                _isInitializing = false;
            }
            catch (Exception ex)
            {
	            _essLogger.LogError(ex, new Dictionary<string, string> { { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } });
	            _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }
        private void Cancel()
        {
            try
            {
                _auditLogFacade.AddToAuditLog(_pageName, $"'{CancelLabel}' button was activated.");
                _messenger.Send(new StatusBarMessage(isVisible: true));
                _navigation.NavigateTo<PollworkerManageTimeViewModel>(_pollworker, NavigationFrameEnum.ContextFrame);
            }
            catch (Exception ex)
            {
	            _essLogger.LogError(ex, new Dictionary<string, string> { { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } });
	            _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }

        private void Save()
        {
            try
            {
	            _auditLogFacade.AddToAuditLog(_pageName, $"'{SaveLabel}' button was activated.");

	            var valid = TimeCardsValidation();
	            if (valid)
                {
                    Task.Run(async () => await _pollworkerFacade.SaveTimeCardAsync(_timeCard)).Wait();
                    _navigation.NavigateTo<PollworkerManageTimeViewModel>(_pollworker, NavigationFrameEnum.ContextFrame);
                }
            }
            catch (Exception ex)
            {
	            _essLogger.LogError(ex, new Dictionary<string, string> { { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } });
	            _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }

        private void CheckEventHandler()
        {
            if (!ClockOutEnableChecked)
            {
                // Setting _showValidationMessage false to avoid bursting validation message (future times) when properties changed
                _showValidationMessage = false;

                SelectedOutHour = null;
                SelectedOutMinute = null;
                _timeCard.EndTime = null;
                _isAMOut = true;
                RaisePropertyChanged(nameof(IsAMOut));

                // Setting _showValidationMessage true to allow validation message (f to show again after properties changed
                _showValidationMessage = true;
            }
            IsSaveEnabled = Validate();
        }

        private bool Validate()
        {
            var startDateTime = ValidateIn();
            var endDateTime = ValidateOut();

            if (!startDateTime.HasValue)
	            return false;

            _timeCard.StartTime = startDateTime.Value.ToUniversalTime();
            if (endDateTime.HasValue)
            {
	            if (!(endDateTime >= startDateTime))
		            return false;

	            _timeCard.EndTime = endDateTime.Value.ToUniversalTime();
	            return true;
            }

            return (((ActionType == ActionType.Add && !ClockOutEnableChecked) || ActionType == ActionType.Edit)
                    && SelectedOutHour == null && SelectedOutMinute == null);
        }
        // First level of validation. Make sure the InTime is less than the OutTime if there is an OutTime. A valid condition will allow the Save button to be enabled.
        private DateTime? ValidateIn()
        {
            if (_isInitializing)
                return null;

            if (SelectedInHour == null || SelectedInMinute == null)
	            return null;

            var selected = SelectedInHour?.UnitNumber ?? 0;
            var minutes = SelectedInMinute?.UnitNumber ?? 0;

            _inHour = (IsAMIn ? 0 : 1) * 12 + (selected < 12 ? selected : 0);

            DateTime? startDateTime = new DateTime(
	            DateTime.UtcNow.ToLocalTime().Year,
	            DateTime.UtcNow.ToLocalTime().Month,
	            DateTime.UtcNow.ToLocalTime().Day,
	            _inHour.Value, minutes, 0);

            if (startDateTime.Value > DateTime.Now)
            {
                if (_showValidationMessage)
                    _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, $"{ClockInLabel} time cannot be set past current time."));

                return null;
            }

            return startDateTime;
        }
        private DateTime? ValidateOut()
        {
            if (_isInitializing)
                return null;

            if (SelectedOutHour == null || SelectedOutMinute == null)
	            return null;

            var selected = SelectedOutHour?.UnitNumber ?? 0;
            var minutes = SelectedOutMinute?.UnitNumber ?? 0;

            _outHour = (IsAMOut ? 0 : 1) * 12 + (selected < 12 ? selected : 0);

            DateTime? endDateTime = new DateTime(
	            DateTime.UtcNow.ToLocalTime().Year,
	            DateTime.UtcNow.ToLocalTime().Month,
	            DateTime.UtcNow.ToLocalTime().Day,
	            _outHour.Value, minutes, 0);

            if (endDateTime.Value > DateTime.Now)
            {
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, $"{ClockOutLabel} time cannot be set past current time."));
                return null;
            }

            return endDateTime;
        }

        private bool TimeCardsValidation()
        {
	        if (_timeCards.Count == 0)
		        return true;

	        foreach (var timeCard in _timeCards)
	        {
		        if (!timeCard.TimeCardGuid.Equals(_timeCard.TimeCardGuid))
		        {
			        if (!timeCard.EndTime.HasValue)
				        continue;

                    //If the prior time card overlaps the time is not valid
			        if (timeCard.StartTime <= _timeCard.EndTime && _timeCard.StartTime <= timeCard.EndTime)
			        {
                        _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, "Times being set overlap with other time entries for Poll Worker"));
                        return false;
			        }
		        }
		        else
                {
                    //Validate edits for time card
			        if (_timeCard.StartTime <= _timeCard.EndTime)
			        {
				        return true;
			        }

                    _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, "Times being set are not valid."));
                    return false;
                }
	        }

	        return true;
        }

        private void ClearTimes()
        {
            SelectedInHour = null;
            SelectedInMinute = null;
            SelectedOutHour = null;
            SelectedOutMinute = null;
            IsAMIn = true;
            IsAMOut = true;
            ClockOutEnableChecked = false;
        }
    }

    public enum ActionType
    {
        Add,
        Edit,
        Hammer
    }
}
