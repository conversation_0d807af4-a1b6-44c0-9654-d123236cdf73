using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Facade.SurrenderMailInBallot;
using ESS.Pollbook.Core.Messaging;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.CommandWpf;
using GalaSoft.MvvmLight.Messaging;
using System.Windows.Input;

namespace ESS.Pollbook.ViewModel.SurrenderMailInBallot
{
    public class SurrenderMailInBallotResponseViewModel : ViewModelBase
    {
        private readonly IFrameNavigationService _navigation;
        private readonly IMessenger _messenger;
        private readonly ISurrenderMailInBallotFacade _surrenderMailInBallotFacade;
        private readonly IAuditLogFacade _auditLogFacade;

        private bool _answer;
        private readonly string _pageName;

        private string _messageTitle;
        public string MessageTitle
        {
            get => _messageTitle;
            private set => Set(ref _messageTitle, value);
        }

        private string _message;

        public string Message
        {
            get => _message;
            private set => Set(ref _message, value);
        }
        public static string CompleteLabel => UIText.Complete;
        public ICommand CompleteCommand { get; set; }

        public SurrenderMailInBallotResponseViewModel(IFrameNavigationService navigationService, IMessenger messengerService, ISurrenderMailInBallotFacade surrenderMailInBallotFacade, IAuditLogFacade auditLogFacade)
        {
            _navigation = navigationService;
            _messenger = messengerService;

            _surrenderMailInBallotFacade = surrenderMailInBallotFacade;
            _auditLogFacade = auditLogFacade;

            _pageName = _navigation.PageName(this.GetType().FullName);

            _messenger.Register<SurrenderMailInBallotResponseMessage>(this, MsgHandler);

            CompleteCommand = new RelayCommand(Complete);
        }

        public void PageIsLoaded()
        {
            if (_navigation.Parameter == null) return;

            var msg = (SurrenderMailInBallotResponseMessage)_navigation.Parameter;
            MsgHandler(msg);
        }

        private void MsgHandler(SurrenderMailInBallotResponseMessage msg)
        {
            if (msg == null) return;

            Message = (!string.IsNullOrEmpty(msg.Message)) ? msg.Message : string.Empty;
            MessageTitle = msg.MessageTitle;
            _answer = msg.Answer;
        }

        private void Complete()
        {
            _auditLogFacade.AddToAuditLog(_pageName, $"'{CompleteLabel}' button was activated.");

            IssueBallotHelper.Voter =
                _surrenderMailInBallotFacade.ProcessMailInBallot(_answer, IssueBallotHelper.Voter);

            _navigation.CloseModalWindow();

            _messenger.Send(new SurrenderMailInBallotCompleteMessage()
            {
                HasSurrenderedMailInBallot = _answer
            });
        }
    }
}
