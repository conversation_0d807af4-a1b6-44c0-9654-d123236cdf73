using ESS.Pollbook.Core.Commands;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Facade.HostSync;
using ESS.Pollbook.Facade.PollbookTransaction;
using ESS.Pollbook.Facade.PQC;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.Messaging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ESS.Pollbook.Facade.Configuration;

namespace ESS.Pollbook.ViewModel
{
    public class ApplyingConfigurationUpdatesDoneViewModel : ViewModelBase
    {
        private readonly IFrameNavigationService _navigation;
        private readonly IAuditLogFacade _auditLogFacade;
        private readonly IPollbookTransactionFacade _transactionFacade;
        private readonly IHostSyncFacade _hostSyncFacade;
        private readonly IEssLogger _essLogger;
        private readonly IMessenger _messenger;
        private readonly IPQCFacade _pqcFacade;
        private readonly IPollbookConfigurationFacade _pollbookConfigurationFacade;

        private string PageName => _navigation.PageName(GetType().FullName);
        public string SignOutLabel => UIText.SignOut;
        public string BackLabel => UIText.Back;

        public string Message { get; set; }
        public bool ShowSignOut { get; set; }
        public ICommandAsync SignOutCommand => new CommandAsync(SignOut);
        public ICommandAsync BackCommand => new CommandAsync(Back);

        public ApplyingConfigurationUpdatesDoneViewModel(IFrameNavigationService navigationService,
                                                        IAuditLogFacade auditLogFacade,
                                                        IPollbookTransactionFacade transactionFacade,
                                                        IHostSyncFacade hostSyncFacade,
                                                        IPollbookConfigurationFacade pollbookConfigurationFacade,
                                                        IPQCFacade pqcFacade,
                                                        IEssLogger essLogger,
                                                        IMessenger messenger
                                                        )
        {
            _navigation = navigationService;
            _auditLogFacade = auditLogFacade;
            _transactionFacade = transactionFacade;
            _hostSyncFacade = hostSyncFacade;
            _pollbookConfigurationFacade = pollbookConfigurationFacade;
            _pqcFacade = pqcFacade;
            _essLogger = essLogger;
            _messenger = messenger;

            _pollbookConfigurationFacade = pollbookConfigurationFacade;
        }

        public void PageLoaded()
        {
            try
            {
                var noUpdates = (int)_navigation.Parameter;
                Message = (noUpdates > 0) ? UIText.ConfigUpdateSuccessful : UIText.NoConfigUpdatesAvailable;
                ShowSignOut = (noUpdates > 0);
                RaisePropertyChanged(nameof(Message));
                RaisePropertyChanged(nameof(ShowSignOut));
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string> { { "Action", "AlertDetailViewModel.PageIsLoaded" } };
                _essLogger.LogError(ex, logProps);
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }

        private async Task SignOut()
        {
            await _auditLogFacade.AddToAuditLogAsync(PageName, $"'{SignOutLabel}' button was activated.");
            SignOutHelper signOutHelper = new SignOutHelper(_transactionFacade, _hostSyncFacade, _pollbookConfigurationFacade, _essLogger, _messenger, _navigation);
            await signOutHelper.SignOut();
            var dbCloseResult = await _pqcFacade.CloseConnectionsAsync();
            if (dbCloseResult)
            {
                SystemDetails.IsPQCVerified = false;
            }
        }

        private async Task Back()
        {
            try
            {
                await _auditLogFacade.AddToAuditLogAsync(PageName, $"'{BackLabel}' button was activated.");
                _navigation.NavigateTo<DashboardViewModel>(NavigationFrameEnum.MainFrame);
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string> { { "Action", "ApplyingConfigurationUpdatesDoneViewModel.Back" } };
                _essLogger.LogError(ex, logProps);
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }
    }
}
