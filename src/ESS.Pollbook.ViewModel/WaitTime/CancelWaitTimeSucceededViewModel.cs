using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Facade.WaitTime;
using ESS.Pollbook.Core.Messaging;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.Command;
using GalaSoft.MvvmLight.Messaging;
using System;
using System.Collections.Generic;
using System.Windows.Input;

namespace ESS.Pollbook.ViewModel.WaitTime
{
    public class CancelWaitTimeSucceededViewModel : ViewModelBase
    {
        private readonly IFrameNavigationService _navigation;
        private readonly IMessenger _messenger;
        private readonly IWaitTimeFacade _waitTimeFacade;
        private readonly IEssLogger _essLogger;
        private readonly IAuditLogFacade _auditLogFacade;
        private string PageName => _navigation.PageName(this.GetType().FullName);
        public ICommand GenerateNewCommand => new RelayCommand(GenerateNew);
        public string GenerateNewLabel => UIText.GenerateNew;

        private int _issuedToken;

        public CancelWaitTimeSucceededViewModel(IFrameNavigationService navigationService, IMessenger messenger, IWaitTimeFacade waitTimeFacade, IEssLogger essLogger, IAuditLogFacade auditLogFacade)
        {
            _navigation = navigationService;
            _messenger = messenger;
            _waitTimeFacade = waitTimeFacade;
            _essLogger = essLogger;
            _auditLogFacade = auditLogFacade;

            _messenger.Register<LastWaitTimeTransactionMessage>(this, ReceiveIssuedToken);
        }

        private void ReceiveIssuedToken(LastWaitTimeTransactionMessage message)
        {
            _issuedToken = message.WaitTimeToken;
        }

        private void GenerateNew()
        {
            try
            {
                _auditLogFacade.AddToAuditLog(PageName, $"'{GenerateNewLabel}' button was activated.");
                int token = WaitTimeUtil.GenerateWaitTimeToken(_issuedToken);
                WaitTimeUtil.CreateWaitTimeGeneratedTransaction(token, _waitTimeFacade);
                _messenger.Send(new WaitTimeTokenMessage(waitTimeToken: token, timestamp: DateTime.Now));
                _navigation.NavigateTo<DisplayWaitTimeTokenViewModel>(NavigationFrameEnum.ContextFrame, false, withVerticalEffect: true);
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string>();
                logProps.Add("Action", "CancelWaitTimeSucceededViewModel.GenerateNew");
                _essLogger.LogError(ex, logProps);
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }
    }
}
