using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Core.Utilities;
using ESS.Pollbook.Facade.WaitTime;
using System;
using System.Threading.Tasks;

namespace ESS.Pollbook.ViewModel.WaitTime
{
	public static class WaitTimeUtil
	{
		public static int GenerateWaitTimeToken(int previousToken = 0)
		{
			int token;
			do
			{
				token = CryptoRandom.Instance.Next(1000, 10001);
			} while (token == previousToken);

			return token;
		}

        public static void CreateWaitTimeGeneratedTransaction(int token, IWaitTimeFacade waitTimeFacade)
        {
	        var transaction = PollbookTransactionDto.Create();
	        transaction.SystemIdentifier = SystemDetails.SystemId;
            transaction.PollingPlaceId = LoggedInPollplaceInfo.LoggedInPollPlace.PollingPlaceId;
            transaction.TransactionType = TransactionType.Custom.ToString();
            transaction.DeviceName = SystemDetails.MachineName;
            transaction.SerialNumber = SystemDetails.DeviceSerialNumber;
            transaction.ProcessingStatus = nameof(ProcessingStatus.Ignored);
            transaction.RecordSource = nameof(ProcessingRecordSource.Pollbook);

         Task.Run(async () => await waitTimeFacade.CreateWaitTimeGeneratedTransaction(transaction, DateTime.Now, token));
        }

        public static void CreateWaitTimeEnteredTransaction(IWaitTimeFacade waitTimeFacade)
        {
            var transaction = PollbookTransactionDto.Create();
            transaction.SystemIdentifier = SystemDetails.SystemId;
            transaction.PollingPlaceId = LoggedInPollplaceInfo.LoggedInPollPlace.PollingPlaceId;
            transaction.TransactionType = TransactionType.Custom.ToString();
            transaction.DeviceName = SystemDetails.MachineName;
            transaction.SerialNumber = SystemDetails.DeviceSerialNumber;
            transaction.ProcessingStatus = nameof(ProcessingStatus.Ignored);
            transaction.RecordSource = nameof(ProcessingRecordSource.Pollbook);

         TimeSpan waitTime = (TimeSpan)LoggedInPollplaceInfo.WaitTime;
            Task.Run(async () => await waitTimeFacade.CreateWaitTimeEnteredTransaction(transaction, DateTime.Now, (int)waitTime.TotalMinutes));
        }
    }
}
