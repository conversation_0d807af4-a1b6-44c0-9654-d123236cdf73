using ESS.Pollbook.Components.Business.Printing;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Facade.ElectionSettings;
using ESS.Pollbook.Core.Messaging;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.Command;
using GalaSoft.MvvmLight.Messaging;
using System;
using System.Collections.Generic;
using System.Windows.Input;

namespace ESS.Pollbook.ViewModel.WaitTime
{
    public class DisplayWaitTimeTokenViewModel : ViewModelBase
    {
        private readonly IFrameNavigationService _navigation;
        private readonly IMessenger _messenger;
        private readonly IPrintWaitTimeFactory _printWaitTimeFactory;
        private readonly IElectionSettingsFacade _electionSettingsFacade;
        private readonly IEssLogger _essLogger;
        private readonly IAuditLogFacade _auditLogFacade;

        private readonly string _pageName;

        private readonly ICommand _printCommand;
        public ICommand PrintCommand => _printCommand;
        public string PrintLabel => UIText.Print;

        private readonly string _electionName;
        private DateTime _timestamp;

        private int _waitTimeToken;
        public int WaitTimeToken
        {
            get
            {
                return _waitTimeToken;
            }
            set
            {
                _waitTimeToken = value;
                RaisePropertyChanged();
            }
        }

        private string _generatedDateTime;
        public string GeneratedDateTime
        {
            get
            {
                return _generatedDateTime;
            }
            set
            {
                _generatedDateTime = value;
                RaisePropertyChanged();
            }
        }

        public bool IsPrintButtonVisible => SystemConfiguration.ElectionConfiguration.PrintingEnabled;

        public DisplayWaitTimeTokenViewModel(IFrameNavigationService navigationService,
            IMessenger messenger,
            IPrintWaitTimeFactory printWaitTimeFactory,
            IElectionSettingsFacade electionSettingsFacade,
            IEssLogger essLogger,
            IAuditLogFacade auditLogFacade)
        {
            _navigation = navigationService;
            _messenger = messenger;
            _printWaitTimeFactory = printWaitTimeFactory;
            _electionSettingsFacade = electionSettingsFacade;
            _essLogger = essLogger;
            _auditLogFacade = auditLogFacade;

            _pageName = _navigation.PageName(this.GetType().FullName);

            _printCommand = new RelayCommand(Print);

            _messenger.Register<WaitTimeTokenMessage>(this, ReceiveWaitTimeToken);

            _electionName = ElectionSettings.ElectionName; 
        }

        private void ReceiveWaitTimeToken(WaitTimeTokenMessage message)
        {
            WaitTimeToken = message.WaitTimeToken;
            _timestamp = message.Timestamp;
            GeneratedDateTime = _timestamp.ToString("MM/dd/yyyy hh:mm tt");
        }

        private void Print()
        {
            try
            {
                _auditLogFacade.AddToAuditLog(_pageName, $"'{PrintLabel}' button was activated.");
                var response = _printWaitTimeFactory.PrintData(_electionName, _timestamp.ToString("MM/dd/yyyy hh:mm tt"), WaitTimeToken.ToString());

                if (response == null || !string.IsNullOrEmpty(response.PrinterErrorMessage))
                {
                    _navigation.NavigateTo<PrintWaitTimeTokenFailedViewModel>(NavigationFrameEnum.ContextFrame);
                }
                else
                {
                    _navigation.NavigateTo<PrintWaitTimeTokenSucceededViewModel>(NavigationFrameEnum.ContextFrame);
                }
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string>();
                logProps.Add("Action", "DisplayWaitTimeTokenViewModel.Print");
                _essLogger.LogError(ex, logProps);
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }
    }
}
