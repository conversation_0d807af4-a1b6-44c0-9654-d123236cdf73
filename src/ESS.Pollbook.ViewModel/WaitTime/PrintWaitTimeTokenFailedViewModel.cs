using ESS.Pollbook.Components.Business.Printing;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Facade.ElectionSettings;
using ESS.Pollbook.Core.Messaging;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.Command;
using GalaSoft.MvvmLight.Messaging;
using System;
using System.Collections.Generic;
using System.Windows.Input;

namespace ESS.Pollbook.ViewModel.WaitTime
{
    public class PrintWaitTimeTokenFailedViewModel : ViewModelBase
    {
        private readonly IFrameNavigationService _navigation;
        private readonly IMessenger _messenger;
        private readonly IPrintWaitTimeFactory _printWaitTimeFactory;
        private readonly IElectionSettingsFacade _electionSettingsFacade;
        private readonly IEssLogger _essLogger;
        private readonly IAuditLogFacade _auditLogFacade;

        private readonly string _pageName;

        private readonly ICommand _backCommand;
        private readonly ICommand _retryCommand;

        private string _electionName;
        private DateTime _timestamp;
        private int _token;

        public ICommand BackCommand => _backCommand;

        public ICommand RetryCommand => _retryCommand;

        public string BackLabel => UIText.Back;
        public string RetryLabel => UIText.Retry;

        public PrintWaitTimeTokenFailedViewModel(IFrameNavigationService frameNavigationService,
            IMessenger messenger,
            IPrintWaitTimeFactory printWaitTimeFactory,
            IElectionSettingsFacade electionSettingsFacade,
            IEssLogger essLogger,
            IAuditLogFacade auditLogFacade)
        {
            _navigation = frameNavigationService;
            _messenger = messenger;
            _printWaitTimeFactory = printWaitTimeFactory;
            _electionSettingsFacade = electionSettingsFacade;
            _essLogger = essLogger;
            _auditLogFacade = auditLogFacade;

            _pageName = _navigation.PageName(this.GetType().FullName);

            _backCommand = new RelayCommand(Back);
            _retryCommand = new RelayCommand(Retry);

            _messenger.Register<WaitTimeTokenMessage>(this, ReceiveWaitTimeInformation);

            _electionName = ElectionSettings.ElectionName;
        }

        private void ReceiveWaitTimeInformation(WaitTimeTokenMessage message)
        {
            _timestamp = message.Timestamp;
            _token = message.WaitTimeToken;
        }

        private void Back()
        {
            try
            {
                _auditLogFacade.AddToAuditLog(_pageName, $"'{BackLabel}' button was activated.");
                _navigation.GoBack();
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string>();
                logProps.Add("Action", "PrintWaitTimeTokenFailedViewModel.Back");
                _essLogger.LogError(ex, logProps);
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }

        private void Retry()
        {
            try
            {
                _auditLogFacade.AddToAuditLog(_pageName, $"'{RetryLabel}' button was activated.");
                var response = _printWaitTimeFactory.PrintData(_electionName, _timestamp.ToString("MM/dd/yyyy hh:mm tt"), _token.ToString());

                if (response == null || !string.IsNullOrEmpty(response.PrinterErrorMessage))
                {
                    // Just stay on this page
                }
                else
                {
                    _navigation.NavigateTo<PrintWaitTimeTokenSucceededViewModel>(NavigationFrameEnum.ContextFrame);
                }
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string>();
                logProps.Add("Action", "PrintWaitTimeTokenFailedViewModel.Retry");
                _essLogger.LogError(ex, logProps);
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, SystemDetails.GenericErrorMessage));
            }
        }
    }
}
