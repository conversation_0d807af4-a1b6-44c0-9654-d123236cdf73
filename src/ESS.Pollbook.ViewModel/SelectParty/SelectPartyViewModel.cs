using ESS.Pollbook.Core.Commands;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Core.UICore;
using ESS.Pollbook.Facade.AuditLog;
using ESS.Pollbook.Facade.PagesTitle;
using ESS.Pollbook.Facade.PollbookDefinedText;
using ESS.Pollbook.Facade.PrecinctSplit;
using ESS.Pollbook.Facade.VoterBallot;
using ESS.Pollbook.ViewModel.Affidavit;
using ESS.Pollbook.Core.Messaging;
using ESS.Pollbook.ViewModel.SelectBallotType;
using ESS.Pollbook.ViewModel.Utils;
using ESS.Pollbook.ViewModel.VoterBallot;
using GalaSoft.MvvmLight;
using GalaSoft.MvvmLight.Command;
using GalaSoft.MvvmLight.Messaging;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using System.Windows.Input;
using static ESS.Pollbook.ViewModel.IssueBallotHelper;

namespace ESS.Pollbook.ViewModel.SelectParty
{
	public class SelectPartyViewModel : ViewModelBase
	{
      #region Properties

      private readonly IFrameNavigationService _navigation;
      private readonly IMessenger _messenger;
      private readonly IEssLogger _essLogger;
      private readonly IPagesTitleFacade _pagesTitleFacade;
      private readonly IVoterBallotFacade _voterBallotFacade;
      private readonly IPrecinctSplitFacade _precinctSplitFacade;
      private readonly IPollbookDefinedTextFacade _pollbookDefinedTextFacade;
      private readonly IAuditLogFacade _auditLogFacade;
      private readonly IPrintBallotUtil _printBallotUtil;

      private string PageName => _navigation.PageName(GetType().FullName);

      public ICommand BackCommand => new RelayCommand(Back);
      public ICommandAsync NextCommandAsync => new CommandAsync(NextAsync);
      public ICommand ClearCommand => new RelayCommand(ClearAndReset);

      public string BackLabel => UIText.Back;

      private bool _issueBallotInProgress;

      private static VoterDto Voter => IssueBallotHelper.Voter;

      private List<VoterBallotDto> _ballotStyleTypeItems;

      public List<VoterBallotDto> BallotStyleTypeItems
      {
         get => _ballotStyleTypeItems;
         set => Set(ref _ballotStyleTypeItems, value);
      }

      private List<BallotStyleTypeDto> _ballotStyleTypeNames = new List<BallotStyleTypeDto>();

      public List<BallotStyleTypeDto> BallotStyleTypeNames
      {
         get => _ballotStyleTypeNames;
         set
         {
            Set(ref _ballotStyleTypeNames, value);
            RaisePropertyChanged(nameof(NextEnabled));
         }
      }

      private BallotStyleTypeDto _selectedBallotStyleTypeName;

      public BallotStyleTypeDto SelectedBallotStyleTypeName
      {
         get => _selectedBallotStyleTypeName;
         set
         {
            Set(ref _selectedBallotStyleTypeName, value);
            GetSelectedBallotStyleTypeItem();
            RaisePropertyChanged(nameof(NextEnabled));
         }
      }

      private List<PartyDto> _partyNames = new List<PartyDto>();

      public List<PartyDto> PartyNames
      {
         get => _partyNames;
         set => Set(ref _partyNames, value);
      }

      private PartyDto _selectedPartyName;

      public PartyDto SelectedPartyName
      {
         get => _selectedPartyName;
         set
         {
            if (!Set(ref _selectedPartyName, value)) return;

            LoadBallotStyleTypes();

            RaisePropertyChanged(nameof(IsBallotStyleTypeEnabled));
            RaisePropertyChanged(nameof(NextEnabled));
         }
      }

      private VoterBallotDto _selectedPrecinctParty;

      public string LabelContent =>
         _pagesTitleFacade.PopulateTitleContent(IssueBallotHelper.Voter.VoterBallotDto.IsProvisional,
            IssueBallotHelper.IsReissuingBallot);

      public bool NextEnabled => SelectedBallotStyleTypeItem != null;

      public static bool IsGeneralElectionType => string.Equals(
         SystemConfiguration.ElectionConfiguration.ElectionType, nameof(ElectionTypeEnum.General),
         StringComparison.OrdinalIgnoreCase);

      public static bool IsProvisional => Voter.VoterBallotDto.IsProvisional;

      public bool IsBallotStyleTypeVisible => IsProvisional;

      public bool IsBallotStyleTypeEnabled => SelectedPartyName != null;


      /// <summary>
      ///    get{}
      ///    agreed upon notes with Audrey
      ///    1) if provisional: always has precinct. and always show party list in any type of primary
      ///    2) standard voter never sees this page.
      ///    3) if provisional, always show unless a general
      ///    4) open primary - always show
      ///    notes from Audrey
      ///    in closed primary you can't pick your party, don't show - unless provisional
      ///    Provisional- Precinct Always displays
      ///    Provisional- Party always displays in a primary election (Open, Closed, Mixed)
      ///    Standard- General & Closed Primary will not see the page
      ///    Standard- Open Primary will see the page
      ///    Standard- Mixed Primary could see the page.
      ///    default to false
      /// </summary>
      public bool IsPartyListVisible
      {
         get
         {
            if (string.Equals(SystemConfiguration.ElectionConfiguration.ElectionType,
                   nameof(ElectionTypeEnum.OpenPrimary), StringComparison.OrdinalIgnoreCase)
                || string.Equals(SystemConfiguration.ElectionConfiguration.ElectionType,
                   nameof(ElectionTypeEnum.ClosedPrimary), StringComparison.OrdinalIgnoreCase)
                || string.Equals(SystemConfiguration.ElectionConfiguration.ElectionType,
                   nameof(ElectionTypeEnum.MixedPrimary), StringComparison.OrdinalIgnoreCase)
               )
               return true;

            return !string.Equals(SystemConfiguration.ElectionConfiguration.ElectionType,
                      nameof(ElectionTypeEnum.General), StringComparison.OrdinalIgnoreCase)
                   && IssueBallotHelper.Voter.VoterBallotDto.IsProvisional;
         }
      }

      public bool IsPartyEnabled => SelectedPrecinctSplitItem != null;

      private string _searchTerm = string.Empty;

      public string SearchTerm
      {
         get => _searchTerm;
         set
         {
            if (_searchTerm == value) return;

            Clear();

            if (!Set(ref _searchTerm, value)) return;

            RaisePropertyChanged(nameof(DisplayResults));
            RaisePropertyChanged(nameof(NoResults));
            Search();
            NavigationCommands.BrowseBack.InputGestures.Clear();
         }
      }

      private List<PrecinctSplitDto> _results = new List<PrecinctSplitDto>();

      public ReadOnlyObservableCollection<PrecinctSplitDto> Results => GetResults();
      public bool NoResults => SearchTerm.Length > 0 && Results.Count == 0;

      private bool _displayResults;

      public bool DisplayResults
      {
         get => _displayResults && SearchTerm.Length > 0 && Results.Count > 0 && SelectedPrecinctSplitItem == null;
         set => Set(ref _displayResults, value);
      }

      private VoterBallotDto _selectedBallotStyleTypeItem;

      public VoterBallotDto SelectedBallotStyleTypeItem
      {
         get => _selectedBallotStyleTypeItem;

         set
         {
            if (!Set(ref _selectedBallotStyleTypeItem, value)) return;

            _selectedPrecinctParty = GetPrecinctPartyBySelectedBallotStyleTypeItem();
            RaisePropertyChanged(nameof(NextEnabled));
         }
      }

      private PrecinctSplitDto _selectedPrecinctSplitItem;

      public PrecinctSplitDto SelectedPrecinctSplitItem
      {
         get => _selectedPrecinctSplitItem;

         set
         {
            if (!Set(ref _selectedPrecinctSplitItem, value)) return;
            Clear();

            if (SelectedPrecinctSplitItem == null)
            {
               _partyNames.Clear();
               _selectedPartyName = null;

               _ballotStyleTypeNames.Clear();
               _selectedBallotStyleTypeName = null;
            }
            else
            {
               _searchTerm = _selectedPrecinctSplitItem == null
                  ? string.Empty
                  : _selectedPrecinctSplitItem.PrecinctSplitName;
               _results = Task.Run(async () =>
                  await _precinctSplitFacade.GetPrecinctSplitsNameByPollingPlaceAndSearchTerm(
                     LoggedInPollplaceInfo.LoggedInPollPlace.PollingPlaceId, SearchTerm)).Result.ToList();
               LoadParties();
            }

            LoadBallotStyleTypes();

            RaisePropertyChanged(nameof(SearchTerm));
            RaisePropertyChanged(nameof(DisplayResults));
            RaisePropertyChanged(nameof(NextEnabled));
            RaisePropertyChanged(nameof(IsBallotStyleTypeVisible));

            RaisePropertyChanged(nameof(IsPartyEnabled));
            RaisePropertyChanged(nameof(SelectedPartyName));
            RaisePropertyChanged(nameof(PartyNames));

            RaisePropertyChanged(nameof(SelectedBallotStyleTypeName));
            RaisePropertyChanged(nameof(BallotStyleTypeNames));
         }
      }

      private string _selectedLanguage;

      private string SelectedLanguage
      {
         get => _selectedLanguage;
         set
         {
            Set(ref _selectedLanguage, value);
            RaisePropertyChanged(nameof(Title));
            RaisePropertyChanged(nameof(PartyHeader));
            RaisePropertyChanged(nameof(PrecinctHeader));
            RaisePropertyChanged(nameof(BallotStyleTypeHeader));

            PartyWaterMark =
               _pollbookDefinedTextFacade.GetPollbookDefinedTextForLanguage("Select", SelectedLanguage);
            BallotStyleWaterMark = PartyWaterMark;

            RaisePropertyChanged(nameof(LabelContent));
         }
      }

      private string PartyAndOrPrecinctSubTitle =>
         _pollbookDefinedTextFacade.GetPollbookDefinedTextForLanguage("Select_Party_Precinct_Title",
            SelectedLanguage);

      public string Title => PartyAndOrPrecinctSubTitle;

      public string BallotStyleTypeHeader =>
         _pollbookDefinedTextFacade.GetPollbookDefinedTextForLanguage("Ballot_Style_Type_Header", SelectedLanguage);

      public string PartyHeader =>
         _pollbookDefinedTextFacade.GetPollbookDefinedTextForLanguage("Party_Header", SelectedLanguage);

      public string PrecinctHeader =>
         _pollbookDefinedTextFacade.GetPollbookDefinedTextForLanguage("Precinct_Header", SelectedLanguage);

      private string _partyWaterMark;

      public string PartyWaterMark
      {
         get => _partyWaterMark;
         set => Set(ref _partyWaterMark, value);
      }

      private string _ballotStyleWaterMark;

      public string BallotStyleWaterMark
      {
         get => _ballotStyleWaterMark;
         set => Set(ref _ballotStyleWaterMark, value);
      }

      private string _nextButtonText;

      public string NextButtonText
      {
         get => _nextButtonText;
         private set => Set(ref _nextButtonText, value);
      }

      public bool ShowFlipScreen => SystemConfiguration.ElectionConfiguration.EnableScreenRotation;

      private static bool IsReissuingBallot => IssueBallotHelper.IsReissuingBallot;

      #endregion

      public SelectPartyViewModel(IFrameNavigationService navigationService,
         IMessenger messengerService,
         IPagesTitleFacade pagesTitleFacade,
         IPrecinctSplitFacade precinctSplitFacade,
         IVoterBallotFacade voterBallotFacade,
         IEssLogger essLogger,
         IPollbookDefinedTextFacade pollbookDefinedTextFacade,
         IAuditLogFacade auditLogFacade,
         IPrintBallotUtil printBallotUtil)
      {
         _navigation = navigationService;
         _messenger = messengerService;
         _pagesTitleFacade = pagesTitleFacade;
         _precinctSplitFacade = precinctSplitFacade;
         _voterBallotFacade = voterBallotFacade;
         _essLogger = essLogger;
         _pollbookDefinedTextFacade = pollbookDefinedTextFacade;
         _auditLogFacade = auditLogFacade;
         _printBallotUtil = printBallotUtil;

         _messenger.Register<ResetPartiesMessage>(this, ResetParties);
         _messenger.Register<LanguageSelectedMessage>(this, LanguageSelectedMessageHandler);
      }

      public void PageIsInitialized()
      {
         try
         {
            _issueBallotInProgress = false;
            SelectedLanguage = _pollbookDefinedTextFacade.GetPollbookDefinedTextCurrentLanguage();
            PartyWaterMark =
               _pollbookDefinedTextFacade.GetPollbookDefinedTextForLanguage("Select", SelectedLanguage);
            BallotStyleWaterMark = PartyWaterMark;

            Reset();
            RaisePropertyChanged(nameof(NextEnabled));

            if (SystemConfiguration.ElectionConfiguration.ExpressVoteEnabled(
                   LoggedInPollplaceInfo.IsEarlyVotingPollPlace,
                   Voter.VoterBallotDto.IsProvisional))
               NextButtonText = UIText.Next;
            else
               NextButtonText = IsReissuingBallot ? UIText.ReissueBallot : UIText.IssueBallot;

            if (SelectedPrecinctSplitItem == null
                && (
                   IssueBallotHelper.Voter.ValidPoll ||
                   LoggedInPollplaceInfo.LoggedInPollPlace.PollingPlaceVoteCenterIndicator == 1
                )
                && !_issueBallotInProgress)
            {
	            _selectedPrecinctSplitItem =
		            _voterBallotFacade.GetPrecinctSplitNameByPrecinctSplitId(Voter.PrecinctSplitId);
	            _searchTerm = _selectedPrecinctSplitItem?.PrecinctSplitName;
	            _results = _voterBallotFacade.GetPrecinctSplitNamesByPollingSearchTerm(SearchTerm);

               RaisePropertyChanged(nameof(SearchTerm));
               RaisePropertyChanged(nameof(NoResults));
            }

            LoadParties();
            LoadBallotStyleTypes();

            RaisePropertyChanged(nameof(SelectedPrecinctSplitItem));
            RaisePropertyChanged(nameof(IsBallotStyleTypeEnabled));
            RaisePropertyChanged(nameof(IsPartyEnabled));
         }
         catch (Exception ex)
         {
            var logProps = new Dictionary<string, string>
               {{"Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}"}};
            _essLogger.LogError(ex, logProps);
         }
      }

      private void GetSelectedBallotStyleTypeItem()
      {
         try
         {
            if (SelectedPrecinctSplitItem == null || SelectedPartyName == null || SelectedBallotStyleTypeName == null)
            {
               SelectedBallotStyleTypeItem = null;
               return;
            }

            SelectedBallotStyleTypeItem =
               _voterBallotFacade.GetBallotStyleByPrecinctSplitIdAndPartyIdAndBallotStyleTypeId(
                  SelectedPrecinctSplitItem.PrecinctSplitId,
                  SelectedPartyName.PartyId,
                  _selectedBallotStyleTypeName.BallotStyleTypeId);
         }
         catch (Exception ex)
         {
            var logProps = new Dictionary<string, string>
               {{"Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}"}};
            _essLogger.LogError(ex, logProps);
         }
      }

      private void UpdateBallotStyleTypeName()
      {
         if (BallotStyleTypeNames == null || BallotStyleTypeNames.Count == 0)
         {
            var logProps = new Dictionary<string, string>
               {{"Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name}};
            _essLogger.LogError("There are no available Ballot Style Types", logProps);

            return;
         }

         _selectedBallotStyleTypeName = BallotStyleTypeNames.Count == 1
            ? BallotStyleTypeNames.First()
            : BallotStyleTypeNames
               .Find(o => o.BallotStyleTypeId == Voter.VoterBallotStyleTypeJurisdictionEnumValueId);

         RaisePropertyChanged(nameof(SelectedBallotStyleTypeName));

         GetSelectedBallotStyleTypeItem();
      }

      private VoterBallotDto GetPrecinctPartyBySelectedBallotStyleTypeItem()
      {
         if (SelectedPrecinctSplitItem == null
             || SelectedPartyName == null
             || SelectedBallotStyleTypeName == null)
            return null;

         return _voterBallotFacade
            .GetBallotStyleByPrecinctSplitIdAndPartyIdAndBallotStyleTypeId(
               SelectedPrecinctSplitItem.PrecinctSplitId,
               SelectedPartyName.PartyId,
               SelectedBallotStyleTypeName.BallotStyleTypeId);
      }

      private void Back()
      {
         try
         {
            _auditLogFacade.AddToAuditLog(PageName, $"'{BackLabel}' button was activated.");
            ScreenDisplayFlip.FlipToDefault();

            _messenger.Send(new StatusBarMessage(true));
            _navigation.NavigateTo<DashboardViewModel>(NavigationFrameEnum.MainFrame, false);
            _navigation.GoBack();
         }
         catch (Exception ex)
         {
            var logProps = new Dictionary<string, string>
               {{"Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}"}};
            _essLogger.LogError(ex, logProps);
         }
      }

      private async Task NextAsync()
      {
         try
         {
            await _auditLogFacade.AddToAuditLogAsync(PageName, $"'{NextButtonText}' button was activated.");
            ScreenDisplayFlip.FlipToDefault();

            if (_issueBallotInProgress) return;

            _issueBallotInProgress = true;

            if (IsProvisional)
            {
               SelectedBallotStyleTypeItem.IsProvisional = IsProvisional;
               SelectedBallotStyleTypeItem.ProvisionalId = Voter.VoterBallotDto.ProvisionalId;
               SelectedBallotStyleTypeItem.ProvisionalReasonEnumId = Voter.VoterBallotDto.ProvisionalReasonEnumId;
               SelectedBallotStyleTypeItem.ProvisionalReasonOtherText = Voter.VoterBallotDto.ProvisionalReasonOtherText;
               Voter.VoterBallotDto = SelectedBallotStyleTypeItem;
            }

            Voter.PrecinctParty = new PrecinctPartyDto
            {
	            PrecinctSplitId = SelectedBallotStyleTypeItem.PrecinctSplitId,
	            PrecinctName = SelectedBallotStyleTypeItem.PrecinctName,
	            PrecinctSplitDisplayName = SelectedBallotStyleTypeItem.PrecinctSplitDisplayName,
	            PartyId = SelectedBallotStyleTypeItem.PartyId,
	            PartySourcePartyKey = SelectedBallotStyleTypeItem.PartySourcePartyKey,
	            PartyName = SelectedBallotStyleTypeItem.PartyName,
	            PartyDisplayName = SelectedBallotStyleTypeItem.PartyDisplayName,
	            PartyDisplayOrderNumber = SelectedBallotStyleTypeItem.PartyDisplayOrderNumber,
	            BallotStyleId = SelectedBallotStyleTypeItem.BallotStyleId,
	            BallotStyleCode = SelectedBallotStyleTypeItem.BallotStyleCode,
	            PrecinctSplitBallotStyleId = SelectedBallotStyleTypeItem.PrecinctSplitBallotStyleId,
	            BallotStyleShortDescriptionText = SelectedBallotStyleTypeItem.BallotStyleDescription,
	            PartyAffiliationAffidavit =
		            _voterBallotFacade.GetValuePartyAffiliationAffidavit(Voter.PartyId,
			            SelectedBallotStyleTypeItem.PartyId)
            };

            _messenger.Send(new StatusBarMessage(true));

            var isAffidavitConditionMet = SystemConfiguration.ElectionConfiguration.EnablePartyAffiliationAffidavit
                                 && IssueBallotHelper.Voter.PartyId != IssueBallotHelper.Voter.PrecinctParty.PartyId;

            if (isAffidavitConditionMet)
            {
               switch (SystemConfiguration.ElectionConfiguration.EnableTexasWorkflow)
               {
                  case false when (Voter.PrecinctParty.PartyAffiliationAffidavit 
                                   || IssueBallotHelper.Voter.VoterBallotDto.IsProvisional):
                     _navigation.NavigateTo<PartyAffiliationAffidavitViewModel>(NavigationFrameEnum.ContextFrame, suppressHorizontalEffect: true);
                     _issueBallotInProgress = false;
                     return;
                  case true when Voter.PrecinctParty.PartyAffiliationAffidavit:
                     var message =
                        _pollbookDefinedTextFacade.GetPollbookDefinedTextForLanguage("PartyChangeDropDownSubTitle",
                           SelectedLanguage);
                     var gcDto = new GenericConfirmationDto()
                     {
                        Title = _pollbookDefinedTextFacade.GetPollbookDefinedTextForLanguage("PartyChangeDropDownTitle", SelectedLanguage),
                        Message = message.Replace("[other]", Voter.PartyName).Replace("[new]", _selectedPartyName.PartyDisplayName),
                        YesButtonLabel = _pollbookDefinedTextFacade.GetPollbookDefinedTextForLanguage("YesButtonLabel", SelectedLanguage),
                        NoButtonLabel = _pollbookDefinedTextFacade.GetPollbookDefinedTextForLanguage("NoButtonLabel", SelectedLanguage)
                     };
                     _navigation.NavigateTo<GenericConfirmationViewModel>(parameter: gcDto, navigationFrame: NavigationFrameEnum.ModalFrame, registerHistory: false, suppressHorizontalEffect: true, withVerticalEffect: true);
                     _messenger.Send(gcDto);
                     _issueBallotInProgress = false;
                     return;
               }
            }

            if (NextButtonText == UIText.Next)
            {
               if (SelectedBallotType == BallotType.Unknown)
               {
                  _navigation.NavigateTo<DashboardViewModel>(NavigationFrameEnum.MainFrame, false, suppressHorizontalEffect: true);
                  _navigation.NavigateTo<SelectBallotTypeViewModel>(NavigationFrameEnum.ContextFrame, suppressHorizontalEffect: true);
               }
               else
               {
                  Voter.NeedExpressVoteCard = SelectedBallotType == BallotType.ExpressVote;

                  if (SystemConfiguration.ElectionConfiguration.DacEnabled
                      && SystemConfiguration.ElectionConfiguration.ExpressVotePrintingEnabled(
                         LoggedInPollplaceInfo.IsEarlyVotingPollPlace,
                         IssueBallotHelper.Voter.VoterBallotDto.IsProvisional))
                  {
                     _navigation.NavigateTo<ActivationCardViewModel>(NavigationFrameEnum.ContextFrame, suppressHorizontalEffect: true);
                     _messenger.Send(new RegisterSmartCardEventsMessage());
                     return;
                  }

                  if (SystemConfiguration.ElectionConfiguration.ExpressVoteEnabled(
                          LoggedInPollplaceInfo.IsEarlyVotingPollPlace,
                          Voter.VoterBallotDto.IsProvisional) &&
                      SystemConfiguration.ElectionConfiguration.ExpressVotePrintingEnabled(
                          LoggedInPollplaceInfo.IsEarlyVotingPollPlace,
                          Voter.VoterBallotDto.IsProvisional))
                  {
                      Voter.NeedExpressVoteCard = true;
                      _messenger.Send(new ExpressVoteCardActivationMessage());

                      _navigation.NavigateTo<ExpressVoteActivationCardViewModel>(
                          NavigationFrameEnum.ContextFrame, false, suppressHorizontalEffect: true);
                  }
                  else
                  {
                      _messenger.Send(new IssueBallotMessage(GetType().Name));
                      _navigation.NavigateTo<VoterIssueBallotViewModel>(NavigationFrameEnum.ContextFrame, false, suppressHorizontalEffect: true);
                  }
               }
            }
            else
            {
               await _printBallotUtil.PrintBallotAsync(Voter);
            }
         }
         catch (Exception ex)
         {
            var logProps = new Dictionary<string, string>
               {{"Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name}};
            _essLogger.LogError(ex, logProps);
            _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error,
               SystemDetails.GenericErrorMessage));
         }
      }

      private void ClearAndReset()
      {
         try
         {
            _auditLogFacade.AddToAuditLog(PageName, $"'{UIText.Clear}' button was activated.");
            Reset();
         }
         catch (Exception ex)
         {
             var logProps = new Dictionary<string, string>
                 {{"Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}"}};
                _essLogger.LogError(ex, logProps);
            _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error,
               SystemDetails.GenericErrorMessage));
         }
      }

      private void LoadBallotStyleTypes()
      {
         if (SelectedPrecinctSplitItem == null || SelectedPartyName == null) return;

            try
            {
                BallotStyleTypeNames =
                   _voterBallotFacade.LoadBallotStyleTypes(SelectedPrecinctSplitItem.PrecinctSplitId,
                   SelectedPartyName?.PartyId ?? NullIf(Voter.VoterBallotDto.PartyId, 0) ?? Voter.PartyId);
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string>
                {
                    {"Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name}
                };
                _essLogger.LogError(ex, logProps);

                BallotStyleTypeNames = new List<BallotStyleTypeDto>();
            }

         UpdateBallotStyleTypeName();
         RaisePropertyChanged(nameof(IsBallotStyleTypeVisible));
      }

      private void LoadParties()
      {
         try
         {
            if (Voter?.VoterBallotDto == null || (IsProvisional && SelectedPrecinctSplitItem == null)) return;

            UpdatePartyNames();
            RaisePropertyChanged(nameof(IsPartyListVisible));
         }
         catch (Exception ex)
         {
            var logProps = new Dictionary<string, string>
               {{"Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name}};
            _essLogger.LogError(ex, logProps);
         }
      }

      private void UpdatePartyNames()
      {
         var precinctSplitId = IsProvisional
            ? SelectedPrecinctSplitItem.PrecinctSplitId
            : Voter.PrecinctSplitId;

         PartyNames = IsProvisional
            ? _voterBallotFacade.GetParties(precinctSplitId)
            : _voterBallotFacade.GetPartiesByVoter(Voter);

         SelectedPartyName = IsPartyListVisible ?  null : _partyNames.FirstOrDefault();

         RaisePropertyChanged(nameof(BallotStyleWaterMark));
         RaisePropertyChanged(nameof(SelectedPartyName));
      }

      private void ResetParties(ResetPartiesMessage partiesMessage)
      {
         try
         {
            Reset();
         }
         catch (Exception ex)
         {
            var logProps = new Dictionary<string, string>
               {{"Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name}};
            _essLogger.LogError(ex, logProps);
         }
      }

      private void Search()
      {
         try
         {
            if (string.IsNullOrWhiteSpace(SearchTerm))
            {
               Clear();
            }
            else
            {
               SelectedPrecinctSplitItem = null;
               if (_precinctSplitFacade == null) return;

               _results = _voterBallotFacade.GetPrecinctSplitNamesByPollingSearchTerm(SearchTerm);

               DisplayResults = true;

               RaisePropertyChanged(nameof(NextEnabled));
               RaisePropertyChanged(nameof(Results));
               RaisePropertyChanged(nameof(NoResults));
               RaisePropertyChanged(nameof(DisplayResults));
            }
         }
         catch (Exception ex)
         {
            var logProps = new Dictionary<string, string>
               {{"Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name}};
            _essLogger.LogError(ex, logProps);
         }
      }

      private void Reset()
      {
         SearchTerm = string.Empty;
         SelectedPrecinctSplitItem = null;
         _selectedPrecinctParty = null;
         _results.Clear();

         Clear();

         RaisePropertyChanged(nameof(Results));
         RaisePropertyChanged(nameof(NoResults));
         RaisePropertyChanged(nameof(NextEnabled));
      }

      private void Clear()
      {
         SelectedBallotStyleTypeItem = null;
         SelectedPartyName = null;
         DisplayResults = false;
      }

      private ReadOnlyObservableCollection<PrecinctSplitDto> GetResults()
      {
         return new ReadOnlyObservableCollection<PrecinctSplitDto>(
            new ObservableCollection<PrecinctSplitDto>(_results));
      }

      private void LanguageSelectedMessageHandler(LanguageSelectedMessage msg)
      {
         SelectedLanguage = msg.Language;
      }

      private T? NullIf<T>(T left, T right) where T : struct
      {
         return EqualityComparer<T>.Default.Equals(left, right) ? (T?) null : left;
      }
   }
}