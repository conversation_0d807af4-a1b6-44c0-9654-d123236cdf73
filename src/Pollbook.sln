
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.3.32819.101
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Pollbook", "Pollbook\Pollbook.csproj", "{C747BDF7-F2A1-4BC1-90C4-90D5807EB54C}"
	ProjectSection(ProjectDependencies) = postProject
		{36DDDE06-C65A-4334-8354-A931AFABB0E8} = {36DDDE06-C65A-4334-8354-A931AFABB0E8}
		{538196C4-10A3-45E0-BA05-9A1A85C447DB} = {538196C4-10A3-45E0-BA05-9A1A85C447DB}
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "0 - CrossCutting", "0 - CrossCutting", "{A391A854-F542-401A-8E4C-D5737EC23736}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ESS.Pollbook.Core", "ESS.Pollbook.Core\ESS.Pollbook.Core.csproj", "{258F9726-45F1-49B5-B51C-85E56A2CD2F8}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "1 - UX", "1 - UX", "{EDE0E8CF-E88B-4048-88FE-077398785C9D}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "3 - Facade", "3 - Facade", "{C5D927C2-6483-4C28-AADB-6CECE50EF5E5}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ESS.Pollbook.Facade", "ESS.Pollbook.Facade\ESS.Pollbook.Facade.csproj", "{C3782A32-191B-4573-AC68-3112D4BA7101}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "4 - Components", "4 - Components", "{3743E600-3A01-4F40-B3D5-4F36C3657435}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "5 - Hardware", "5 - Hardware", "{8751CA7F-2B98-47A0-85FF-DB88468F4ED9}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ESS.Pollbook.ViewModel", "ESS.Pollbook.ViewModel\ESS.Pollbook.ViewModel.csproj", "{4B137F05-B98F-437C-8C0C-FEFD43896923}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "2 - ViewModel", "2 - ViewModel", "{6EC72848-3EDE-445E-B828-C817CD2BBA76}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ESS.Pollbook.Components.Business", "ESS.Pollbook.Components.Business\ESS.Pollbook.Components.Business.csproj", "{BCC37AC3-9DD7-4DC6-B13F-26F85204CB99}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ESS.Pollbook.Components.Repository", "ESS.Pollbook.Components.Repository\ESS.Pollbook.Components.Repository.csproj", "{2584B9A5-FEA2-4A58-832D-4A3E53639F03}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ESS.Pollbook.Hardware", "ESS.Pollbook.Hardware\ESS.Pollbook.Hardware.csproj", "{538196C4-10A3-45E0-BA05-9A1A85C447DB}"
EndProject
Project("{D954291E-2A0B-460D-934E-DC6B0785DB48}") = "Pollbook.Shared", "Pollbook.Shared\Pollbook.Shared.shproj", "{84DF89C1-06D9-40FD-8B1C-38B038C9CCAB}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "7 - Networking", "7 - Networking", "{6247A5A7-F068-4C90-B806-6A277E7B728F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ESS.ELLEGO.Rest", "ESS.ELLEGO.Rest\ESS.ELLEGO.Rest.csproj", "{54B7E228-3FDE-47F0-A17F-49F22E966690}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ESS.Pollbook.Components.Business.Test", "ESS.Pollbook.Components.Business.Test\ESS.Pollbook.Components.Business.Test.csproj", "{939B5FE1-98CE-4312-9F50-5E4814D3FD25}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "8 - Integration", "8 - Integration", "{EF3EFE4C-B1F9-45EF-9173-FF076C9C88FB}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ESS.Pollbook.Core.Test", "ESS.Pollbook.Core.Test\ESS.Pollbook.Core.Test.csproj", "{83A9A8F6-AAB9-4846-A2AC-F07907AF2872}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ESS.Pollbook.OAuth", "ESS.Pollbook.OAuth\ESS.Pollbook.OAuth.csproj", "{E749AF37-9C1B-42E5-A6D5-14D7D88D4A34}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ESS.ELLEGO.ServiceBus.Core", "ESS.ELLEGO.ServiceBus.Core\ESS.ELLEGO.ServiceBus.Core.csproj", "{047C2BAA-2041-4EA0-8167-16DC70E2AAB7}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ESS.Integration.SmartCardService.Test", "ESS.Integration.SmartCardService.Test\ESS.Integration.SmartCardService.Test.csproj", "{200EE076-9112-4495-9300-528241C29B63}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ESS.Integration.SmartCardService", "ESS.Integration.SmartCardService\ESS.Integration.SmartCardService.csproj", "{B23B4909-77A8-468C-8FFE-55E76FFD7188}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ESS.Pollbook.Hardware.Test", "ESS.Pollbook.Hardware.Test\ESS.Pollbook.Hardware.Test.csproj", "{08833D23-8729-4A53-8AE7-8B632528FCB9}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ESS.Pollbook.Components.Repository.Test", "ESS.Pollbook.Components.Repository.Test\ESS.Pollbook.Components.Repository.Test.csproj", "{98C7B62A-342E-4B2C-8D21-3046B55ABCD9}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ESS.Pollbook.ViewModel.Test", "ESS.Pollbook.ViewModel.Test\ESS.Pollbook.ViewModel.Test.csproj", "{8092E0B6-3D00-4AC0-9C61-EC550B51A3E1}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ESS.Pollbook.Facade.Test", "ESS.Pollbook.Facade.Test\ESS.Pollbook.Facade.Test.csproj", "{BABD7AAF-2042-40A6-82AD-9DA3A210991D}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "6 - DataAccess", "6 - DataAccess", "{A451EEE7-B1E7-4592-B4F1-A9B8E5D71EAD}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ESS.ELLEGO.ServiceBus.Core.Test", "ESS.ELLEGO.ServiceBus.Core.Test\ESS.ELLEGO.ServiceBus.Core.Test.csproj", "{A1DE0B4F-A79C-495F-8F75-92AC8CE63C15}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ESS.Pollbook.DataAccess", "ESS.Pollbook.DataAccess\ESS.Pollbook.DataAccess.csproj", "{36DDDE06-C65A-4334-8354-A931AFABB0E8}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "9 - MasterProcessor", "9 - MasterProcessor", "{91627A5A-3162-4771-8DB0-6DE9F09A0520}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ESS.Pollbook.MasterProcessor", "ESS.Pollbook.MasterProcessor\ESS.Pollbook.MasterProcessor.csproj", "{964C0930-B431-4C95-857C-839F58C58C73}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ESS.Pollbook.MasterProcessor.Abstractions", "ESS.Pollbook.MasterProcessor.Abstractions\ESS.Pollbook.MasterProcessor.Abstractions.csproj", "{EF162430-4C29-4669-94C0-0D46E1ABC37A}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{C747BDF7-F2A1-4BC1-90C4-90D5807EB54C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C747BDF7-F2A1-4BC1-90C4-90D5807EB54C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C747BDF7-F2A1-4BC1-90C4-90D5807EB54C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C747BDF7-F2A1-4BC1-90C4-90D5807EB54C}.Release|Any CPU.Build.0 = Release|Any CPU
		{258F9726-45F1-49B5-B51C-85E56A2CD2F8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{258F9726-45F1-49B5-B51C-85E56A2CD2F8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{258F9726-45F1-49B5-B51C-85E56A2CD2F8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{258F9726-45F1-49B5-B51C-85E56A2CD2F8}.Release|Any CPU.Build.0 = Release|Any CPU
		{C3782A32-191B-4573-AC68-3112D4BA7101}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C3782A32-191B-4573-AC68-3112D4BA7101}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C3782A32-191B-4573-AC68-3112D4BA7101}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C3782A32-191B-4573-AC68-3112D4BA7101}.Release|Any CPU.Build.0 = Release|Any CPU
		{4B137F05-B98F-437C-8C0C-FEFD43896923}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4B137F05-B98F-437C-8C0C-FEFD43896923}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4B137F05-B98F-437C-8C0C-FEFD43896923}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4B137F05-B98F-437C-8C0C-FEFD43896923}.Release|Any CPU.Build.0 = Release|Any CPU
		{BCC37AC3-9DD7-4DC6-B13F-26F85204CB99}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BCC37AC3-9DD7-4DC6-B13F-26F85204CB99}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BCC37AC3-9DD7-4DC6-B13F-26F85204CB99}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BCC37AC3-9DD7-4DC6-B13F-26F85204CB99}.Release|Any CPU.Build.0 = Release|Any CPU
		{2584B9A5-FEA2-4A58-832D-4A3E53639F03}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2584B9A5-FEA2-4A58-832D-4A3E53639F03}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2584B9A5-FEA2-4A58-832D-4A3E53639F03}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2584B9A5-FEA2-4A58-832D-4A3E53639F03}.Release|Any CPU.Build.0 = Release|Any CPU
		{538196C4-10A3-45E0-BA05-9A1A85C447DB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{538196C4-10A3-45E0-BA05-9A1A85C447DB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{538196C4-10A3-45E0-BA05-9A1A85C447DB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{538196C4-10A3-45E0-BA05-9A1A85C447DB}.Release|Any CPU.Build.0 = Release|Any CPU
		{54B7E228-3FDE-47F0-A17F-49F22E966690}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{54B7E228-3FDE-47F0-A17F-49F22E966690}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{54B7E228-3FDE-47F0-A17F-49F22E966690}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{54B7E228-3FDE-47F0-A17F-49F22E966690}.Release|Any CPU.Build.0 = Release|Any CPU
		{939B5FE1-98CE-4312-9F50-5E4814D3FD25}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{939B5FE1-98CE-4312-9F50-5E4814D3FD25}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{939B5FE1-98CE-4312-9F50-5E4814D3FD25}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{83A9A8F6-AAB9-4846-A2AC-F07907AF2872}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{83A9A8F6-AAB9-4846-A2AC-F07907AF2872}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{83A9A8F6-AAB9-4846-A2AC-F07907AF2872}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E749AF37-9C1B-42E5-A6D5-14D7D88D4A34}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E749AF37-9C1B-42E5-A6D5-14D7D88D4A34}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E749AF37-9C1B-42E5-A6D5-14D7D88D4A34}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E749AF37-9C1B-42E5-A6D5-14D7D88D4A34}.Release|Any CPU.Build.0 = Release|Any CPU
		{047C2BAA-2041-4EA0-8167-16DC70E2AAB7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{047C2BAA-2041-4EA0-8167-16DC70E2AAB7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{047C2BAA-2041-4EA0-8167-16DC70E2AAB7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{047C2BAA-2041-4EA0-8167-16DC70E2AAB7}.Release|Any CPU.Build.0 = Release|Any CPU
		{200EE076-9112-4495-9300-528241C29B63}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{200EE076-9112-4495-9300-528241C29B63}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{200EE076-9112-4495-9300-528241C29B63}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B23B4909-77A8-468C-8FFE-55E76FFD7188}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B23B4909-77A8-468C-8FFE-55E76FFD7188}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B23B4909-77A8-468C-8FFE-55E76FFD7188}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B23B4909-77A8-468C-8FFE-55E76FFD7188}.Release|Any CPU.Build.0 = Release|Any CPU
		{08833D23-8729-4A53-8AE7-8B632528FCB9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{08833D23-8729-4A53-8AE7-8B632528FCB9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{08833D23-8729-4A53-8AE7-8B632528FCB9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{98C7B62A-342E-4B2C-8D21-3046B55ABCD9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{98C7B62A-342E-4B2C-8D21-3046B55ABCD9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{98C7B62A-342E-4B2C-8D21-3046B55ABCD9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8092E0B6-3D00-4AC0-9C61-EC550B51A3E1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8092E0B6-3D00-4AC0-9C61-EC550B51A3E1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8092E0B6-3D00-4AC0-9C61-EC550B51A3E1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BABD7AAF-2042-40A6-82AD-9DA3A210991D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BABD7AAF-2042-40A6-82AD-9DA3A210991D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BABD7AAF-2042-40A6-82AD-9DA3A210991D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A1DE0B4F-A79C-495F-8F75-92AC8CE63C15}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A1DE0B4F-A79C-495F-8F75-92AC8CE63C15}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A1DE0B4F-A79C-495F-8F75-92AC8CE63C15}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{36DDDE06-C65A-4334-8354-A931AFABB0E8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{36DDDE06-C65A-4334-8354-A931AFABB0E8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{36DDDE06-C65A-4334-8354-A931AFABB0E8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{36DDDE06-C65A-4334-8354-A931AFABB0E8}.Release|Any CPU.Build.0 = Release|Any CPU
		{964C0930-B431-4C95-857C-839F58C58C73}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{964C0930-B431-4C95-857C-839F58C58C73}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{964C0930-B431-4C95-857C-839F58C58C73}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{964C0930-B431-4C95-857C-839F58C58C73}.Release|Any CPU.Build.0 = Release|Any CPU
		{EF162430-4C29-4669-94C0-0D46E1ABC37A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EF162430-4C29-4669-94C0-0D46E1ABC37A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EF162430-4C29-4669-94C0-0D46E1ABC37A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EF162430-4C29-4669-94C0-0D46E1ABC37A}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{C747BDF7-F2A1-4BC1-90C4-90D5807EB54C} = {EDE0E8CF-E88B-4048-88FE-077398785C9D}
		{258F9726-45F1-49B5-B51C-85E56A2CD2F8} = {A391A854-F542-401A-8E4C-D5737EC23736}
		{C3782A32-191B-4573-AC68-3112D4BA7101} = {C5D927C2-6483-4C28-AADB-6CECE50EF5E5}
		{4B137F05-B98F-437C-8C0C-FEFD43896923} = {6EC72848-3EDE-445E-B828-C817CD2BBA76}
		{BCC37AC3-9DD7-4DC6-B13F-26F85204CB99} = {3743E600-3A01-4F40-B3D5-4F36C3657435}
		{2584B9A5-FEA2-4A58-832D-4A3E53639F03} = {3743E600-3A01-4F40-B3D5-4F36C3657435}
		{538196C4-10A3-45E0-BA05-9A1A85C447DB} = {8751CA7F-2B98-47A0-85FF-DB88468F4ED9}
		{84DF89C1-06D9-40FD-8B1C-38B038C9CCAB} = {EDE0E8CF-E88B-4048-88FE-077398785C9D}
		{54B7E228-3FDE-47F0-A17F-49F22E966690} = {6247A5A7-F068-4C90-B806-6A277E7B728F}
		{939B5FE1-98CE-4312-9F50-5E4814D3FD25} = {3743E600-3A01-4F40-B3D5-4F36C3657435}
		{83A9A8F6-AAB9-4846-A2AC-F07907AF2872} = {A391A854-F542-401A-8E4C-D5737EC23736}
		{E749AF37-9C1B-42E5-A6D5-14D7D88D4A34} = {6247A5A7-F068-4C90-B806-6A277E7B728F}
		{047C2BAA-2041-4EA0-8167-16DC70E2AAB7} = {6247A5A7-F068-4C90-B806-6A277E7B728F}
		{200EE076-9112-4495-9300-528241C29B63} = {EF3EFE4C-B1F9-45EF-9173-FF076C9C88FB}
		{B23B4909-77A8-468C-8FFE-55E76FFD7188} = {EF3EFE4C-B1F9-45EF-9173-FF076C9C88FB}
		{08833D23-8729-4A53-8AE7-8B632528FCB9} = {8751CA7F-2B98-47A0-85FF-DB88468F4ED9}
		{98C7B62A-342E-4B2C-8D21-3046B55ABCD9} = {3743E600-3A01-4F40-B3D5-4F36C3657435}
		{8092E0B6-3D00-4AC0-9C61-EC550B51A3E1} = {6EC72848-3EDE-445E-B828-C817CD2BBA76}
		{BABD7AAF-2042-40A6-82AD-9DA3A210991D} = {C5D927C2-6483-4C28-AADB-6CECE50EF5E5}
		{A1DE0B4F-A79C-495F-8F75-92AC8CE63C15} = {6247A5A7-F068-4C90-B806-6A277E7B728F}
		{36DDDE06-C65A-4334-8354-A931AFABB0E8} = {A451EEE7-B1E7-4592-B4F1-A9B8E5D71EAD}
		{964C0930-B431-4C95-857C-839F58C58C73} = {91627A5A-3162-4771-8DB0-6DE9F09A0520}
		{EF162430-4C29-4669-94C0-0D46E1ABC37A} = {91627A5A-3162-4771-8DB0-6DE9F09A0520}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {33E996ED-8E61-410F-AAF1-3CDF76136067}
	EndGlobalSection
	GlobalSection(SharedMSBuildProjectFiles) = preSolution
		Pollbook.Shared\Pollbook.Shared.projitems*{84df89c1-06d9-40fd-8b1c-38b038c9ccab}*SharedItemsImports = 13
		Pollbook.Shared\Pollbook.Shared.projitems*{c747bdf7-f2a1-4bc1-90c4-90d5807eb54c}*SharedItemsImports = 5
	EndGlobalSection
EndGlobal
