using System.Threading.Tasks;
using Windows.Devices.SmartCards;
using ESS.Pollbook.Components.Repository.Device;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.Device;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;

namespace ESS.Pollbook.Facade.Test.Device
{
   [TestClass]
   public class SmartCardFacadeTests
   {
      private ISmartCardFacade _smartCardFacade;

      private Mock<ISmartCardRepository> _smartCardRepositoryMock;
      private Mock<IEssLogger> _essLoggerMock;

      private const string PreferredSmartCardReader = "ACS ACR39U ICC Reader 0";

      [TestInitialize]
      public void Initialize()
      {
         _essLoggerMock = new Mock<IEssLogger>();
         _smartCardRepositoryMock = new Mock<ISmartCardRepository>();

         SystemConfiguration.ElectionConfiguration = new PollbookConfigurationDto
         {
            DacCardReaderName = PreferredSmartCardReader,
            DacPin = "2806022",
            DacVerificationCode = "395A772673387C533F78413742346121366C7E446558315E5432256F355B744A"
         };
      }

      [TestCleanup]
      public void Cleanup()
      {
         // clean up test objects
      }

      [TestMethod]
      public async Task IsSmartCardReaderAttached_PreferredCardReaderAttached_Success()
      {
         _smartCardRepositoryMock.Setup(x
            => x.IsSmartCardReaderAttachedAsync()).ReturnsAsync(true);

         _smartCardFacade = new SmartCardFacade(_smartCardRepositoryMock.Object, _essLoggerMock.Object);

         var actual = await _smartCardFacade.IsSmartCardReaderAttachedAsync();

         Assert.IsTrue(actual);
      }

      [TestMethod]
      public async Task IsSmartCardReaderAttached_PreferredReaderNotAttached_Failed()
      {
         _smartCardRepositoryMock.Setup(x
            => x.IsSmartCardReaderAttachedAsync()).ReturnsAsync(false);

         _smartCardFacade = new SmartCardFacade(_smartCardRepositoryMock.Object, _essLoggerMock.Object);

         var actual = await _smartCardFacade.IsSmartCardReaderAttachedAsync();

         Assert.IsFalse(actual);
      }

      [TestMethod]
      public async Task IsCardPresent_LookingForCard_Success()
      {
         _smartCardRepositoryMock.Setup(x
            => x.IsCardPresentAsync()).ReturnsAsync(true);

         _smartCardFacade = new SmartCardFacade(_smartCardRepositoryMock.Object, _essLoggerMock.Object);

         var actual = await _smartCardFacade.IsCardPresentAsync();

         Assert.IsTrue(actual);
      }

      [TestMethod]
      public async Task IsCardPresent_LookingForCard_Failed()
      {
         _smartCardRepositoryMock.Setup(x
            => x.IsCardPresentAsync()).ReturnsAsync(false);

         _smartCardFacade = new SmartCardFacade(_smartCardRepositoryMock.Object, _essLoggerMock.Object);

         var actual = await _smartCardFacade.IsCardPresentAsync();

         Assert.IsFalse(actual);
      }

      [TestMethod]
      public async Task IsVoterCard_LookingForVoterCard_Success()
      {
         _smartCardRepositoryMock.Setup(x
            => x.IsVoterCardAsync()).ReturnsAsync(true);

         _smartCardFacade = new SmartCardFacade(_smartCardRepositoryMock.Object, _essLoggerMock.Object);

         var actual = await _smartCardFacade.IsVoterCardAsync();

         Assert.IsTrue(actual);
      }

      [TestMethod]
      public async Task IsVoterCard_LookingForVoterCard_Failed()
      {
         _smartCardRepositoryMock.Setup(x
            => x.IsVoterCardAsync()).ReturnsAsync(false);

         _smartCardFacade = new SmartCardFacade(_smartCardRepositoryMock.Object, _essLoggerMock.Object);

         var actual = await _smartCardFacade.IsVoterCardAsync();

         Assert.IsFalse(actual);
      }

      [TestMethod]
      public async Task WriteToCardAsync_Success()
      {
         _smartCardRepositoryMock.Setup(x
            => x.WriteToCardAsync(string.Empty, 0, string.Empty)).ReturnsAsync(0);

         _smartCardFacade = new SmartCardFacade(_smartCardRepositoryMock.Object, _essLoggerMock.Object);

         var actual = await _smartCardFacade.WriteToCardAsync(string.Empty, 0, string.Empty);

         Assert.IsTrue(actual);
      }

      [TestMethod]
      public async Task WriteToCardAsync_ReturnedFailure_Failed()
      {
         _smartCardRepositoryMock.Setup(x
            => x.WriteToCardAsync(string.Empty, 0, string.Empty)).ReturnsAsync(100);

         _smartCardFacade = new SmartCardFacade(_smartCardRepositoryMock.Object, _essLoggerMock.Object);

         var actual = await _smartCardFacade.WriteToCardAsync(string.Empty, 0, string.Empty);

         Assert.IsFalse(actual);
      }
   }
}