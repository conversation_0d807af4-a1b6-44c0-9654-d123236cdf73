using ESS.Pollbook.Components.Repository.Configuration;
using ESS.Pollbook.Components.Repository.IncrementalUpdates;
using ESS.Pollbook.Components.Repository.PollbookTransaction;
using ESS.Pollbook.Components.Repository.VoterBallot;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.Configuration;
using ESS.Pollbook.Facade.Device;
using ESS.Pollbook.Facade.ElectionSettings;
using ESS.Pollbook.Facade.HostSync;
using ESS.Pollbook.Facade.Pollworker;
using ESS.Pollbook.Facade.VoterBallot;
using ESS.Pollbook.Hardware.Storage;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;

namespace ESS.Pollbook.Facade.Test.Configuration
{
	[TestClass]
	public class PollbookConfigurationFacadeTests
	{
      private IPollbookConfigurationRepository _configurationRepository;
      private IPrinterFacade _printerFacade;
      private IHostSyncFacade _hostSyncFacade;
      private IStorageLocator _storageLocator;
      private IPollbookTransactionRepository _pollbookTransactionRepository;
      private IIncrementalUpdatesRepository _incrementalUpdatesRepository;
      private IPollworkerConfigurationFacade _pollworkerConfigurationFacade;
      private IVoterBallotFacade _voterBallotFacade;
      private IEssLogger _essLogger;

      private IElectionSettingsFacade _electionSettingsFacade;
      private IPollbookConfigurationFacade _pollbookConfigurationFacade;

      private Mock<IHostSyncFacade> hostSyncFacadeMock;

      private readonly IEnumerable<JSONTableAliasDto> jsonTableAlias;
      private readonly DateTime lastUpdatedDatetime;
      DownloadConfigurationsDto configUpdates;
      DeviceCountsDto deviceCounts;

      private readonly string _testElectionName = "Test Election";
      private readonly int _testLocalTransactions = 10;
      private readonly int _testSentToServer = 11;

      private string _iniFile;
      private string _iniOriginalContent;

      public PollbookConfigurationFacadeTests()
      {
         jsonTableAlias = GetJsonTableAlias();
         lastUpdatedDatetime = GetLastUpdatedDateTime();
         deviceCounts = GetDeviceCounts();
      }

      [TestInitialize]
      public void TestInitialize()
      {
         _iniFile = GetHelperFilePath(@"..\..\..\Configuration\Support\ElectionSettings.ini");

         _iniOriginalContent = File.ReadAllText(_iniFile);

         var configurationRepositoryMock = new Mock<IPollbookConfigurationRepository>();


         configurationRepositoryMock.Setup(x => x.GetJsonTableAliasDetails()).ReturnsAsync(jsonTableAlias);
         configurationRepositoryMock.Setup(x => x.GetPollbookConfiguration())
             .Returns(new PollbookConfigurationDto()
             { BODPrinters = new List<PrinterDto>(), ExpressPollPrinters = new List<PrinterDto>() });
         _configurationRepository = configurationRepositoryMock.Object;

         var pollbookTransactionRepositoryMock = new Mock<IPollbookTransactionRepository>();
         pollbookTransactionRepositoryMock.Setup(x => x.GetLatestConfigurationAppliedDateTime()).ReturnsAsync(lastUpdatedDatetime);
         pollbookTransactionRepositoryMock.Setup(x => x.GetDeviceBallotsIssuedCountsAsync(SystemDetails.MachineName)).ReturnsAsync(deviceCounts);

         _pollbookTransactionRepository = pollbookTransactionRepositoryMock.Object;

         var incrementalUpdatesRepoMock = new Mock<IIncrementalUpdatesRepository>();
         _incrementalUpdatesRepository = incrementalUpdatesRepoMock.Object;

         hostSyncFacadeMock = new Mock<IHostSyncFacade>();
         _hostSyncFacade = hostSyncFacadeMock.Object;

         var storageLocatorMock = new Mock<IStorageLocator>();
         storageLocatorMock.Setup(x => x.GetElectionSettingsFilePath()).Returns(_iniFile);
         _storageLocator = storageLocatorMock.Object;

         var pollworkerConfigurationFacade = new Mock<IPollworkerConfigurationFacade>();
         _pollworkerConfigurationFacade = pollworkerConfigurationFacade.Object;

         var loggerMock = new Mock<IEssLogger>();
         _essLogger = loggerMock.Object;

         var printerFacadeMock = new Mock<IPrinterFacade>();
         _printerFacade = printerFacadeMock.Object;

         _electionSettingsFacade = new ElectionSettingsFacade(_essLogger, _storageLocator);

         var voterBallotRepositoryMock = new Mock<IVoterBallotRepository>();
         _voterBallotFacade = new VoterBallotFacade(voterBallotRepositoryMock.Object, _essLogger);

         _pollbookConfigurationFacade = new ESS.Pollbook.Facade.Configuration.PollbookConfigurationFacade(
	         _configurationRepository,
	         _printerFacade,
	         _hostSyncFacade,
	         _storageLocator,
	         _pollbookTransactionRepository,
	         _pollworkerConfigurationFacade,
             _incrementalUpdatesRepository,
             _voterBallotFacade,
	         _essLogger);
      }

      [TestCleanup]
      public void Cleanup()
      {
         ExecuteWithRetry(
            () => { File.WriteAllText(_iniFile, _iniOriginalContent); return true; },
            5, 50
         );
        }

      [TestMethod]
      public async Task CheckAndApplyConfigurationsUpdatesTest()
      {
         var path = GetHelperFilePath(@"..\..\..\Configuration\Support\Config.json");
         configUpdates = GetConfigUpdates(path);

         hostSyncFacadeMock.Setup(x => x.GetConfigurationsUpdateAsync(lastUpdatedDatetime))
             .ReturnsAsync(configUpdates);

         Core.StaticValues.ElectionSettings.ElectionName = _testElectionName;
         var count = await _pollbookConfigurationFacade.CheckAndApplyPollbookConfigurationsUpdatesAsync();
         Assert.AreEqual("Steve Config Update Test 7260", Core.StaticValues.ElectionSettings.ElectionName,
             "Election Name Not updated");
         Assert.AreEqual(10, count, "All config updates are not processed.");
      }

      [TestMethod]
      public void CheckAndApplyConfigurationsUpdates_NoDataTest()
      {
         var path = GetHelperFilePath(@"..\..\..\Configuration\Support\Config1.json");
         configUpdates = GetConfigUpdates(path);

         hostSyncFacadeMock.Setup(x => x.GetConfigurationsUpdateAsync(lastUpdatedDatetime))
             .ReturnsAsync(configUpdates);

         var count = _pollbookConfigurationFacade.CheckAndApplyPollbookConfigurationsUpdatesAsync();
         Assert.AreEqual(0, count.Result, "All config updates are not processed.");
      }

      [TestMethod]
      public void UpdateElectionSettingsIniFileElectionNameTest()
      {
         Core.StaticValues.ElectionSettings.ElectionName = _testElectionName;
         _pollbookConfigurationFacade.UpdateElectionSettingsIniFile().Wait();
         _electionSettingsFacade.GetElectionInfo();
         Assert.AreEqual(_testElectionName, Core.StaticValues.ElectionSettings.ElectionName,
             "Election Name Not updated");
      }

      [TestMethod]
      public void UpdateElectionSettingsIniFileTransactionCountsTest()
      {
         _pollbookConfigurationFacade.UpdateElectionSettingsIniFileTransactionCounts().Wait();
         _electionSettingsFacade.GetElectionInfo();
         Assert.AreEqual(_testLocalTransactions.ToString(), Core.StaticValues.ElectionSettings.LocalTransactions,
             "LocalTransactions count Not updated");
         Assert.AreEqual(_testSentToServer.ToString(), Core.StaticValues.ElectionSettings.SentToServer,
             "SentToServer count Not updated");
      }

      private static string GetHelperFilePath(string relativePathToFile)
      {
         var currentAssemblyPath = Path.GetDirectoryName(Assembly.GetExecutingAssembly().CodeBase)
             ?.Replace(@"file:\", string.Empty);
         if (currentAssemblyPath == null) return string.Empty;
         var relativePath = Path.Combine(currentAssemblyPath, relativePathToFile);
         return Path.GetFullPath(relativePath);
      }

      private static DownloadConfigurationsDto GetConfigUpdates(string path)
      {
         var collection = new DownloadConfigurationsDto();
         if (!File.Exists(path)) return collection;
         using (var r = new StreamReader(path))
         {
            var json = r.ReadToEnd();
            collection = JsonConvert.DeserializeObject<DownloadConfigurationsDto>(json);
         }

         return collection;
      }

      private static DateTime GetLastUpdatedDateTime()
      {
         var lastUpdatedDateTime = DateTime.Now;
         return lastUpdatedDateTime;
      }

      private static IEnumerable<JSONTableAliasDto> GetJsonTableAlias()
      {
         var path = GetHelperFilePath(@"..\..\..\Configuration\Support\jsonTableAlias.json");
         var collection = new List<JSONTableAliasDto>();
         if (!File.Exists(path)) return collection;
         using (var r = new StreamReader(path))
         {
            var json = r.ReadToEnd();
            collection = JsonConvert.DeserializeObject<List<JSONTableAliasDto>>(json);
         }

         return collection;
      }

      private DeviceCountsDto GetDeviceCounts()
      {
         return new DeviceCountsDto()
         {
            LocalBallotTransactions = _testLocalTransactions,
            LocalBallotTransactionsSentToServer = _testSentToServer
         };
      }

      //Method used to retry the execution of a method (Used for cleanup when the file gets locked between tests)
      private bool ExecuteWithRetry(Func<bool> method, int retryCount, int timeBetweenTries)
      {
	      for (int i = 0; i < retryCount; i++)
	      {
		      try
		      {
			      method.DynamicInvoke();
			      break;
		      }
		      catch
		      {
			      if ((i + 1) == retryCount)
			      {
				      throw;
			      }
			      else
			      {
				      Thread.Sleep(timeBetweenTries);
			      }
		      }
	      }
          return true;
      }
    }
}