[{"SQLServerSchema": "Election", "SQLServerTable": "Election_Configuration", "SQLServerField": "Election_Configuration_Name", "SQLiteTable": "Election_Election_Configuration", "SQLiteField": "Election_Configuration_Name", "JSONTableAlias": "Con", "JSONFieldAlias": "Name", "IsUpdateField": false, "IsPK": true, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Election_Configuration", "SQLServerField": "Election_Configuration_Value", "SQLiteTable": "Election_Election_Configuration", "SQLiteField": "Election_Configuration_Value", "JSONTableAlias": "Con", "JSONFieldAlias": "Value", "IsUpdateField": true, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Enumeration_State_Change", "SQLServerField": "Common_Enumeration_ID", "SQLiteTable": "Election_Enumeration_State_Change", "SQLiteField": "Common_Enumeration_ID", "JSONTableAlias": "ESC", "JSONFieldAlias": "EnumID", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Enumeration_State_Change", "SQLServerField": "Post_Jurisdiction_Enumeration_Value_ID", "SQLiteTable": "Election_Enumeration_State_Change", "SQLiteField": "Post_Jurisdiction_Enumeration_Value_ID", "JSONTableAlias": "ESC", "JSONFieldAlias": "PstEValID", "IsUpdateField": true, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Enumeration_State_Change", "SQLServerField": "Pre_Jurisdiction_Enumeration_Value_ID", "SQLiteTable": "Election_Enumeration_State_Change", "SQLiteField": "Pre_Jurisdiction_Enumeration_Value_ID", "JSONTableAlias": "ESC", "JSONFieldAlias": "PreEValID", "IsUpdateField": true, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Enumeration_State_Change", "SQLServerField": "Record_Update_Application_Datetime", "SQLiteTable": "Election_Enumeration_State_Change", "SQLiteField": "Record_Update_Application_Datetime", "JSONTableAlias": "ESC", "JSONFieldAlias": "RUAppDt", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Enumeration_State_Change", "SQLServerField": "Record_Update_Application_Name", "SQLiteTable": "Election_Enumeration_State_Change", "SQLiteField": "Record_Update_Application_Name", "JSONTableAlias": "ESC", "JSONFieldAlias": "RUAppN", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Enumeration_State_Change", "SQLServerField": "Record_Update_Application_User_Name", "SQLiteTable": "Election_Enumeration_State_Change", "SQLiteField": "Record_Update_Application_User_Name", "JSONTableAlias": "ESC", "JSONFieldAlias": "RUAppU", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Enumeration_State_Change", "SQLServerField": "Record_Update_User_Name", "SQLiteTable": "Election_Enumeration_State_Change", "SQLiteField": "Record_Update_User_Name", "JSONTableAlias": "ESC", "JSONFieldAlias": "RUUN", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Enumeration_State_Change", "SQLServerField": "Record_Update_UTC_Datetime", "SQLiteTable": "Election_Enumeration_State_Change", "SQLiteField": "Record_Update_UTC_Datetime", "JSONTableAlias": "ESC", "JSONFieldAlias": "RUDt", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Enumeration_State_Change", "SQLServerField": "State_Change_Action_ID", "SQLiteTable": "Election_Enumeration_State_Change", "SQLiteField": "State_Change_Action_ID", "JSONTableAlias": "ESC", "JSONFieldAlias": "SCAID", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Enumeration_State_Change", "SQLServerField": "State_Change_ID", "SQLiteTable": "Election_Enumeration_State_Change", "SQLiteField": "State_Change_ID", "JSONTableAlias": "ESC", "JSONFieldAlias": "SCID", "IsUpdateField": false, "IsPK": true, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Jurisdiction_Enumeration_Value", "SQLServerField": "Common_Enumeration_Value_ID", "SQLiteTable": "Election_Jurisdiction_Enumeration_Value", "SQLiteField": "Common_Enumeration_Value_ID", "JSONTableAlias": "JEnumVal", "JSONFieldAlias": "CEVID", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": true}, {"SQLServerSchema": "Election", "SQLServerTable": "Jurisdiction_Enumeration_Value", "SQLServerField": "Jurisdiction_Enumeration_Turnout_Calculation_Indicator", "SQLiteTable": "Election_Jurisdiction_Enumeration_Value", "SQLiteField": "Jurisdiction_Enumeration_Turnout_Calculation_Indicator", "JSONTableAlias": "JEnumVal", "JSONFieldAlias": "Calc", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": true}, {"SQLServerSchema": "Election", "SQLServerTable": "Jurisdiction_Enumeration_Value", "SQLServerField": "Jurisdiction_Enumeration_Value_Code", "SQLiteTable": "Election_Jurisdiction_Enumeration_Value", "SQLiteField": "Jurisdiction_Enumeration_Value_Code", "JSONTableAlias": "JEnumVal", "JSONFieldAlias": "Code", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": true}, {"SQLServerSchema": "Election", "SQLServerTable": "Jurisdiction_Enumeration_Value", "SQLServerField": "Jurisdiction_Enumeration_Value_Description_Text", "SQLiteTable": "Election_Jurisdiction_Enumeration_Value", "SQLiteField": "Jurisdiction_Enumeration_Value_Description_Text", "JSONTableAlias": "JEnumVal", "JSONFieldAlias": "Desc", "IsUpdateField": true, "IsPK": false, "IsInsertRecord": true}, {"SQLServerSchema": "Election", "SQLServerTable": "Jurisdiction_Enumeration_Value", "SQLServerField": "Jurisdiction_Enumeration_Value_ID", "SQLiteTable": "Election_Jurisdiction_Enumeration_Value", "SQLiteField": "Jurisdiction_Enumeration_Value_ID", "JSONTableAlias": "JEnumVal", "JSONFieldAlias": "ID", "IsUpdateField": false, "IsPK": true, "IsInsertRecord": true}, {"SQLServerSchema": "Election", "SQLServerTable": "Jurisdiction_Enumeration_Value", "SQLServerField": "Jurisdiction_Enumeration_Value_Name", "SQLiteTable": "Election_Jurisdiction_Enumeration_Value", "SQLiteField": "Jurisdiction_Enumeration_Value_Name", "JSONTableAlias": "JEnumVal", "JSONFieldAlias": "Name", "IsUpdateField": true, "IsPK": false, "IsInsertRecord": true}, {"SQLServerSchema": "Election", "SQLServerTable": "Jurisdiction_Enumeration_Value", "SQLServerField": "Record_Update_Application_Datetime", "SQLiteTable": "Election_Jurisdiction_Enumeration_Value", "SQLiteField": "Record_Update_Application_Datetime", "JSONTableAlias": "JEnumVal", "JSONFieldAlias": "RUAppDt", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": true}, {"SQLServerSchema": "Election", "SQLServerTable": "Jurisdiction_Enumeration_Value", "SQLServerField": "Record_Update_Application_Name", "SQLiteTable": "Election_Jurisdiction_Enumeration_Value", "SQLiteField": "Record_Update_Application_Name", "JSONTableAlias": "JEnumVal", "JSONFieldAlias": "RUAppN", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": true}, {"SQLServerSchema": "Election", "SQLServerTable": "Jurisdiction_Enumeration_Value", "SQLServerField": "Record_Update_Application_User_Name", "SQLiteTable": "Election_Jurisdiction_Enumeration_Value", "SQLiteField": "Record_Update_Application_User_Name", "JSONTableAlias": "JEnumVal", "JSONFieldAlias": "RUAppU", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": true}, {"SQLServerSchema": "Election", "SQLServerTable": "Jurisdiction_Enumeration_Value", "SQLServerField": "Record_Update_User_Name", "SQLiteTable": "Election_Jurisdiction_Enumeration_Value", "SQLiteField": "Record_Update_User_Name", "JSONTableAlias": "JEnumVal", "JSONFieldAlias": "RUUN", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": true}, {"SQLServerSchema": "Election", "SQLServerTable": "Jurisdiction_Enumeration_Value", "SQLServerField": "Record_Update_UTC_Datetime", "SQLiteTable": "Election_Jurisdiction_Enumeration_Value", "SQLiteField": "Record_Update_UTC_Datetime", "JSONTableAlias": "JEnumVal", "JSONFieldAlias": "RUDt", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": true}, {"SQLServerSchema": "Election", "SQLServerTable": "Party", "SQLServerField": "Party_Abbreviated_Name", "SQLiteTable": "Election_Party", "SQLiteField": "Party_Abbreviated_Name", "JSONTableAlias": "Pty", "JSONFieldAlias": "PAName", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Party", "SQLServerField": "Party_Display_Name", "SQLiteTable": "Election_Party", "SQLiteField": "Party_Display_Name", "JSONTableAlias": "Pty", "JSONFieldAlias": "DName", "IsUpdateField": true, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Party", "SQLServerField": "Party_Display_Order_Number", "SQLiteTable": "Election_Party", "SQLiteField": "Party_Display_Order_Number", "JSONTableAlias": "Pty", "JSONFieldAlias": "DON", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Party", "SQLServerField": "Party_ID", "SQLiteTable": "Election_Party", "SQLiteField": "Party_ID", "JSONTableAlias": "Pty", "JSONFieldAlias": "ID", "IsUpdateField": false, "IsPK": true, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Party", "SQLServerField": "Party_Name", "SQLiteTable": "Election_Party", "SQLiteField": "Party_Name", "JSONTableAlias": "Pty", "JSONFieldAlias": "PName", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Party", "SQLServerField": "Party_Source_Party_Key", "SQLiteTable": "Election_Party", "SQLiteField": "Party_Source_Party_Key", "JSONTableAlias": "Pty", "JSONFieldAlias": "Key", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Party", "SQLServerField": "Record_Update_Application_Datetime", "SQLiteTable": "Election_Party", "SQLiteField": "Record_Update_Application_Datetime", "JSONTableAlias": "Pty", "JSONFieldAlias": "RUAppDt", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Party", "SQLServerField": "Record_Update_Application_Name", "SQLiteTable": "Election_Party", "SQLiteField": "Record_Update_Application_Name", "JSONTableAlias": "Pty", "JSONFieldAlias": "RUAppN", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Party", "SQLServerField": "Record_Update_Application_User_Name", "SQLiteTable": "Election_Party", "SQLiteField": "Record_Update_Application_User_Name", "JSONTableAlias": "Pty", "JSONFieldAlias": "RUAppU", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Party", "SQLServerField": "Record_Update_User_Name", "SQLiteTable": "Election_Party", "SQLiteField": "Record_Update_User_Name", "JSONTableAlias": "Pty", "JSONFieldAlias": "RUUN", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Party", "SQLServerField": "Record_Update_UTC_Datetime", "SQLiteTable": "Election_Party", "SQLiteField": "Record_Update_UTC_Datetime", "JSONTableAlias": "Pty", "JSONFieldAlias": "RUDt", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Pollbook_Defined_Text", "SQLServerField": "Pollbook_Defined_Text_Description", "SQLiteTable": "Election_Pollbook_Defined_Text", "SQLiteField": "Pollbook_Defined_Text_Description", "JSONTableAlias": "PDT", "JSONFieldAlias": "Desc", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Pollbook_Defined_Text", "SQLServerField": "Pollbook_Defined_Text_ID", "SQLiteTable": "Election_Pollbook_Defined_Text", "SQLiteField": "Pollbook_Defined_Text_ID", "JSONTableAlias": "PDT", "JSONFieldAlias": "ID", "IsUpdateField": false, "IsPK": true, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Pollbook_Defined_Text", "SQLServerField": "Pollbook_Defined_Text_Language", "SQLiteTable": "Election_Pollbook_Defined_Text", "SQLiteField": "Pollbook_Defined_Text_Language", "JSONTableAlias": "PDT", "JSONFieldAlias": "<PERSON>", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Pollbook_Defined_Text", "SQLServerField": "Pollbook_Defined_Text_Name", "SQLiteTable": "Election_Pollbook_Defined_Text", "SQLiteField": "Pollbook_Defined_Text_Name", "JSONTableAlias": "PDT", "JSONFieldAlias": "Name", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Pollbook_Defined_Text", "SQLServerField": "Pollbook_Defined_Text_Value", "SQLiteTable": "Election_Pollbook_Defined_Text", "SQLiteField": "Pollbook_Defined_Text_Value", "JSONTableAlias": "PDT", "JSONFieldAlias": "Value", "IsUpdateField": true, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Pollbook_Defined_Text", "SQLServerField": "Record_Update_Application_Datetime", "SQLiteTable": "Election_Pollbook_Defined_Text", "SQLiteField": "Record_Update_Application_Datetime", "JSONTableAlias": "PDT", "JSONFieldAlias": "RUAppDt", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Pollbook_Defined_Text", "SQLServerField": "Record_Update_Application_Name", "SQLiteTable": "Election_Pollbook_Defined_Text", "SQLiteField": "Record_Update_Application_Name", "JSONTableAlias": "PDT", "JSONFieldAlias": "RUAppN", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Pollbook_Defined_Text", "SQLServerField": "Record_Update_Application_User_Name", "SQLiteTable": "Election_Pollbook_Defined_Text", "SQLiteField": "Record_Update_Application_User_Name", "JSONTableAlias": "PDT", "JSONFieldAlias": "RUAppU", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Pollbook_Defined_Text", "SQLServerField": "Record_Update_User_Name", "SQLiteTable": "Election_Pollbook_Defined_Text", "SQLiteField": "Record_Update_User_Name", "JSONTableAlias": "PDT", "JSONFieldAlias": "RUUN", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Pollbook_Defined_Text", "SQLServerField": "Record_Update_UTC_Datetime", "SQLiteTable": "Election_Pollbook_Defined_Text", "SQLiteField": "Record_Update_UTC_Datetime", "JSONTableAlias": "PDT", "JSONFieldAlias": "RUDt", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "PollbookPrinter", "SQLServerField": "BOD_Printer", "SQLiteTable": "Election_PollbookPrinter", "SQLiteField": "BOD_Printer", "JSONTableAlias": "Print", "JSONFieldAlias": "BOD", "IsUpdateField": true, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "PollbookPrinter", "SQLServerField": "LabelIndicator", "SQLiteTable": "Election_PollbookPrinter", "SQLiteField": "LabelIndicator", "JSONTableAlias": "Print", "JSONFieldAlias": "LI", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "PollbookPrinter", "SQLServerField": "PaperSource", "SQLiteTable": "Election_PollbookPrinter", "SQLiteField": "PaperSource", "JSONTableAlias": "Print", "JSONFieldAlias": "PS", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "PollbookPrinter", "SQLServerField": "PaperType", "SQLiteTable": "Election_PollbookPrinter", "SQLiteField": "PaperType", "JSONTableAlias": "Print", "JSONFieldAlias": "PT", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "PollbookPrinter", "SQLServerField": "PollbookPrinterID", "SQLiteTable": "Election_PollbookPrinter", "SQLiteField": "PollbookPrinterID", "JSONTableAlias": "Print", "JSONFieldAlias": "ID", "IsUpdateField": false, "IsPK": true, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "PollbookPrinter", "SQLServerField": "PrinterDisplayName", "SQLiteTable": "Election_PollbookPrinter", "SQLiteField": "PrinterDisplayName", "JSONTableAlias": "Print", "JSONFieldAlias": "DName", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "PollbookPrinter", "SQLServerField": "PrinterName", "SQLiteTable": "Election_PollbookPrinter", "SQLiteField": "PrinterName", "JSONTableAlias": "Print", "JSONFieldAlias": "Name", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "PollbookPrinter", "SQLServerField": "Record_Update_Application_Datetime", "SQLiteTable": "Election_PollbookPrinter", "SQLiteField": "Record_Update_Application_Datetime", "JSONTableAlias": "Print", "JSONFieldAlias": "RUAppDt", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "PollbookPrinter", "SQLServerField": "Record_Update_Application_Name", "SQLiteTable": "Election_PollbookPrinter", "SQLiteField": "Record_Update_Application_Name", "JSONTableAlias": "Print", "JSONFieldAlias": "RUAppN", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "PollbookPrinter", "SQLServerField": "Record_Update_Application_User_Name", "SQLiteTable": "Election_PollbookPrinter", "SQLiteField": "Record_Update_Application_User_Name", "JSONTableAlias": "Print", "JSONFieldAlias": "RUAppU", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "PollbookPrinter", "SQLServerField": "Record_Update_User_Name", "SQLiteTable": "Election_PollbookPrinter", "SQLiteField": "Record_Update_User_Name", "JSONTableAlias": "Print", "JSONFieldAlias": "RUUN", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "PollbookPrinter", "SQLServerField": "Record_Update_UTC_Datetime", "SQLiteTable": "Election_PollbookPrinter", "SQLiteField": "Record_Update_UTC_Datetime", "JSONTableAlias": "Print", "JSONFieldAlias": "RUDt", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "PollbookPrinter", "SQLServerField": "SelectedBODPrinter", "SQLiteTable": "Election_PollbookPrinter", "SQLiteField": "SelectedBODPrinter", "JSONTableAlias": "Print", "JSONFieldAlias": "SBOD", "IsUpdateField": true, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "PollbookPrinter", "SQLServerField": "SelectedExpressPollPrinter", "SQLiteTable": "Election_PollbookPrinter", "SQLiteField": "SelectedExpressPollPrinter", "JSONTableAlias": "Print", "JSONFieldAlias": "SEPP", "IsUpdateField": true, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Polling_Place", "SQLServerField": "Polling_Place_Address_City_Name", "SQLiteTable": "Election_Polling_Place", "SQLiteField": "Polling_Place_Address_City_Name", "JSONTableAlias": "PP", "JSONFieldAlias": "City", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Polling_Place", "SQLServerField": "Polling_Place_Address_Full_Text", "SQLiteTable": "Election_Polling_Place", "SQLiteField": "Polling_Place_Address_Full_Text", "JSONTableAlias": "PP", "JSONFieldAlias": "Full", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Polling_Place", "SQLServerField": "Polling_Place_Address_House_Number", "SQLiteTable": "Election_Polling_Place", "SQLiteField": "Polling_Place_Address_House_Number", "JSONTableAlias": "PP", "JSONFieldAlias": "HN", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Polling_Place", "SQLServerField": "Polling_Place_Address_ISO_Country_Code", "SQLiteTable": "Election_Polling_Place", "SQLiteField": "Polling_Place_Address_ISO_Country_Code", "JSONTableAlias": "PP", "JSONFieldAlias": "CC", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Polling_Place", "SQLServerField": "Polling_Place_Address_ISO_State_Province_Code", "SQLiteTable": "Election_Polling_Place", "SQLiteField": "Polling_Place_Address_ISO_State_Province_Code", "JSONTableAlias": "PP", "JSONFieldAlias": "State", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Polling_Place", "SQLServerField": "Polling_Place_Address_Latitude", "SQLiteTable": "Election_Polling_Place", "SQLiteField": "Polling_Place_Address_Latitude", "JSONTableAlias": "PP", "JSONFieldAlias": "Lat", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Polling_Place", "SQLServerField": "Polling_Place_Address_Longitude", "SQLiteTable": "Election_Polling_Place", "SQLiteField": "Polling_Place_Address_Longitude", "JSONTableAlias": "PP", "JSONFieldAlias": "<PERSON>", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Polling_Place", "SQLServerField": "Polling_Place_Address_Postal_Additional_Code", "SQLiteTable": "Election_Polling_Place", "SQLiteField": "Polling_Place_Address_Postal_Additional_Code", "JSONTableAlias": "PP", "JSONFieldAlias": "Zip4", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Polling_Place", "SQLServerField": "Polling_Place_Address_Postal_Code", "SQLiteTable": "Election_Polling_Place", "SQLiteField": "Polling_Place_Address_Postal_Code", "JSONTableAlias": "PP", "JSONFieldAlias": "Zip", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Polling_Place", "SQLServerField": "Polling_Place_Address_Street_Name", "SQLiteTable": "Election_Polling_Place", "SQLiteField": "Polling_Place_Address_Street_Name", "JSONTableAlias": "PP", "JSONFieldAlias": "Str", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Polling_Place", "SQLServerField": "Polling_Place_Address_Unit_Name", "SQLiteTable": "Election_Polling_Place", "SQLiteField": "Polling_Place_Address_Unit_Name", "JSONTableAlias": "PP", "JSONFieldAlias": "Unit", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Polling_Place", "SQLServerField": "Polling_Place_Code", "SQLiteTable": "Election_Polling_Place", "SQLiteField": "Polling_Place_Code", "JSONTableAlias": "PP", "JSONFieldAlias": "Code", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Polling_Place", "SQLServerField": "Polling_Place_County_ID", "SQLiteTable": "Election_Polling_Place", "SQLiteField": "Polling_Place_County_ID", "JSONTableAlias": "PP", "JSONFieldAlias": "CID", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Polling_Place", "SQLServerField": "Polling_Place_Display_Name", "SQLiteTable": "Election_Polling_Place", "SQLiteField": "Polling_Place_Display_Name", "JSONTableAlias": "PP", "JSONFieldAlias": "DName", "IsUpdateField": true, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Polling_Place", "SQLServerField": "Polling_Place_ID", "SQLiteTable": "Election_Polling_Place", "SQLiteField": "Polling_Place_ID", "JSONTableAlias": "PP", "JSONFieldAlias": "ID", "IsUpdateField": false, "IsPK": true, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Polling_Place", "SQLServerField": "Polling_Place_Name", "SQLiteTable": "Election_Polling_Place", "SQLiteField": "Polling_Place_Name", "JSONTableAlias": "PP", "JSONFieldAlias": "Name", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Polling_Place", "SQLServerField": "Polling_Place_Number", "SQLiteTable": "Election_Polling_Place", "SQLiteField": "Polling_Place_Number", "JSONTableAlias": "PP", "JSONFieldAlias": "<PERSON><PERSON>", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Polling_Place", "SQLServerField": "Polling_Place_Source_Polling_Place_Key", "SQLiteTable": "Election_Polling_Place", "SQLiteField": "Polling_Place_Source_Polling_Place_Key", "JSONTableAlias": "PP", "JSONFieldAlias": "Key", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Polling_Place", "SQLServerField": "Polling_Place_Telephone_Number", "SQLiteTable": "Election_Polling_Place", "SQLiteField": "Polling_Place_Telephone_Number", "JSONTableAlias": "PP", "JSONFieldAlias": "Tele", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Polling_Place", "SQLServerField": "Polling_Place_Type_Enumeration_ID", "SQLiteTable": "Election_Polling_Place", "SQLiteField": "Polling_Place_Type_Enumeration_ID", "JSONTableAlias": "PP", "JSONFieldAlias": "TypeID", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Polling_Place", "SQLServerField": "Polling_Place_Vote_Center_Indicator", "SQLiteTable": "Election_Polling_Place", "SQLiteField": "Polling_Place_Vote_Center_Indicator", "JSONTableAlias": "PP", "JSONFieldAlias": "VC", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Polling_Place", "SQLServerField": "Record_Update_Application_Datetime", "SQLiteTable": "Election_Polling_Place", "SQLiteField": "Record_Update_Application_Datetime", "JSONTableAlias": "PP", "JSONFieldAlias": "RUAppDt", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Polling_Place", "SQLServerField": "Record_Update_Application_Name", "SQLiteTable": "Election_Polling_Place", "SQLiteField": "Record_Update_Application_Name", "JSONTableAlias": "PP", "JSONFieldAlias": "RUAppN", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Polling_Place", "SQLServerField": "Record_Update_Application_User_Name", "SQLiteTable": "Election_Polling_Place", "SQLiteField": "Record_Update_Application_User_Name", "JSONTableAlias": "PP", "JSONFieldAlias": "RUAppU", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Polling_Place", "SQLServerField": "Record_Update_User_Name", "SQLiteTable": "Election_Polling_Place", "SQLiteField": "Record_Update_User_Name", "JSONTableAlias": "PP", "JSONFieldAlias": "RUUN", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Polling_Place", "SQLServerField": "Record_Update_UTC_Datetime", "SQLiteTable": "Election_Polling_Place", "SQLiteField": "Record_Update_UTC_Datetime", "JSONTableAlias": "PP", "JSONFieldAlias": "RUDt", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Precinct", "SQLServerField": "Precinct_County_ID", "SQLiteTable": "Election_Precinct", "SQLiteField": "Precinct_County_ID", "JSONTableAlias": "P", "JSONFieldAlias": "CID", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Precinct", "SQLServerField": "Precinct_ID", "SQLiteTable": "Election_Precinct", "SQLiteField": "Precinct_ID", "JSONTableAlias": "P", "JSONFieldAlias": "ID", "IsUpdateField": false, "IsPK": true, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Precinct", "SQLServerField": "Precinct_Name", "SQLiteTable": "Election_Precinct", "SQLiteField": "Precinct_Name", "JSONTableAlias": "P", "JSONFieldAlias": "Name", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Precinct", "SQLServerField": "Precinct_Number", "SQLiteTable": "Election_Precinct", "SQLiteField": "Precinct_Number", "JSONTableAlias": "P", "JSONFieldAlias": "<PERSON><PERSON>", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Precinct", "SQLServerField": "Precint_Display_Name", "SQLiteTable": "Election_Precinct", "SQLiteField": "Precint_Display_Name", "JSONTableAlias": "P", "JSONFieldAlias": "DName", "IsUpdateField": true, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Precinct", "SQLServerField": "Record_Update_Application_Datetime", "SQLiteTable": "Election_Precinct", "SQLiteField": "Record_Update_Application_Datetime", "JSONTableAlias": "P", "JSONFieldAlias": "RUAppDt", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Precinct", "SQLServerField": "Record_Update_Application_Name", "SQLiteTable": "Election_Precinct", "SQLiteField": "Record_Update_Application_Name", "JSONTableAlias": "P", "JSONFieldAlias": "RUAppN", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Precinct", "SQLServerField": "Record_Update_Application_User_Name", "SQLiteTable": "Election_Precinct", "SQLiteField": "Record_Update_Application_User_Name", "JSONTableAlias": "P", "JSONFieldAlias": "RUAppU", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Precinct", "SQLServerField": "Record_Update_User_Name", "SQLiteTable": "Election_Precinct", "SQLiteField": "Record_Update_User_Name", "JSONTableAlias": "P", "JSONFieldAlias": "RUUN", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Precinct", "SQLServerField": "Record_Update_UTC_Datetime", "SQLiteTable": "Election_Precinct", "SQLiteField": "Record_Update_UTC_Datetime", "JSONTableAlias": "P", "JSONFieldAlias": "RUDt", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Precinct_Split", "SQLServerField": "Precinct_Split_Display_Name", "SQLiteTable": "Election_Precinct_Split", "SQLiteField": "Precinct_Split_Display_Name", "JSONTableAlias": "PS", "JSONFieldAlias": "DName", "IsUpdateField": true, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Precinct_Split", "SQLServerField": "Precinct_Split_ID", "SQLiteTable": "Election_Precinct_Split", "SQLiteField": "Precinct_Split_ID", "JSONTableAlias": "PS", "JSONFieldAlias": "ID", "IsUpdateField": false, "IsPK": true, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Precinct_Split", "SQLServerField": "Precinct_Split_Name", "SQLiteTable": "Election_Precinct_Split", "SQLiteField": "Precinct_Split_Name", "JSONTableAlias": "PS", "JSONFieldAlias": "Name", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Precinct_Split", "SQLServerField": "Precinct_Split_Precinct_ID", "SQLiteTable": "Election_Precinct_Split", "SQLiteField": "Precinct_Split_Precinct_ID", "JSONTableAlias": "PS", "JSONFieldAlias": "PID", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Precinct_Split", "SQLServerField": "Precinct_Split_Source_Precinct_Split_Key", "SQLiteTable": "Election_Precinct_Split", "SQLiteField": "Precinct_Split_Source_Precinct_Split_Key", "JSONTableAlias": "PS", "JSONFieldAlias": "Key", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Precinct_Split", "SQLServerField": "Precinct_Split_Voter_Count", "SQLiteTable": "Election_Precinct_Split", "SQLiteField": "Precinct_Split_Voter_Count", "JSONTableAlias": "PS", "JSONFieldAlias": "Cnt", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Precinct_Split", "SQLServerField": "Record_Update_Application_Datetime", "SQLiteTable": "Election_Precinct_Split", "SQLiteField": "Record_Update_Application_Datetime", "JSONTableAlias": "PS", "JSONFieldAlias": "RUAppDt", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Precinct_Split", "SQLServerField": "Record_Update_Application_Name", "SQLiteTable": "Election_Precinct_Split", "SQLiteField": "Record_Update_Application_Name", "JSONTableAlias": "PS", "JSONFieldAlias": "RUAppN", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Precinct_Split", "SQLServerField": "Record_Update_Application_User_Name", "SQLiteTable": "Election_Precinct_Split", "SQLiteField": "Record_Update_Application_User_Name", "JSONTableAlias": "PS", "JSONFieldAlias": "RUAppU", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Precinct_Split", "SQLServerField": "Record_Update_User_Name", "SQLiteTable": "Election_Precinct_Split", "SQLiteField": "Record_Update_User_Name", "JSONTableAlias": "PS", "JSONFieldAlias": "RUUN", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Precinct_Split", "SQLServerField": "Record_Update_UTC_Datetime", "SQLiteTable": "Election_Precinct_Split", "SQLiteField": "Record_Update_UTC_Datetime", "JSONTableAlias": "PS", "JSONFieldAlias": "RUDt", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "PrinterFormatField", "SQLServerField": "ContentBold", "SQLiteTable": "Election_PrinterFormatField", "SQLiteField": "ContentBold", "JSONTableAlias": "PrintFF", "JSONFieldAlias": "CB", "IsUpdateField": true, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "PrinterFormatField", "SQLServerField": "ContentFontFamilyID", "SQLiteTable": "Election_PrinterFormatField", "SQLiteField": "ContentFontFamilyID", "JSONTableAlias": "PrintFF", "JSONFieldAlias": "CFFID", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "PrinterFormatField", "SQLServerField": "ContentFontSize", "SQLiteTable": "Election_PrinterFormatField", "SQLiteField": "ContentFontSize", "JSONTableAlias": "PrintFF", "JSONFieldAlias": "CFS", "IsUpdateField": true, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "PrinterFormatField", "SQLServerField": "ContentReversed", "SQLiteTable": "Election_PrinterFormatField", "SQLiteField": "ContentReversed", "JSONTableAlias": "PrintFF", "JSONFieldAlias": "CR", "IsUpdateField": true, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "PrinterFormatField", "SQLServerField": "ContentUnderline", "SQLiteTable": "Election_PrinterFormatField", "SQLiteField": "ContentUnderline", "JSONTableAlias": "PrintFF", "JSONFieldAlias": "CUL", "IsUpdateField": true, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "PrinterFormatField", "SQLServerField": "CustomLabel", "SQLiteTable": "Election_PrinterFormatField", "SQLiteField": "CustomLabel", "JSONTableAlias": "PrintFF", "JSONFieldAlias": "CLbl", "IsUpdateField": true, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "PrinterFormatField", "SQLServerField": "DisplayContent", "SQLiteTable": "Election_PrinterFormatField", "SQLiteField": "DisplayContent", "JSONTableAlias": "PrintFF", "JSONFieldAlias": "DC", "IsUpdateField": true, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "PrinterFormatField", "SQLServerField": "DisplayLabel", "SQLiteTable": "Election_PrinterFormatField", "SQLiteField": "DisplayLabel", "JSONTableAlias": "PrintFF", "JSONFieldAlias": "DLbl", "IsUpdateField": true, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "PrinterFormatField", "SQLServerField": "EarlyVote", "SQLiteTable": "Election_PrinterFormatField", "SQLiteField": "EarlyVote", "JSONTableAlias": "PrintFF", "JSONFieldAlias": "EV", "IsUpdateField": true, "IsPK": true, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "PrinterFormatField", "SQLServerField": "FieldName", "SQLiteTable": "Election_PrinterFormatField", "SQLiteField": "FieldName", "JSONTableAlias": "PrintFF", "JSONFieldAlias": "FNAME", "IsUpdateField": false, "IsPK": true, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "PrinterFormatField", "SQLServerField": "HorizontalLine", "SQLiteTable": "Election_PrinterFormatField", "SQLiteField": "HorizontalLine", "JSONTableAlias": "PrintFF", "JSONFieldAlias": "HL", "IsUpdateField": true, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "PrinterFormatField", "SQLServerField": "Justified", "SQLiteTable": "Election_PrinterFormatField", "SQLiteField": "Justified", "JSONTableAlias": "PrintFF", "JSONFieldAlias": "J", "IsUpdateField": true, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "PrinterFormatField", "SQLServerField": "LabelBold", "SQLiteTable": "Election_PrinterFormatField", "SQLiteField": "LabelBold", "JSONTableAlias": "PrintFF", "JSONFieldAlias": "LB", "IsUpdateField": true, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "PrinterFormatField", "SQLServerField": "LabelFontFamilyID", "SQLiteTable": "Election_PrinterFormatField", "SQLiteField": "LabelFontFamilyID", "JSONTableAlias": "PrintFF", "JSONFieldAlias": "LFFID", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "PrinterFormatField", "SQLServerField": "LabelFontSize", "SQLiteTable": "Election_PrinterFormatField", "SQLiteField": "LabelFontSize", "JSONTableAlias": "PrintFF", "JSONFieldAlias": "LFS", "IsUpdateField": true, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "PrinterFormatField", "SQLServerField": "LabelReversed", "SQLiteTable": "Election_PrinterFormatField", "SQLiteField": "LabelReversed", "JSONTableAlias": "PrintFF", "JSONFieldAlias": "FR", "IsUpdateField": true, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "PrinterFormatField", "SQLServerField": "LabelUnderline", "SQLiteTable": "Election_PrinterFormatField", "SQLiteField": "LabelUnderline", "JSONTableAlias": "PrintFF", "JSONFieldAlias": "LUL", "IsUpdateField": true, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "PrinterFormatField", "SQLServerField": "LineNumber", "SQLiteTable": "Election_PrinterFormatField", "SQLiteField": "LineNumber", "JSONTableAlias": "PrintFF", "JSONFieldAlias": "LNum", "IsUpdateField": true, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "PrinterFormatField", "SQLServerField": "Record_Update_Application_Datetime", "SQLiteTable": "Election_PrinterFormatField", "SQLiteField": "Record_Update_Application_Datetime", "JSONTableAlias": "PrintFF", "JSONFieldAlias": "RUAppDt", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "PrinterFormatField", "SQLServerField": "Record_Update_Application_Name", "SQLiteTable": "Election_PrinterFormatField", "SQLiteField": "Record_Update_Application_Name", "JSONTableAlias": "PrintFF", "JSONFieldAlias": "RUAppN", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "PrinterFormatField", "SQLServerField": "Record_Update_Application_User_Name", "SQLiteTable": "Election_PrinterFormatField", "SQLiteField": "Record_Update_Application_User_Name", "JSONTableAlias": "PrintFF", "JSONFieldAlias": "RUAppU", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "PrinterFormatField", "SQLServerField": "Record_Update_User_Name", "SQLiteTable": "Election_PrinterFormatField", "SQLiteField": "Record_Update_User_Name", "JSONTableAlias": "PrintFF", "JSONFieldAlias": "RUUN", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "PrinterFormatField", "SQLServerField": "Record_Update_UTC_Datetime", "SQLiteTable": "Election_PrinterFormatField", "SQLiteField": "Record_Update_UTC_Datetime", "JSONTableAlias": "PrintFF", "JSONFieldAlias": "RUDt", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Voting_Status_Mapping", "SQLServerField": "Ballot_Issued", "SQLiteTable": "Election_Voting_Status_Mapping", "SQLiteField": "Ballot_Issued", "JSONTableAlias": "VSM", "JSONFieldAlias": "BI", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Voting_Status_Mapping", "SQLServerField": "CommentText", "SQLiteTable": "Election_Voting_Status_Mapping", "SQLiteField": "CommentText", "JSONTableAlias": "VSM", "JSONFieldAlias": "Com", "IsUpdateField": true, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Voting_Status_Mapping", "SQLServerField": "Mapping_ID", "SQLiteTable": "Election_Voting_Status_Mapping", "SQLiteField": "Mapping_ID", "JSONTableAlias": "VSM", "JSONFieldAlias": "ID", "IsUpdateField": false, "IsPK": true, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Voting_Status_Mapping", "SQLServerField": "Record_Update_Application_Datetime", "SQLiteTable": "Election_Voting_Status_Mapping", "SQLiteField": "Record_Update_Application_Datetime", "JSONTableAlias": "VSM", "JSONFieldAlias": "RUAppDt", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Voting_Status_Mapping", "SQLServerField": "Record_Update_Application_Name", "SQLiteTable": "Election_Voting_Status_Mapping", "SQLiteField": "Record_Update_Application_Name", "JSONTableAlias": "VSM", "JSONFieldAlias": "RUAppN", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Voting_Status_Mapping", "SQLServerField": "Record_Update_Application_User_Name", "SQLiteTable": "Election_Voting_Status_Mapping", "SQLiteField": "Record_Update_Application_User_Name", "JSONTableAlias": "VSM", "JSONFieldAlias": "RUAppU", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Voting_Status_Mapping", "SQLServerField": "Record_Update_User_Name", "SQLiteTable": "Election_Voting_Status_Mapping", "SQLiteField": "Record_Update_User_Name", "JSONTableAlias": "VSM", "JSONFieldAlias": "RUUN", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Voting_Status_Mapping", "SQLServerField": "Record_Update_UTC_Datetime", "SQLiteTable": "Election_Voting_Status_Mapping", "SQLiteField": "Record_Update_UTC_Datetime", "JSONTableAlias": "VSM", "JSONFieldAlias": "RUDt", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Voting_Status_Mapping", "SQLServerField": "StatusText", "SQLiteTable": "Election_Voting_Status_Mapping", "SQLiteField": "StatusText", "JSONTableAlias": "VSM", "JSONFieldAlias": "St", "IsUpdateField": true, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Voting_Status_Mapping", "SQLServerField": "Surrender_Mail_Ballot", "SQLiteTable": "Election_Voting_Status_Mapping", "SQLiteField": "Surrender_Mail_Ballot", "JSONTableAlias": "VSM", "JSONFieldAlias": "SurB", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Voting_Status_Mapping", "SQLServerField": "Voter_Absentee_Code_Jurisdiction_Enumeration_Value_ID", "SQLiteTable": "Election_Voting_Status_Mapping", "SQLiteField": "Voter_Absentee_Code_Jurisdiction_Enumeration_Value_ID", "JSONTableAlias": "VSM", "JSONFieldAlias": "AbsValID", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}, {"SQLServerSchema": "Election", "SQLServerTable": "Voting_Status_Mapping", "SQLServerField": "Voter_Status_Code_Jurisdiction_Enumeration_Value_ID", "SQLiteTable": "Election_Voting_Status_Mapping", "SQLiteField": "Voter_Status_Code_Jurisdiction_Enumeration_Value_ID", "JSONTableAlias": "VSM", "JSONFieldAlias": "VSValID", "IsUpdateField": false, "IsPK": false, "IsInsertRecord": false}]