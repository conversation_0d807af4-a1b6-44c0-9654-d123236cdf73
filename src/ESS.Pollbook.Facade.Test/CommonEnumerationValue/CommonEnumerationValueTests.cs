using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using ESS.Pollbook.Components.Repository.CommonEnumerationValue;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Facade.CommonEnumerationValue;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using Newtonsoft.Json;

namespace ESS.Pollbook.Facade.Test.CommonEnumerationValue
{
	[TestClass]
	public class CommonEnumerationValueTests
	{
		private readonly IEnumerable<CommonEnumerationValueDto> _cevDtos;
		private Mock<ICommonEnumerationValueRepository> _repoMock;
		private ICommonEnumerationValueFacade _commonEnumerationValueFacade;

		public CommonEnumerationValueTests()
		{
			// get Enum data from json
			var jsonPath = GetHelperFilePath(@"..\..\..\CommonEnumerationValue\Support\CommonEnumerationValues.json");
			// deserialize from json. Done in constructor so that we do not create this collection before each TestMethod.
			_cevDtos = GetCommonEnumerationValueDtosFromJson(jsonPath);
		}

		[ClassInitialize]
		public static void ClassInitialize(TestContext testContext)
		{
			// Called once before running the tests of the class. 
		}

		/// <summary>
		/// Called once before each test after the constructor
		/// </summary>
		[TestInitialize]
		public void TestInitialize()
		{
			_repoMock = new Mock<ICommonEnumerationValueRepository>();
		}

		#region TestMethods

		[TestMethod]
		public async Task CommonEnumerationValueFacade_GetAllCommonEnumerationValues()
		{
			#region Arrange

			// assign return value for the method in the mock
			_repoMock.Setup(c => c.CommonEnumerationValuesAsync).Returns(Task.FromResult(_cevDtos));
			// assign the local variable a Mock Object, this value can be passed in the constructor
			var repo = _repoMock.Object;
			_commonEnumerationValueFacade = new CommonEnumerationValueFacade(repo);

			#endregion Arrange

			#region Act

			var cev = await _commonEnumerationValueFacade.GetAllCommonEnumerationValuesAsync();

			#endregion Act

			#region Assert

			Assert.AreEqual(149, cev.Count());
			_repoMock.Verify(x => x.CommonEnumerationValuesAsync, Times.Exactly(1));

			#endregion Assert
		}

		[DataTestMethod]
		[DataRow(300, 34, 196)]
		[DataRow(200, 34, 195)]
		[DataRow(100, 36, 201)]
		[TestMethod]
		public void CommonEnumerationValueFacade_GetCommonEnumerationValueId(int enumValueNumber, int enumerationId, int expected)
		{
			#region Arrange

			_repoMock.Setup(c => c.GetCommonEnumerationValueAsync(enumValueNumber, enumerationId))
				.Returns(Task.FromResult(_cevDtos.FirstOrDefault(x => x.EnumerationValueNumber == enumValueNumber && x.EnumerationId == enumerationId)));
			var repo = _repoMock.Object;
			_commonEnumerationValueFacade = new CommonEnumerationValueFacade(repo);

			#endregion Arrange

			#region Act

			var enumerationValueId = _commonEnumerationValueFacade.GetCommonEnumerationValueIdAsync(enumValueNumber, enumerationId);

			#endregion Act

			#region Assert

			var actual = enumerationValueId.Result;
			Assert.AreEqual(expected, actual);

			#endregion Assert
		}

		[TestMethod]
		public void CommonEnumerationValueFacade_Enums_Count()
		{
			var enumerationNames = _cevDtos.Select(x => x.EnumerationName).ToList();
			var bar = enumerationNames.Distinct().OrderBy(x => x);

			Assert.AreEqual(35, bar.Count());
		}

		#endregion TestMethods

		#region Support Methods

		private IEnumerable<CommonEnumerationValueDto> GetCommonEnumerationValueDtosFromJson(string path)
		{
			var collection = new List<CommonEnumerationValueDto>
			{
				Capacity = 0
			};
			if (!File.Exists(path)) return collection;
			using (var r = new StreamReader(path))
			{
				var json = r.ReadToEnd();
				collection = JsonConvert.DeserializeObject<List<CommonEnumerationValueDto>>(json);
			}

			return collection;
		}

		private string GetHelperFilePath(string relativePathToFile)
		{
			var currentAssemblyPath = Path.GetDirectoryName(Assembly.GetExecutingAssembly().CodeBase)?.Replace(@"file:\", string.Empty);
			if (currentAssemblyPath == null) return string.Empty;
			var relativePath = Path.Combine(currentAssemblyPath, relativePathToFile);
			return Path.GetFullPath(relativePath);
		}

		#endregion // Support Methods
	}
}
