using ESS.ELLEGO.ServiceBus.Core.Interfaces;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Core.Storage;
using ESS.Pollbook.Hardware.Storage;
using ESS.Pollbook.MasterProcessor.Abstractions;
using ESS.Pollbook.MasterProcessor.Abstractions.Shared;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Text.Json;
using System.Threading;

namespace ESS.Pollbook.MasterProcessor.FileWatcher
{
    public class EssPollbookFileWatcher : IEssPollbookFileWatcher, IDisposable
    {
        private readonly IPollbookQueue _queue;
        private readonly IEssMessageFactory _messageFactory;
        private readonly IEssLogger _log;

        private FileSystemWatcher _watcher;
        private string _folder;
        private string _passPhrase;

        private static readonly ReaderWriterLockSlim _rwLock =
            new ReaderWriterLockSlim(LockRecursionPolicy.NoRecursion);

        private int _disposed;
        private bool _intialized = false;

        private const string InProgressExt = ".inprogress";
        private const string FinalExt = ".json";
        private const NotifyFilters Filters = NotifyFilters.FileName | NotifyFilters.LastWrite;
        private const int FsBufferSize = 64 * 1024; // 64 KB
        private const int ReadRetryCount = 3;
        private const int ReadRetryDelayMs = 15;

        private readonly Dictionary<string, string> _logProps = new Dictionary<string, string>(1);
        private readonly string _clsName;

        public EssPollbookFileWatcher(
            IPollbookQueue queue,
            IEssMessageFactory messageFactory,
            IEssLogger logger = null)
        {
            _queue = queue ?? throw new ArgumentNullException(nameof(queue));
            _messageFactory = messageFactory ?? throw new ArgumentNullException(nameof(messageFactory));
            _log = logger;
            _clsName = GetType().Name;
        }

        public void QueueUnprocessedFiles()
        {
            Initialize();

            try
            {
                string[] extensions = { InProgressExt, FinalExt };

                // Get all files (including those in subdirectories)
                IEnumerable<string> allFiles = Directory.EnumerateFiles(_folder, "*",SearchOption.AllDirectories)
                    .Where(x => extensions.Any(ext => ext == Path.GetExtension(x)));

                if (!allFiles.Any())
                    return;

                foreach (string filePath in allFiles)
                {
                    var fixExtension = Path.GetExtension(filePath) == Path.GetExtension(InProgressExt);
                    ReadFileAndQueue(filePath, fixExtension);
                }
            }
            catch (UnauthorizedAccessException ex)
            {
                LogError(ex, nameof(QueueUnprocessedFiles));
            }
            catch (Exception ex)
            {
                LogError(ex, nameof(QueueUnprocessedFiles));
            }
        }

        public void StartFileWatchService()
        {
            Initialize();
            if (_watcher != null) return; // already started

            _watcher = new FileSystemWatcher(_folder)
            {
                NotifyFilter = Filters,
                Filter = "*" + FinalExt,
                IncludeSubdirectories = true,
                InternalBufferSize = FsBufferSize
            };

            _watcher.Renamed += OnRenamed;
            _watcher.Error += OnError;
            _watcher.EnableRaisingEvents = true;
            _watcher.IncludeSubdirectories = true;
        }
        
        private void Initialize()
        {
            if (!_intialized)
            {
                if (string.IsNullOrEmpty(_folder))
                    _folder = StorageLocator.GetFileWatcherFolder();
                if (string.IsNullOrEmpty(_passPhrase))
                    _passPhrase = SystemConfiguration.ElectionConfiguration.PassPhrase;

                _intialized = true;
            }
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public void AddFileToDirectory(PollbookQueueItem item)
        {
            if (item == null || item.ClientId == Guid.Empty) return;

            try
            {
                var fileName = item.ClientId.ToString("N"); // no dashes
                var rootPath = GetFilePath(item);
                if (!Directory.Exists(rootPath))
                {
                    Directory.CreateDirectory(rootPath);
                }

                var tmpPath = Path.Combine(rootPath, fileName + InProgressExt);
                var finalPath = Path.Combine(rootPath, fileName + FinalExt);

                if (File.Exists(tmpPath) ||  File.Exists(finalPath))
                {
                    throw new ArgumentException($"File for ClientId {item.ClientId} already exists for in progess and/or final naming. Unable to add file.", nameof(item.ClientId));
                }

                // serialize + encrypt
                string json = JsonSerializer.Serialize(item);
                string encrypted = _messageFactory.Encrypt(json, _passPhrase);
                byte[] bytes = Encoding.UTF8.GetBytes(encrypted);

                _rwLock.EnterWriteLock();
                try
                {
                    // 1. write to temp file with WriteThrough
                    using (var fs = new FileStream(tmpPath, FileMode.Create, FileAccess.Write,
                               FileShare.None, 4096, FileOptions.WriteThrough))
                    {
                        fs.Write(bytes, 0, bytes.Length);
                    }

                    // 2. atomically move (rename)
                    File.Move(tmpPath, finalPath);
                }
                finally
                {
                    _rwLock.ExitWriteLock();
                }
            }
            catch (Exception ex)
            {
                LogError(ex, nameof(AddFileToDirectory));
            }
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public void RenameFile(PollbookQueueItem item, bool changeToInProgress = false)
        {
            var fileName = item.ClientId.ToString("N"); // no dashes
            var rootPath = GetFilePath(item);
            var fromPath = changeToInProgress ? Path.Combine(rootPath, fileName + FinalExt) : Path.Combine(rootPath, fileName + InProgressExt);
            var toPath = changeToInProgress ? Path.Combine(rootPath, fileName + InProgressExt) : Path.Combine(rootPath, fileName + FinalExt);

            if (!File.Exists(fromPath) || File.Exists(toPath))
            {
                throw new ArgumentException($"File for ClientId {item.ClientId} could be not renamed due to the original did not exist or the final already existed.", nameof(item.ClientId));
            }

            _rwLock.EnterWriteLock();
            try
            {
                File.Move(fromPath, toPath);
            }
            finally
            {
                _rwLock.ExitWriteLock();
            }
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public void RemoveFileFromDirectory(PollbookQueueItem item)
        {
            if (item == null) return;

            try
            {
                string path = Path.Combine(GetFilePath(item), item.ClientId.ToString("N") + InProgressExt);

                _rwLock.EnterWriteLock();
                try
                {
                    if (File.Exists(path))
                        File.Delete(path);
                }
                finally
                {
                    _rwLock.ExitWriteLock();
                }
            }
            catch (Exception ex)
            {
                LogError(ex, nameof(RemoveFileFromDirectory));
            }
        }

        public void EmptyQueues(string path = null)
        {
            if (string.IsNullOrWhiteSpace(path) && string.IsNullOrWhiteSpace(_folder))
            {
                Initialize();
            }
            FileSystemUtil.EmptyPath(string.IsNullOrWhiteSpace(path) ? _folder : path);
        }

        public void CopyQueues(string destinationPath, string queuePath, bool createDestination = true)
        {
            if (string.IsNullOrWhiteSpace(destinationPath) || string.IsNullOrWhiteSpace(queuePath))
            {
                return;
            }

            if (string.IsNullOrWhiteSpace(_folder))
            {
                Initialize();
            }

            var rootSourcePath = Path.Combine(_folder, queuePath);

            rootSourcePath.CopyPath(Path.Combine(destinationPath, queuePath), createDestination);
        }

        private void OnRenamed(object sender, RenamedEventArgs e)
        {
            if (!e.FullPath.EndsWith(FinalExt, StringComparison.OrdinalIgnoreCase))
                return;

            ReadFileAndQueue(e.FullPath);
        }

        private void ReadFileAndQueue(string filePath, bool setToFinalExtension = false)
        {
            try
            {
                PollbookQueueItem item = null;

                for (int attempt = 0; attempt < ReadRetryCount && item == null; attempt++)
                {
                    _rwLock.EnterReadLock();
                    try
                    {
                        if (File.Exists(filePath))
                        {
                            using (var fs = new FileStream(filePath, FileMode.Open, FileAccess.Read,
                                       FileShare.ReadWrite | FileShare.Delete, 4096,
                                       FileOptions.SequentialScan))
                            using (var sr = new StreamReader(fs, Encoding.UTF8, false, 4096, true))
                            {
                                string encrypted = sr.ReadToEnd();
                                string decrypted = _messageFactory.Decrypt(encrypted, _passPhrase);
                                item = JsonSerializer.Deserialize<PollbookQueueItem>(decrypted);
                            }
                        }
                    }
                    finally
                    {
                        _rwLock.ExitReadLock();
                    }

                    if (item == null)
                        Thread.Sleep(ReadRetryDelayMs); // file may still be locked by another process
                }

                if (item == null) return;

                //If the file wasn't created using current election, remove from the file system
                if (item.ElectionGuid != SystemConfiguration.ElectionConfiguration.ElectionDatabaseGuid)
                {
                    RemoveFileFromDirectory(item);
                    return;
                }

                //If items file extension is .inprogress due to file cleanup, get it back to its final extension
                //so the rest of the code performs as intended.
                if(setToFinalExtension)
                    RenameFile(item);

                switch (item.ItemQueueType)
                {
                    case QueueType.Transaction: _queue.Transactions.Enqueue(item); break;
                    case QueueType.Polldata: _queue.Polldata.Enqueue(item); break;
                    default:
                        throw new ArgumentOutOfRangeException(nameof(item.ItemQueueType), item.ItemQueueType, null);
                }
            }
            catch (Exception ex)
            {
                LogError(ex, nameof(OnRenamed));
            }
        }

        private void OnError(object sender, ErrorEventArgs e)
        {
            LogError(e.GetException(), nameof(OnError));

            try
            {
                if (_watcher != null)
                {
                    StopWatcher();
                    StartFileWatchService();
                }
            }
            catch (Exception ex)
            {
                LogError(ex, nameof(OnError));
            }
        }

        private void StopWatcher()
        {
            if (_watcher == null) return;

            _watcher.IncludeSubdirectories = false;
            _watcher.EnableRaisingEvents = false;
            _watcher.Renamed -= OnRenamed;
            _watcher.Error -= OnError;
            _watcher.Dispose();
            _watcher = null;
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        private void LogError(Exception ex, string method)
        {
            if (_log == null) return;

            lock (_logProps)
            {
                _logProps.Clear();
                _logProps["Action"] = $"{_clsName}.{method}";
                _log.LogError(ex, _logProps);
            }
        }

        private string GetFilePath(PollbookQueueItem item)
        {
            switch (item.ItemQueueProcess)
            {
                case QueueProcess.FailedInsertTransaction:
                    return Path.Combine(_folder, StorageLocator.TRANSACTION_INSERT_QUEUE_FOLDER);
                case QueueProcess.FailedUpdateHostTransaction:
                    return Path.Combine(_folder, StorageLocator.TRANSACTION_UPDATE_QUEUE_FOLDER);
                case QueueProcess.SerialNumber:
                    return Path.Combine(_folder, StorageLocator.SERIAL_INSERT_QUEUE_FOLDER);
                default:
                    return _folder;
            }
        }

        public void Dispose()
        {
            if (Interlocked.Exchange(ref _disposed, 1) == 1) return;
            StopWatcher();
            _rwLock?.Dispose();
            GC.SuppressFinalize(this);
        }
    }
}
