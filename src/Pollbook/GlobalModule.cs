using Autofac;
using ESS.ELLEGO.ServiceBus.Core;
using ESS.ELLEGO.ServiceBus.Core.Configuration;
using ESS.ELLEGO.ServiceBus.Core.Factory;
using ESS.ELLEGO.ServiceBus.Core.Interfaces;
using ESS.Pollbook.Core.Interface;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.UICore;
using ESS.Pollbook.DataAccess.Interfaces;
using ESS.Pollbook.DataAccess.Sqlite;
using ESS.Pollbook.Facade.VoterEligibility;
using SQLitePCL;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Reflection;
using ESS.Pollbook.Components.Business.Cache;
using Module = Autofac.Module;

namespace Pollbook
{
    public class GlobalModule : Module
    {
        protected override void Load(ContainerBuilder builder)
        {
            // This line allows SmartAssembly to pick up the MasterProcessor
            // Do not remove it!
            var test = new ESS.Pollbook.MasterProcessor.Queues.PrioritizableQueue();

            // Classes
            var assembly = Assembly.GetExecutingAssembly();
            builder.RegisterAssemblyTypes(assembly)
                .Where(t => t.FullName.ToLower().Contains("pollbook"))
                .InstancePerLifetimeScope();

            //Interfaces
            builder.RegisterAssemblyTypes(assembly)
                .Where(t => t.FullName.ToLower().Contains("pollbook"))
                .AsImplementedInterfaces()
                .InstancePerLifetimeScope();

            var essAssemblies = GetESSAssemblies().ToArray();

            // Classes
            builder.RegisterAssemblyTypes(essAssemblies)
                .InstancePerLifetimeScope();

            //Interfaces
            builder.RegisterAssemblyTypes(essAssemblies)
                .AsImplementedInterfaces()
                .InstancePerLifetimeScope();
            
            builder.RegisterType<TransactionGuidCache>()
                .As<ITransactionGuidCache>()
                .SingleInstance(); // Use singleton for cache
            
            builder.Register(ctx =>
               {
                   return new EssServiceBusConfiguration
                   {
                       TickLength = TimeSpan.FromSeconds(30),
                       NodeInactivityTimeout = TimeSpan.FromSeconds(90),
                       UdpBroadcastPort = 9999,
                       DataTransferPort = 5557,
                       IgnoreInvalidCertificate = true,
                       CertificateValidationLifetime = TimeSpan.FromMinutes(1)
                   };
               }).As<EssServiceBusConfiguration>()
               .InstancePerLifetimeScope();

            builder.RegisterType<EssMessageFactory>()
                .As<IEssMessageFactory>()
                .InstancePerLifetimeScope();

            builder.RegisterType<EssServiceBus>()
               .As<IEssServiceBus>()
               .InstancePerLifetimeScope();

            //Register Log4Net
            builder.Register(ctx =>
            {
                var currentPath = GetCurrentPath();
                var pathToLog4NetConfig = Path.Combine(currentPath, "log4net.config");
                return new EssLogger(pathToLog4NetConfig);
            }).As<IEssLogger>().InstancePerLifetimeScope();
            
            builder.RegisterType<EssSqliteWriterAdapter>()
                .UsingConstructor()
                .Keyed<IEssSqliteWriterAdapter>(SqliteDatabaseType.Polldata).InstancePerLifetimeScope();

            builder.RegisterType<EssSqliteWriterAdapter>()
                .UsingConstructor()
                .Keyed<IEssSqliteWriterAdapter>(SqliteDatabaseType.AuditLog).InstancePerLifetimeScope();

            builder.RegisterType<EssSqliteWriterAdapter>()
                .UsingConstructor()
                .Keyed<IEssSqliteWriterAdapter>(SqliteDatabaseType.Pollworker).InstancePerLifetimeScope();

            builder.RegisterType<EssSqliteWriterAdapter>()
                .UsingConstructor()
                .Keyed<IEssSqliteWriterAdapter>(SqliteDatabaseType.RegionalResults).InstancePerLifetimeScope();

            builder.RegisterType<EssSqliteWriterAdapter>()
                .UsingConstructor()
                .Keyed<IEssSqliteWriterAdapter>(SqliteDatabaseType.Signature).InstancePerLifetimeScope();

            builder.RegisterType<EssSqliteWriterAdapter>()
                .UsingConstructor()
                .Keyed<IEssSqliteWriterAdapter>(SqliteDatabaseType.Transaction).InstancePerLifetimeScope();

            
            
            builder.RegisterType<EssSqliteReaderAdapter>()
                .UsingConstructor()
                .Keyed<IEssSqliteReaderAdapter>(SqliteDatabaseType.Polldata).InstancePerLifetimeScope();

            builder.RegisterType<EssSqliteReaderAdapter>()
                .UsingConstructor()
                .Keyed<IEssSqliteReaderAdapter>(SqliteDatabaseType.AuditLog).InstancePerLifetimeScope();

            builder.RegisterType<EssSqliteReaderAdapter>()
                .UsingConstructor()
                .Keyed<IEssSqliteReaderAdapter>(SqliteDatabaseType.Pollworker).InstancePerLifetimeScope();

            builder.RegisterType<EssSqliteReaderAdapter>()
                .UsingConstructor()
                .Keyed<IEssSqliteReaderAdapter>(SqliteDatabaseType.RegionalResults).InstancePerLifetimeScope();

            builder.RegisterType<EssSqliteReaderAdapter>()
                .UsingConstructor()
                .Keyed<IEssSqliteReaderAdapter>(SqliteDatabaseType.Signature).InstancePerLifetimeScope();

            builder.RegisterType<EssSqliteReaderAdapter>()
                .UsingConstructor()
                .Keyed<IEssSqliteReaderAdapter>(SqliteDatabaseType.Transaction).InstancePerLifetimeScope();

            builder.RegisterType<AppConfig>()
           .Named("appConfig", typeof(IAppConfig))
           .InstancePerLifetimeScope();

            //Property injection.
            builder.RegisterType<VoterEligibilityFacade>()
                .As<IVoterEligibilityFacade>()
                .PropertiesAutowired();

            Batteries_V2.Init();
        }

        private static string GetCurrentPath()
        {
            var exe = Process.GetCurrentProcess().MainModule.FileName;
            return Path.GetDirectoryName(exe);
        }

        private IEnumerable<Assembly> GetESSAssemblies()
        {
            // With obfuscation, we no longer have actual DLL files on disk to load from when running a release from our build system.
            // So the assemblies must be loaded by their specific full names. This list can be maintained by using Windows Explorer to
            // view the DLLs present in a Release folder, looking only at those whose names start with "ESS.". To see the Version,
            // include the "File Version" column by selecting it under View/Add Columns/Choose Columns.
            List<string> essAssemblies = new List<string>
            {
                "ESS.Pollbook.DataAccess,                     Version=1.0.0.0, Culture=neutral, PublicKeyToken=null",
                "ESS.Pollbook.OAuth,                          Version=1.0.0.0, Culture=neutral, PublicKeyToken=null",
                "ESS.ELLEGO.Rest,                             Version=1.1.0.0, Culture=neutral, PublicKeyToken=null",
                "ESS.ELLEGO.ServiceBus.Core,                  Version=1.0.0.0, Culture=neutral, PublicKeyToken=null",
                "ESS.Pollbook.Core,                           Version=1.0.0.0, Culture=neutral, PublicKeyToken=null",
                "ESS.Pollbook.Components.Business,            Version=1.0.0.0, Culture=neutral, PublicKeyToken=null",
                "ESS.Pollbook.Components.Repository,          Version=1.0.0.0, Culture=neutral, PublicKeyToken=null",
                "ESS.Pollbook.Facade,                         Version=1.0.0.0, Culture=neutral, PublicKeyToken=null",
                "ESS.Pollbook.Hardware,                       Version=1.0.0.0, Culture=neutral, PublicKeyToken=null",
                "ESS.Pollbook.ViewModel,                      Version=1.0.0.0, Culture=neutral, PublicKeyToken=null",
                "ESS.Integration.SmartCardService,            Version=1.0.0.0, Culture=neutral, PublicKeyToken=null",
                "ESS.Pollbook.MasterProcessor,                Version=1.0.0.0, Culture=neutral, PublicKeyToken=null",
                "ESS.Pollbook.MasterProcessor.Abstractions,   Version=1.0.0.0, Culture=neutral, PublicKeyToken=null"
            };

            return essAssemblies.Select(Assembly.Load);
        }
    }
}