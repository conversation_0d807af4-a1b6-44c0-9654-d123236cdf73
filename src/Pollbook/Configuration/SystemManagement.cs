using ESS.Pollbook.Core.Interface;
using ESS.Pollbook.Core.Model;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Printing;
using System.Linq;
using System.Management;
using System.Reflection;
using System.Text.RegularExpressions;
using ESS.Pollbook.Core.Constants;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;

namespace Pollbook.Configuration
{
   public class SystemManagement : ISystemManagement
   {
	   readonly Dictionary<SourceBatteryStatusEnum, BatteryStatusEnum> _batteryStatusMaps =
		   new Dictionary<SourceBatteryStatusEnum, BatteryStatusEnum>();

	   private readonly IEssLogger _essLogger;

	   public SystemManagement(IEssLogger essLogger)
	   {
		   _essLogger = essLogger;
		   InitBatteryStatusMaps();
	   }

	   private void InitBatteryStatusMaps()
	   {
		   _batteryStatusMaps.Add(SourceBatteryStatusEnum.Discharging, BatteryStatusEnum.Discharging);
		   _batteryStatusMaps.Add(SourceBatteryStatusEnum.AcConnected, BatteryStatusEnum.AcConnected);
		   _batteryStatusMaps.Add(SourceBatteryStatusEnum.FullyCharged, BatteryStatusEnum.FullyCharged);
		   _batteryStatusMaps.Add(SourceBatteryStatusEnum.Low, BatteryStatusEnum.Low);
		   _batteryStatusMaps.Add(SourceBatteryStatusEnum.Critical, BatteryStatusEnum.Critical);
		   _batteryStatusMaps.Add(SourceBatteryStatusEnum.Charging, BatteryStatusEnum.Charging);
		   _batteryStatusMaps.Add(SourceBatteryStatusEnum.ChargingAndHigh, BatteryStatusEnum.ChargingAndHigh);
		   _batteryStatusMaps.Add(SourceBatteryStatusEnum.ChargingAndLow, BatteryStatusEnum.ChargingAndLow);
		   _batteryStatusMaps.Add(SourceBatteryStatusEnum.ChargingAndCritical, BatteryStatusEnum.ChargingAndCritical);
		   _batteryStatusMaps.Add(SourceBatteryStatusEnum.Undefined, BatteryStatusEnum.Undefined);
		   _batteryStatusMaps.Add(SourceBatteryStatusEnum.PartiallyCharged, BatteryStatusEnum.PartiallyCharged);
	   }

	   #region Battery Life Methods

	   public float GetBatteryPercentRemaining()
	   {
		   float percentRemaining = 0L;
		   var query = new ObjectQuery("Select * FROM Win32_Battery");
		   var searcher = new ManagementObjectSearcher(query);
		   var collection = searcher.Get();

		   foreach (var mo in collection.OfType<ManagementObject>())
		   {
			   var chargeRemaining = (from PropertyData prop in mo.Properties
				   where prop.Name == "EstimatedChargeRemaining"
				   select prop).FirstOrDefault();

			   if (chargeRemaining == null) continue;

			   var batteryPercentRemaining = Convert.ToInt64(chargeRemaining.Value);

			   percentRemaining = (batteryPercentRemaining > 100) ? 100 : batteryPercentRemaining;

			   break;
		   }

		   return percentRemaining;
	   }
	   
	   public string GetBatteryRemainingTime()
	   {
		   try
		   {
			   var query = new ObjectQuery("Select * FROM Win32_Battery");
			   var searcher = new ManagementObjectSearcher(query);
			   var collection = searcher.Get();

			   foreach (var mo in collection.OfType<ManagementObject>())
			   {
				   var estimatedRunTime = (from PropertyData prop in mo.Properties
					   where prop.Name == "EstimatedRunTime"
					   select prop).FirstOrDefault();

				   if (estimatedRunTime == null) continue;

				   // EstimatedRunTime is returned in minutes
				   var runTimeInMinutes = Convert.ToInt64(estimatedRunTime.Value);
            
				   // If the value is 71582788, it means the system cannot determine the value
				   if (runTimeInMinutes == 71582788)
				   {
					   return "Unknown";
				   }
				   else
				   {
					   int hours = (int)(runTimeInMinutes / 60);
					   int minutes = (int)(runTimeInMinutes % 60);
                
					   if (hours > 0)
					   {
						   return $"{hours} hour{(hours != 1 ? "s" : "")} {minutes} minute{(minutes != 1 ? "s" : "")}";
					   }
					   else
					   {
						   return $"{minutes} minute{(minutes != 1 ? "s" : "")}";
					   }
				   }
			   }

			   return "Unknown";
		   }
		   catch (Exception ex)
		   {
			   var logProps = new Dictionary<string, string> { { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } };
			   _essLogger.LogError(ex, logProps);
        
			   return "Unknown";
		   }
	   }

	   public BatteryStatusEnum GetBatteryStatus()
	   {
		   var query = new ObjectQuery("Select * From Win32_Battery");
		   var status = SourceBatteryStatusEnum.Undefined;

		   using (var searcher = new ManagementObjectSearcher(query))
		   using (var objectCollection = searcher.Get())
		   {
			   foreach (var mObj in objectCollection.OfType<ManagementObject>())
			   {
				   var pData = mObj.Properties["BatteryStatus"];

				   if (pData != null
				       && pData.Value != null
				       && Enum.IsDefined(typeof(SourceBatteryStatusEnum), pData.Value))
				   {
					   status = (SourceBatteryStatusEnum)pData.Value;
				   }
			   }
		   }

		   return _batteryStatusMaps[status];
	   }

	   #endregion

	   #region Printer Methods

	   #region Public Methods

	   /// <summary>
	   ///not all printers will report status, state and WorkOffline the same
	   /// Printer:                 Epson                       Dymo                    Star Micronics
	   /// Condition:         Attached  Not Attached    Attached    Not Attached    Attached    Not Attached
	   /// PrinterState:         0          4096           0            0               0           128(0)*
	   /// PrinterStatus:        3            2            3            3               3            1(3)*
	   /// WorkOffline:        false        false        false         true            false        true
	   /// 
	   /// In some cases, when the star micronics printer is creating copies of the printer installed, denoted by the addition of (copy1) for example,
	   /// it's possible that the response will have the original printer and it's values would have stateId = 0, status = 3 and workOffline = true.
	   /// 
	   /// MSDN Article Quote:
	   /// If you are retrieving PrinterStatus = 3 or PrinterState = 0, the printer driver may not be feeding accurate information into WMI. 
	   /// WMI retrieves the printer information from the spoolsv.exe process. It is possible the printer driver does not report its status to the spooler. 
	   /// In this case, Win32_Printer reports the printer as Idle.
	   /// 
	   ///  PrinterState => One of the possible states relating to this printer. This property is obsolete(deprecated). In place of this property, use PrinterStatus.
	   ///  0 Idle - for more information, see the Remarks section below.
	   ///  1 Paused
	   ///  2 Error
	   ///  3 Pending Deletion
	   ///  4 Paper Jam
	   ///  5 Paper Out
	   ///  6 Manual Feed
	   ///  7 Paper Problem
	   ///  8 Offline
	   ///  9 I/O Active
	   ///  10 Busy
	   ///  11 Printing
	   ///  12 Output Bin Full
	   ///  13 Not Available
	   ///  14 Waiting
	   ///  15 Processing
	   ///  16 Initialization
	   ///  17 Warming Up
	   ///  18 Toner Low
	   ///  19 No Toner
	   ///  20 Page Punt
	   ///  21 User Intervention Required
	   ///  22 Out of Memory
	   ///  23 Door Open
	   ///  24 Server_Unknown
	   ///  25 Power Save
	   ///  
	   ///  PrinterStatus => Status information for a printer that is different from information specified in the logical device Availability property.
	   ///  1 = 'Other'
	   ///  2 = 'Unknown'
	   ///  3 = 'Idle'
	   ///  4 = 'Printing'
	   ///  5 = 'Warmup'
	   ///  6 = 'Stopped Printing'
	   ///  7 = 'Offline'
	   ///
	   /// </summary>
	   /// <param name="printerName"></param>
	   /// <param name="getFullPrinterNameWithNetworkPath"></param>
	   /// <returns></returns>
	   public PrintResponse GetPrinterInformationByName(string printerName, bool getFullPrinterNameWithNetworkPath)
	   {
		   ManagementObject printer = null;
		   var connectedArray = new[] { "3", "4", "5" };

		   try
		   {
			   if (!string.IsNullOrEmpty(printerName))
			   {
				   var printerSearcherByNameQuery = $"SELECT * from Win32_Printer WHERE Name LIKE '%{printerName}%'";

				   using (var printerSearcherByName = new ManagementObjectSearcher(printerSearcherByNameQuery))
				   using (var coll = printerSearcherByName.Get())
				   {
					   /* look through the collection of printer objects that matched the criteria
					    * then select the object that is connected -- this is determined by status = 3,4 or 5 and
					    * work offline = false
					    */
					   foreach (var p in coll)
					   {
						   var obj = (ManagementObject)p;

						   var status = obj.Properties["PrinterStatus"].Value != null
							   ? obj.Properties["PrinterStatus"].Value.ToString()
							   : String.Empty;
						   var offline = (bool?)obj.Properties["WorkOffline"].Value ?? false;
						   if (connectedArray.Contains(status) && !offline) printer = obj;
					   }

					   if (printer == null) printer = (from ManagementObject p in coll select (p)).FirstOrDefault();
				   }
			   }

			   return printer == null
				   ? null
				   : MapPrinterManagementObjectToResponse(printer, getFullPrinterNameWithNetworkPath);
		   }
		   catch (ManagementException ex)
		   {
			   var logProps = new Dictionary<string, string>
			   {
				   { "Action", "Getting printer information" },
				   { "printerName", printerName },
				   { "getFullPrinterNameWithNetworkPath", getFullPrinterNameWithNetworkPath.ToString() }
			   };
			   _essLogger.LogError(ex, logProps);

			   return new PrintResponse { PrinterName = "None" };
		   }
	   }


	   public PrintResponse GetActiveDymoPrinterInformationByName()
	   {
		   ManagementObject printer = null;

		   try
		   {
			   var printerSearcherByNameQuery =
				   $"SELECT * from Win32_Printer WHERE Name LIKE 'DYMO LabelWriter 450 Turbo%'";

			   using (var printerSearcherByName = new ManagementObjectSearcher(printerSearcherByNameQuery))
				   foreach (var obj in printerSearcherByName.Get())
				   {
					   var printerObj = (ManagementObject)obj;

					   var online = (printerObj["WorkOffline"].ToString().ToLower() != "true");
					   if (!online) continue;

					   printer = printerObj;
					   break;
				   }

			   return printer != null ? MapPrinterManagementObjectToResponse(printer, true) : default(PrintResponse);
		   }
		   catch (ManagementException ex)
		   {
			   var logProps = new Dictionary<string, string> { { "Action", "Getting printer information by name" } };
			   _essLogger.LogError(ex, logProps);

			   return null;
		   }
	   }

	   static readonly string printersSearcherQuery = "SELECT * from Win32_Printer";

	   public List<PrintResponse> GetAllPrinters()
	   {
		   var printResponses = new List<PrintResponse>();

		   try
		   {
			   ManagementObjectCollection printers;
			   using (var printersSearcher = new ManagementObjectSearcher(printersSearcherQuery))
			   {
				   printers = printersSearcher.Get();
			   }

			   printResponses.AddRange(from ManagementBaseObject printer in printers
				   select MapPrinterManagementObjectToResponse((ManagementObject)printer, false));
			   return printResponses;
		   }
		   catch (ManagementException ex)
		   {
			   var logProps = new Dictionary<string, string> { { "Action", "Getting all printers" } };
			   _essLogger.LogError(ex, logProps);

			   return printResponses;
		   }
	   }

	   public string QuerySystem(string queryString, string name)
	   {
		   var comPortName = string.Empty;

		   try
		   {
			   // Search WMI all Win32_SerialPort objects for device using specified "Description" field.
			   // The "Description" field should be the same value as the following:
			   // "Windows Device Manager" -> "Ports (COM & LPT)" -> "<Port Name>" -> "Properties" -> "Details" -> "Device description"
			   System.Management.ManagementObjectSearcher searcher =
				   new System.Management.ManagementObjectSearcher(queryString);

			   // get the serial port collection
			   System.Management.ManagementObjectCollection serialPortColl = searcher.Get();


			   // get serial port collection enumerator
			   System.Management.ManagementObjectCollection.ManagementObjectEnumerator serialPortCollEnum =
				   serialPortColl.GetEnumerator();

			   // current serial port management object
			   System.Management.ManagementObject serialPortObj = null;

			   // process the serial port object (this only supports one plugged-in device)
			   if (serialPortCollEnum.MoveNext())
			   {
				   // get current serial port object
				   serialPortObj = serialPortCollEnum.Current as System.Management.ManagementObject;
				   if (serialPortObj != null) comPortName = serialPortObj[name].ToString();
				   // get the com port name 
			   }
		   }
		   catch (System.Management.ManagementException)
		   {
			   // discard management (WMI) exceptions as they are generally thrown when the 
			   // device is removed during processing
		   }
		   catch (System.ComponentModel.Win32Exception)
		   {
			   // discard Win32 exceptions as they are generally thrown when the 
			   // device is removed during processing
		   }
		   catch (System.IO.IOException)
		   {
			   // discard IO exceptions as they are generally thrown when the 
			   // device is removed during processing
		   }

		   return comPortName;
	   }

	   public void PrintReportLines(PrintRequest request)
	   {
		   try
		   {
			   if (request.ReportType == ReportTypes.BallotTotals || request.ReportType == ReportTypes.SpoiledBallot)
			   {
				   using (PrintJob job = new PrintJob(_essLogger))
				   {
					   var lineData = new List<ReportLine>();
					   var font = new Font(request.FontFamily, int.Parse(request.FontSize));
					   job.Print(lineData, request, font);
				   }
			   }
			   else if (request.ReportType == ReportTypes.WaitTime)
			   {
				   using (var job = new PrintJob(_essLogger))
				   {
					   var lineData = new List<ReportLine>();
					   var waitTimeData = request.PrintData;
					   var lines = Regex.Replace(waitTimeData, Environment.NewLine, "\n").Split('\n');

					   foreach (var line in lines)
					   {
						   lineData.Add(new ReportLine(line));
					   }

					   job.PrintWaitTime(lineData, request);
				   }
			   }
			   else
			   {
				   var report = request.PrintData;
				   var report2 = request.PrintByteData;

				   var lineData = new List<ReportLine>();
				   if (report.Length > 0)
				   {
					   var separator = new char[] { '\n' };

					   var splitOptions = StringSplitOptions.None;

					   var printer = SystemConfiguration.ElectionConfiguration.ExpressPollPrinters
						   .FirstOrDefault(p =>
							   p.PollbookPrinterId == SystemConfiguration.ElectionConfiguration
								   .SelectedExpressPollPrinterId);

					   var paperTypeIsLabel = printer?.IsLabel ?? false;
					   if (paperTypeIsLabel)
						   splitOptions = StringSplitOptions.RemoveEmptyEntries;

					   var lines = Regex.Replace(report, Environment.NewLine, "\n").Split(separator, splitOptions);

					   // loop through each line and look for any binary data tags
					   foreach (var line in lines)
					   {
						   if (line.Contains(VoterAuthorityDocLabelConstant.CONST_SIGNATURE))
						   {
							   //add extra line space above signature
							   lineData.Add(new ReportLine(string.Empty));
						   }

						   lineData.Add(new ReportLine(line));

						   if (line.Contains(VoterAuthorityDocLabelConstant.CONST_SIGNATURE) && report2 != null)
						   {
							   lineData.Add(new ReportLine(report2));
						   }
					   }
				   }

				   if (lineData.Count != 0)
				   {
					   using (var job = new PrintJob(_essLogger))
					   {
						   var font = new Font(request.FontFamily, int.Parse(request.FontSize));
						   job.Print(lineData, request, font);
					   }
				   }
			   }
		   }
		   catch (Exception ex)
		   {
			   var logProps = new Dictionary<string, string>
			   {
				   { "Action", "Print Job - Printing report lines" },
				   { "PrintRequest.PrinterName", request.PrinterName }
			   };
			   _essLogger.LogError(ex, logProps);
		   }
	   }

	   #endregion

	   /// <summary>
	   /// Map the ManagementObject for the specified printer with full printerName to a PrinterResponse.
	   /// </summary>
	   /// <param name="printer"></param>
	   /// <param name="getFullPrinterNameWithNetworkPath"></param>
	   /// <returns></returns>
	   private PrintResponse MapPrinterManagementObjectToResponse(ManagementObject printer,
		   bool getFullPrinterNameWithNetworkPath)
	   {
		   PrintResponse response = new PrintResponse { PrinterName = "None" };
		   if (printer != null)
		   {
			   response.Location = printer.Properties["Location"].Value != null
				   ? printer.Properties["Location"].Value.ToString()
				   : String.Empty;

			   response.PortNumber = printer.Properties["PortName"].Value != null
				   ? printer.Properties["PortName"].Value.ToString()
				   : String.Empty;

			   response.PrinterDeviceID = printer.Properties["DeviceID"].Value != null
				   ? printer.Properties["DeviceID"].Value.ToString()
				   : String.Empty;

			   response.PrinterDriverName = printer.Properties["DriverName"].Value != null
				   ? printer.Properties["DriverName"].Value.ToString()
				   : String.Empty;

			   var printerName = printer.Properties["Name"].Value.ToString();
			   if (!string.IsNullOrWhiteSpace(printerName))
			   {
				   if (getFullPrinterNameWithNetworkPath)
				   {
					   response.PrinterName = printerName;
				   }
				   else
				   {
					   var index1 = printerName.IndexOf("\\", StringComparison.Ordinal);
					   var index2 = printerName.LastIndexOf("\\", StringComparison.Ordinal);

					   if (index1 >= 0 && index2 >= 0)
						   printerName = printerName.Remove(index1, index2 - index1);
					   response.PrinterName = printerName.Replace("\\", "");
				   }
			   }

			   response.PortNumber = printer.Properties["PortName"].Value != null
				   ? printer.Properties["PortName"].Value.ToString()
				   : String.Empty;

			   response.PrinterStateId = printer.Properties["PrinterState"].Value != null
				   ? printer.Properties["PrinterState"].Value.ToString()
				   : String.Empty;

			   response.PrinterStatusId = printer.Properties["PrinterStatus"].Value != null
				   ? printer.Properties["PrinterStatus"].Value.ToString()
				   : String.Empty;

			   response.ServerName = printer.Properties["ServerName"].Value != null
				   ? printer.Properties["ServerName"].Value.ToString()
				   : String.Empty;

			   response.ShareName = printer.Properties["ShareName"].Value != null
				   ? printer.Properties["ShareName"].Value.ToString()
				   : String.Empty;

			   response.WorkOffline = (bool?)printer.Properties["WorkOffline"].Value ?? false;
		   }

		   return response;
	   }

	   #endregion
   }
}
