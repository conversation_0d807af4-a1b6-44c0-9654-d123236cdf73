<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <log4net>
    <root>
      <level value="DEBUG" />
      <appender-ref ref="RollingFileAppender" />
    </root>
    <appender name="RollingFileAppender" type="log4net.Appender.RollingFileAppender">
      <file value="C:\StorageData\pollbook.log" />
      <appendToFile value="true" />
      <lockingModel type="log4net.Appender.FileAppender+MinimalLock" />
      <rollingStyle value="Date" />
	    <datePattern value="yyyyMMdd" />
      <maxSizeRollBackups value="15" />
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%date (UTC: %utcdate) [%thread] %-level - %message%newline" />
      </layout>
    </appender>
  </log4net>
</configuration>