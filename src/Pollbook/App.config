<?xml version="1.0" encoding="utf-8"?>
<configuration>
   <configSections>
	  <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
   </configSections>
   <startup>
	  <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8" />
   </startup>
   <appSettings>

	  <add key="Election" value="County General" />
	  <add key="ignoreNetworks" value="ESS PB 5G" />
	  <add key="minimumSignalQuality" value="20" />

	  <!--TODO:  These values are temporary.  Allows testers to do some filtering-->
	  <add key="MaxRecordThreshold" value="25" />
	  <!--<add key="BarcodeRegex" value="(?s)DCS(?&lt;LastName&gt;(?:[a-zA-Z]+))(?:.)+DCT(?&lt;FirstName&gt;(?:[a-zA-Z]+))(?:.)+DAQ(?&lt;DriversLicense&gt;(?:[A-Z0-9-]+))(?-s)" />-->
	  <add key="BarcodeRegex" value="(?s)DAQ(?&lt;DriversLicense&gt;(?:[A-Z0-9-]+))(?:.)+DCS(?&lt;LastName&gt;(?:[a-zA-Z]+))(?:.)+DAC(?&lt;FirstName&gt;(?:[a-zA-Z]+))(?:.)+DBB{1}(?&lt;DOB&gt;(?:\d{8}))(?-s)" />
	  <add key="Barcode2DRegex" value="({|DAA|DAG|DAI|DAJ|DAK|DAQ|DAR|DAS|DAT|DBA|DBB|DBC|DBD|DAU|DAW|DAY|DAZ|DBK|PAA|PAB|PAC|PAD|PAE|PAF|DAB|DAC|DAD|DAE|DAF|DAH|DAL|DAM|DAN|DAO|DAP|DAV|DAX|DBE|DBF|DBG|DBH|DBI|DBJ|DBL|DBM|DBN|DBO|DBP|DBQ|DBR|DBS|DCA|DCB|DCD|DCS|DCT|DCF|DCG|DCH|DDE|DDF|DDG|DCI|DCJ|DCK|DCU|DCE|DCL|DCM|DCN|DCO|DCP|DCQ|DCR|DDA|DDB|DDC|DDD|DDH|DDI|DDJ|DDK|DDL|ZGZ|ZGB|ZGD|ZGE|ZGF|ZGG|ZGH|ZGI|ZGJ|ZGK|ZGL|ZGM|ZG|DL}*)" />
	  <add key="Barcode1DRegex" value=".{5}(.{9})" />
	  <add key="VoterSearchSortOrder" value=" LastName, FirstName, DateOfBirth DESC" />
	  <add key="ClientSettingsProvider.ServiceUri" value="" />
	  <add key="CanceledBallotCount" value="2" />
	  <add key="SyncPointURL" value="https://syncpoint.centralpointqa.com/HostSync/v2.0.0/api" />
	  <add key="HostConnectionCheckSeconds" value="60" />
	  <add key="HostConnectionTimeoutMilliseconds" value="10000" />

	  <!-- url to use for retrieving the public ip of device -->
	  <add key="PublicIPUrl" value="https:\\api.ipify.org" />
	  <!-- url to used for network maintenence profile management -->
	  <add key="ProfileDocumentNameSpace" value="http://www.microsoft.com/networking/WLAN/profile/v1"/>
	   
	  <!--The number of seconds to wait to check for new voter transactions from SyncPoint -->
	  <add key="DownloadTransactionsFrequency" value="10" />
	  <add key="FailedTransactionRetryCount" value="3" />
	  <add key="FailedTransactionRetryWaitSeconds" value="300" />
	  <add key="VoterTransactionsWaitSeconds" value="10" />
	  <add key="VoterTransactionsWaitSecondsMin" value="30" />

	  <add key="DynamsoftCameraManagerProductKey" value="f0068NQAAAI4iaVh8G9imPL3LWeHXo95lnN9m0rlXU1uHDEYS7ax+0z7JcVm80tt/cgwniu08kLvj7tOQj2B61re6R+yeLo8=" />
	  <add key="DynamsoftBarcodeReaderProductKey" value="f0068dAAAAIYOz6TQhNQ+zC3zCWj7kIxU7xaOVeEwikRpDfprESpjztPVzATRbH0s2Zw7Cl4cfGwTRer7zIIiAD5OFFiS1Zk=" />
	  <add key="UpgradeScriptName" value="PRE_INSTALL_UPGRADE_SCRIPT.ps1" />
	  <add key="UpgradePassword" value="sn7P4vFay9Bhg+vRXRDbBWgQVOnTktMKhFagtc1LhKYLO0TAbnwmSKbDoWE5rqyaSRxTI3IPLI3B" />
	  <add key="UpgradePassPhrase" value="2803FE2003B64B37892BAD3BFD944E64" />

	  <!--SQLite/SQLCipher Pragmas -->
	  <add key="PragmaCiphermemorysecurity" value="0" />
	  <add key="PragmaBusyTimeOut" value="1000" />
	  <add key="PragmaSynchronous" value="1" />
	  <add key="PragmaSecuredelete" value="0" />
	  <add key="PragmaReadUncommitted" value="1" />
	   <add key="PragmaCipherKdfIter" value="1000" />

	  <!--affidavit text formatting configs-->
	  <add key="UseSmartTextWrapping" value="1" />
	  <add key="SmartTextDelimiter" value="/" />
   </appSettings>

   <runtime>
	  <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
		 <dependentAssembly>
			<assemblyIdentity name="log4net" publicKeyToken="669e0ddf0bb1aa2a" culture="neutral" />
			<bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
		 </dependentAssembly>
	  </assemblyBinding>
   </runtime>

   <entityFramework>
	  <defaultConnectionFactory type="System.Data.Entity.Infrastructure.LocalDbConnectionFactory, EntityFramework">
		 <parameters>
			<parameter value="v13.0" />
		 </parameters>
	  </defaultConnectionFactory>
	  <providers>
		 <provider invariantName="System.Data.SQLite.EF6" type="System.Data.SQLite.EF6.SQLiteProviderServices, System.Data.SQLite.EF6" />
		 <provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer" />
	  </providers>
   </entityFramework>

   <system.web>
	  <membership defaultProvider="ClientAuthenticationMembershipProvider">
		 <providers>
			<add name="ClientAuthenticationMembershipProvider" type="System.Web.ClientServices.Providers.ClientFormsAuthenticationMembershipProvider, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" serviceUri="" />
		 </providers>
	  </membership>
	  <roleManager defaultProvider="ClientRoleProvider" enabled="true">
		 <providers>
			<add name="ClientRoleProvider" type="System.Web.ClientServices.Providers.ClientRoleProvider, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" serviceUri="" cacheTimeout="86400" />
		 </providers>
	  </roleManager>
   </system.web>

   <system.serviceModel>
	  <bindings>
		 <netNamedPipeBinding>
			<binding name="NetNamedPipeBinding_ISmartCardHelper" closeTimeout="00:01:00" openTimeout="00:01:00" receiveTimeout="00:01:00" sendTimeout="00:01:00" transactionFlow="false" transferMode="Buffered" transactionProtocol="OleTransactions" hostNameComparisonMode="StrongWildcard" maxBufferPoolSize="524288" maxBufferSize="65536" maxConnections="10" maxReceivedMessageSize="65536">
			   <readerQuotas maxDepth="32" maxStringContentLength="8192" maxArrayLength="16384" maxBytesPerRead="4096" maxNameTableCharCount="16384" />
			   <security mode="None">
				  <transport protectionLevel="EncryptAndSign" />
			   </security>
			</binding>
		 </netNamedPipeBinding>
	  </bindings>
	  <client>
		 <endpoint address="net.pipe://localhost/DVS.SmartCardHelperService.Api/service" binding="netNamedPipeBinding" bindingConfiguration="NetNamedPipeBinding_ISmartCardHelper" contract="SmartCardConnectedServiceProxy.ISmartCardHelper" name="NetNamedPipeBinding_ISmartCardHelper" />
	  </client>
   </system.serviceModel>
</configuration>
