using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Media;
using System.Windows.Shapes;
using WindowsInput;
using static Pollbook.OnScreenKeyboard.OnScreenKeyboard;

namespace Pollbook.OnScreenKeyboard.Keys
{
    public class Key : ToggleButton
	{
		private readonly OnScreenKeySpecifier[] _keyValues;

		public OnScreenKeyboard Keyboard;

		public Key(int row, int column, OnScreenKeySpecifier[] keyValues)
		{
			_keyValues = keyValues;
			SetLabel(keyValues[0].Label);

			GridRow = row;
			GridColumn = column;

			Click += KeyPressEventHandler;
		}

		// Most keys don't use the ToggleButton IsChecked for anything, so IsChecked is just set to false. They insert their current key
		// label into the current text field. But the ShiftLock/NumLock keys override this handler since they behave differently.
		internal virtual void KeyPressEventHandler(object sender, RoutedEventArgs routedEventArgs)
		{
			IsChecked = false;
			OnClick(new OnScreenKeyPressEventArgs(Execute));
		}

		internal void UpdateKeyLabels(KeyboardStateEnum keyboardState)
		{
			SetLabel(_keyValues[IndexFromState(keyboardState)].Label);
		}

		// Most keys need the index that corresponds to the current state; just cast the state enum to an int. The index is used to find
		// the label appropriate for the keyboard state. But keys that have only one label (Space, Backspace, Return) need to override this 
		// so it always returns 0.
		protected virtual int IndexFromState(KeyboardStateEnum keyboardState)
		{
			return (int)keyboardState;
		}

		protected void SetLabel(object keyLabel)
		{
			if (keyLabel is Path)
			{
				var viewbox = new Viewbox
				{
					Height = 50,
					Width = 50,
					Margin = new Thickness(-26, 0, 0, 0), // The SVG path contains absolute positions, we need to offset to center the image
					Stretch = Stretch.Uniform,
					HorizontalAlignment = HorizontalAlignment.Left,
					VerticalAlignment = VerticalAlignment.Top
				};
				Path path = (Path)keyLabel;
				if (path.Parent != null)
				{
					Viewbox parent = (Viewbox)path.Parent;
					parent.Child = null;
				}
				viewbox.Child = path;
				var intermediateGrid = new Grid();
				intermediateGrid.Children.Add(viewbox);
				intermediateGrid.HorizontalAlignment = HorizontalAlignment.Center;
				intermediateGrid.VerticalAlignment = VerticalAlignment.Center;
				Content = intermediateGrid;
			}
			else
			{
				Content = Value = keyLabel;
			}
		}

		internal event EventHandler<OnScreenKeyPressEventArgs> OnScreenKeyPressEvent;

		internal void OnClick(OnScreenKeyPressEventArgs arg)
		{
			OnScreenKeyPressEvent?.Invoke(this, arg);
		}

		public int GridRow
		{
			get { return (int)GetValue(Grid.RowProperty); }
			set { SetValue(Grid.RowProperty, value); }
		}

		public int GridColumn
		{
			get { return (int)GetValue(Grid.ColumnProperty); }
			set { SetValue(Grid.ColumnProperty, value); }
		}

		public GridLength GridWidth { get; set; }

		public object Value { get; protected set; }

		// Most keys just add their current key label into the text of the current element, and then simulate their keystroke. 
		// Return key overrides this with its own behavior.

		protected virtual void Execute(FrameworkElement frameworkElement)
		{
			var key = _keyValues[IndexFromState(Keyboard.KeyboardState)];
			bool shifted = key.Shift;

			// To accommodate machines with a physical keyboard that has Caps Lock pressed, reverse the shifted flag
			if (System.Windows.Forms.Control.IsKeyLocked(System.Windows.Forms.Keys.CapsLock) &&
				(Keyboard.KeyboardState == KeyboardStateEnum.Uppercase || Keyboard.KeyboardState == KeyboardStateEnum.Lowercase))
            {
				shifted = !shifted;
            }

			if (shifted)
			{
				InputSimulator.SimulateModifiedKeyStroke(VirtualKeyCode.SHIFT, key.KeyCode);
			}
			else
            {
				InputSimulator.SimulateKeyPress(key.KeyCode);
			}
		}
	}
}
