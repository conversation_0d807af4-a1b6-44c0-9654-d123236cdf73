using System.Windows;
using System.Windows.Media;
using System.Windows.Shapes;
using static Pollbook.OnScreenKeyboard.OnScreenKeyboard;

namespace Pollbook.OnScreenKeyboard.Keys
{
	public class ShiftLockKey : Key
	{
		private static readonly Geometry ShiftIcon = Geometry.Parse("M77.4683544,35.421213 L101.468354,61.4177498 L88.1683544,61.417213 L88.1688143,76.2032917 L66.8759246,76.2032917 L66.8753544,61.417213 L53.4683544,61.4177498 L77.4683544,35.421213 Z");
		private static readonly Path ShiftImage = new Path
		{
			Fill = new SolidColorBrush(Color.FromRgb(0xCF, 0xD7, 0xDE)),
			Data = ShiftIcon,
			Stroke = new SolidColorBrush(Color.FromRgb(0x15, 0x20, 0x25)),
			StrokeThickness = 4,
			HorizontalAlignment = HorizontalAlignment.Left,
			VerticalAlignment = VerticalAlignment.Top
		};

		private static readonly Geometry ShiftIconBlue = Geometry.Parse("M77.4683544,38.3696353 L58.0369062,59.4175669 L68.8752773,59.417133 L68.8758475,74.2032917 L86.1687521,74.2032917 L86.1682922,59.4171323 L96.8998013,59.4175654 L77.4683544,38.3696353 Z");
		private static readonly Path ShiftImageBlue = new Path
		{
			Fill = new SolidColorBrush(Color.FromRgb(0x00, 0x98, 0xCA)),
			Data = ShiftIconBlue,
			Stroke = new SolidColorBrush(Color.FromRgb(0x00, 0x98, 0xCA)), // This stroke that's the same color as the arrow makes this arrow have the same proportions as the one above.
			StrokeThickness = 4,
			HorizontalAlignment = HorizontalAlignment.Left,
			VerticalAlignment = VerticalAlignment.Top
		};

		public ShiftLockKey(int row, int column)
			: base(row, column, new [] {
				new OnScreenKeySpecifier(ShiftImageBlue, WindowsInput.VirtualKeyCode.RSHIFT),
				new OnScreenKeySpecifier(ShiftImage, WindowsInput.VirtualKeyCode.RSHIFT),
				new OnScreenKeySpecifier("#+=", WindowsInput.VirtualKeyCode.RSHIFT),
				new OnScreenKeySpecifier("123", WindowsInput.VirtualKeyCode.RSHIFT) })
		{
			Background = new SolidColorBrush(Color.FromRgb(207, 215, 222));
		}

		internal override void KeyPressEventHandler(object sender, RoutedEventArgs routedEventArgs)
		{
			OnClick(new OnScreenKeyPressEventArgs(new OnScreenKeyStateModifier(OnScreenKeyModifierType.Shift, IsChecked == false)));
		}
	}
}
