using System;
using System.Globalization;
using System.Windows.Data;

namespace Pollbook.UICore
{
    [ValueConversion(typeof(decimal), typeof(double))]
    public class PercentageConverter : IMultiValueConverter
    {
        public object Convert(object[] value, Type targetType, object parameter, CultureInfo culture)
        {
            decimal actualWidth;
            decimal percent;

            if (!decimal.TryParse(value[0].ToString(), out actualWidth)
                || !decimal.TryParse(value[1].ToString(), out percent))
            {
                return 0;
            }

            return (double)Math.Floor(actualWidth * percent);
        }

        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            return null;
        }
    }
}
