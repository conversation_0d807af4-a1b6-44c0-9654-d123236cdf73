using System.Linq;
using System.Windows;
using System.Windows.Input;

namespace Pollbook.UICore
{
    public class PBMaskedTextBox : KBMaskedTextBox
    {
        public string AdditionalCharacters
        {
            get => (string)GetValue(CharactersToAllowProperty);
            set => SetValue(CharactersToAllowProperty, value);
        }

        public static readonly DependencyProperty CharactersToAllowProperty =
            DependencyProperty.Register(nameof(AdditionalCharacters), typeof(string), typeof(PBMaskedTextBox),
                new FrameworkPropertyMetadata(string.Empty));
        
        protected override void OnTextInput(TextCompositionEventArgs e)
        {
            if (e.Text.Length > 0)
            {
                char key = e.Text[0];

                var additionalCharacters = AdditionalCharacters.ToCharArray();

                // Allow uppercase letters and digits, space, apostrophe, dash and period.
                if ((key >= 'A' && key <= 'Z') || (key >= 'a' && key <= 'z') || (key >= '0' && key <= '9') ||
                    key == ' ' || key == '\'' || key == '-' || key == '.'
                    || additionalCharacters.Contains(key))
                {
                    base.OnTextInput(e);
                    return;
                }

                // Otherwise, drop the key
                e.Handled = true;
            }
        }
    }
}
