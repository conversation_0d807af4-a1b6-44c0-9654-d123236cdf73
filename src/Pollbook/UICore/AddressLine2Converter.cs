using ESS.Pollbook;
using System;
using System.Globalization;
using System.Windows.Data;

namespace Pollbook.UICore
{
    public class AddressLine2Converter : IMultiValueConverter
    {
        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            try
            {
                var city = CaseConverter.ToUpperFirstLetter(values[0]?.ToString());
                var state = (values[1]?.ToString());
                var zip = values[2]?.ToString();
                return string.Format("{0}, {1} {2}", city, state, zip);
            }
            catch
            {
                return string.Empty;
            }
        }

        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            return null;
        }
    }
}
