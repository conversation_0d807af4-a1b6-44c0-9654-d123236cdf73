using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using System.Xml.Linq;
using ESS.Pollbook.Core.Interface.UICore;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.UICore;
using Pollbook.Interfaces;
using Pollbook.WorkFlow;

namespace Pollbook.UICore
{
    public class LoadConfigs : ILoadConfigs
    {
        private readonly IXmlHelper _xmlHelper;
        private readonly IEssLogger _logger;

        public LoadConfigs(IXmlHelper xmlHelper)
        {
            _xmlHelper = xmlHelper;
            _logger = new EssLogger();
        }

        public void LoadWorkFlowConfig(string filePath)
        {
            try
            {
                var doc = XElement.Load(filePath);

                var pageNavigationMapperCollection = new Dictionary<string, PageNavigationMapper>();
                foreach (var element in doc.Descendants("Page"))
                {
                    var data = _xmlHelper.DeserializeWorkFlowFile(element.ToString());
                    pageNavigationMapperCollection.Add($"{data.FromPage}/{data.ButtonName}", data);
                }
                WorkFlowConstants.PageNavigationMapperCollection = pageNavigationMapperCollection;
            }
            catch (Exception ex)
            {
                Dictionary<string, string> logProps = new Dictionary<string, string>();
                logProps.Add("Action", "Loading workflow from configuration");
                _logger.LogError(ex, logProps);
            }
        }

        public string GetExecutableDirectory()
        {
            //get path of the executing assembly
            return Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
        }
    }
}
