using System;
using System.Windows;
using System.Windows.Data;

namespace Pollbook.UICore
{
    public class ZeroCollapsedNonZeroVisible : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, System.Globalization.CultureInfo culture)
        {
            if (!int.TryParse(value.ToString(), out var intValue)) return Visibility.Visible;
            return intValue < 2 ? Visibility.Collapsed : Visibility.Visible;
        }

        public object ConvertBack(object value, Type targetType, object parameter, System.Globalization.CultureInfo culture)
        {
            return DependencyProperty.UnsetValue;
        }
    }
}
