using System.Windows.Input;

namespace Pollbook.UICore
{
    public class EmailMaskedTextBox : KBMaskedTextBox
    {
        protected override void OnTextInput(TextCompositionEventArgs e)
        {
            char key = e.Text[0];

            // Allow uppercase letters and digits, space, apostrophe, dash and period.
            if ((key >= 'A' && key <= 'Z') || (key >= 'a' && key <= 'z') || (key >= '0' && key <= '9') || key == '-' || key == '.' || key == '_' || key == '@')
            {
                base.OnTextInput(e);
                return;
            }

            // Otherwise, drop the key
            e.Handled = true;
        }
    }
}
