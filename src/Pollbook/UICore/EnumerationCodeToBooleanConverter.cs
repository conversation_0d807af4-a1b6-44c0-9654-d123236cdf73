using ESS.Pollbook.Core.Common;
using System;
using System.Globalization;
using System.Windows.Data;

namespace Pollbook.UICore
{
    public class EnumerationCodeToBooleanConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            //Enumeration Values that should be converted to true
            if (value is string str && targetType.IsAssignableFrom(typeof(bool)))
            {
                return str.Equals(nameof(ReasonsDisplayType.OtherText), StringComparison.CurrentCultureIgnoreCase);
            }

            return false;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            return null;
        }
    }
}
