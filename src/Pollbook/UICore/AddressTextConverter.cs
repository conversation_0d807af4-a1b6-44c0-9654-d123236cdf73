using System;
using System.Globalization;
using System.Text.RegularExpressions;
using System.Windows.Data;

namespace Pollbook.UICore
{
    public class AddressTextConverter : IMultiValueConverter
    {
        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            try
            {
                var searchText = values[0].ToString();
                var fullAddress = values[1].ToString();
                return GetNotHighlightedWord(searchText, fullAddress);
            }
            catch
            {
                return string.Empty;
            }
        }

        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            return null;
        }

        private string GetNotHighlightedWord(string searchTerm, string fullAddress)
        {
            searchTerm = ConvertToTitleCase(searchTerm);
            fullAddress = ConvertToTitleCase(fullAddress);

            Regex regex = new Regex(@"^\d+\s?");
            var res = regex.Replace(searchTerm, string.Empty, 1);

            Regex regex2 = new Regex(@"([0-9])*\-([0-9])*\s?" + res);

            return regex2.Replace(fullAddress, string.Empty);
        }

        private string ConvertToTitleCase(string text)
        {
            TextInfo textInfo = new CultureInfo("en-US", false).TextInfo;
            return textInfo.ToTitleCase(text.ToLower());
        }
    }
}
