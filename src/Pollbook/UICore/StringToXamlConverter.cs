using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Security;
using System.Text.RegularExpressions;
using System.Windows;
using System.Windows.Data;
using System.Windows.Markup;
using System.Xml;

namespace Pollbook.UICore
{

    [ValueConversion(typeof(string), typeof(object))]
    public class StringToXamlConverter : IMultiValueConverter
    {
        private static readonly string START_BRACKET = "|~S~|";
        private static readonly string END_BRACKET = "|~E~|";

        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            string input = "";
            List<string> keys = new List<string>();

            if (values[0] != null)
            {
                input = values[0].ToString();
            }

            if (values.Length == 2
                && values[1] != null
                && values[1] != DependencyProperty.UnsetValue)
            {
                keys.Add(values[1].ToString());
            }

            // Find first tag, and highlight
            string replaceKey = null;

            foreach (string key in keys)
            {
                // Starts with key
                var regex = new Regex("^" + Regex.Escape(key), RegexOptions.IgnoreCase);

                // Swap for formatted text
                replaceKey = regex.Match(input, 0)?.Value;

                if (!string.IsNullOrEmpty(replaceKey))
                {
                    input = regex.Replace(input, $"{START_BRACKET}{replaceKey}{END_BRACKET}", 1);

                    break;
                }
            }

            if (input == null || input == "{DisconnectedItem}")
            {
                return null;
            }

            // Escape incoming text
            string escapedXml = SecurityElement.Escape(input).Replace(" ", "&#160;");

            // Inject highlighted text
            string withTags = escapedXml.Replace(START_BRACKET, "</Run><Run Style=\"{DynamicResource highlight}\">").Replace(END_BRACKET, "</Run><Run Style=\"{DynamicResource normal}\">");
            string wrappedInput = $"<TextBlock xmlns=\"http://schemas.microsoft.com/winfx/2006/xaml/presentation\"><Run Style=\"{{DynamicResource normal}}\">{withTags}</Run></TextBlock>";

            // Remove empty Runs
            wrappedInput = wrappedInput.Replace("<Run Style=\"{DynamicResource normal}\"></Run>", "");

            using (StringReader stringReader = new StringReader(wrappedInput))
            using (XmlReader xmlReader = XmlReader.Create(stringReader))
            {
                return XamlReader.Load(xmlReader);
            }
        }

        public object[] ConvertBack(object value, Type[] targetType, object parameter, CultureInfo culture)
        {
            return null;
        }
    }
}
