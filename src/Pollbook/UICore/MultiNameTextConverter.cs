using ESS.Pollbook;
using System;
using System.Globalization;
using System.Windows.Data;

namespace Pollbook.UICore
{
    public class MultiNameTextConverter : IMultiValueConverter
    {
        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            try
            {
                var firstName = CaseConverter.ToUpperFirstLetter(values[0]?.ToString());
                var middleName = CaseConverter.ToUpperFirstLetter(values[1]?.ToString()); ;
                var lastName = CaseConverter.ToUpperFirstLetter(values[2]?.ToString()); ;
                return string.IsNullOrWhiteSpace(middleName) ? string.Format("{0} {1}", firstName, lastName) : string.Format("{0} {1} {2}", firstName, middleName, lastName);
            }
            catch
            {
                return string.Empty;
            }
        }

        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            return null;
        }
    }
}
