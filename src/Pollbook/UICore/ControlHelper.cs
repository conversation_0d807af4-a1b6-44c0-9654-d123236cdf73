using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;

namespace Pollbook.UICore
{
   public static class ControlHelper
    {
        // The setting of this static should probably be protected with a lock.
        private static Point? _lastTapLocation;

        private static int _lastTimestamp = 0;

        /// <summary>
        /// The disable double click property
        /// </summary>
        public static readonly DependencyProperty DisableDoubleClickProperty =
            DependencyProperty.RegisterAttached("DisableDoubleClick", typeof(bool), typeof(ControlHelper), new FrameworkPropertyMetadata(false, OnDisableDoubleClickChanged));

        /// <summary>
        /// Sets the disable double click.
        /// </summary>
        /// <param name="element">The element.</param>
        /// <param name="value">if set to <c>true</c> [value].</param>
        public static void SetDisableDoubleClick(UIElement element, bool value)
        {
            element.SetValue(DisableDoubleClickProperty, value);
        }

        /// <summary>
        /// Gets the disable double click.
        /// </summary>
        /// <param name="element">The element.</param>
        /// <returns></returns>
        public static bool GetDisableDoubleClick(UIElement element)
        {
            return (bool)element.GetValue(DisableDoubleClickProperty);
        }

        /// <summary>
        /// Called when [disable double click changed].
        /// </summary>
        /// <param name="d">The d.</param>
        /// <param name="e">The <see cref="DependencyPropertyChangedEventArgs"/> instance containing the event data.</param>
        private static void OnDisableDoubleClickChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            var control = (Control)d;
            if ((bool)e.NewValue)
            {
                control.PreviewMouseDown -= HandleMouseDoubleClick;
                control.PreviewMouseDown += HandleMouseDoubleClick;

                control.TouchDown -= HandleDoubleTouch;
                control.TouchDown += HandleDoubleTouch;
            }
        }

        private static void HandleMouseDoubleClick(object sender, MouseButtonEventArgs args)
        {
            if (args.ClickCount > 1)
            {
                args.Handled = true;
            }
        }

        private static void HandleDoubleTouch(object sender, TouchEventArgs args)
        {
            var control = (IInputElement)sender;
            if (IsDoubleTap(args, control))
            {
                args.Handled = true;
            }
        }

        private static bool IsDoubleTap(TouchEventArgs args, IInputElement iInputElement)
        {
            Point currentTapPosition = args.GetTouchPoint(iInputElement).Position;
            bool tapsAreCloseInDistance = false;
            if (_lastTapLocation != null)
            {
                tapsAreCloseInDistance = GetDistanceBetweenPoints(currentTapPosition, (Point)_lastTapLocation) < 70;
            }
            _lastTapLocation = currentTapPosition;

            // This replaces the previous TimeSpan calculation
            bool tapsAreCloseInTime = args.Timestamp - _lastTimestamp < 700;

            if (tapsAreCloseInTime && tapsAreCloseInDistance)
            {
                _lastTapLocation = null;
            }

            _lastTimestamp = args.Timestamp;

            return tapsAreCloseInDistance && tapsAreCloseInTime;
        }

        private static double GetDistanceBetweenPoints(Point a, Point b)
        {
            return Math.Abs(Point.Subtract(a, b).Length);
        }
    }
}
