using System;
using System.ComponentModel;
using System.Reflection;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Threading;

namespace Pollbook
{
    public static class Extensions
    {
        public static string ToDescription<TEnum>(this TEnum value)
        {
            if (value == null)
            {
                return string.Empty;
            }

            FieldInfo fi = value.GetType().GetField(value.ToString());
            DescriptionAttribute[] attributes = (DescriptionAttribute[])fi.GetCustomAttributes(typeof(DescriptionAttribute), false);

            return attributes.Length > 0 ? attributes[0].Description : value.ToString();
        }

        public static T FindParent<T>(this DependencyObject child) where T : DependencyObject
        {
            if (child == null)
            {
                return null;
            }

            //get parent item
            DependencyObject parentObject = VisualTreeHelper.GetParent(child);

            //we've reached the end of the tree
            if (parentObject == null) return null;

            //check if the parent matches the type we're looking for
            T parent = parentObject as T;
            if (parent != null)
                return parent;
            else
                return FindParent<T>(parentObject);
        }

        public static void ScrollToTopOffset(this ScrollViewer scrollViewer, object item, double offset = 0)
        {
            // Scroll immediately if possible
            if (!scrollViewer.TryScrollToTopOffset(item, offset))
            {
                // Otherwise wait until everything is loaded, then scroll
                scrollViewer.Dispatcher.BeginInvoke(DispatcherPriority.Loaded, new Action(() =>
                {
                    scrollViewer.TryScrollToTopOffset(item, offset);
                }));
            }
        }

        private static bool TryScrollToTopOffset(this ScrollViewer scrollViewer, object item, double offset)
        {
            if (scrollViewer == null) return false;
            if (item == null || !(item is UIElement)) return false;

            try
            {
                UIElement ue = (UIElement)item;

                var itemTransform = ue.TransformToAncestor(scrollViewer).Transform(new Point(0, 0));

                scrollViewer.ScrollToVerticalOffset(itemTransform.Y + scrollViewer.VerticalOffset - offset);

                return true;
            }
            catch
            {
                return false;
            }
        }

        public static T GetFirstChildOfType<T>(this DependencyObject dependencyObject) where T : DependencyObject
        {
            if (dependencyObject == null)
            {
                return null;
            }

            for (var i = 0; i < VisualTreeHelper.GetChildrenCount(dependencyObject); i++)
            {
                var child = VisualTreeHelper.GetChild(dependencyObject, i);

                var result = (child as T) ?? GetFirstChildOfType<T>(child);

                if (result != null)
                {
                    return result;
                }
            }

            return null;
        }
    }
}
