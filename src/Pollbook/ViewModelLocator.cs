using Autofac;
using Autofac.Extras.CommonServiceLocator;
using CommonServiceLocator;
using ESS.Pollbook.ViewModel;
using ESS.Pollbook.ViewModel.Affidavit;
using ESS.Pollbook.ViewModel.Checklist;
using ESS.Pollbook.ViewModel.HelpCenter;
using ESS.Pollbook.ViewModel.IncrementalUpdates;
using ESS.Pollbook.ViewModel.InstantMessage;
using ESS.Pollbook.ViewModel.Maintenance;
using ESS.Pollbook.ViewModel.Password;
using ESS.Pollbook.ViewModel.PollPlaceDetails;
using ESS.Pollbook.ViewModel.PollPlaceSearch;
using ESS.Pollbook.ViewModel.Pollworker;
using ESS.Pollbook.ViewModel.Pollworker.Search;
using ESS.Pollbook.ViewModel.Pollworker.TimeCard;
using ESS.Pollbook.ViewModel.RegionalResults;
using ESS.Pollbook.ViewModel.Reports;
using ESS.Pollbook.ViewModel.SelectBallotType;
using ESS.Pollbook.ViewModel.SelectParty;
using ESS.Pollbook.ViewModel.SelectProvisionalReasonAndId;
using ESS.Pollbook.ViewModel.SelectReissueBallotReason;
using ESS.Pollbook.ViewModel.Supervisor;
using ESS.Pollbook.ViewModel.SurrenderMailInBallot;
using ESS.Pollbook.ViewModel.SystemControl;
using ESS.Pollbook.ViewModel.SystemStats;
using ESS.Pollbook.ViewModel.Utils;
using ESS.Pollbook.ViewModel.VerifyVoterId;
using ESS.Pollbook.ViewModel.Voter;
using ESS.Pollbook.ViewModel.VoterBallot;
using ESS.Pollbook.ViewModel.VoterDisabilityOath;
using ESS.Pollbook.ViewModel.VoterExport;
using ESS.Pollbook.ViewModel.VoterSearch;
using ESS.Pollbook.ViewModel.VoterSignature;
using ESS.Pollbook.ViewModel.VoterVerification;
using ESS.Pollbook.ViewModel.WaitTime;

namespace Pollbook
{
    /// <summary>
    /// This class contains static references to all the view models in the
    /// application and provides an entry point for the bindings.
    /// </summary>
    public class ViewModelLocator
    {

        static ViewModelLocator()
        {
            if (!ServiceLocator.IsLocationProviderSet)
            {
                RegisterServices(registerFakes: true);
            }
        }

        public static void RegisterServices(ContainerBuilder registrations = null, bool registerFakes = false)
        {
            var builder = new ContainerBuilder();

            builder.RegisterModule<GlobalModule>();
            builder.RegisterModule<NavigationModule>();
            builder.RegisterModule<MessengerModule>();
            builder.RegisterModule<KeyboardModule>();

            var container = builder.Build();

            ServiceLocator.SetLocatorProvider(() => new AutofacServiceLocator(container));
        }

        public MainWindowViewModel MainWindowView => ServiceLocator.Current.GetInstance<MainWindowViewModel>();

        public AdvancedVoterSearchViewModel AdvancedVoterSearch =>
            ServiceLocator.Current.GetInstance<AdvancedVoterSearchViewModel>();

        public BatteryWarningViewModel BatteryWarning => ServiceLocator.Current.GetInstance<BatteryWarningViewModel>();

        public PollPlaceSearchViewModel PollPlaceSearch => ServiceLocator.Current.GetInstance<PollPlaceSearchViewModel>();

        public ChangePollPlaceSearchViewModel ChangePollPlaceSearch => ServiceLocator.Current.GetInstance<ChangePollPlaceSearchViewModel>();

        public WrongPollLocationViewModel WrongPollLocation => ServiceLocator.Current.GetInstance<WrongPollLocationViewModel>();

        public VoterViewModel Voter => ServiceLocator.Current.GetInstance<VoterViewModel>();

        public LoginViewModel Login => ServiceLocator.Current.GetInstance<LoginViewModel>();

        public LaunchViewModel Launch => ServiceLocator.Current.GetInstance<LaunchViewModel>();

        public DashboardViewModel Dashboard => ServiceLocator.Current.GetInstance<DashboardViewModel>();

        public VoterIssueBallotViewModel VoterIssueBallot => ServiceLocator.Current.GetInstance<VoterIssueBallotViewModel>();

        public VoterCheckInCompleteViewModel VoterCheckInComplete => ServiceLocator.Current.GetInstance<VoterCheckInCompleteViewModel>();

        public PrintingReceiptViewModel PrintingReceipt => ServiceLocator.Current.GetInstance<PrintingReceiptViewModel>();

        public PrintAuthDocFailedViewModel PrintAuthDocFailed => ServiceLocator.Current.GetInstance<PrintAuthDocFailedViewModel>();

        public PrintBallotTotalsFailedViewModel PrintBallotTotalsFailed => ServiceLocator.Current.GetInstance<PrintBallotTotalsFailedViewModel>();

        public PrintSpoiledBallotFailedViewModel PrintSpoiledBallotFailed => ServiceLocator.Current.GetInstance<PrintSpoiledBallotFailedViewModel>();

        public PrintVotedListFailedViewModel PrintVotedListFailed => ServiceLocator.Current.GetInstance<PrintVotedListFailedViewModel>();

        public PrintBallotReissuedListFailedViewModel PrintBallotReissuedListFailed =>
            ServiceLocator.Current.GetInstance<PrintBallotReissuedListFailedViewModel>();

        public PrintPollPlaceDetailsFailedViewModel PrintPollPlaceDetailsFailed => ServiceLocator.Current.GetInstance<PrintPollPlaceDetailsFailedViewModel>();

        public VoterSignatureViewModel VoterSignature => ServiceLocator.Current.GetInstance<VoterSignatureViewModel>();

        public SelectSkipSignatureReasonViewModel SelectSkipSignatureReason => ServiceLocator.Current.GetInstance<SelectSkipSignatureReasonViewModel>();

        public PollPlaceDetailsViewModel PollPlaceDetails => ServiceLocator.Current.GetInstance<PollPlaceDetailsViewModel>();

        public AlertsAndConversationsViewModel AlertsAndConversations => ServiceLocator.Current.GetInstance<AlertsAndConversationsViewModel>();

        public AlertViewModel Alert => ServiceLocator.Current.GetInstance<AlertViewModel>();

        public GenericAlertViewModel GenericAlert => ServiceLocator.Current.GetInstance<GenericAlertViewModel>();

        public AlertDetailViewModel AlertDetail => ServiceLocator.Current.GetInstance<AlertDetailViewModel>();

        public ComposeViewModel Compose => ServiceLocator.Current.GetInstance<ComposeViewModel>();

        public ConversationViewModel Conversation => ServiceLocator.Current.GetInstance<ConversationViewModel>();

        public MonitorPollViewModel MonitorPoll => ServiceLocator.Current.GetInstance<MonitorPollViewModel>();

        public FullVotedListReportViewModel FullVotedListReport => ServiceLocator.Current.GetInstance<FullVotedListReportViewModel>();

        public BallotTotalsReportViewModel BallotTotalsReport => ServiceLocator.Current.GetInstance<BallotTotalsReportViewModel>();

        public BallotReissuedReportViewModel BallotReIssuedReport => ServiceLocator.Current.GetInstance<BallotReissuedReportViewModel>();

        public SpoiledBallotReportViewModel SpoiledBallotReport => ServiceLocator.Current.GetInstance<SpoiledBallotReportViewModel>();

        public SignatureComparisonViewModel SigComparisionViewModel => ServiceLocator.Current.GetInstance<SignatureComparisonViewModel>();

        public SelectPartyViewModel SelectParty => ServiceLocator.Current.GetInstance<SelectPartyViewModel>();

        public SystemStatsViewModel SystemStats => ServiceLocator.Current.GetInstance<SystemStatsViewModel>();

        public CancelBallotViewModel CancelBallot => ServiceLocator.Current.GetInstance<CancelBallotViewModel>();

        public SignOutViewModel SignOut => ServiceLocator.Current.GetInstance<SignOutViewModel>();

        public DualLogoutViewModel DualLogout => ServiceLocator.Current.GetInstance<DualLogoutViewModel>();

        public ShutDownViewModel ShutDown => ServiceLocator.Current.GetInstance<ShutDownViewModel>();

        public AddressChangedViewModel AddressChanged => ServiceLocator.Current.GetInstance<AddressChangedViewModel>();

        public LoadElectionConfirmationViewModel LoadElectionConfirmation => ServiceLocator.Current.GetInstance<LoadElectionConfirmationViewModel>();

        public SendTextViewModel SendText => ServiceLocator.Current.GetInstance<SendTextViewModel>();

        public SendTextErrorViewModel SendTextError => ServiceLocator.Current.GetInstance<SendTextErrorViewModel>();

        public MaintenanceViewModel Maintenance => ServiceLocator.Current.GetInstance<MaintenanceViewModel>();

        public ElectionLoadedViewModel ElectionLoaded => ServiceLocator.Current.GetInstance<ElectionLoadedViewModel>();

        public ElectionLoadingViewModel ElectionLoading => ServiceLocator.Current.GetInstance<ElectionLoadingViewModel>();

        public ElectionLoadingFailedViewModel ElectionLoadingFailed => ServiceLocator.Current.GetInstance<ElectionLoadingFailedViewModel>();

        public BackupTransactionsLoadingViewModel BackupTransactionsLoading => ServiceLocator.Current
            .GetInstance<BackupTransactionsLoadingViewModel>();

        public BackupTransactionsLoadedViewModel BackupTransactionsLoaded => ServiceLocator.Current
            .GetInstance<BackupTransactionsLoadedViewModel>();

        public FailedViewModel FailedViewModel => ServiceLocator.Current
           .GetInstance<FailedViewModel>();

        public SelectTaskTypeViewModel SelectTaskTypeViewModel => ServiceLocator.Current.GetInstance<SelectTaskTypeViewModel>();

        public ManageElectionViewModel ManageElection => ServiceLocator.Current.GetInstance<ManageElectionViewModel>();

        public TransactionsClearedViewModel TransactionsCleared => ServiceLocator.Current.GetInstance<TransactionsClearedViewModel>();

        public VerifySoftwareViewModel VerifySoftware => ServiceLocator.Current.GetInstance<VerifySoftwareViewModel>();

        public TransactionsClearingViewModel TransactionsClearing => ServiceLocator.Current.GetInstance<TransactionsClearingViewModel>();

        public TransactionsClearedFailedViewModel TransactionsClearedFailed => ServiceLocator.Current.GetInstance<TransactionsClearedFailedViewModel>();

        public UploadingTransactionsViewModel UploadingTransactions => ServiceLocator.Current.GetInstance<UploadingTransactionsViewModel>();

        public UploadedTransactionsViewModel UploadedTransactions => ServiceLocator.Current.GetInstance<UploadedTransactionsViewModel>();

        public SelectBallotTypeViewModel SelectBallotTypeViewModel => ServiceLocator.Current.GetInstance<SelectBallotTypeViewModel>();

        public ExpressVoteActivationCardViewModel ExpressVoteActivationCardViewModel => ServiceLocator.Current.GetInstance<ExpressVoteActivationCardViewModel>();

        public TransactionsClearConfirmationViewModel TransactionsClearConfirmation => ServiceLocator.Current.GetInstance<TransactionsClearConfirmationViewModel>();

        public ConfigureElectionViewModel ConfigureElection => ServiceLocator.Current.GetInstance<ConfigureElectionViewModel>();

        public ConfigureElectionLandingViewModel ConfigureElectionLanding => ServiceLocator.Current.GetInstance<ConfigureElectionLandingViewModel>();

        public ConfigurePrintingViewModel ConfigurePrinting => ServiceLocator.Current.GetInstance<ConfigurePrintingViewModel>();

        public VoterBallotDetailsViewModel VoterBallotDetails => ServiceLocator.Current.GetInstance<VoterBallotDetailsViewModel>();

        public LoadingViewModel Loading => ServiceLocator.Current.GetInstance<LoadingViewModel>();

        public SelectReissueBallotReasonViewModel ReissueBallotReason => ServiceLocator.Current.GetInstance<SelectReissueBallotReasonViewModel>();

        public SelectProvisionalReasonAndIdViewModel ProvisionalReasonAndId => ServiceLocator.Current.GetInstance<SelectProvisionalReasonAndIdViewModel>();

        public ManageVoterViewModel ManageVoter => ServiceLocator.Current.GetInstance<ManageVoterViewModel>();

        public SupervisorPwEntryViewModel SupervisorPWEntry => ServiceLocator.Current.GetInstance<SupervisorPwEntryViewModel>();

        public BallotCancelDetailsViewModel BallotCancelDetails => ServiceLocator.Current.GetInstance<BallotCancelDetailsViewModel>();

        public VoterEditDetailsViewModel VoterEditDetails => ServiceLocator.Current.GetInstance<VoterEditDetailsViewModel>();

        public VoterAddDetailsViewModel VoterAddDetails => ServiceLocator.Current.GetInstance<VoterAddDetailsViewModel>();

        public PasswordViewModel Password => ServiceLocator.Current.GetInstance<PasswordViewModel>();

        public IncrementalUpdatesLoadingViewModel IncrementalUpdatesLoading => ServiceLocator.Current.GetInstance<IncrementalUpdatesLoadingViewModel>();

        public IncrementalUpdatesLoadedViewModel IncrementalUpdatesLoaded => ServiceLocator.Current.GetInstance<IncrementalUpdatesLoadedViewModel>();

        public UpgradeDeviceViewModel UpgradeDevice => ServiceLocator.Current.GetInstance<UpgradeDeviceViewModel>();

        public IncrementalUpdatesFailedViewModel IncrementalUpdatesFailed => ServiceLocator.Current.GetInstance<IncrementalUpdatesFailedViewModel>();

        public OptionsViewModel Options => ServiceLocator.Current.GetInstance<OptionsViewModel>();

        public WaitTimeViewModel WaitTime => ServiceLocator.Current.GetInstance<WaitTimeViewModel>();

        public CurrentWaitTimeTokenViewModel CurrentWaitTimeToken => ServiceLocator.Current.GetInstance<CurrentWaitTimeTokenViewModel>();

        public CancelWaitTimeTokenViewModel CancelWaitTimeToken => ServiceLocator.Current.GetInstance<CancelWaitTimeTokenViewModel>();

        public CancelWaitTimeSucceededViewModel CancelWaitTimeTokenSucceeded => ServiceLocator.Current.GetInstance<CancelWaitTimeSucceededViewModel>();

        public DisplayWaitTimeTokenViewModel PrintWaitTimeToken => ServiceLocator.Current.GetInstance<DisplayWaitTimeTokenViewModel>();

        public PrintWaitTimeTokenSucceededViewModel PrintWaitTimeTokenSucceeded => ServiceLocator.Current.GetInstance<PrintWaitTimeTokenSucceededViewModel>();

        public PrintWaitTimeTokenFailedViewModel PrintWaitTimeTokenFailed => ServiceLocator.Current.GetInstance<PrintWaitTimeTokenFailedViewModel>();

        public EnterWaitTimeTokenViewModel EnterWaitTimeToken => ServiceLocator.Current.GetInstance<EnterWaitTimeTokenViewModel>();

        public DisplayWaitTimeViewModel DisplayWaitTime => ServiceLocator.Current.GetInstance<DisplayWaitTimeViewModel>();

        public ElectionNotFoundViewModel ElectionNotFound => ServiceLocator.Current.GetInstance<ElectionNotFoundViewModel>();

        public VoterExportLoadingViewModel VoterExportLoading => ServiceLocator.Current.GetInstance<VoterExportLoadingViewModel>();

        public VoterExportLoadedViewModel VoterExportLoaded => ServiceLocator.Current.GetInstance<VoterExportLoadedViewModel>();

        public VoterExportFailedViewModel VoterExportFailed => ServiceLocator.Current.GetInstance<VoterExportFailedViewModel>();

        public BalotarViewModel BalotarViewModel => ServiceLocator.Current.GetInstance<BalotarViewModel>();

        public PQCViewModel PQC => ServiceLocator.Current.GetInstance<PQCViewModel>();

        public RestartSystemViewModel RestartSystem => ServiceLocator.Current.GetInstance<RestartSystemViewModel>();

        public SdCardCopyingViewModel SdCardCopying => ServiceLocator.Current.GetInstance<SdCardCopyingViewModel>();

        public VoterEditViewModel VoterEdit => ServiceLocator.Current.GetInstance<VoterEditViewModel>();

        public VoterAddViewModel VoterAdd => ServiceLocator.Current.GetInstance<VoterAddViewModel>();

        public VoterVerificationViewModel VoterVerification => ServiceLocator.Current.GetInstance<VoterVerificationViewModel>();

        public PollingPlaceVoterListReportViewModel PollingPlaceVoterListReport => ServiceLocator.Current.GetInstance<PollingPlaceVoterListReportViewModel>();

        public PrintPollingPlaceVoterListFailedViewModel PrintPollingPlaceVoterListFailed => ServiceLocator.Current.GetInstance<PrintPollingPlaceVoterListFailedViewModel>();

        public VerifyVoterIdListAViewModel VerifyVoterIdListA => ServiceLocator.Current.GetInstance<VerifyVoterIdListAViewModel>();

        public VerifyVoterIdListBViewModel VerifyVoterIdListB => ServiceLocator.Current.GetInstance<VerifyVoterIdListBViewModel>();

        public VerifyVoterIdReasonableImpedimentDeclarationViewModel VerifyVoterIdReasonableImpedimentDeclaration => ServiceLocator.Current.GetInstance<VerifyVoterIdReasonableImpedimentDeclarationViewModel>();

        public VerifyVoterIdViewModel VerifyVoterId => ServiceLocator.Current.GetInstance<VerifyVoterIdViewModel>();

        public VoterVerificationsConfirmationViewModel VoterVerificationsConfirmation => ServiceLocator.Current.GetInstance<VoterVerificationsConfirmationViewModel>();

        public VerifyVoterIdSignatureViewModel VerifyVoterIdSignature => ServiceLocator.Current.GetInstance<VerifyVoterIdSignatureViewModel>();

        public VoterCannotBeEditedViewModel VoterCannotBeEdited => ServiceLocator.Current.GetInstance<VoterCannotBeEditedViewModel>();

        public VoterReasonableImpedimentViewModel VoterReasonableImpediment => ServiceLocator.Current.GetInstance<VoterReasonableImpedimentViewModel>();

        public VoterReasonableImpedimentReasonsViewModel VoterReasonableImpedimentReasons => ServiceLocator.Current.GetInstance<VoterReasonableImpedimentReasonsViewModel>();

        public AffidavitTemplateViewModel AffidavitTemplate => ServiceLocator.Current.GetInstance<AffidavitTemplateViewModel>();

        public AffidavitCaptureNameAddressViewModel AffidavitCaptureNameAddress => ServiceLocator.Current.GetInstance<AffidavitCaptureNameAddressViewModel>();

        public AffidavitConfirmationViewModel AffidavitConfirmation => ServiceLocator.Current.GetInstance<AffidavitConfirmationViewModel>();

        public VoterNameVerificationViewModel VoterNameVerification => ServiceLocator.Current.GetInstance<VoterNameVerificationViewModel>();

        public PrintingAffidavitReportViewModel PrintingAffidavitReport => ServiceLocator.Current.GetInstance<PrintingAffidavitReportViewModel>();

        public PrintingAffidavitReportFailedViewModel PrintingAffidavitReportFailed => ServiceLocator.Current.GetInstance<PrintingAffidavitReportFailedViewModel>();

        public PartyAffiliationAffidavitViewModel PartyAffiliationAffidavit => ServiceLocator.Current.GetInstance<PartyAffiliationAffidavitViewModel>();

        public AffirmationOfResidenceAffidavitViewModel AffirmationOfResidenceAffidavit => ServiceLocator.Current.GetInstance<AffirmationOfResidenceAffidavitViewModel>();

        public DeviceLockedViewModel DeviceLocked => ServiceLocator.Current.GetInstance<DeviceLockedViewModel>();

        public WifiKeyEntryViewModel WifiKeyEntry => ServiceLocator.Current.GetInstance<WifiKeyEntryViewModel>();

        public WifiMaintenanceViewModel WifiMaintenance => ServiceLocator.Current.GetInstance<WifiMaintenanceViewModel>();

        public WifiNetworkConfigurationViewModel WifiNetworkConfiguration => ServiceLocator.Current.GetInstance<WifiNetworkConfigurationViewModel>();

        public WifiNetworkPropertiesViewModel WifiNetworkProperties => ServiceLocator.Current.GetInstance<WifiNetworkPropertiesViewModel>();

        public WifiConnectionErrorViewModel WifiConnectionError => ServiceLocator.Current.GetInstance<WifiConnectionErrorViewModel>();

        public PollworkerManagementViewModel PollworkerManagement => ServiceLocator.Current.GetInstance<PollworkerManagementViewModel>();

        public PollworkerActionConfirmationViewModel PollworkerActionConfirmation => ServiceLocator.Current.GetInstance<PollworkerActionConfirmationViewModel>();

        public PollworkerAddEditViewModel PollworkerAddEdit => ServiceLocator.Current.GetInstance<PollworkerAddEditViewModel>();

        public PollworkerClockOutReasonViewModel PollworkerClockOutReason => ServiceLocator.Current.GetInstance<PollworkerClockOutReasonViewModel>();

        public PollworkerDetailViewModel PollworkerDetail => ServiceLocator.Current.GetInstance<PollworkerDetailViewModel>();

        public PollworkerJobConfirmationViewModel PollworkerJobConfirmation => ServiceLocator.Current.GetInstance<PollworkerJobConfirmationViewModel>();

        public PollworkerOathViewModel PollworkerOath => ServiceLocator.Current.GetInstance<PollworkerOathViewModel>();

        public PollworkerSearchViewModel PollworkerSearch => ServiceLocator.Current.GetInstance<PollworkerSearchViewModel>();

        public PollworkerTimeHistoryDetailsViewModel PollworkerTimeHistoryDetails => ServiceLocator.Current.GetInstance<PollworkerTimeHistoryDetailsViewModel>();

        public PollworkerAddEditTimeViewModel PollworkerAddEditTime => ServiceLocator.Current.GetInstance<PollworkerAddEditTimeViewModel>();

        public PollworkerApprovalViewModel PollworkerApproval => ServiceLocator.Current.GetInstance<PollworkerApprovalViewModel>();

        public PollworkerManageTimeViewModel PollworkerManageTime => ServiceLocator.Current.GetInstance<PollworkerManageTimeViewModel>();

        public PollworkerViewModel Pollworker => ServiceLocator.Current.GetInstance<PollworkerViewModel>();

        public PrintingBallotViewModel PrintingBallot => ServiceLocator.Current.GetInstance<PrintingBallotViewModel>();

        public PrintBallotFailedViewModel PrintBallotFailed => ServiceLocator.Current.GetInstance<PrintBallotFailedViewModel>();

        public SurrenderMailInBallotViewModel SurrenderMailInBallot => ServiceLocator.Current.GetInstance<SurrenderMailInBallotViewModel>();

        public SurrenderMailInBallotResponseViewModel SurrenderMailInBallotResponse => ServiceLocator.Current.GetInstance<SurrenderMailInBallotResponseViewModel>();

        public HelpCenterViewModel HelpCenter => ServiceLocator.Current.GetInstance<HelpCenterViewModel>();

        public HelpCenterContentViewModel HelpCenterContent => ServiceLocator.Current.GetInstance<HelpCenterContentViewModel>();

        public VerifyVoterIdExemptViewModel VerifyVoterIdExempt => ServiceLocator.Current.GetInstance<VerifyVoterIdExemptViewModel>();

        public VerifyVoterRegistrationForNotInRosterViewModel VerifyVoterRegistrationForNotInRoster => ServiceLocator.Current.GetInstance<VerifyVoterRegistrationForNotInRosterViewModel>();

        public LanguagesViewModel Languages => ServiceLocator.Current.GetInstance<LanguagesViewModel>();

        public ApplyingConfigurationUpdatesViewModel ApplyingConfigurationUpdates => ServiceLocator.Current.GetInstance<ApplyingConfigurationUpdatesViewModel>();

        public ApplyingConfigurationUpdatesFailedViewModel ApplyingConfigurationUpdatesFailed => ServiceLocator.Current.GetInstance<ApplyingConfigurationUpdatesFailedViewModel>();

        public ApplyingConfigurationUpdatesDoneViewModel ApplyingConfigurationUpdatesDone => ServiceLocator.Current.GetInstance<ApplyingConfigurationUpdatesDoneViewModel>();

        public ConfigurationUpdateAlertViewModal ConfigurationUpdateAlert => ServiceLocator.Current.GetInstance<ConfigurationUpdateAlertViewModal>();

        public ActivationCardViewModel ActivationCard => ServiceLocator.Current.GetInstance<ActivationCardViewModel>();

        public NotInRosterReasonsViewModel NotInRosterReasons => ServiceLocator.Current.GetInstance<NotInRosterReasonsViewModel>();

        public VoterDisabilityOathSignatureViewModel VoterDisabilityOathSignature => ServiceLocator.Current.GetInstance<VoterDisabilityOathSignatureViewModel>();

        public SignatureCanvasViewModel SignatureCanvasVM => ServiceLocator.Current.GetInstance<SignatureCanvasViewModel>();

        public RegionalResultsPollPlaceSearchViewModel RegionalResultsPollPlaceSearch => ServiceLocator.Current.GetInstance<RegionalResultsPollPlaceSearchViewModel>();

        public RegionalResultsPasswordViewModel RegionalResultsPassword => ServiceLocator.Current.GetInstance<RegionalResultsPasswordViewModel>();

        public RegionalResultsErrorViewModel RegionalResultsError => ServiceLocator.Current.GetInstance<RegionalResultsErrorViewModel>();
        public RegionalResultsTransmitResultsViewModel RegionalResultsTransmitResults => ServiceLocator.Current.GetInstance<RegionalResultsTransmitResultsViewModel>();
        public RegionalResultsLoadingViewModel RegionalResultsLoading => ServiceLocator.Current.GetInstance<RegionalResultsLoadingViewModel>();
        public ChecklistViewModel Checklist => ServiceLocator.Current.GetInstance<ChecklistViewModel>();

        public ManageDeviceViewModel ManageDevice => ServiceLocator.Current.GetInstance<ManageDeviceViewModel>();

        public ManageDeviceLoginViewModel ManageDeviceLogin => ServiceLocator.Current.GetInstance<ManageDeviceLoginViewModel>();

        public CaptureIdVoterConfirmationViewModel CaptureIdConfirmation =>
	        ServiceLocator.Current.GetInstance<CaptureIdVoterConfirmationViewModel>();

        public CaptureIdVoterVerificationViewModel CaptureIdVoterVerification =>
	        ServiceLocator.Current.GetInstance<CaptureIdVoterVerificationViewModel>();

        public CaptureIdVoterPresentFormViewModel CaptureIdVoterPresentForm =>
	        ServiceLocator.Current.GetInstance<CaptureIdVoterPresentFormViewModel>();
        public PrintAddressChangedDocFailedViewModel PrintAddressChangedDocFailed => ServiceLocator.Current.GetInstance<PrintAddressChangedDocFailedViewModel>();

        public GenericConfirmationViewModel GenericConfirmation =>
           ServiceLocator.Current.GetInstance<GenericConfirmationViewModel>();

        public static void Cleanup()
        {
            // TODO Clear the ViewModels
        }
    }
}
