<Page x:Class="Pollbook.Pages.Pollworker.TimeCard.PollworkerView"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:i="clr-namespace:System.Windows.Interactivity;assembly=System.Windows.Interactivity"
      xmlns:uiCore="clr-namespace:Pollbook.UICore"
      xmlns:userControls="clr-namespace:Pollbook.UserControls"
      mc:Ignorable="d"
      Title="PollworkerView"
      Loaded="PollworkerView_OnLoaded"
      DataContext="{Binding Pollworker, Source={StaticResource Locator}}"
      Background="{StaticResource Gray7Brush}"
      d:DesignHeight="1200" d:DesignWidth="1920">

    <Page.Resources>
        <uiCore:StringToXamlConverter x:Key="StringToXamlConverter" />
        <uiCore:BooleanToVisibilityConverter x:Key="InverseBooleanToVisibility" True="Collapsed" False="Visible"/>
    </Page.Resources>

    <Grid Background="White">
        <Grid.RowDefinitions>
            <RowDefinition Height="auto" />
            <RowDefinition Height="auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="auto" />
        </Grid.RowDefinitions>

        <Grid Grid.Row="0" Margin="120,50,120,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"></ColumnDefinition>
                <ColumnDefinition Width="Auto"></ColumnDefinition>
            </Grid.ColumnDefinitions>
            <StackPanel Grid.Column="0" Orientation="Vertical" Margin="0,0,20,0">
                <Label Content="{Binding PollingPlaceLabel}" Style="{StaticResource StandardLightLabel}"/>
                <TextBlock Text="{Binding PollingPlace}" Style="{StaticResource  Display3BoldTextBlock}" TextWrapping="NoWrap" TextTrimming="CharacterEllipsis" />
            </StackPanel>
            <StackPanel Grid.Column="1" Orientation="Vertical">
                <Label Content="{Binding DateLabel}" Style="{StaticResource StandardLightLabel}"/>
                <TextBlock Text="{Binding CurrentDate}" Style="{StaticResource Display3BoldTextBlock}" />
            </StackPanel>
        </Grid>

        <StackPanel Grid.Row="1"  Margin="120,60,120,0">
            <TextBlock Text="{Binding Path=PollworkersList.Count, StringFormat={}{0} Poll Workers}" Style="{StaticResource BoldTextBlock}" />
        </StackPanel>
        
        <StackPanel Grid.Row="2" Orientation="Vertical" Margin="120,0,120,0" VerticalAlignment="Stretch">
            <ScrollViewer PanningMode="VerticalOnly" 
                          CanContentScroll="False"
                          Name="scroller"
                          Height="740"
                          HorizontalScrollBarVisibility="Disabled" 
                          VerticalScrollBarVisibility="Hidden" 
                          PreviewMouseWheel="ScrollViewer_PreviewMouseWheel" 
                          TouchDown="bodyScollViewer_TouchDown" 
                          VerticalAlignment="Stretch">

                <ListView 
                        x:Name="lvwPollworkers"
                        ItemsSource="{Binding PollworkersList, IsAsync=True}"                
                        BorderThickness="0"
                        Margin="0,0,0,180"
                        SelectionChanged="PollworkerSelected"
                        Background="{StaticResource Gray7Brush}"
                        VirtualizingPanel.IsVirtualizing="True"
                        VirtualizingPanel.VirtualizationMode="Recycling"
                        VirtualizingPanel.CacheLengthUnit="Item"
                        VirtualizingPanel.CacheLength="8096"
                        SelectionMode="Single"
                        Padding="0"
                        Height="Auto"
                        Grid.IsSharedSizeScope="true"
                        VerticalContentAlignment="Stretch"
                        HorizontalContentAlignment="Stretch">

                    <ListView.ItemContainerStyle>
                        <Style TargetType="ListViewItem">
                            <Setter Property="BorderBrush" Value="Transparent" />
                            <Setter Property="BorderThickness" Value="0" />
                            <Setter Property="Padding" Value="0,0,0,0" />
                            <Setter Property="FontFamily" Value="Noto Sans Display" />
                            <Setter Property="FontSize" Value="30" />
                            <Setter Property="Foreground" Value="{StaticResource Gray2Brush}"/>
                            <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
                        </Style>
                    </ListView.ItemContainerStyle>

                    <!--hack to avoid ListView's automatic scrollviewer-->
                    <ListView.Template>
                        <ControlTemplate>
                            <ItemsPresenter></ItemsPresenter>
                        </ControlTemplate>
                    </ListView.Template>

                    <ListView.ItemTemplate>
                        <DataTemplate>
                            <ContentControl>
                                <Border Height="130" VerticalAlignment="Center"  Margin="0,0,0,5">
                                    <Border.Resources>
                                        <LinearGradientBrush x:Key="LastItemBrush" StartPoint="0,0" EndPoint="0,1">
                                            <GradientStop Color="{StaticResource Gray7Color}" Offset="0" />
                                            <GradientStop Color="{StaticResource Gray7Color}" Offset="0.667"/>
                                            <GradientStop Color="{StaticResource Gray5Color}" Offset="1" />
                                        </LinearGradientBrush>
                                    </Border.Resources>
                                    <Border.Style>
                                        <Style TargetType="Border">
                                            <Style.Setters>
                                                <Setter Property="CornerRadius" Value="3" />
                                            </Style.Setters>
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding StatusColor}" Value="Green">
                                                    <Setter Property="Background" Value="{StaticResource SecondaryLightGreenBrush}" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding StatusColor}" Value="Yellow">
                                                    <Setter Property="Background" Value="{StaticResource SecondaryLightYellowBrush}" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding StatusColor}" Value="Red">
                                                    <Setter Property="Background" Value="{StaticResource SecondaryLightRedBrush2}" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding StatusColor}" Value="Gray">
                                                    <Setter Property="Background" Value="{StaticResource Gray8Brush}" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Border.Style>
                                    
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <!-- Last Name-->
                                            <ColumnDefinition Width="*" SharedSizeGroup="A"/>
                                            <ColumnDefinition Width="*"/>
                                            <!-- First Name-->
                                            <ColumnDefinition Width="*" SharedSizeGroup="B"/>
                                            <ColumnDefinition Width="*"/>
                                            <!-- Role/Title-->
                                            <ColumnDefinition Width="*" SharedSizeGroup="C"/>
                                            <ColumnDefinition Width="*"/>
                                            <!-- time cards-->
                                            <ColumnDefinition Width="330" SharedSizeGroup="D"/>
                                            <ColumnDefinition Width="*"/>
                                            <!-- status-->
                                            <ColumnDefinition Width="250" />
                                            <ColumnDefinition Width="*"/>
                                            <!--arrow-->
                                            <ColumnDefinition Width="40"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- last name -->
                                        <ContentControl Grid.Column="0" VerticalAlignment="Center" Margin="85,0,10,0">
                                            <ContentControl.Content>
                                                <MultiBinding Converter="{StaticResource StringToXamlConverter}">
                                                    <Binding Path="LastName" />
                                                </MultiBinding>
                                            </ContentControl.Content>
                                        </ContentControl>

                                        <!-- first name -->
                                        <ContentControl Grid.Column="2" VerticalAlignment="Center"  Margin="10,0,10,0">
                                            <ContentControl.Content>
                                                <MultiBinding Converter="{StaticResource StringToXamlConverter}">
                                                    <Binding Path="FirstName" />
                                                </MultiBinding>
                                            </ContentControl.Content>
                                        </ContentControl>

                                        <!-- title/role -->
                                        <ContentControl Grid.Column="4" VerticalAlignment="Center"  Margin="10,0,10,0">
                                            <ContentControl.Content>
                                                <MultiBinding Converter="{StaticResource StringToXamlConverter}">
                                                    <Binding Path="TitleName" />
                                                </MultiBinding>
                                            </ContentControl.Content>
                                        </ContentControl>
                                               
                                        <!-- next to last time card -->
                                        <StackPanel Grid.Column="6" Orientation="Vertical" VerticalAlignment="Center" Margin="10,0,10,0">
                                            <TextBlock Text="{Binding PriorToLastTimeCardTimes}"  HorizontalAlignment="Left" VerticalAlignment="Center">
                                                <TextBlock.Style>
                                                    <Style TargetType="TextBlock">
                                                        <Style.Triggers>
                                                            <DataTrigger Binding="{Binding PriorToLastTimeCardTimes}" Value="">
                                                                <Setter Property="Visibility" Value="Collapsed" />
                                                            </DataTrigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </TextBlock.Style>
                                            </TextBlock>

                                            <!-- last time card -->
                                            <TextBlock  Text="{Binding LastTimeCardTimes}"  VerticalAlignment="Center" HorizontalAlignment="Left">
                                                <TextBlock.Style>
                                                    <Style TargetType="TextBlock">
                                                        <Style.Triggers>
                                                            <DataTrigger Binding="{Binding LastTimeCardTimes}" Value="">
                                                                <Setter Property="Visibility" Value="Collapsed" />
                                                            </DataTrigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </TextBlock.Style>
                                            </TextBlock>
                                        </StackPanel>

                                       
                                        <!--status-->
                                        <StackPanel Grid.Column="8" Orientation="Horizontal" Margin="10,0,0,0">
                                            <StackPanel.Resources>
                                                <Path x:Key="ClockedOut" Stretch="Fill">
                                                    <Path.Style>
                                                        <Style TargetType="Path" BasedOn="{StaticResource BallotStandardIcon}" />
                                                    </Path.Style>
                                                </Path>

                                                <Path x:Key="ClockedIn" Stretch="Fill">
                                                    <Path.Style>
                                                        <Style TargetType="Path" BasedOn="{StaticResource BallotIssuedIcon}" />
                                                    </Path.Style>
                                                </Path>

                                                <Path x:Key="WrongPollingPlace" Stretch="Fill">
                                                    <Path.Style>
                                                        <Style TargetType="Path" BasedOn="{StaticResource  BallotProvisionalIcon}" />
                                                    </Path.Style>
                                                </Path>
                                            </StackPanel.Resources>
                                            <Viewbox Width="30" VerticalAlignment="Center">
                                                <ContentControl>
                                                    <ContentControl.Style>
                                                        <Style TargetType="{x:Type ContentControl}">
                                                            <Style.Triggers>
                                                                <DataTrigger Binding="{Binding StatusColor}" Value="Green">
                                                                    <Setter Property="Content" Value="{StaticResource ClockedOut}" />
                                                                </DataTrigger>
                                                                <DataTrigger Binding="{Binding StatusColor}" Value="Red">
                                                                    <Setter Property="Content" Value="{StaticResource ClockedIn}" />
                                                                </DataTrigger>
                                                                <DataTrigger Binding="{Binding StatusColor}" Value="Yellow">
                                                                    <Setter Property="Content" Value="{StaticResource WrongPollingPlace}" />
                                                                </DataTrigger>
                                                            </Style.Triggers>
                                                        </Style>
                                                    </ContentControl.Style>
                                                </ContentControl>
                                            </Viewbox>
                                       
                                            <TextBlock Text="{Binding StatusDisplayName}" Margin="10,0,0,0" VerticalAlignment="Center" HorizontalAlignment="Left"/>
                                        </StackPanel>
                                        
                                        <StackPanel Grid.Column="10" VerticalAlignment="Center" Margin="0,0,25,0">
                                            <Viewbox  Width="14" HorizontalAlignment="Right">
                                                <Path>
                                                    <Path.Style>
                                                        <Style TargetType="{x:Type Path}" BasedOn="{StaticResource LinkArrowIcon}" />
                                                    </Path.Style>
                                                </Path>
                                            </Viewbox>
                                        </StackPanel>
                                    </Grid>
                                </Border>
                            </ContentControl>
                        </DataTemplate>
                    </ListView.ItemTemplate>

                    <i:Interaction.Triggers>
                        <i:EventTrigger EventName="SelectionChanged">
                            <i:InvokeCommandAction Command="{Binding SelectPollworkerCommand}" CommandParameter="{Binding ElementName=lvwPollworkers, Path=SelectedItem}"></i:InvokeCommandAction>
                        </i:EventTrigger>
                    </i:Interaction.Triggers>
                </ListView>
            </ScrollViewer>
        </StackPanel>
        
        <Grid Grid.Row="3" Background="White">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>
            <StackPanel Grid.Column="0" Orientation="Horizontal" HorizontalAlignment="Left">
                <Button Content="{Binding BackLabel}"
                        userControls:ButtonHelper.DisableMultipleClicks="True"
                        Command="{Binding BackCommand}"
                        Style="{StaticResource SecondaryLargeButton}"
                        Margin="40"/>
            </StackPanel>
            <StackPanel Grid.Column="1"  Orientation="Horizontal" HorizontalAlignment="Right" Visibility="{Binding Path=IsSingleCheckInEnabled, Converter={StaticResource InverseBooleanToVisibility}}">
                <Button Content="{Binding ApproveLabel}"
                        userControls:ButtonHelper.DisableMultipleClicks="True"
                        Command="{Binding ApprovalCommand}"
                        Style="{StaticResource PrimaryLargeButton}"
                        Margin="40"
                        IsEnabled="{Binding ApprovalEnabled}"/>
            </StackPanel>
        </Grid>
    </Grid>
</Page>
