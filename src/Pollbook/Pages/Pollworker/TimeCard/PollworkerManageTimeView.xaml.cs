using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.ViewModel.Pollworker.TimeCard;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;


namespace Pollbook.Pages.Pollworker.TimeCard
{
    /// <summary>
    /// Interaction logic for PollworkerManageTimeView.xaml
    /// </summary>
    public partial class PollworkerManageTimeView : Page, IContextView
    {
        public bool IsModal => true;
        public SolidColorBrush PrimaryBackgroundBrush => (SolidColorBrush)Application.Current.FindResource("Gray8Brush");

        public PollworkerManageTimeView()
        {
            InitializeComponent();
        }

        private void PollworkerManageTimeView_OnLoaded(object sender, RoutedEventArgs e)
        {
            ((PollworkerManageTimeViewModel) DataContext).PageLoaded();
        }
        private void TimeCardSelected(object sender, RoutedEventArgs e)
        {
            var lv = (ListView)sender;
            var context = (PollworkerManageTimeViewModel)DataContext;
            context.TimeCardSelected = (TimeCardDto)lv.SelectedItem;
        }

    }
}
