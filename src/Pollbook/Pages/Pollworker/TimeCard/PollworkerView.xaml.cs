using CommonServiceLocator;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.ViewModel.Pollworker.TimeCard;
using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;

namespace Pollbook.Pages.Pollworker.TimeCard
{
    /// <summary>
    /// Interaction logic for PollworkerView.xaml
    /// </summary>
    public partial class PollworkerView : Page, IContextView
    {
        public bool IsModal => true;
        public SolidColorBrush PrimaryBackgroundBrush => (SolidColorBrush)Application.Current.FindResource("Gray8Brush");

        public PollworkerView()
        {
            InitializeComponent();
            try
            {
                ViewModelLocator locator = new ViewModelLocator();
                var manageTime = locator.PollworkerManageTime;
            }
            catch (Exception ex)
            {
                var essLogger = ServiceLocator.Current.GetInstance<IEssLogger>();
                var logProps = new Dictionary<string, string>();
                logProps.Add("Action", "PollworkerView");
                essLogger.LogError(ex, logProps);
            }
        }
        public async void PollworkerView_OnLoaded(object sender, RoutedEventArgs e)
        {
            await ((PollworkerViewModel)DataContext).PageLoaded();
        }
        private void PollworkerSelected(object sender, RoutedEventArgs e)
        {
            var lv = (ListView)sender;
            var context = (PollworkerViewModel)DataContext;
            context.PollworkerSelected = (PollworkerDto)lv.SelectedItem;
        }

        private void ScrollViewer_PreviewMouseWheel(object sender, System.Windows.Input.MouseWheelEventArgs e)
        {
            ScrollViewer scv = (ScrollViewer)sender;
            scv.ScrollToVerticalOffset(scv.VerticalOffset - e.Delta);
            e.Handled = true;
        }

        private void bodyScollViewer_TouchDown(object sender, TouchEventArgs e)
        {
            ScrollViewer scv = (ScrollViewer)sender;
            scv.Focus();
        }
    }
}
