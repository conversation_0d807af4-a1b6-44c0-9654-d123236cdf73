using ESS.Pollbook.ViewModel.Pollworker.Search;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace Pollbook.Pages.Pollworker.Search
{
    /// <summary>
    /// Interaction logic for PollworkerJobConfirmationView.xaml
    /// </summary>
    public partial class PollworkerJobConfirmationView : Page, IContextView
    {
        public bool IsModal => false;
        public SolidColorBrush PrimaryBackgroundBrush => (SolidColorBrush)Application.Current.FindResource("Gray8Brush");

        public PollworkerJobConfirmationView()
        {
            InitializeComponent();
            Loaded += ((PollworkerJobConfirmationViewModel)DataContext).OnLoad;
            Unloaded += ((PollworkerJobConfirmationViewModel)DataContext).OnUnLoad;
        }
    }
}
