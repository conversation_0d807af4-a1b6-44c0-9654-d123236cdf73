<Page x:Class="Pollbook.Pages.Pollworker.Search.PollworkerClockOutReasonView"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:userControls="clr-namespace:Pollbook.UserControls"
      mc:Ignorable="d"
      Title="PollworkerClockOutReasonsView"
      Loaded="PollworkerClockOutReasonView_OnLoaded"
      DataContext="{Binding PollworkerClockOutReason, Source={StaticResource Locator}}"
      Background="{StaticResource Gray7Brush}"
      d:DesignHeight="1200" d:DesignWidth="1800">

  <Grid >
    <Grid.RowDefinitions>
      <RowDefinition Height="auto" />
      <RowDefinition Height="*" />
      <RowDefinition Height="auto" />
    </Grid.RowDefinitions>

    <StackPanel Margin="0,80,0,40" Grid.Row="0">
      <Viewbox Stretch="Uniform" VerticalAlignment="Center" HorizontalAlignment="Center" Width="100" Margin="0,0,0,60">
        <Path Style="{StaticResource SuccessIcon}" Stretch="Fill" />
      </Viewbox>

      <TextBlock Style="{StaticResource Display1TextBlock}" Margin="0,0,0,30"
                 Text="{Binding PageTitle}" 
                 HorizontalAlignment="Center"
                 TextAlignment="Center" TextWrapping="Wrap" />

      <TextBlock Style="{StaticResource TextBlockStandardLightLabel}" Margin="0,0,0,30"
                 Text="{Binding PageInstructions}" 
                 HorizontalAlignment="Center"
                 TextAlignment="Center" TextWrapping="Wrap" />
    </StackPanel>

        <Grid Grid.Row="1"  HorizontalAlignment="Center"  Margin="0,0,0,60">
          <Grid.ColumnDefinitions>
              <ColumnDefinition Width="*" />
              <ColumnDefinition Width="*" />
          </Grid.ColumnDefinitions>
          <Grid.RowDefinitions>
              <RowDefinition Height="Auto" />
              <RowDefinition Height="Auto" />
              <RowDefinition Height="Auto" />
              <RowDefinition Height="Auto" />
          </Grid.RowDefinitions>

          <RadioButton Grid.Column="1" Grid.Row="0" 
                       Content="{Binding RbOne}" 
                       Visibility="{Binding RbOneVisibility}" 
                       GroupName="ActivitiesGroup" 
                       Style="{StaticResource StandardRadioButton}"
                       Margin="0,20"
                       Checked="rbg_Checked"
                       IsChecked="{Binding ModeArray[0], Mode=TwoWay}"/>
          <RadioButton Grid.Column="1" Grid.Row="1" 
                       Content="{Binding RbTwo}"
                       Visibility="{Binding RbTwoVisibility}"
                       GroupName="ActivitiesGroup" 
                       Style="{StaticResource StandardRadioButton}"
                       Margin="0,20"
                       Checked="rbg_Checked"
                       IsChecked="{Binding ModeArray[1], Mode=TwoWay}"/>
          <RadioButton Grid.Column="1" Grid.Row="2" 
                       Content="{Binding RbThree}"
                       Visibility="{Binding RbThreeVisibility}"
                       GroupName="ActivitiesGroup" 
                       Style="{StaticResource StandardRadioButton}"
                       Margin="0,20"
                       Checked="rbg_Checked"
                       IsChecked="{Binding ModeArray[2], Mode=TwoWay}"/>
          <RadioButton Grid.Column="1" Grid.Row="3" 
                       Content="{Binding RbFour}"
                       Visibility="{Binding RbFourVisibility}"
                       GroupName="ActivitiesGroup" 
                       Style="{StaticResource StandardRadioButton}"
                       Margin="0,20"
                       Checked="rbg_Checked"
                       IsChecked="{Binding ModeArray[3], Mode=TwoWay}"/>
      </Grid>

        <StackPanel Grid.Row="2" Background="{StaticResource WhiteBrush}" >
            <Grid Margin="40,40,40,40">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>
                <StackPanel Grid.Column="0" Orientation="Horizontal" HorizontalAlignment="Left">
                    <Button Content="{Binding BackLabel}"
                            userControls:ButtonHelper.DisableMultipleClicks="True"
                            Command="{Binding BackCommand}"
                            Style="{StaticResource SecondaryLargeButton}"/>
                </StackPanel>
                <StackPanel Grid.Column="1"  Orientation="Horizontal" HorizontalAlignment="Right">
                    <Button Content="{Binding NextLabel}"
                            userControls:ButtonHelper.DisableMultipleClicks="True"
                            Command="{Binding NextCommand}"
                            Style="{StaticResource PrimaryLargeButton}"
                            IsEnabled="{Binding IsNextButtonEnabled}" />
               </StackPanel>
            </Grid>
    </StackPanel>
  </Grid>
</Page>
