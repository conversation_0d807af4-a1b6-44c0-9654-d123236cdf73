<Page x:Class="Pollbook.Pages.Pollworker.Search.PollworkerTimeHistoryDetailsView"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:uiCore="clr-namespace:Pollbook.UICore"
      xmlns:userControls="clr-namespace:Pollbook.UserControls"
      mc:Ignorable="d" 
      Title="PollworkerTimeHistoryDetailsView"
      Loaded="PollworkerTimeHistoryDetailsView_OnLoaded"
      DataContext="{Binding PollworkerTimeHistoryDetails, Source={StaticResource Locator}}"
      Background="{StaticResource BackgroundBrush}"
      d:DesignHeight="1200" d:DesignWidth="1920">

    <Page.Resources>
        <uiCore:StringToXamlConverter x:Key="StringToXamlConverter" />
        <uiCore:BooleanToVisibilityConverter x:Key="InverseBooleanToVisibility" True="Collapsed" False="Visible"/>
    </Page.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="120,50,120,20">
            <StackPanel Orientation="Vertical">
                <Label Content="{Binding PageTitle}" Style="{StaticResource LargeTitle}" />
            </StackPanel>
        </StackPanel>

        <Grid Grid.Row="1" Background="{StaticResource Gray8Brush}"  Margin="120,0,120,20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>
            <Label Grid.Row="0" Content="{Binding TimeCardDate, FallbackValue='Todays Date Goes Here'}" Style="{StaticResource LargeBoldLabel}" Margin="50,20,50,0"/>

            <Grid Grid.Row="1" VerticalAlignment="Stretch" Margin="0,40,0,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="*" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                       
                    <!-- header-->
                    <Grid Grid.Row="0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="50" SharedSizeGroup="Margin"/>
                            <ColumnDefinition Width="1.25*" SharedSizeGroup="A"/>
                            <ColumnDefinition SharedSizeGroup="B">
                                <ColumnDefinition.Style>
                                    <Style TargetType="{x:Type ColumnDefinition}">
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding Path=IsSingleCheckInEnabled}" Value="True">
                                                <Setter Property="Width" Value="0"/>
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Path=IsSingleCheckInEnabled}" Value="False">
                                                <Setter Property="Width" Value="1.25*"/>
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </ColumnDefinition.Style>
                            </ColumnDefinition>
                            <ColumnDefinition SharedSizeGroup="C">
                                <ColumnDefinition.Style>
                                    <Style TargetType="{x:Type ColumnDefinition}">
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding Path=IsSingleCheckInEnabled}" Value="True">
                                                <Setter Property="Width" Value="0"/>
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Path=IsSingleCheckInEnabled}" Value="False">
                                                <Setter Property="Width" Value="1.25*"/>
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </ColumnDefinition.Style>
                            </ColumnDefinition>
                            <ColumnDefinition Width="3.5*" SharedSizeGroup="D"/>
                            <ColumnDefinition Width="2.5*" SharedSizeGroup="E"/>
                            <ColumnDefinition Width="50" SharedSizeGroup="Margin"/>
                        </Grid.ColumnDefinitions>

                        <Label Grid.Column="1" Margin="0,0,15,0" Content="{Binding ClockInHeader, FallbackValue='Clock In'}" Style="{StaticResource StandardLightLabel}" HorizontalAlignment="Left"/>
                        <Label Grid.Column="2" Margin="0,0,15,0"  Content="{Binding ClockOutHeader, FallbackValue='Clock Out'}" Style="{StaticResource StandardLightLabel}" HorizontalAlignment="Left"
                               Visibility="{Binding Path=IsSingleCheckInEnabled, Converter={StaticResource InverseBooleanToVisibility}}"/>
                        <Label Grid.Column="3" Margin="0,0,15,0"  Content="{Binding TotalTimeHeader, FallbackValue='Total Time'}" Style="{StaticResource StandardLightLabel}" HorizontalAlignment="Left"
                               Visibility="{Binding Path=IsSingleCheckInEnabled, Converter={StaticResource InverseBooleanToVisibility}}"/>
                        <Label Grid.Column="4" Margin="0,0,15,0" Content="{Binding PollingPlaceHeader, FallbackValue='Polling Place'}" Style="{StaticResource StandardLightLabel}" HorizontalAlignment="Left"/>
                        <Label Grid.Column="5" Margin="0,0,0,0" Content="{Binding RoleHeader, FallbackValue='Role'}" Style="{StaticResource StandardLightLabel}" HorizontalAlignment="Left"/>
                    </Grid>

                    <!-- time cards -->
                    <ListView Grid.Row="1"
                        ItemsSource="{Binding TimeCards, IsAsync=True}"
                        ScrollViewer.CanContentScroll="False"
                        ScrollViewer.VerticalScrollBarVisibility="Auto"
                        ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                        BorderThickness="0"
                        VirtualizingPanel.IsVirtualizing="True"
                        VirtualizingPanel.VirtualizationMode="Recycling"
                        VirtualizingPanel.CacheLengthUnit="Item"
                        VirtualizingPanel.CacheLength="8096"
                        SelectionMode="Single" 
                        HorizontalContentAlignment="Stretch"
                        Background="{StaticResource Gray8Brush}"
                        Margin="0,18,0,0">

                        <ListView.Resources>
                            <Style TargetType="ScrollViewer">
                                <Setter Property="Template" Value="{StaticResource TransparentScrollViewerTemplate}"/>
                                <Setter Property="PanningMode" Value="VerticalFirst"/>
                            </Style>
                        </ListView.Resources>

                        <ListView.ItemContainerStyle>
                            <Style TargetType="ListViewItem" BasedOn="{StaticResource ListViewContainer}">
                                <Setter Property="BorderBrush" Value="{StaticResource Gray6Brush}"/>
                                <Setter Property="BorderThickness" Value="0,2,0,0"/>
                            </Style>
                        </ListView.ItemContainerStyle>
                        
                        <ListView.ItemTemplate>
                            <DataTemplate>
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="50" SharedSizeGroup="Margin"/>
                                        <ColumnDefinition Width="1.25*" SharedSizeGroup="A"/>
                                        <ColumnDefinition SharedSizeGroup="B">
                                            <ColumnDefinition.Style>
                                                <Style TargetType="{x:Type ColumnDefinition}">
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Page}}, Path=DataContext.IsSingleCheckInEnabled}" Value="True">
                                                            <Setter Property="Width" Value="0"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Page}}, Path=DataContext.IsSingleCheckInEnabled}" Value="False">
                                                            <Setter Property="Width" Value="1.25*"/>
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </ColumnDefinition.Style>
                                        </ColumnDefinition>
                                        <ColumnDefinition SharedSizeGroup="C">
                                            <ColumnDefinition.Style>
                                                <Style TargetType="{x:Type ColumnDefinition}">
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Page}}, Path=DataContext.IsSingleCheckInEnabled}" Value="True">
                                                            <Setter Property="Width" Value="0"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Page}}, Path=DataContext.IsSingleCheckInEnabled}" Value="False">
                                                            <Setter Property="Width" Value="1.25*"/>
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </ColumnDefinition.Style>
                                        </ColumnDefinition>
                                        <ColumnDefinition Width="3.5*" SharedSizeGroup="D"/>
                                        <ColumnDefinition Width="2.5*" SharedSizeGroup="E"/>
                                        <ColumnDefinition Width="50" SharedSizeGroup="Margin"/>
                                    </Grid.ColumnDefinitions>
                                    
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="111" />
                                    </Grid.RowDefinitions>

                                    <ContentControl Grid.Column="1" VerticalAlignment="Center" HorizontalAlignment="Left" Style="{StaticResource Display3BoldFont}" Margin="5,0,15,0">
                                        <ContentControl.Content>
                                            <MultiBinding Converter="{StaticResource StringToXamlConverter}">
                                                <Binding Path="StartTimeString" FallbackValue="Test Content"/>
                                            </MultiBinding>
                                        </ContentControl.Content>
                                    </ContentControl>

                                    <ContentControl Grid.Column="2" VerticalAlignment="Center" HorizontalAlignment="Left" Style="{StaticResource Display3BoldFont}" Margin="5,0,15,0"
                                                    Visibility="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Page}}, Path=DataContext.IsSingleCheckInEnabled, Converter={StaticResource InverseBooleanToVisibility}}">
                                        <ContentControl.Content>
                                            <MultiBinding Converter="{StaticResource StringToXamlConverter}">
                                                <Binding Path="EndTimeString" FallbackValue="Test Content"/>
                                            </MultiBinding>
                                        </ContentControl.Content>
                                    </ContentControl>

                                    <ContentControl Grid.Column="3" VerticalAlignment="Center" HorizontalAlignment="Left" Style="{StaticResource Display3BoldFont}" Margin="5,0,15,0"
                                                    Visibility="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Page}}, Path=DataContext.IsSingleCheckInEnabled, Converter={StaticResource InverseBooleanToVisibility}}">
                                        <ContentControl.Content>
                                            <MultiBinding Converter="{StaticResource StringToXamlConverter}">
                                                <Binding Path="TotalTimeDisplay" FallbackValue="Test Content"/>
                                            </MultiBinding>
                                        </ContentControl.Content>
                                    </ContentControl>

                                    <ContentControl Grid.Column="4" VerticalAlignment="Center" HorizontalAlignment="Left"  Style="{StaticResource Display3BoldFont}" Margin="5,0,0,0">
                                        <ContentControl.Content>
                                            <TextBlock Text="{Binding PollingPlaceDisplayName}" TextWrapping="NoWrap" TextTrimming="CharacterEllipsis"></TextBlock>
                                        </ContentControl.Content>
                                    </ContentControl>

                                    <ContentControl Grid.Column="5" VerticalAlignment="Center" HorizontalAlignment="Left" Style="{StaticResource Display3BoldFont}" Margin="5,0,0,0">
                                        <ContentControl.Content>
                                            <MultiBinding Converter="{StaticResource StringToXamlConverter}">
                                                <Binding Path="TitleName" FallbackValue="Test Title Here"/>
                                            </MultiBinding>
                                        </ContentControl.Content>
                                    </ContentControl>
                                </Grid>
                            </DataTemplate>
                        </ListView.ItemTemplate>
                    </ListView>

                    <!-- Grand Total -->
                    <Grid Grid.Row="2" Background="{StaticResource Gray7Brush}" Visibility="{Binding Path=IsSingleCheckInEnabled, Converter={StaticResource InverseBooleanToVisibility}}">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="90"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="50" SharedSizeGroup="Margin"/>
                            <ColumnDefinition Width="Auto" SharedSizeGroup="A"/>
                            <ColumnDefinition Width="Auto" SharedSizeGroup="B" />
                            <ColumnDefinition Width="50" SharedSizeGroup="Margin"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="1" Text="Grand Total: " VerticalAlignment="Center" HorizontalAlignment="Right" Style="{StaticResource InstructionalSemiBoldTextBlock}"/>
                        <Label Grid.Column="2" Content="{Binding GrandTotal, FallbackValue='1:18'}" Style="{StaticResource Body1SemiBoldLightGray2Font}" VerticalAlignment="Center" HorizontalAlignment="Left"/>
                    </Grid>
                </Grid>
            </Grid>
        </Grid>

        <Grid Grid.Row="2" Background="{StaticResource WhiteBrush}">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>
            <StackPanel Grid.Column="0" Orientation="Horizontal" HorizontalAlignment="Left">
                <Button Content="{Binding BackLabel}"
                        userControls:ButtonHelper.DisableMultipleClicks="True"
                        Command="{Binding BackCommand}"
                        Style="{StaticResource SecondaryLargeButton}"
                        Margin="40"/>
            </StackPanel>
        </Grid>
    </Grid>
</Page>
