using ESS.Pollbook.ViewModel.Affidavit;
using ESS.Pollbook.ViewModel.Pollworker.Search;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace Pollbook.Pages.Pollworker.Search
{
    /// <summary>
    /// Interaction logic for PollworkerDetailView.xaml
    /// </summary>
    public partial class PollworkerDetailView : Page, IContextView
    {
        public bool IsModal => false;

        public SolidColorBrush PrimaryBackgroundBrush => (SolidColorBrush)Application.Current.FindResource("BackgroundBrush");

        private readonly PollworkerDetailViewModel _viewModel;

        public PollworkerDetailView()
        {
            InitializeComponent();
            _viewModel = (PollworkerDetailViewModel)DataContext;
        }

        private async void PollworkerDetailView_OnLoaded(object sender, RoutedEventArgs e)
        {
	        await _viewModel.OnPageLoaded();
        }

    }
}
