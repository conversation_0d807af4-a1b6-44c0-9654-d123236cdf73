using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using CommonServiceLocator;
using ESS.Pollbook.ViewModel.Infrastructure;
using ESS.Pollbook.ViewModel.Pollworker.Search;
using Pollbook.UICore;
using Pollbook.UserControls;

namespace Pollbook.Pages.Pollworker.Search
{
    /// <summary>
    ///     Interaction logic for PollworkerSearchView.xaml
    /// </summary>
    public partial class PollworkerSearchView : IContextView
    {
        public bool IsModal => false;
        public SolidColorBrush PrimaryBackgroundBrush => (SolidColorBrush)Application.Current.FindResource("WhiteBrush");

        private IKeyboardService _keyboardService;
        private PollworkerSearchViewModel _viewModel;

        public PollworkerSearchView()
        {
            InitializeComponent();
            _keyboardService = ServiceLocator.Current.GetInstance<IKeyboardService>();
            FocusManager.AddGotFocusHandler(this, Page_GotFocus);
        }

        public void Page_Loaded(object sender, RoutedEventArgs e)
        {
            _viewModel = (PollworkerSearchViewModel)DataContext;
            _viewModel.PollworkerSearch_Loaded();
            LastNameSearchBox.TakeKeyboardFocusOnLoad = true;
            _viewModel.FocusLastName += ViewModelFocusLastName;
            _viewModel.OnSearchSet += ResetScroll;
        }

        public void Page_Unload(object sender, RoutedEventArgs e)
        {
            _keyboardService.TextDeactivated();
            _viewModel.FocusLastName -= ViewModelFocusLastName;
            _viewModel.OnSearchSet -= ResetScroll;
        }
        
        private void ViewModelFocusLastName()
        {
            LastNameSearchBox.MaskedItem.Focus();
            LastNameSearchBox.MaskedItem.SelectionStart = LastNameSearchBox.MaskedItem.Text.Length;
        }

        private void ScrollViewer_PreviewMouseWheel(object sender, MouseWheelEventArgs e)
        {
            ScrollViewer scv = (ScrollViewer)sender;
            scv.ScrollToVerticalOffset(scv.VerticalOffset - e.Delta);
            e.Handled = true;
        }

        private void BodyScollViewer_TouchDown(object sender, TouchEventArgs e)
        {
            ScrollViewer scv = (ScrollViewer)sender;
            scv.Focus();
        }

        private void Page_GotFocus(object sender, EventArgs e)
        {
            if (Keyboard.FocusedElement is TextBox || Keyboard.FocusedElement is WatermarkTextBox || Keyboard.FocusedElement is WatermarkComboBox)
            {
                FrameworkElement focusedElement = (FrameworkElement)Keyboard.FocusedElement;
                _keyboardService.TextActivated(focusedElement, allowPageToMoveUp: false);
            }
            else
            {
                _keyboardService.TextDeactivated();
            }
        }

        public void SearchBox_PreviewMouseDown(object sender, MouseButtonEventArgs e)
        {
            _keyboardService.TextActivated((FrameworkElement)sender);
        }

        public void SearchBox_PreviewTouchDown(object sender, TouchEventArgs e)
        {
            _keyboardService.TextActivated((FrameworkElement)sender);
        }

        private void ClearAll_Click(object sender, RoutedEventArgs e)
        {
            LastNameSearchBox.MaskedItem.Focus();
        }

        private void ResetScroll()
        {
            scroller.ScrollToHome();
        }
    }

}
