using ESS.Pollbook.ViewModel.Pollworker;
using ESS.Pollbook.ViewModel.Pollworker.Search;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;

namespace Pollbook.Pages.Pollworker.Search
{
    /// <summary>
    /// Interaction logic for PollworkerOathView.xaml
    /// </summary>
    public partial class PollworkerOathView : Page, IContextView
    {
        public bool IsModal => true;
        public SolidColorBrush PrimaryBackgroundBrush => (SolidColorBrush)Application.Current.FindResource("Gray8Brush");

        public PollworkerOathView()
        {
            InitializeComponent();

            Loaded += ((PollworkerOathViewModel)DataContext).OnLoad;
            Unloaded += ((PollworkerOathViewModel)DataContext).OnUnLoad;
            Unloaded += PollworkerOathView_Unloaded;
        }

        private void PollworkerOathView_Unloaded(object sender, RoutedEventArgs e)
        {
            SignatureCanvas.Strokes?.Clear();
        }

        private void clearSignature_mouseDown(object sender, MouseButtonEventArgs e)
        {
            SignatureCanvas.Strokes?.Clear();
            ((PollworkerOathViewModel)DataContext).HasStrokes = false;
        }

        private void SignatureCanvas_OnStrokeCollected(object sender, InkCanvasStrokeCollectedEventArgs e)
        {
            ((PollworkerOathViewModel)DataContext).HasStrokes = true;
        }
    }
}
