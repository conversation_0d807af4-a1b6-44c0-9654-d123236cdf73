using CommonServiceLocator;
using Dynamsoft;
using Dynamsoft.DBR;
using Dynamsoft.UVC;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Facade.Barcode;
using ESS.Pollbook.ViewModel.Infrastructure;
using ESS.Pollbook.ViewModel.Pollworker.Search;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Text.RegularExpressions;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Threading;

namespace Pollbook.Pages.Pollworker.Search
{
	public partial class PollworkerAddEditView : IContextView, IDisposable
	{
		private readonly PollworkerAddEditViewModel _viewModel;

		private readonly IEssLogger _logger;
		private readonly IKeyboardService _keyboardService;

		public bool IsModal => true;

		public SolidColorBrush PrimaryBackgroundBrush =>
			(SolidColorBrush)Application.Current.FindResource("BackgroundBrush");

		/// <summary>
		/// So this variable is a little janky.  Going to leave it for 7280 but will be gone in 7290.
		/// </summary>
		private bool _enableBarcodeSearch;

		// Dynamsoft
		private CameraManager _cameraManager;
		private Camera _camera;
		private BarcodeReader _dbr;

		private static readonly Regex Rgx = new Regex("[0-9]");

		/// <summary>
		/// Just a flag to determine if the Dynamsoft has been initialized.
		/// </summary>
		private bool _cameraHasBeenInitialized;

		/// <summary>
		/// When Scanning and displaying the image, we do not want to create and dispose of 
		/// our unmanaged space each frame.  We reuse the allocations and this does improve
		/// display performance.
		/// </summary>
		private MemoryStream _memStream;

		private BackgroundWorker _idLookup;
		private bool _disposed;

		public PollworkerAddEditView()
		{
			InitializeComponent();
			_viewModel = DataContext as PollworkerAddEditViewModel;

			try
			{
				ScanBtn.Content = UIText.ScanPollworkerId;
				_keyboardService = ServiceLocator.Current.GetInstance<IKeyboardService>();
				_logger = ServiceLocator.Current.GetInstance<IEssLogger>();
			}
			catch (Exception ex)
			{
				_logger?.LogError(ex,
					new Dictionary<string, string>
						{ { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
			}
		}

		private async void PollworkerAddEditView_OnLoaded(object sender, RoutedEventArgs e)
		{
			InitializeCamera();
			txtFirstName.MaskedItem.Focus(); // from constructor

			ScanBtn.Visibility = SystemConfiguration.PollworkerConfiguration.EnablePollworkerScanner
				? Visibility.Visible
				: Visibility.Hidden;

			await ((PollworkerAddEditViewModel)DataContext).PageOnLoadAsync();
		}

		/// <summary>
		/// Unload Screen
		/// </summary>
		private void PollworkerAddEditView_UnLoaded(object sender, RoutedEventArgs e)
		{
			try
			{
				if (_idLookup != null)
				{
					_idLookup.DoWork -= ExamineBarcodeResults;
					_idLookup.Dispose();
				}

				CameraBarcodeScanGrid.Visibility = Visibility.Hidden;
				TurnOffCamera();
				((PollworkerAddEditViewModel)DataContext).PageUnloaded();
			}
			catch (Exception ex)
			{
				_logger?.LogError(ex,
					new Dictionary<string, string>
						{ { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
			}

			_keyboardService.TextDeactivated();
		}

		private void Tab_SelectionChanged(object sender, RoutedEventArgs e)
		{
			if (!(sender is TabControl tab))
				return;

			switch (tab.SelectedIndex)
			{
				case 0:
					//Pollworker
					txtFirstName.Focus();
					break;
				case 1:
					//address
					txtStreetAddress.Focus();
					break;
				case 2:
					//contact
					txtHomePhoneNumber.Focus();
					break;
			}
		}

		#region User Control and Events

		private void ScanBtn_OnClick(object sender, RoutedEventArgs e)
		{
			if (CameraBarcodeScanGrid.Visibility == Visibility.Visible)
				TurnOffCamera();
			else
				TurnOnCamera();
		}

		private void NumericOnlyControl_PreviewTextInput(object sender, TextCompositionEventArgs e)
		{
			e.Handled = !Rgx.IsMatch(e.Text);
		}

		private void AlphabetOnly_PreviewTextInput(object sender, TextCompositionEventArgs e)
		{
			e.Handled = !Regex.IsMatch(e.Text, "^[a-zA-Z]");
		}

		private void NumericOnlyControl_PreviewKeyDown(object sender, KeyEventArgs e)
		{
			if (e.Key == Key.Space)
				e.Handled = true;
			base.OnPreviewKeyDown(e);
		}

		#endregion

		private void InitializeCamera()
		{
			if (_cameraHasBeenInitialized)
				return;

			_idLookup = new BackgroundWorker();
			_idLookup.DoWork += ExamineBarcodeResults;

			_cameraManager =
				new CameraManager(
					System.Configuration.ConfigurationManager.AppSettings["DynamsoftCameraManagerProductKey"]);

			BarcodeReader.InitLicense(
				System.Configuration.ConfigurationManager.AppSettings["DynamsoftBarcodeReaderProductKey"], out _);
			_dbr = new BarcodeReader();
			UpdateDynamsoftBarcodeReaderSettings();
			_cameraHasBeenInitialized = true;
		}

		private bool TurnOnCamera()
		{
			CameraBarcodeScanGrid.Visibility = Visibility.Visible;

            try
			{
				if (!_cameraHasBeenInitialized || _cameraManager == null)
					return false;

				_camera = LocateWebCamera();
				if (_camera == null)
					return false;

				_camera.Open();
				_memStream = new MemoryStream();
				_camera.OnFrameCaptrue += Camera_OnFrameCapture;
				_camera.OnFrameCaptrue += Camera_UpdateDisplay;

                ScanBtn.Content = UIText.ScanPollworkerIdManualEntry;
				
                // Set up barcode search.
				_enableBarcodeSearch = true;
				return true;
			}
			catch (Exception ex)
			{
				// If the Open failed, it may be that the camera is in use. This can happen on development/test machines, not so much on a real Pollbook.
				_logger.LogError(ex,
					new Dictionary<string, string>
						{ { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
				return false;
			}
		}

		[MethodImpl(MethodImplOptions.AggressiveInlining)]
		private void Camera_UpdateDisplay(Bitmap bitmap)
		{
			if (bitmap == null)
				return;

			var currentFrame = new BitmapImage(); // cant be optimized.  Freeze() wrecks it
			_memStream.Position = 0;
			bitmap.Save(_memStream, ImageFormat.Bmp);
			currentFrame.BeginInit();
			currentFrame.StreamSource = _memStream;
			currentFrame.CacheOption = BitmapCacheOption.OnLoad;
			currentFrame.EndInit();
			currentFrame.Freeze();
			VideoImage.Dispatcher.BeginInvoke(new Action(() => VideoImage.Source = currentFrame));
		}

		/// <summary>
		/// This event is fired off by the Camera.  The bitmap that
		/// is provided as an argument is disposed by the Dynamsoft
		/// library upon completion.  Hence, you can not pass the
		/// argument bitmap to a background process because it will
		/// have been disposed.
		/// </summary>
		private void Camera_OnFrameCapture(Bitmap bitmap)
		{
			try
			{
				if (!_enableBarcodeSearch)
					return;

				var scanResult = _dbr.DecodeBitmap(bitmap, string.Empty);
				if (scanResult.Length == 0)
					return;

				if (_idLookup.IsBusy)
					return;

				_idLookup.RunWorkerAsync(scanResult);
			}
			catch (Exception ex)
			{
				_logger.LogError(ex,
					new Dictionary<string, string>
						{ { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
			}
		}

		private void ExamineBarcodeResults(object sender, DoWorkEventArgs e)
		{
			if (!(e.Argument is TextResult[] textResults))
				return;

			try
			{
				foreach (var t in textResults)
				{
					switch (t.BarcodeFormat)
					{
						case EnumBarcodeFormat.BF_PDF417:
							Dispatcher.BeginInvoke(DispatcherPriority.Normal,
								new Action(() => BarcodeScannedFromCamera2D(t.BarcodeText)));
							TurnOffCamera();
							return;
					}
				}
			}
			catch (Exception exp)
			{
				_logger.LogError(exp,
					new Dictionary<string, string>
						{ { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
			}
		}

		/// <summary>
		/// This is the default and is looking for the rear camera of a tablet.
		/// </summary>
		/// <returns></returns>
		private Camera LocateWebCamera()
		{
			var cameraNames = _cameraManager.GetCameraNames() ?? new List<string>();
			if (cameraNames.Count == 0)
				return null;

			Camera result = null;
			short alternativeCameraIndex = -1;

			for (short cameraIndex = 0; cameraIndex < cameraNames.Count; cameraIndex++)
			{
				// Attempt to locate an alternative camera why we are here.
				if (alternativeCameraIndex < 0
				    && cameraNames[cameraIndex].ToLower().Contains("logi webcam"))
				{
					alternativeCameraIndex = cameraIndex;
				}

				if (cameraNames[cameraIndex].IndexOf("rear", StringComparison.CurrentCultureIgnoreCase) < 0)
					continue;

				result = _cameraManager.SelectCamera(cameraIndex);
				break;
			}

			// No rear, how about alternative?
			if (result == null && alternativeCameraIndex >= 0)
				result = _cameraManager.SelectCamera(alternativeCameraIndex);

			// No alternative, how about The first camera
			if (result == null && cameraNames.Count > 0)
				result = _cameraManager.SelectCamera(0);

			if (result != null)
			{
				result.CurrentResolution = result.SupportedResolutions.Find(x => x.Width == 1280 && x.Height == 720) ??
				                           result.SupportedResolutions.OrderBy(x => Math.Abs(x.Height - 1280))
					                           .First();
			}

			return result;
		}

		private void UpdateDynamsoftBarcodeReaderSettings()
		{
			var settings = _dbr.GetRuntimeSettings();

			settings.ExpectedBarcodesCount = 0;
			settings.MaxAlgorithmThreadCount = 2;

			settings.BarcodeFormatIds = (int)(EnumBarcodeFormat.BF_PDF417 | EnumBarcodeFormat.BF_CODE_39);
			settings.BinarizationModes[0] = EnumBinarizationMode.BM_LOCAL_BLOCK;

			settings.LocalizationModes[1] = EnumLocalizationMode.LM_LINES;
			settings.LocalizationModes[2] = EnumLocalizationMode.LM_SCAN_DIRECTLY;

			settings.DeblurModes[0] = EnumDeblurMode.DM_SHARPENING;
			settings.DeblurModes[1] = EnumDeblurMode.DM_SMOOTHING;

			settings.FurtherModes.GrayscaleTransformationModes[0] = EnumGrayscaleTransformationMode.GTM_ORIGINAL;

			_dbr.UpdateRuntimeSettings(settings);

			_dbr.SetModeArgument("BinarizationModes", 0, "BlockSizeX", "5", out _);
			_dbr.SetModeArgument("BinarizationModes", 0, "BlockSizeY", "5", out _);
			_dbr.SetModeArgument("BinarizationModes", 0, "EnableFillBinaryVacancy", "1", out _);
			_dbr.SetModeArgument("BinarizationModes", 0, "ThresholdCompensation", "10", out _);
		}

		/// <summary>
		/// Process Results from Camera
		/// </summary>
		/// <param name="scannedData"></param>
		public void BarcodeScannedFromCamera2D(string scannedData)
		{
			var dlp = new DriversLicenseParser();
			try
			{
				_viewModel.MapDriverLicenseData(dlp.Parse2DBarcode(scannedData));
			}
			catch (Exception ex)
			{
				_logger?.LogError(ex,
					new Dictionary<string, string>
						{ { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
			}
		}

		/// <summary>
		/// This is cleanup of the camera.
		/// </summary>
		private void TurnOffCamera()
		{
			if (_camera != null)
			{
				_camera.OnFrameCaptrue -= Camera_OnFrameCapture;
				_camera.OnFrameCaptrue -= Camera_UpdateDisplay;
			}

			CameraBarcodeScanGrid.Dispatcher.BeginInvoke(new Action(() =>
				CameraBarcodeScanGrid.Visibility = Visibility.Hidden));

            ScanBtn.Content = UIText.ScanPollworkerId;

            // clear out image. Else next start will flash previous image artifact.
            VideoImage.Dispatcher.BeginInvoke(new Action(() => VideoImage.Source = null));

			_memStream?.Dispose();
			_camera?.Dispose();
			_camera = null;

			GC.Collect(GC.MaxGeneration, GCCollectionMode.Forced, true);
		}

		/// <summary>
		/// This will never fire as we keep the object in memory for reuse, but this
		/// does make Fortify happy.
		/// </summary>
		public void Dispose()
		{
			Dispose(true);
			GC.SuppressFinalize(this);
		}

		protected virtual void Dispose(bool disposing)
		{
			if (_disposed)
				return;

			_cameraManager?.Dispose();
			_camera?.Dispose();
			_dbr?.Dispose();
			_memStream?.Dispose();

			_idLookup?.Dispose();
			_disposed = true;
		}
	}
}