using ESS.Pollbook.ViewModel.Pollworker.Search;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace Pollbook.Pages.Pollworker.Search
{
    /// <summary>
    /// Interaction logic for PollworkerClockOutReasonView.xaml
    /// </summary>
    public partial class PollworkerClockOutReasonView : Page, IContextView
    {
        public bool IsModal => true;
        public SolidColorBrush PrimaryBackgroundBrush => (SolidColorBrush)Application.Current.FindResource("Gray8Brush");

        public PollworkerClockOutReasonView()
        {
            InitializeComponent();
        }

        private void rbg_Checked(object sender, RoutedEventArgs e)
        {
            ((PollworkerClockOutReasonViewModel)DataContext).rbg_Checked(sender, e);
        }

        private async void PollworkerClockOutReasonView_OnLoaded(object sender, RoutedEventArgs e)
        {
            await ((PollworkerClockOutReasonViewModel)DataContext).OnPageLoaded();
        }
    }
}
