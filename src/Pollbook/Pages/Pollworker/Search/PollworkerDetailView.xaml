<Page x:Class="Pollbook.Pages.Pollworker.Search.PollworkerDetailView"
	xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
	xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
	xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
	xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:uiCore="clr-namespace:Pollbook.UICore"
    xmlns:userControls="clr-namespace:Pollbook.UserControls"
    mc:Ignorable="d"
        Title="PollworkerDetailView"
        Loaded="PollworkerDetailView_OnLoaded"
        DataContext="{Binding PollworkerDetail, Source={StaticResource Locator}}"
        Background="{StaticResource WhiteBrush}"
        d:DesignHeight="1200" d:DesignWidth="1800">
    <Page.Resources>
        <uiCore:StringToXamlConverter x:Key="StringToXamlConverter" />
        <uiCore:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <uiCore:BooleanToVisibilityConverter x:Key="InverseBooleanToVisibility" True="Collapsed" False="Visible"/>
    </Page.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="auto" />
        </Grid.RowDefinitions>
        <!-- Header -->
        <Grid Grid.Row="0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2.5*"></ColumnDefinition>
                <ColumnDefinition Width="*"></ColumnDefinition>
            </Grid.ColumnDefinitions>
            <StackPanel Grid.Column="0" Margin="120,40,54,40">
                <Label Content="{Binding FullName}" Style="{StaticResource LargeTitle}" FontSize="48"/>
                <Label Content="{Binding FullAddress}" Style="{StaticResource StandardLightLabel}" Foreground="{StaticResource Gray2Brush}" />
            </StackPanel>
            <Button Grid.Column="1"
                    userControls:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding EditPollworkerCommand}"
                    HorizontalAlignment="Right"
                    VerticalAlignment="Bottom"
                    Margin="0,0,50,30"
                    IsEnabled="{Binding IsEditPollworkerEnabled}">
                <Button.Template>
                    <ControlTemplate TargetType="Button">
                        <ContentPresenter />
                    </ControlTemplate>
                </Button.Template>
                <Grid>
                    <StackPanel Orientation="Horizontal" Margin="92,0,0,0">
                        <TextBlock Text="{Binding EditPollworkerLabel}"  Margin="0,48,5,48">
                            <TextBlock.Style>
                                <Style TargetType="{x:Type TextBlock}" BasedOn="{StaticResource Display4BoldTextBlock}">
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding IsEditPollworkerEnabled}" Value="True">
                                            <Setter Property="IsEnabled" Value="True"/>
                                            <Setter Property="Foreground" Value="{StaticResource BluetifulBrush}" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding IsEditPollworkerEnabled}" Value="False">
                                            <Setter Property="IsEnabled" Value="False"/>
                                            <Setter Property="Foreground" Value="{StaticResource Gray5Brush}" />
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                        </TextBlock>
                        <Viewbox Stretch="Uniform" VerticalAlignment="Center" HorizontalAlignment="Center" Width="16" Margin="0,3,50,16">
                            <Path>
                                <Path.Style >
                                    <Style TargetType="{x:Type Path}" BasedOn="{StaticResource LinkArrowIcon}">
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding IsEditPollworkerEnabled}" Value="True">
                                                <Setter Property="Fill" Value="{StaticResource BluetifulBrush}" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding IsEditPollworkerEnabled}" Value="False">
                                                <Setter Property="Fill" Value="{StaticResource Gray5Brush}" />
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </Path.Style>
                            </Path>
                        </Viewbox>
                    </StackPanel>
                </Grid>
                <Button.Style>
                    <Style TargetType="Button" BasedOn="{StaticResource PrimaryLargeButton}">
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding IsEditPollworkerEnabled}" Value="True">
                                <Setter Property="IsEnabled" Value="True"/>
                            </DataTrigger>
                            <DataTrigger Binding="{Binding IsEditPollworkerEnabled}" Value="False">
                                <Setter Property="IsEnabled" Value="False"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </Button.Style>
            </Button>
        </Grid>
        <!-- Tabs -->
        <TabControl Grid.Row="1" 
                BorderThickness="0 2 0 0" 
                    Margin="120 0 120 0"
                SelectedIndex="{Binding SelectedTab, Mode=TwoWay}" >
            <TabControl.Resources>
                <Style TargetType="TabItem">
                    <Setter Property="Template">
                        <Setter.Value>
                            <ControlTemplate TargetType="TabItem">
                                <Border Name="Panel" CornerRadius="3, 3, 0, 0" Width="296" Height="100" Margin="-2,0,10,0" Background="White">
                                    <StackPanel Orientation="Vertical" VerticalAlignment="Bottom">
                                    <ContentPresenter x:Name="ContentSite" VerticalAlignment="Center" HorizontalAlignment="Left" Margin="0 0 0 30">
                                        <ContentPresenter.Content>
                                            <ContentControl x:Name="ContentCtl" Content="{TemplateBinding Header}" Style="{StaticResource Body1BoldFont}"/>
                                        </ContentPresenter.Content>
                                    </ContentPresenter>
                                        <Rectangle Fill="{StaticResource SecondaryDarkBlueBrush}" Height="6" Width="{Binding ActualWidth, ElementName=ContentCtl}" VerticalAlignment="Bottom" HorizontalAlignment="Left">
                                            <Rectangle.Style>
                                                <Style TargetType="Rectangle">
                                                    <Setter Property="Visibility" Value="Hidden"/>
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type TabItem}}, Path=IsSelected}" Value="True">
                                                            <Setter Property="Visibility" Value="Visible"/>
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </Rectangle.Style>
                                        </Rectangle>
                                    </StackPanel>
                                </Border>
                                <ControlTemplate.Triggers>
                                    <Trigger Property="IsSelected" Value="True">
                                        <Setter TargetName="Panel" Property="Background" Value="White" />
                                        <Setter TargetName="ContentCtl" Property="Foreground" Value="{StaticResource SecondaryDarkBlueBrush}"/>
                                    </Trigger>
                                    <Trigger Property="IsSelected" Value="False">
                                        <Setter TargetName="Panel" Property="Background" Value="White" />
                                        <Setter TargetName="ContentCtl" Property="Foreground" Value="{StaticResource Gray4Brush}"/>
                                    </Trigger>
                                    <Trigger Property="TabIndex" Value="0">
                                        <Setter TargetName="Panel" Property="Margin" Value="0,0,10,0" />
                                    </Trigger>
                                </ControlTemplate.Triggers>
                            </ControlTemplate>
                        </Setter.Value>
                    </Setter>
                </Style>
            </TabControl.Resources>
            <!-- Pollworker Info-->
            <TabItem TabIndex="0" Header="Poll Worker">
                <Grid Margin="0,40,0,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>

                    <!-- General Info -->
                    <Border Grid.Column="0" CornerRadius="3" BorderThickness="2" BorderBrush="{StaticResource Gray6Brush}" Margin="0,0,20,0">
                        <StackPanel Orientation="Vertical" Margin="50,33,50,20">
                            <Grid >
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="*" />
                                </Grid.RowDefinitions>
                                <StackPanel Grid.Row="0" Grid.Column="0" Margin="0,0,0,10">
                                    <Label Content="{Binding DoBLabel}" Style="{StaticResource StandardLightLabel}" />
                                    <TextBlock Text="{Binding DateOfBirth}" Style="{StaticResource Display3BoldTextBlock}"/>
                                </StackPanel>
                                <StackPanel Grid.Row="0" Grid.Column="1" Margin="0,0,0,10">
                                    <Label Content="{Binding JobTitleLabel}" Style="{StaticResource StandardLightLabel}" />
                                    <TextBlock Text="{Binding JobTitle}" Style="{StaticResource Display3BoldTextBlock}"/>
                                </StackPanel>
                                <StackPanel Grid.Row="1" Grid.Column="0" Margin="0,0,0,10" Visibility="{Binding PhoneVisible, Converter={StaticResource BooleanToVisibilityConverter}}">
                                    <Label Content="{Binding PhoneLabel}" Style="{StaticResource StandardLightLabel}" />
                                    <TextBlock Text="{Binding Phone}" Style="{StaticResource Display3BoldTextBlock}"/>
                                </StackPanel>
                                <StackPanel Grid.Row="1" Grid.Column="1" Margin="0,0,0,10">
                                    <Label Content="{Binding PollPlaceLabel}" Style="{StaticResource StandardLightLabel}" />
                                    <TextBlock Text="{Binding PollPlace}" Style="{StaticResource Display3BoldTextBlock}" TextWrapping="Wrap" TextTrimming="CharacterEllipsis"/>
                                </StackPanel>
                            </Grid>

                            <StackPanel Visibility="{Binding ShowParty, Converter={StaticResource BooleanToVisibilityConverter}}">
                                <Label Content="{Binding PartyLabel}" Style="{StaticResource StandardLightLabel}" />
                                <TextBlock Text="{Binding Party}" Style="{StaticResource Display3BoldTextBlock}" TextWrapping="Wrap" TextTrimming="CharacterEllipsis" Height="100"/>
                            </StackPanel>
                        </StackPanel>
                    </Border>
                    
                    <!-- Time Card Info -->
                    <Border Grid.Column="1" CornerRadius="3" BorderThickness="2" BorderBrush="{StaticResource Gray6Brush}" Margin="5,0,0,0">
                        <Grid Margin="50,33,50,33" Width="auto">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="*" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>
                            <Label Grid.Row="0" Grid.Column="0" Content="{Binding CurrentDate, FallbackValue='Todays Date Goes Here'}" Style="{StaticResource Display3BoldFont}" Margin="0,0,0,10" />
                            <!-- headers -->
                            <Grid Grid.Row="1">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="2*" SharedSizeGroup="A"/>
                                    <ColumnDefinition SharedSizeGroup="B">
                                        <ColumnDefinition.Style>
                                            <Style TargetType="{x:Type ColumnDefinition}">
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding Path=IsSingleCheckInEnabled}" Value="True">
                                                        <Setter Property="Width" Value="0"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Path=IsSingleCheckInEnabled}" Value="False">
                                                        <Setter Property="Width" Value="2*"></Setter>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </ColumnDefinition.Style>
                                    </ColumnDefinition>
                                    <ColumnDefinition SharedSizeGroup="C">
                                        <ColumnDefinition.Style>
                                            <Style TargetType="{x:Type ColumnDefinition}">
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding Path=IsSingleCheckInEnabled}" Value="True">
                                                        <Setter Property="Width" Value="0"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Path=IsSingleCheckInEnabled}" Value="False">
                                                        <Setter Property="Width" Value="1.5*"></Setter>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </ColumnDefinition.Style>
                                    </ColumnDefinition>
                                </Grid.ColumnDefinitions>
                                <Label Grid.Column="0" Content="{Binding ClockInHeader, FallbackValue='Clock In'}" Style="{StaticResource StandardLightLabel}"   />
                                <Label Grid.Column="1" Content="{Binding ClockOutHeader, FallbackValue='Clock Out'}" Style="{StaticResource StandardLightLabel}" Visibility="{Binding Path=IsSingleCheckInEnabled, Converter={StaticResource InverseBooleanToVisibility}}"/>
                                <Label Grid.Column="2" Content="{Binding TotalTimeHeader, FallbackValue='Total'}" Style="{StaticResource StandardLightLabel}" Visibility="{Binding Path=IsSingleCheckInEnabled, Converter={StaticResource InverseBooleanToVisibility}}"/>
                            </Grid>
                            
                            <!-- time cards -->
                            <ListView Grid.Row="2"
                                    ItemsSource="{Binding TodaysTimeCards, IsAsync=True}"
                                    ScrollViewer.CanContentScroll="False"
                                    ScrollViewer.VerticalScrollBarVisibility="Hidden"
                                    ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                                    BorderThickness="0"
                                    VirtualizingPanel.IsVirtualizing="True"
                                    VirtualizingPanel.VirtualizationMode="Recycling"
                                    VirtualizingPanel.CacheLengthUnit="Item"
                                    VirtualizingPanel.CacheLength="8096"
                                    SelectionMode="Single">
                                <ListView.ItemContainerStyle>
                                    <Style TargetType="ListViewItem">
                                        <Setter Property="BorderThickness" Value="0" />
                                        <Setter Property="Padding" Value="0,0,0,0" />
                                        <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
                                        <Setter Property="VerticalContentAlignment" Value="Stretch"/>
                                    </Style>
                                </ListView.ItemContainerStyle>
                                <ListView.ItemTemplate>
                                    <DataTemplate>
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="2*" SharedSizeGroup="A"/>
                                                <ColumnDefinition SharedSizeGroup="B">
                                                    <ColumnDefinition.Style>
                                                        <Style TargetType="{x:Type ColumnDefinition}">
                                                            <Style.Triggers>
                                                                <DataTrigger Binding="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Page}}, Path=DataContext.IsSingleCheckInEnabled}" Value="True">
                                                                    <Setter Property="Width" Value="0"/>
                                                                </DataTrigger>
                                                                <DataTrigger Binding="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Page}}, Path=DataContext.IsSingleCheckInEnabled}" Value="False">
                                                                    <Setter Property="Width" Value="2*"></Setter>
                                                                </DataTrigger>
                                                            </Style.Triggers>
                                                        </Style>
                                                    </ColumnDefinition.Style>
                                                </ColumnDefinition>
                                                <ColumnDefinition SharedSizeGroup="C">
                                                    <ColumnDefinition.Style>
                                                        <Style TargetType="{x:Type ColumnDefinition}">
                                                            <Style.Triggers>
                                                                <DataTrigger Binding="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Page}}, Path=DataContext.IsSingleCheckInEnabled}" Value="True">
                                                                    <Setter Property="Width" Value="0"/>
                                                                </DataTrigger>
                                                                <DataTrigger Binding="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Page}}, Path=DataContext.IsSingleCheckInEnabled}" Value="False">
                                                                    <Setter Property="Width" Value="1.5*"></Setter>
                                                                </DataTrigger>
                                                            </Style.Triggers>
                                                        </Style>
                                                    </ColumnDefinition.Style>
                                                </ColumnDefinition>
                                            </Grid.ColumnDefinitions>
                                            <ContentControl Grid.Column="0" VerticalAlignment="Center" Style="{StaticResource Display3BoldFont}" Margin="5,0,0,0">
                                                <ContentControl.Content>
                                                    <MultiBinding Converter="{StaticResource StringToXamlConverter}">
                                                        <Binding Path="StartTimeString" />
                                                    </MultiBinding>
                                                </ContentControl.Content>
                                            </ContentControl>
                                            <ContentControl Grid.Column="1" VerticalAlignment="Center" Style="{StaticResource Display3BoldFont}" Margin="5,0,0,0"
                                                            Visibility="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Page}}, Path=DataContext.IsSingleCheckInEnabled, Converter={StaticResource InverseBooleanToVisibility}}">
                                                <ContentControl.Content>
                                                    <MultiBinding Converter="{StaticResource StringToXamlConverter}">
                                                        <Binding Path="EndTimeString" />
                                                    </MultiBinding>
                                                </ContentControl.Content>
                                            </ContentControl>
                                            <ContentControl Grid.Column="2" VerticalAlignment="Center" Style="{StaticResource Display3BoldFont}" Margin="5,0,0,0"
                                                            Visibility="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Page}}, Path=DataContext.IsSingleCheckInEnabled, Converter={StaticResource InverseBooleanToVisibility}}">
                                                <ContentControl.Content>
                                                    <MultiBinding Converter="{StaticResource StringToXamlConverter}">
                                                        <Binding Path="TotalTimeDisplay" />
                                                    </MultiBinding>
                                                </ContentControl.Content>
                                            </ContentControl>
                                        </Grid>
                                    </DataTemplate>
                                </ListView.ItemTemplate>
                            </ListView>
                            
                            <!-- Grand Total -->
                            <Grid Grid.Row="3" Visibility="{Binding Path=IsSingleCheckInEnabled, Converter={StaticResource InverseBooleanToVisibility}}">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="2*" SharedSizeGroup="A"/>
                                    <ColumnDefinition Width="2*" SharedSizeGroup="B"/>
                                    <ColumnDefinition Width="1*" SharedSizeGroup="C"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="1" Text="Grand Total: " VerticalAlignment="Bottom" HorizontalAlignment="Right" Style="{StaticResource TextBlockStandardLightLabel}"  Margin="0,0,0,8" />
                                <Label Grid.Column="2" Content="{Binding TodaysGrandTotal, FallbackValue='0.05'}"  HorizontalContentAlignment="Left" VerticalAlignment="Bottom" Style="{StaticResource Display3BoldFont}"  Margin="0,0,0,2" />
                            </Grid>
                        </Grid>
                    </Border>
                </Grid>
            </TabItem>
            
            <!-- Work History-->
            <TabItem Header="Work History">
                <StackPanel HorizontalAlignment="Stretch">
                    <Border BorderBrush="{StaticResource Gray6Brush}" BorderThickness="0,0,0,2" CornerRadius="3, 3, 0, 0"  Margin="0,40,0,0"  Background="{StaticResource Gray8Brush}">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="110"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="2.7*" SharedSizeGroup="Date"/>
                                <ColumnDefinition Width="2.5*" SharedSizeGroup="Total" />
                                <ColumnDefinition Width="1.25*" SharedSizeGroup="ViewDetails" />
                            </Grid.ColumnDefinitions>
                            <Label Grid.Column="0" Content="{Binding DateHeader}" Style="{StaticResource StandardLightLabel}" Margin="80,0,0,0" VerticalContentAlignment="Center"/>
                            <Label Grid.Column="1" Content="{Binding TotalHeader}" Style="{StaticResource StandardLightLabel}"  VerticalContentAlignment="Center"/>
                            <Label Grid.Column="2" Content="{Binding ViewDetailsHeader}" Style="{StaticResource StandardLightLabel}"  VerticalContentAlignment="Center"/>
                        </Grid>
                    </Border>
                    <ListView
                            Margin="0,0,0,40"
                            ItemsSource="{Binding WorkHistory, IsAsync=True}"
                            ScrollViewer.CanContentScroll="True"
                            ScrollViewer.VerticalScrollBarVisibility="Auto"
                            ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                            VirtualizingPanel.IsVirtualizing="True"
                            VirtualizingPanel.VirtualizationMode="Recycling"
                            VirtualizingPanel.CacheLengthUnit="Item"
                            VirtualizingPanel.CacheLength="8096"
                            SelectionMode="Single"
                            BorderThickness="0"
                            Padding="0"
                            VerticalAlignment="Stretch"
                            Height="472">
                        
                        <ListView.ItemContainerStyle>
                            <Style TargetType="ListViewItem" BasedOn="{StaticResource ListViewContainer}">
                                <Setter Property="HorizontalContentAlignment"  Value="Stretch"/>
                                <Setter Property="BorderBrush" Value="{StaticResource Gray6Brush}" />
                                <Setter Property="BorderThickness" Value="0,0,0,2" />
                                <Setter Property="Background" Value="{StaticResource Gray8Brush}"/>
                            </Style>
                        </ListView.ItemContainerStyle>

                        <ListView.Resources>
                            <Style TargetType="ScrollViewer">
                                <Setter Property="Template" Value="{StaticResource TransparentScrollViewerTemplate}"/>
                                <Setter Property="PanningMode" Value="VerticalFirst"/>
                            </Style>
                        </ListView.Resources>

                        <ListView.ItemTemplate>
                            <DataTemplate>
                                <ContentControl>
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="2.7*" SharedSizeGroup="Date"/>
                                            <ColumnDefinition Width="2.5*" SharedSizeGroup="Total" />
                                            <ColumnDefinition Width="1.25*" SharedSizeGroup="ViewDetails" />
                                        </Grid.ColumnDefinitions>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="124" />
                                        </Grid.RowDefinitions>
                                        <ContentControl Grid.Column="0" VerticalAlignment="Center" Style="{StaticResource Display3BoldFont}" Margin="85,0,0,0">
                                            <ContentControl.Content>
                                                <MultiBinding Converter="{StaticResource StringToXamlConverter}">
                                                    <Binding Path="DayDisplay" />
                                                </MultiBinding>
                                            </ContentControl.Content>
                                        </ContentControl>
                                        <ContentControl Grid.Column="1" VerticalAlignment="Center" Style="{StaticResource Display3BoldFont}" Margin="5,0,0,0"
                                                        Visibility="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Page}}, Path=DataContext.IsSingleCheckInEnabled, Converter={StaticResource InverseBooleanToVisibility}}">
                                            <ContentControl.Content>
                                                <MultiBinding Converter="{StaticResource StringToXamlConverter}">
                                                    <Binding Path="TotalTimeDisplay" />
                                                </MultiBinding>
                                            </ContentControl.Content>
                                        </ContentControl>
                                        <ContentControl Grid.Column="1" VerticalAlignment="Center" Style="{StaticResource Display3BoldFont}" Margin="5,0,0,0"
                                                        Visibility="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Page}}, Path=DataContext.IsSingleCheckInEnabled, Converter={StaticResource BooleanToVisibilityConverter}}">
                                            <ContentControl.Content>
                                                <MultiBinding Converter="{StaticResource StringToXamlConverter}">
                                                    <Binding Path="StartTimeDisplay" />
                                                </MultiBinding>
                                            </ContentControl.Content>
                                        </ContentControl>
                                        <StackPanel Grid.Column="2" Orientation="Horizontal" HorizontalAlignment="Left" VerticalAlignment="Center" Margin="5,0,0,0">
                                            <Button Command="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Page}}, Path=DataContext.ViewCommand}"
                                                    userControls:ButtonHelper.DisableMultipleClicks="True"
                                                    CommandParameter="{Binding Day}"
                                                    HorizontalAlignment="Right" 
                                                    VerticalAlignment="Center" 
                                                    Margin="0,0,0,0"  
                                                    Style="{StaticResource PrimaryLargeButton}">
                                                <Button.Template>
                                                    <ControlTemplate TargetType="Button">
                                                        <ContentPresenter />
                                                    </ControlTemplate>
                                                </Button.Template>
                                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Left" VerticalAlignment="Center">
                                                    <TextBlock Text="{Binding RelativeSource={RelativeSource Mode=FindAncestor, 
                                                                    AncestorType={x:Type Page}}, 
                                                                    Path=DataContext.ViewCommandLabel}"
                                                                    HorizontalAlignment="Left"
                                                                    Style="{StaticResource Display3LinkTextBlock}" />
                                                </StackPanel>
                                            </Button>
                                        </StackPanel>
                                    </Grid>
                                </ContentControl>
                            </DataTemplate>
                        </ListView.ItemTemplate>
                    </ListView>
                </StackPanel>
            </TabItem>
        </TabControl>
        <Grid Grid.Row="2" Background="White">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>
            <StackPanel Grid.Column="0" Orientation="Horizontal" HorizontalAlignment="Left">
                <Button Content="{Binding BackLabel}"
                        userControls:ButtonHelper.DisableMultipleClicks="True"
                        Command="{Binding BackCommand}"
                        Style="{StaticResource SecondaryLargeButton}"
                        Margin="40"/>
            </StackPanel>
            <StackPanel Grid.Column="1"  Orientation="Horizontal" HorizontalAlignment="Right">
                <Button Content="{Binding ClockLabel}" 
                        userControls:ButtonHelper.DisableMultipleClicks="True"
                        Command="{Binding ClockCommand}"
                        IsEnabled="{Binding EnableClock}"
                        Margin="40"
                        Style="{StaticResource PrimaryLargeButton}"/>
            </StackPanel>
        </Grid>
    </Grid>
</Page>
