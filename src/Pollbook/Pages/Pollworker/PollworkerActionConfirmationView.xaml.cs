using System;
using ESS.Pollbook.ViewModel.Pollworker;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace Pollbook.Pages.Pollworker
{
    /// <summary>
    /// Interaction logic for PollworkerActionConfirmationView.xaml
    /// </summary>
    public partial class PollworkerActionConfirmationView : Page, IContextView
    {
        public bool IsModal => true;

        public SolidColorBrush PrimaryBackgroundBrush => (SolidColorBrush)Application.Current.FindResource("Gray8Brush");

        public PollworkerActionConfirmationView()
        {
            InitializeComponent();
            Loaded += ((PollworkerActionConfirmationViewModel)DataContext).OnLoad;
            Unloaded += ((PollworkerActionConfirmationViewModel)DataContext).OnUnLoad;
        }
    }
}
