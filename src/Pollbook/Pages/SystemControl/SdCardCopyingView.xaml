<Page x:Class="Pollbook.Pages.SystemControl.SdCardCopyingView"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:local="clr-namespace:Pollbook.Pages.SystemControl"
      xmlns:userControls="clr-namespace:Pollbook.UserControls"
      mc:Ignorable="d"
      DataContext="{Binding SdCardCopying, Source={StaticResource Locator}}"
      Background="{StaticResource Gray7Brush}"
      d:DesignHeight="1200" d:DesignWidth="1920"
      Title="Backup Transactions">
    <Grid Background="White">
        <StackPanel VerticalAlignment="Top" HorizontalAlignment="Center" Margin="0,250,0,0" Background="White">
            <userControls:Spinner />
            <TextBlock Text="SD Card Detected" 
                       Style="{StaticResource Display1TextBlock}" HorizontalAlignment="Center"/>
            <TextBlock Text="Please wait while transactions are backed up to SD Card." 
                       Style="{StaticResource Display1TextBlock}" HorizontalAlignment="Center"
                       TextAlignment="Center" Width="1209px" TextWrapping="Wrap" Foreground="Black"/>
        </StackPanel>
    </Grid>
</Page>