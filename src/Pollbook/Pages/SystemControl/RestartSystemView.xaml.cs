using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace Pollbook.Pages.SystemControl
{
    /// <summary>
    /// Interaction logic for RestartSystemView.xaml
    /// </summary>
    public partial class RestartSystemView : Page, IContextView
    {
        public bool IsModal => true;

        public SolidColorBrush PrimaryBackgroundBrush => (SolidColorBrush)Application.Current.FindResource("Gray8Brush");

        public RestartSystemView()
        {
            InitializeComponent();
        }
    }
}
