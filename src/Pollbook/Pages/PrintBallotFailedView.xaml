<Page x:Class="Pollbook.Pages.PrintBallotFailedView"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:userControls="clr-namespace:Pollbook.UserControls"
      mc:Ignorable="d"
      DataContext="{Binding PrintBallotFailed, Source={StaticResource Locator}}"
      Background="{StaticResource Gray7Brush}"
      d:DesignHeight="1200" d:DesignWidth="1920"
      Title="PrintBallotFailedView">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="auto" />
        </Grid.RowDefinitions>
        <Grid Grid.Row="0" Margin="120,40,120,0">
            <Label Content="{Binding LabelContent}" HorizontalAlignment="Left" VerticalAlignment="Top" Margin="10,0,0,0">
                <Label.Style>
                    <Style TargetType="Label" BasedOn="{StaticResource Body1SemiBoldFont}" />
                </Label.Style>
            </Label>
            <Label Content="Create Paper Ballot" HorizontalAlignment="Left" VerticalAlignment="Top" Margin="10,40,0,0">
                <Label.Style>
                    <Style TargetType="Label" BasedOn="{StaticResource Display2SemiBoldFont}" />
                </Label.Style>
            </Label>
        </Grid>
        <!-- Body -->
        <Grid Grid.Row="1" Background="{StaticResource Gray7Brush}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Top" Margin="0,100,0,0">
                <Viewbox Height="120" Width="120" Stretch="Uniform" HorizontalAlignment="Center">
                    <Path>
                        <Path.Style>
                            <Style TargetType="{x:Type Path}" BasedOn="{StaticResource ErrorIcon}" />
                        </Path.Style>
                    </Path>
                </Viewbox>
                <TextBlock Text="{Binding ErrorMessage}" HorizontalAlignment="Center" Margin="0,30,0,0">
                    <TextBlock.Style>
                        <Style TargetType="TextBlock" BasedOn="{StaticResource Display1TextBlock}" />
                    </TextBlock.Style>
                </TextBlock>
                <TextBlock Text="{Binding ErrorDesc}" HorizontalAlignment="Center" Margin="0,-5,0,0">
                    <TextBlock.Style>
                        <Style TargetType="TextBlock" BasedOn="{StaticResource StandardTextBlock}" />
                    </TextBlock.Style>
                </TextBlock>
            </StackPanel>
        </Grid>

        <!-- Buttons -->
        <Grid Grid.Row="2" Background="White">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="auto" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <Button Grid.Column="0"
                    Content="{Binding SkipLabel}"
                    Style="{StaticResource SecondaryLargeButton}"
                    HorizontalAlignment="Left"
                    Margin="40"
                    userControls:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding SkipCommand}" />

            <Button Grid.Column="1"
                    Content="{Binding RetryLabel}"
                    Style="{StaticResource PrimaryLargeButton}"
                    HorizontalAlignment="Right"
                    Margin="40"
                    userControls:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding RetryCommandAsync}"
                    IsEnabled="{Binding RetryEnabled}" />
        </Grid>
    </Grid>
</Page>
