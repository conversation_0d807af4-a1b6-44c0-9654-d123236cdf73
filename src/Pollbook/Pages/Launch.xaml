<UserControl Name="UcLaunchControl" 
        x:Class="Pollbook.Pages.Launch"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:ui1="clr-namespace:Pollbook.UserControls.Composite"
        xmlns:s="clr-namespace:ESS.Pollbook.Core.StaticValues;assembly=ESS.Pollbook.Core"
        xmlns:userControls="clr-namespace:Pollbook.UserControls"
        Loaded="Launch_OnLoaded"
        Initialized="PageInitialized"
        DataContext="{Binding Launch, Source={StaticResource Locator}}"
        mc:Ignorable="d"
        d:DesignHeight="1080" d:DesignWidth="1920">

    <UserControl.Resources>
        <BooleanToVisibilityConverter x:Key="BoolToVisibilityConverter"/>
    </UserControl.Resources>

    <Grid Margin="0,0,0,0">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="*" />
        </Grid.ColumnDefinitions>

        <Grid Grid.Column="0" Background="{StaticResource PrimaryNavyBrush}">
            <Grid.RowDefinitions>
                <RowDefinition Height="*"></RowDefinition>
                <RowDefinition Height="auto"></RowDefinition>
                <RowDefinition Height="145"></RowDefinition>
                <RowDefinition Height="*"></RowDefinition>
                <RowDefinition Height="62"></RowDefinition>
            </Grid.RowDefinitions>

            <StackPanel Grid.Row="1">
                <Image Width="520" Height="520" Source="..\Styles\Images\logo.jpg"/>
            </StackPanel>

            <Label Grid.Row="2" Style="{StaticResource Display1BoldLightFont}"
                   Content="ExpressPoll" 
                   HorizontalAlignment="Center"
                   VerticalAlignment="Bottom"/>

            <StackPanel Grid.Row="3">
                <!-- Device Name -->
                <Label ContentStringFormat="Device Name: {0}" Content="{Binding Path=DeviceName}" Style="{StaticResource Body2SemiBoldLightFont}" HorizontalAlignment="Center" Margin="0,24,0,0"/>

                <!-- APP Version -->
                <Label ContentStringFormat="App Version: {0}" Content="{Binding Path=AppVersion}" Style="{StaticResource Body2SemiBoldLightFont}" HorizontalAlignment="Center"/>

                <!-- Database Version -->
                <Label ContentStringFormat="Database Version: {0}" Content="{Binding Path=DatabaseVersion}" Style="{StaticResource Body2SemiBoldLightFont}" HorizontalAlignment="Center"/>

                <!-- Configuration Version -->
                <Label ContentStringFormat="Configuration Version: {0}" Content="{Binding Path=ConfigurationVersion}" Style="{StaticResource Body2SemiBoldLightFont}" HorizontalAlignment="Center"/>
               
                <!-- Last Sync Date/Time-->
                <Label ContentStringFormat="Last Sync: {0}" Content="{Binding Path=LastSyncDateTime}" Style="{StaticResource Body2SemiBoldLightFont}" HorizontalAlignment="Center"/>
            </StackPanel>

            <Grid Grid.Row="4" Margin="0 ,0,29,0" HorizontalAlignment="Center">
                <!--Upload Sync Status Percentage-->
                <Label ContentStringFormat="Upload Sync Status: {0}%" Content="{Binding Path=UploadSyncStatus}" Style="{StaticResource Body2SemiBoldLightFont}" HorizontalAlignment="Center"/>
            </Grid>
        </Grid>

        <Grid Grid.Column="1" Background="{StaticResource WhiteBrush}" Margin="0,-3,0,3">
            <Grid.RowDefinitions>
                <RowDefinition Height="auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <Grid Grid.Row="1" Margin="120,147,120,0" HorizontalAlignment="Center">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"></RowDefinition>
                    <RowDefinition Height="Auto"></RowDefinition>
                </Grid.RowDefinitions>

                <TextBlock Grid.Row="0" Text="{Binding JurisdictionName}" Style="{StaticResource TitleTextBlock}" HorizontalAlignment="Stretch" TextAlignment="Center" />
                <TextBlock Grid.Row="1" TextWrapping="Wrap" Text="{Binding ElectionName}" Style="{StaticResource Display1TextBlock}" HorizontalAlignment="Stretch" Margin="0,70,0,0" TextAlignment="Center" />
            </Grid>

            <!-- Launch Button Standard blue button for Non Test Mode, Marigold colored button for Test Mode -->
            <Button Grid.Row="2"
                    userControls:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding LaunchCommand}"
                    Width="Auto"
                    Height="152"
                    BorderThickness="10"
                    Content="{Binding LaunchLabel}" 
                    Margin="120,50,120,0" 
                    RenderTransformOrigin="0.5,0.5" 
                    VerticalAlignment="Top" >
                <Button.Style>
                    <Style TargetType="Button" BasedOn="{StaticResource PrimaryLargeButton}">
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding TestMode}" Value="True">
                                <Setter Property="Background" Value="{StaticResource SecondaryDarkYellowBrush}"/>
                                <Setter Property="Foreground" Value="Black"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </Button.Style>
            </Button>

            <Grid Grid.Row="3" Margin="120,0,120,60" >
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                <Button Grid.Column="0"
                        Style="{StaticResource SecondarySmallButton}"
                        userControls:ButtonHelper.DisableMultipleClicks="True"
                        Command="{Binding MaintenanceCommand}"
                        Width="350">
                    <Border CornerRadius="10">
                        <TextBlock Text="{Binding MaintenanceLabel}"/>
                    </Border>
                </Button>
                <Button Grid.Column="2"
                        Style="{StaticResource SecondarySmallButton}"
                        userControls:ButtonHelper.DisableMultipleClicks="True"
                        Command="{Binding ShutdownCommand}"
                        Width="330">
                    <Border CornerRadius="10">
                        <TextBlock Text="{Binding ShutdownLabel}"/>
                    </Border>
                </Button>
            </Grid>

            <Grid Grid.Row="0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="auto"/>
                </Grid.ColumnDefinitions>
                <ui1:CustomSystemStatusBar Grid.Column="1" />
            </Grid>
        </Grid>

        <Grid Grid.Column="0" 
              ColumnSpan="2"
              Grid.Row="0" 
              Visibility="{Binding ErrorWindowVisible, Converter={StaticResource BoolToVisibilityConverter}, FallbackValue=Visible}" 
              Background= "#A9152025">

            <StackPanel Background="{StaticResource WhiteBrush}" VerticalAlignment="Top">
                <StackPanel Margin="0,120,0,0" HorizontalAlignment="Center" >
                    <Viewbox Stretch="Uniform" VerticalAlignment="Center" HorizontalAlignment="Center" Width="100" Margin="0,0,0,60">
                        <Path Style="{StaticResource LargeBallotIssuedIcon}" Stretch="Fill" />
                    </Viewbox>
                    <TextBlock Style="{StaticResource Display1TextBlock}" 
                               Margin="0,0,0,30" 
                               Text="Updates Failed to Apply" 
                               TextAlignment="Center" />
                    <TextBlock Style="{StaticResource StandardTextBlock}" 
                               Width="1200"  
                               Margin="0,0,0,30" 
                               Foreground="{StaticResource Gray2Brush}" 
                               Text="Please contact Elections Office for assistance." 
                               HorizontalAlignment="Center" 
                               TextAlignment="Center" 
                               TextWrapping="Wrap" />
                </StackPanel>

                <StackPanel  Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,100,0,100">
                    <Button Width="724"
                            Style="{StaticResource PrimaryLargeButton}" 
                            Content="{Binding OkLabel}" 
                            userControls:ButtonHelper.DisableMultipleClicks="True"
                            Command="{Binding CloseErrorWindowCommand}" />
                </StackPanel>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>