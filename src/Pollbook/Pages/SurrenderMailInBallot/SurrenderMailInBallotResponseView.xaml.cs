using ESS.Pollbook.ViewModel.SurrenderMailInBallot;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace Pollbook.Pages.SurrenderMailInBallot
{
    /// <summary>
    /// Interaction logic for SurrenderMailInBallotYesView.xaml
    /// </summary>
    public partial class SurrenderMailInBallotYesView : Page, IContextView
    {
        public bool IsModal => false;
        public SolidColorBrush PrimaryBackgroundBrush => (SolidColorBrush)Application.Current.FindResource("Gray8Brush");

        public SurrenderMailInBallotYesView()
        {
            InitializeComponent();
        }

        private void Page_OnLoaded(object sender, RoutedEventArgs e)
        {
            ((SurrenderMailInBallotResponseViewModel)DataContext).PageIsLoaded();
        }
    }
}
