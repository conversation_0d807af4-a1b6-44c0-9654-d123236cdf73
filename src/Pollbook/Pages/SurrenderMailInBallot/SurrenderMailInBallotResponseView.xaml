<Page x:Class="Pollbook.Pages.SurrenderMailInBallot.SurrenderMailInBallotYesView"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:core="clr-namespace:Pollbook.UICore"
      xmlns:userControls="clr-namespace:Pollbook.UserControls"
      mc:Ignorable="d" 
      d:DesignHeight="450" d:DesignWidth="800"
      Title="SurrenderMailInBallotResponseView"
      Loaded="Page_OnLoaded"
      DataContext="{Binding SurrenderMailInBallotResponse, Source={StaticResource Locator}}"
      Background="White">

    <Grid Background="White" Margin="100,0">
        <Grid.RowDefinitions>
            <RowDefinition Height="auto" />
            <RowDefinition Height="175" />
        </Grid.RowDefinitions>

        <StackPanel Margin="0,0,0,80">
            <Viewbox Stretch="Uniform" VerticalAlignment="Center" HorizontalAlignment="Center" Width="100" Margin="0,0,0,60">
                <Path Style="{StaticResource WarningIconBlue}" Stretch="Fill" />
            </Viewbox>
            <TextBlock Style="{StaticResource Display2TextBlock}" Margin="0,0,0,30" Text="{Binding MessageTitle}" HorizontalAlignment="Center" TextAlignment="Center" TextWrapping="Wrap" />
            <TextBlock Style="{StaticResource InstructionalLargeTextBlock}" Foreground="{StaticResource Gray2Brush}" Text="{Binding Message}" HorizontalAlignment="Center" TextAlignment="Center" TextWrapping="Wrap" />
        </StackPanel>

        <!-- Buttons -->
        <StackPanel Orientation="Vertical" Grid.Row="1">
            <Button Grid.Column="0"
                    Style="{StaticResource PrimaryLargeButton}"
                    Content="{Binding CompleteLabel}"
                    userControls:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding CompleteCommand}"
                    HorizontalAlignment="Center"
                    Width="500"/>
        </StackPanel>
    </Grid>
</Page>
