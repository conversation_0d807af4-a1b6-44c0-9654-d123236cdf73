using ESS.Pollbook.ViewModel.SurrenderMailInBallot;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace Pollbook.Pages.SurrenderMailInBallot
{
    /// <summary>
    /// Interaction logic for SurrenderMailInBallotView.xaml
    /// </summary>
    public partial class SurrenderMailInBallotView : Page, IContextView
    {
        public bool IsModal => true;
        public SolidColorBrush PrimaryBackgroundBrush => (SolidColorBrush)Application.Current.FindResource("Gray8Brush");

        public SurrenderMailInBallotView()
        {
            InitializeComponent();
        }

        private void Page_OnLoaded(object sender, RoutedEventArgs e)
        {
            ((SurrenderMailInBallotViewModel)DataContext).PageIsLoaded();
        }
    }
}
