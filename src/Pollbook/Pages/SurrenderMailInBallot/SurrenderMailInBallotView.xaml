<Page x:Class="Pollbook.Pages.SurrenderMailInBallot.SurrenderMailInBallotView"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:userControls="clr-namespace:Pollbook.UserControls"
      mc:Ignorable="d" 
      d:DesignHeight="1200" d:DesignWidth="1920"
      Title="SurrenderMailInBallotView"
      DataContext="{Binding SurrenderMailInBallot, Source={StaticResource Locator}}"
      Background="{StaticResource Gray8Brush}"
      Loaded="Page_OnLoaded">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="200*" />
            <RowDefinition Height="600" />
            <RowDefinition Height="auto" />
        </Grid.RowDefinitions>

        <!-- Page Title Row -->
        <StackPanel Grid.Row="0" Background="{StaticResource Gray8Brush}" Margin="0,0,50,30">
            <Label HorizontalAlignment="Left" VerticalAlignment="Top" Margin="121,60,0,0" Content="{Binding PageTitle}" >
                <Label.Style>
                    <Style TargetType="Label" BasedOn="{StaticResource Body1SemiBoldFont}">
                    </Style>
                </Label.Style>
            </Label>
        </StackPanel>

        <StackPanel Grid.Row="1">
            <TextBlock HorizontalAlignment="Center" Text="{Binding LabelContent}" VerticalAlignment="Top" Margin="64,10,0,50" Height="120" Style="{StaticResource Display2SemiBoldTextBlock}" />
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Border Margin="10,0,10,0" >
                    <Grid>
                        <ToggleButton HorizontalAlignment="Center" VerticalAlignment="Center" Style="{StaticResource PrimaryLargeToggleButton}" Focusable="false" IsChecked="{Binding ToggleYes, Mode=TwoWay}" >
                            <TextBlock HorizontalAlignment="Center" TextAlignment="Center" TextWrapping="Wrap">YES</TextBlock>
                        </ToggleButton>
                    </Grid>
                </Border>
                <Border Margin="10,0,10,0" >
                    <Grid>
                        <ToggleButton HorizontalAlignment="Center" VerticalAlignment="Center" Style="{StaticResource PrimaryLargeToggleButton}" Focusable="false" IsChecked="{Binding ToggleNo, Mode=TwoWay}" >
                            <TextBlock HorizontalAlignment="Center" TextAlignment="Center" TextWrapping="Wrap">NO</TextBlock>
                        </ToggleButton>
                    </Grid>
                </Border>
            </StackPanel>
        </StackPanel>

        <!-- Buttons -->
        <Grid Grid.Row="2" Background="White">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="auto" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>
            <Button Grid.Column="0"
                    Content="{Binding BackLabel, FallbackValue='Back'}"
                    Margin="40"
                    userControls:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding BackCommand}">
                <Button.Style>
                    <Style TargetType="Button" BasedOn="{StaticResource SecondaryLargeButton}" />
                </Button.Style>
            </Button>
            <Button Grid.Column="1"
                    HorizontalAlignment="Right"
                    Content="{Binding NextLabel, FallbackValue='Next'}"
                    Margin="40"
                    userControls:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding NextCommand}"
                    IsEnabled="{Binding NextEnabled}">
                <Button.Style>
                    <Style TargetType="Button" BasedOn="{StaticResource PrimaryLargeButton}" />
                </Button.Style>
            </Button>
        </Grid>
    </Grid>
</Page>
