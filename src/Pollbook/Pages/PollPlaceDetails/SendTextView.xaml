<Page x:Class="Pollbook.Pages.PollPlaceDetails.SendTextView"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:ui="clr-namespace:Pollbook.UserControls"
      xmlns:local="clr-namespace:Pollbook.Pages.PollPlaceDetails"
      mc:Ignorable="d" 
      d:DesignHeight="1200" d:DesignWidth="1920"
      DataContext="{Binding SendText, Source={StaticResource Locator}}">
    <Grid>
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <Grid Grid.Column="0" Background="{StaticResource BackgroundBrush}">
                <StackPanel Margin="60,50">
                    <Label Content="Send Text" Style="{StaticResource StandardTitle}" Margin="0,0,0,90" />
                    <Label Content="Message Preview" Style="{StaticResource StandardLightLabel}" FontWeight="Normal" Foreground="{StaticResource Gray2Brush}" Margin="0,0,0,23" />
                    <Border CornerRadius="3" Height="400" BorderThickness="2" BorderBrush="{StaticResource Gray5Brush}">
                        <StackPanel Margin="40,33">
                            <TextBlock Text="{Binding TextMessage}" TextWrapping="Wrap">
                                <TextBlock.Style>
                                    <Style TargetType="TextBlock" BasedOn="{StaticResource Display3BoldTextBlock}" />
                                </TextBlock.Style>
                            </TextBlock>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </Grid>

            <Grid Grid.Column="1" Background="White">
                <StackPanel>
                    <ui:EditListItem Grid.Column="0" x:Name="eliCellPhoneNumber" DataContext="{Binding CellPhoneNumber}" Margin="60,217,60,0">
                        <ui:EditListItem.ContentOneTemplate>
                            <DataTemplate>
                                <ui:MaskedTextBox Text="{Binding Value}" Mask="(*************" PromptChar=" " InputScope="TelephoneNumber" />
                            </DataTemplate>
                        </ui:EditListItem.ContentOneTemplate>
                    </ui:EditListItem>
                </StackPanel>
            </Grid>
        </Grid>

        <StackPanel Grid.Column="2" Margin="0,40,40,0" Orientation="Horizontal" HorizontalAlignment="Right" VerticalAlignment="Top">
            <Button Content="{Binding CancelLabel}"
                    Style="{StaticResource SecondarySmallButton}"
                    Width="180"
                    ui:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding CancelCommand}"
                    Margin="0,0,19,0" />
            <Button Content="{Binding SendLabel}"
                    Style="{StaticResource PrimarySmallButton}"
                    Width="180"
                    ui:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding SendCommand}" />
        </StackPanel>
    </Grid>
</Page>
