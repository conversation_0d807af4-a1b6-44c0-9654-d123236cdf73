using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace Pollbook.Pages.SelectBallotType
{
    /// <summary>
    /// Interaction logic for ExpressVoteActivationCardView.xaml
    /// </summary>
    public partial class ExpressVoteActivationCardView : Page,IContextView
    {
        public bool IsModal => true;
        public SolidColorBrush PrimaryBackgroundBrush => (SolidColorBrush)Application.Current.FindResource("Gray8Brush");
        public ExpressVoteActivationCardView()
        {
            InitializeComponent();

            // Pre-loads the ViewModel that listens for messages
            ViewModelLocator locator = new ViewModelLocator();
            var expressVoteCardViewModel = locator.ExpressVoteActivationCardViewModel;
        }
    }
}
