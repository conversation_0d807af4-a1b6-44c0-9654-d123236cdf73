<Page x:Class="Pollbook.Pages.SelectBallotType.SelectBallotTypeView"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:core="clr-namespace:Pollbook.UICore"
      xmlns:userControls="clr-namespace:Pollbook.UserControls"
      mc:Ignorable="d" 
      d:DesignHeight="1200" d:DesignWidth="1920"
      DataContext="{Binding SelectBallotTypeViewModel, Source={StaticResource Locator}}"
      Background="{StaticResource Gray8Brush}"
      Loaded="Page_Loaded"
      Title="SelectBallotTypeView">
    <Page.Resources>
        <core:BooleanToVisibilityConverter x:Key="OnVisibilityConverter" True="Visible" False="Collapsed" />
    </Page.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="auto" />
        </Grid.RowDefinitions>

        <Grid Grid.Row="0" >
            <Label HorizontalAlignment="Left" Style="{StaticResource Body1SemiBoldFont}" Content="{Binding LabelContent}" VerticalAlignment="Top" Margin="107,40,0,0"/>
            <Label Content="Select Ballot Type" Style="{StaticResource Display2SemiBoldFont}" HorizontalAlignment="Left" VerticalAlignment="Top" Margin="107,96,0,0" />
        </Grid>
        <Grid Grid.Row="1" Margin="0,0,0,80">
          <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Border Margin="10,0,10,0" Visibility="{Binding ExpressVoteEnabled, Converter={StaticResource OnVisibilityConverter}}">
                    <Grid>
                        <ToggleButton HorizontalAlignment="Center" 
                                      VerticalAlignment="Center" 
                                      Style="{StaticResource PrimaryLargeToggleButton}" 
                                      Focusable="false" 
                                      IsChecked="{Binding ExpressVoteActivationIsChecked, Mode=TwoWay}">
                            <ToggleButton.Content>
                                <ItemsControl ItemsSource="{Binding ExpressVoteLabelText}">
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <TextBlock HorizontalAlignment="Center" Text="{Binding}" />
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>
                            </ToggleButton.Content>
                        </ToggleButton>
                    </Grid>
                </Border>
                <Border Margin="10,0,10,0" Visibility="{Binding BalotarEnabled, Converter={StaticResource OnVisibilityConverter}}">
                    <Grid>
                        <ToggleButton HorizontalAlignment="Center" 
                                      VerticalAlignment="Center" 
                                      Style="{StaticResource PrimaryLargeToggleButton}" 
                                      Focusable="false" IsChecked="{Binding BODIsChecked, Mode=TwoWay}">
                            <TextBlock HorizontalAlignment="Center" TextAlignment="Center" TextWrapping="Wrap">BOD Ballot</TextBlock>
                        </ToggleButton>
                    </Grid>
                </Border>
                <Border Margin="10,0,10,0"  
                        Visibility="{Binding PaperBallotEnabled, Converter={StaticResource OnVisibilityConverter}}">
                    <Grid>
                        <ToggleButton HorizontalAlignment="Center" VerticalAlignment="Center" 
                                      Style="{StaticResource PrimaryLargeToggleButton}" 
                                      Focusable="false" 
                                      IsChecked="{Binding PaperBallotIsChecked, Mode=TwoWay}">
                            <ToggleButton.Content>
                                <ItemsControl ItemsSource="{Binding PaperLabelText}">
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <TextBlock HorizontalAlignment="Center" Text="{Binding}" />
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>
                            </ToggleButton.Content>
                        </ToggleButton>
                    </Grid>
                </Border>
            </StackPanel>
        </Grid>

        <!-- Buttons -->
        <Grid Grid.Row="2" Grid.Column="0"  Background="White">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="auto" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>
            <Button Grid.Column="0"
                    Content="{Binding BackLabel}"
                    Margin="40"
                    userControls:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding BackCommand}">
                <Button.Style>
                    <Style TargetType="Button" BasedOn="{StaticResource SecondaryLargeButton}" />
                </Button.Style>
            </Button>
            <Button Grid.Column="1"
                    HorizontalAlignment="Right"
                    Margin="40"
                    userControls:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding IssueBallotCommandAsync}"
                    IsEnabled="{Binding NextEnabled}" >
                <Button.Style>
                    <Style TargetType="Button" BasedOn="{StaticResource PrimaryLargeButton}">
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding IsReissuingBallot}" Value="False">
                                <Setter Property="Content" Value="Issue Ballot"></Setter>
                            </DataTrigger>
                            <DataTrigger Binding="{Binding IsReissuingBallot}" Value="True">
                                <Setter Property="Content" Value="Reissue Ballot"></Setter>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </Button.Style>
            </Button>
        </Grid>
    </Grid>
</Page>
