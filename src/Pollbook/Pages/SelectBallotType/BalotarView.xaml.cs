using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace Pollbook.Pages.SelectBallotType
{
    // [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class BalotarView : Page, IContextView
    {
        public bool IsModal => false;
        public SolidColorBrush PrimaryBackgroundBrush => (SolidColorBrush)Application.Current.FindResource("Gray8Brush");

        public BalotarView()
        {
            InitializeComponent();
            ViewModelLocator locator = new ViewModelLocator();
            var balotarViewModel = locator.BalotarViewModel;
        }
    }
}