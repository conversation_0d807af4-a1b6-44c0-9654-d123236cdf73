using ESS.Pollbook.ViewModel.SelectBallotType;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace Pollbook.Pages.SelectBallotType
{
    /// <summary>
    /// Interaction logic for SelectBallotTypeView.xaml
    /// </summary>

    public partial class SelectBallotTypeView : Page, IContextView
    {
        public bool IsModal => false;
        public SolidColorBrush PrimaryBackgroundBrush => (SolidColorBrush)Application.Current.FindResource("Gray8Brush");

        public SelectBallotTypeView()
        {
            InitializeComponent();
        }

        private void Page_Loaded(object sender, RoutedEventArgs e)
        {
            ((SelectBallotTypeViewModel)DataContext).PageIsLoaded();
        }        
    }
}
