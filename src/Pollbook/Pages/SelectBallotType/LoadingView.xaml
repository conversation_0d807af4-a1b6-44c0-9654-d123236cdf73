<Page x:Class="Pollbook.Pages.SelectBallotType.LoadingView"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:Pollbook.Pages.SelectBallotType"
      xmlns:ui="clr-namespace:Pollbook.UserControls"
      mc:Ignorable="d" 
      Loaded="Page_Loaded"
      DataContext="{Binding Loading, Source={StaticResource Locator}}"
      Background="{StaticResource Gray8Brush}"
      d:DesignHeight="1200" d:DesignWidth="1920">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*" />
            <RowDefinition Height="2*" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>
        <Grid Grid.Row="0">
            <Label Content="{Binding LabelContent}" Style="{StaticResource Body1SemiBoldFont}" HorizontalAlignment="Left" VerticalAlignment="Top" Margin="100,60,0,0" />
            <Label Content="Print Ballot on Demand" Style="{StaticResource Display2SemiBoldFont}" HorizontalAlignment="Left" VerticalAlignment="Top" Margin="100,120,0,0" />
        </Grid>
        <Grid Grid.Row="1">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Top" Margin="0,20,0,0">
                <ui:Spinner />
                <TextBlock Text="Printing..." Style="{StaticResource Display2SemiBoldTextBlock}" HorizontalAlignment="Center" Margin="0,0,0,30" />
            </StackPanel>
        </Grid>
        <Grid Grid.Row="2" Background="White">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="auto" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>
        </Grid>
    </Grid>
</Page>
