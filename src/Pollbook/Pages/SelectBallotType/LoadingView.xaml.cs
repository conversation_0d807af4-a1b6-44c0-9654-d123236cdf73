using ESS.Pollbook.ViewModel.SelectBallotType;
using System.Windows;
using System.Windows.Controls;

namespace Pollbook.Pages.SelectBallotType
{
    /// <summary>
    /// Interaction logic for LoadingView.xaml
    /// </summary>
    public partial class LoadingView : Page
    {
        public bool IsModal => true;
        public LoadingView()
        {
            InitializeComponent();
        }

        private async void Page_Loaded(object sender, RoutedEventArgs e)
        {
            await ((LoadingViewModel)DataContext).PageIsLoaded();
        }
    }
}
