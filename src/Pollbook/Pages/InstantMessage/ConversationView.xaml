<Page x:Class="Pollbook.Pages.InstantMessage.ConversationView"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local1="clr-namespace:Pollbook.UICore"
      xmlns:userControls="clr-namespace:Pollbook.UserControls"
      DataContext="{Binding Conversation, Source={StaticResource Locator}}"
      Loaded="Page_Loaded"
      mc:Ignorable="d" 
      d:DesignHeight="1450" d:DesignWidth="1800"
      Title="Conversation">

    <Page.Resources>
        <Style x:Key="ConversationBubbleTextBox" TargetType="{x:Type TextBox}">
            <Setter Property="FontSize" Value="30" />
            <Setter Property="FontWeight" Value="DemiBold" />
            <Setter Property="BorderThickness" Value="0" />
            <Setter Property="IsReadOnly" Value="True" />
            <Setter Property="KeyboardNavigation.TabNavigation" Value="None"/>
            <Setter Property="FocusVisualStyle" Value="{x:Null}"/>
            <Setter Property="SelectionBrush" Value="Transparent" />
        </Style>
        <Style x:Key="ConversationBubbleRightTextBox" BasedOn="{StaticResource ConversationBubbleTextBox}" TargetType="{x:Type TextBox}">
            <Setter Property="HorizontalAlignment" Value="Right" />
            <Style.Resources>
                <Style TargetType="{x:Type Border}">
                    <Setter Property="CornerRadius" Value="3, 3, 0, 0"/>
                </Style>
            </Style.Resources>
        </Style>
        <Style x:Key="ConversationBubbleLeftTextBox" BasedOn="{StaticResource ConversationBubbleTextBox}" TargetType="{x:Type TextBox}">
            <Setter Property="HorizontalAlignment" Value="Left" />
            <Style.Resources>
                <Style TargetType="{x:Type Border}">
                    <Setter Property="CornerRadius" Value="3, 3, 0, 0"/>
                </Style>
            </Style.Resources>
        </Style>
        <Style x:Key="ConversationDateTime" TargetType="TextBlock">
            <Setter Property="FontSize" Value="24" />
            <Setter Property="FontWeight" Value="DemiBold" />
            <Setter Property="Foreground" Value="{StaticResource Gray3Brush}" />
        </Style>
        <Style x:Key="ConversationTextBox" TargetType="TextBox">
            <Setter Property="FontFamily" Value="Noto Sans Display" />
            <Setter Property="FontSize" Value="30" />
            <Setter Property="Padding" Value="16,16,0,0" />
            <Setter Property="VerticalAlignment" Value="Center" />
            <Setter Property="BorderThickness" Value="4" />
            <Setter Property="BorderBrush" Value="{StaticResource SecondaryDarkBlueBrush}" />
            <Style.Resources>
                <Style TargetType="{x:Type Border}">
                    <Setter Property="CornerRadius" Value="3"/>
                </Style>
            </Style.Resources>
        </Style>
        <local1:BooleanToVisibilityConverter x:Key="CalloutVisibilityConverter" True="Visible" False="Hidden" />
    </Page.Resources>

    <Grid Background="{StaticResource BackgroundBrush}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <StackPanel Grid.Row="0" Orientation="Horizontal" >
            <Button HorizontalAlignment="Left" 
                    VerticalAlignment="Top" 
                    Margin="80,36,0,0" 
                    userControls:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding BackCommand}" >
                <Button.Template>
                    <ControlTemplate TargetType="Button">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="1"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <ContentPresenter Grid.Column="2" />
                            <Viewbox Grid.Column="0" Stretch="Uniform" Width="24" Height="24" Margin="0,32,-15,0" >
                                <Path Style="{StaticResource LinkArrowIcon}" Fill="{StaticResource BluetifulBrush}">
                                    <Path.RenderTransform>
                                        <RotateTransform Angle="180" />
                                    </Path.RenderTransform>
                                </Path>
                            </Viewbox>
                        </Grid>
                    </ControlTemplate>
                </Button.Template>
                <TextBlock Text="{Binding BackLabel}" Foreground="{StaticResource BluetifulBrush}" Height="61">
                    <TextBlock.Style>
                        <Style TargetType="TextBlock" BasedOn="{StaticResource BoldTextBlock}" />
                    </TextBlock.Style>
                </TextBlock>
            </Button>
            <Label HorizontalAlignment="Left" VerticalAlignment="Top" Style="{StaticResource LargeBoldTitleLabel}" Content="{Binding MessageTitle}" Margin="30,20,0,20"></Label>
        </StackPanel>

        <Grid Grid.Row="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="auto" />
                <RowDefinition Height="auto" />
                <RowDefinition Height="auto" />
                <RowDefinition Height="340" />
            </Grid.RowDefinitions>
            <Grid Grid.Row="0" Margin="70,0,88,0" Background="White">
                <TextBlock Text="{Binding ConversationTime}" HorizontalAlignment="Center" Style="{StaticResource ConversationDateTime}" Padding="0,20,0,10" />
            </Grid>
            <StackPanel>
                <ScrollViewer Name="ConversationScrollViewer" Grid.Row="1" Height="440" CanContentScroll="True" PanningMode="Both" Margin="0 0 90 0">
                    <ScrollViewer.Resources>
                        <Style TargetType="ScrollBar" BasedOn="{StaticResource CustomVerticalScrollBarStyle}"/>
                    </ScrollViewer.Resources>
                    <ItemsControl HorizontalAlignment="Stretch" BorderThickness="0" VerticalAlignment="Stretch" Background="White" Margin="70,0,0,0"
                                    ItemsSource="{Binding Conversation}" SizeChanged="ItemsControl_SizeChanged" >
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>

                                <Grid >
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="auto" />
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="auto" />
                                    </Grid.ColumnDefinitions>
                                    <ContentControl Grid.Column="0" Name="ThemIcon" VerticalAlignment="Bottom" Template="{StaticResource InstantMessageThemIcon}" Margin="30,0,0,30"/>
                                    <Grid Grid.Column="1" >
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="auto" />
                                            <RowDefinition Height="60" />
                                        </Grid.RowDefinitions>
                                        <TextBox Grid.Row="0" Text="{Binding Text}" Name="MessageTextBlock" Margin="25,10,25,10" Padding="15,15,15,15" TextWrapping="Wrap" />
                                        <TextBlock Grid.Row="1" Style="{StaticResource ConversationDateTime}" Text="{Binding MessageTime}" Name="MessageDateTime" Margin="25,0,25,0" />
                                    </Grid>
                                    <ContentControl Grid.Column="2" Name="MeIcon" VerticalAlignment="Bottom" Template="{StaticResource InstantMessageMeIcon}" Margin="0,0,30,30"/>
                                </Grid>

                                <DataTemplate.Triggers>
                                    <DataTrigger Binding="{Binding Type}" Value="Sender">
                                        <Setter TargetName="MessageTextBlock" Property="Style" Value="{StaticResource ConversationBubbleRightTextBox}" />
                                        <Setter TargetName="MessageDateTime" Property="HorizontalAlignment" Value="Right" />
                                        <Setter TargetName="MessageTextBlock" Property="Background" Value="{StaticResource SecondaryLightBlueBrush}" />
                                        <Setter TargetName="ThemIcon" Property="Visibility" Value="Collapsed" />
                                        <Setter TargetName="MeIcon" Property="Visibility" Value="Visible" />
                                    </DataTrigger>
                                    <DataTrigger Binding="{Binding Type}" Value="Receiver">
                                        <Setter TargetName="MessageTextBlock" Property="Style" Value="{StaticResource ConversationBubbleLeftTextBox}" />
                                        <Setter TargetName="MessageDateTime" Property="HorizontalAlignment" Value="Left" />
                                        <Setter TargetName="MessageTextBlock" Property="Background" Value="{StaticResource Gray6Brush}" />
                                        <Setter TargetName="ThemIcon" Property="Visibility" Value="Visible" />
                                        <Setter TargetName="MeIcon" Property="Visibility" Value="Collapsed" />
                                    </DataTrigger>
                                </DataTemplate.Triggers>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                </ScrollViewer>
            </StackPanel>
            <Grid Grid.Row="2" Margin="70,0,88,0" Background="White">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="auto" />
                </Grid.ColumnDefinitions>
                <Grid Grid.Column="0">
                    <TextBox Grid.Column="0" Name="ConversationText" Margin="50,20,50,20" Style="{StaticResource ConversationTextBox}" Height="120" VerticalAlignment="Stretch"
                        Loaded="TextBox_Loaded" Unloaded="TextBox_Unloaded" Visibility="{Binding ImConnected, Converter={StaticResource CalloutVisibilityConverter}}"
                        Text="{Binding FollowOnMessageText, UpdateSourceTrigger=PropertyChanged}" KeyUp="TextBox_KeyUp" AcceptsReturn="true" TextWrapping="Wrap" MaxLength="255"
                             PreviewMouseDown="TextBox_PreviewMouseDown" PreviewTouchDown="TextBox_PreviewTouchDown"/>
                    <Canvas>
                        <Border x:Name="Caret" Visibility="Collapsed" Canvas.Left="0" Canvas.Top="0" Width="2" Background="Black" >
                            <Border.Triggers>
                                <EventTrigger RoutedEvent="Border.Loaded">
                                    <BeginStoryboard>
                                        <Storyboard x:Name="CaretStoryBoard" RepeatBehavior="Forever">
                                            <ColorAnimationUsingKeyFrames Storyboard.TargetProperty="Background.Color" Duration="0:0:0:1" FillBehavior="HoldEnd">
                                                <ColorAnimationUsingKeyFrames.KeyFrames>
                                                    <DiscreteColorKeyFrame KeyTime="0:0:0.500" Value="Transparent" />
                                                    <DiscreteColorKeyFrame KeyTime="0:0:0.000" Value="Black" />
                                                </ColorAnimationUsingKeyFrames.KeyFrames>
                                            </ColorAnimationUsingKeyFrames>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </EventTrigger>
                            </Border.Triggers>
                        </Border>
                    </Canvas>
                </Grid>
                <Button Grid.Column="1"
                        Margin="0,20,70,20"
                        Content="{Binding SendLabel}"
                        userControls:ButtonHelper.DisableMultipleClicks="True"
                        Command="{Binding SubmitCommand}"
                        Style="{StaticResource PrimarySmallButton}"
                        IsEnabled="{Binding SubmitIsEnabled}" 
                        Width="180"
                        Click="Button_Click"
                        Visibility="{Binding ImConnected, Converter={StaticResource CalloutVisibilityConverter}}" />
                <TextBlock Grid.ColumnSpan="2" Text="Live Chat connection was interrupted. Re-connecting…" Style="{StaticResource TextBlockStandardLightLabel}" 
                           HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="Red"
                           Visibility="{Binding ImDisconnected, Converter={StaticResource CalloutVisibilityConverter}}"/>
            </Grid>
        </Grid>


    </Grid>
</Page>
