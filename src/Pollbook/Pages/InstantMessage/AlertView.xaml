<Page x:Class="Pollbook.Pages.InstantMessage.AlertView"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:Pollbook.Pages.InstantMessage"
      xmlns:userControls="clr-namespace:Pollbook.UserControls"
      mc:Ignorable="d" 
      DataContext="{Binding Alert, Source={StaticResource Locator}}"
      d:DesignHeight="1450" d:DesignWidth="1800"
      Title="AlertView">

    <Grid Background="White" Margin="100,0">
        <Grid.RowDefinitions>
            <RowDefinition Height="auto" />
            <RowDefinition Height="175" />
        </Grid.RowDefinitions>

        <StackPanel Margin="0,0,0,80">
            <Viewbox Stretch="Uniform" VerticalAlignment="Center" HorizontalAlignment="Center" Width="100" Margin="0,0,0,60">
                <Path Style="{StaticResource WarningIconBlue}" Stretch="Fill" Fill="{StaticResource SecondaryDarkYellowBrush}" />
            </Viewbox>
            <TextBlock Style="{StaticResource Display1TextBlock}" Margin="0,0,0,30" Text="{Binding Title}" HorizontalAlignment="Center" TextAlignment="Center" TextWrapping="Wrap" />
            <TextBlock Style="{StaticResource StandardTextBlock}" Foreground="{StaticResource Gray2Brush}" Text="{Binding Message}" HorizontalAlignment="Center" TextAlignment="Center" TextWrapping="Wrap" />
        </StackPanel>

        <Button Grid.Row="1"
                Style="{StaticResource PrimaryLargeButton}" 
                Content="{Binding OKLabel}" 
                userControls:ButtonHelper.DisableMultipleClicks="True"
                Command="{Binding OkCommand}" 
                HorizontalAlignment="Center" Width="300" Height="140"/>

    </Grid>
</Page>
