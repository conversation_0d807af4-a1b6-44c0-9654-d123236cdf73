<Page x:Class="Pollbook.Pages.InstantMessage.AlertDetailView"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:Pollbook.Pages.InstantMessage"
      xmlns:userControls="clr-namespace:Pollbook.UserControls"
      DataContext="{Binding AlertDetail, Source={StaticResource Locator}}"
      Loaded="Page_Loaded"
      mc:Ignorable="d" 
      d:DesignHeight="450" d:DesignWidth="800"
      Title="AlertDetail">

    <Grid Background="{StaticResource Gray7Brush}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <StackPanel Grid.Row="0" Orientation="Horizontal" >
            <Label HorizontalAlignment="Left" VerticalAlignment="Top" Style="{StaticResource LargeBoldTitleLabel}" Content="{Binding MessageTitle}" Margin="70,40,0,30"></Label>
        </StackPanel>

        <Border Grid.Row="1" CornerRadius="3" Margin="70,0,70,50" Background="White">
            <StackPanel Grid.Row="1" Height="520" >
                <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled" CanContentScroll="True" Height="380" >
                    <TextBlock Text="{Binding AlertMessage}" Style="{StaticResource BoldTextBlock}" Margin="50,50,50,0" TextWrapping="Wrap"  />
                </ScrollViewer>
                <TextBlock Text="{Binding MessageTime}" Style="{StaticResource StandardTextBlock}" Margin="50,50,50,20" />
            </StackPanel>
        </Border>

        <Grid Grid.Row="2" Background="White">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="auto" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <Button Grid.Column="0"
                    Content="{Binding BackLabel}"
                    Margin="40"
                    HorizontalAlignment="Left"
                    VerticalAlignment="Bottom" 
                    Style="{StaticResource SecondaryLargeButton}"
                    userControls:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding BackCommand}" />

        </Grid>

    </Grid>
</Page>
