<Page x:Class="Pollbook.Pages.InstantMessage.AlertsAndConversationsView"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:i="http://schemas.microsoft.com/expression/2010/interactivity"
      xmlns:local="clr-namespace:Pollbook.Pages.InstantMessage"
      xmlns:userControls="clr-namespace:Pollbook.UserControls"
      DataContext="{Binding AlertsAndConversations, Source={StaticResource Locator}}"
      Loaded="Page_Loaded"
      mc:Ignorable="d" 
      d:DesignHeight="450" d:DesignWidth="800"
      Title="AlertsAndConversations">

    <Page.Resources>

        <Style TargetType="ListBoxItem" BasedOn="{StaticResource ListBoxContainerNoHighlight}">
            <Style.Triggers>
                <Trigger Property="ItemsControl.AlternationIndex" Value="0">
                    <Setter Property="Background" Value="White" />
                </Trigger>
                <Trigger Property="ItemsControl.AlternationIndex" Value="1">
                    <Setter Property="Background" Value="{StaticResource Gray6Brush}" />
                </Trigger>
            </Style.Triggers>
        </Style>
        
        <LinearGradientBrush x:Key="Gray1GradientBrush" MappingMode="Absolute" StartPoint="0,0" EndPoint="800,0">
            <GradientStop Color="{StaticResource Gray1Color}" Offset="0.8" />
            <GradientStop Color="Transparent" Offset="1.0" />
        </LinearGradientBrush>
        
        <Style x:Key="StandardHeaderTextBlock" TargetType="TextBlock">
            <Setter Property="TextBlock.Foreground" Value="{StaticResource Gray1GradientBrush}" />
            <Setter Property="TextBlock.FontFamily" Value="Noto Sans Display" />
            <Setter Property="TextBlock.FontSize" Value="30" />
            <Setter Property="Width" Value="800" />
        </Style>

        <Style x:Key="BoldHeaderTextBlock" TargetType="TextBlock">
            <Setter Property="TextBlock.Foreground" Value="{StaticResource Gray1GradientBrush}" />
            <Setter Property="TextBlock.FontFamily" Value="Noto Sans Display" />
            <Setter Property="TextBlock.FontSize" Value="30" />
            <Setter Property="Width" Value="800" />
        </Style>

    </Page.Resources>

    <Grid Background="{StaticResource Gray7Brush}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <Label Grid.Row="0" HorizontalAlignment="Left" VerticalAlignment="Top" Style="{StaticResource LargeBoldTitleLabel}" Content="Messages" Margin="80,20,0,20"></Label>

        <StackPanel Grid.Row="1" >
            <Grid Name="Headings">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="986" />
                    <ColumnDefinition Width="400" />
                    <ColumnDefinition Width="400" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <Label Grid.Column="0" Content="Message" Style="{StaticResource StandardLightLabel}" HorizontalAlignment="Left" Margin="100,0,0,0"/>
                <Label Grid.Column="1" Content="From" Style="{StaticResource StandardLightLabel}" HorizontalAlignment="Left" Margin="45,0,0,0"/>
                <Label Grid.Column="2" Content="Time Sent" Style="{StaticResource StandardLightLabel}" HorizontalAlignment="Left" Margin="45,0,0,0"/>
            </Grid>
            <ListBox ScrollViewer.VerticalScrollBarVisibility="Auto"  ScrollViewer.HorizontalScrollBarVisibility="Disabled" ScrollViewer.CanContentScroll="True" 
                         Margin="70,0,70,30" x:Name="ConversationList" ItemsSource="{Binding Conversations}" BorderThickness="0" 
                         Height="600" Background="{StaticResource Gray7Brush}" >
                <ListBox.ItemContainerStyle>
                    <Style TargetType="ListBoxItem" BasedOn="{StaticResource ListBoxContainerNoHighlightNoMargin}">
                        <Setter Property="Background" Value="White" />
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding HasUnreadMessage}" Value="True">
                                <Setter Property="Background" Value="#FEF5DE"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </ListBox.ItemContainerStyle>
                    <ListBox.ItemTemplate>
                        <DataTemplate>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="32" />
                                    <ColumnDefinition Width="900" />
                                    <ColumnDefinition Width="400" />
                                    <ColumnDefinition Width="310" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>
                                <Viewbox Grid.Column="0" Stretch="Uniform" VerticalAlignment="Center" HorizontalAlignment="Center" Width="24">
                                    <Path Name="MessageTypeIndicator" Fill="{StaticResource BluetifulBrush}" />
                                </Viewbox>
                                <TextBlock Grid.Column="1" Text="{Binding Header}" Name="HeaderTextBlock" Style="{StaticResource StandardHeaderTextBlock}" HorizontalAlignment="Left" Margin="20,0,0,0"/>
                                <TextBlock Grid.Column="2" Text="{Binding Group}" Name="GroupTextBlock" Style="{StaticResource StandardTextBlock}" />
                                <TextBlock Grid.Column="3" Text="{Binding ConversationDatetime}" Name="DateTimeTextBlock" Style="{StaticResource StandardTextBlock}" />
                                <Viewbox Grid.Column="4" Stretch="Uniform" VerticalAlignment="Center" HorizontalAlignment="Center" Width="16" >
                                <Path Style="{StaticResource LinkArrowIcon}" Fill="{StaticResource BluetifulBrush}" />
                                </Viewbox>
                            </Grid>
                        <DataTemplate.Triggers>
                            <DataTrigger Binding="{Binding HasUnreadMessage}" Value="True" >
                                <Setter TargetName="HeaderTextBlock" Property="Style" Value="{StaticResource BoldHeaderTextBlock}" />
                                <Setter TargetName="GroupTextBlock" Property="Style" Value="{StaticResource BoldTextBlock}" />
                                <Setter TargetName="MessageTypeIndicator" Property="Fill" Value="{StaticResource SecondaryDarkYellowBrush}" />
                            </DataTrigger>
                            <DataTrigger Binding="{Binding Type}" Value="Broadcast" >
                                <Setter TargetName="MessageTypeIndicator" Property="Style" Value="{StaticResource AlertBell}" />
                            </DataTrigger>
                            <DataTrigger Binding="{Binding Type}" Value="Ticket" >
                                <Setter TargetName="MessageTypeIndicator" Property="Style" Value="{StaticResource LiveChatSymbol}"/>
                            </DataTrigger>
                        </DataTemplate.Triggers>
                    </DataTemplate>
                    </ListBox.ItemTemplate>

                    <i:Interaction.Triggers>
                        <i:EventTrigger EventName="SelectionChanged">
                            <i:InvokeCommandAction Command="{Binding SelectConversationCommand}" CommandParameter="{Binding ElementName=ConversationList, Path=SelectedItem}"></i:InvokeCommandAction>
                        </i:EventTrigger>
                    </i:Interaction.Triggers>
                </ListBox>
           
            <TextBlock Name="NoItems" Text="There are no messages to display." Style="{StaticResource StandardTextBlock}" HorizontalAlignment="Center" Margin="0,50,0,0"/>
        </StackPanel>

        <Grid Grid.Row="3" Background="White">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="auto" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <Button Grid.Column="1"
                    Content="{Binding ComposeLabel}"
                    Margin="40"
                    HorizontalAlignment="Right"
                    VerticalAlignment="Bottom" 
                    Style="{StaticResource PrimaryLargeButton}"
                    userControls:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding CreateTicketCommand}" />
        </Grid>
    </Grid>
</Page>
