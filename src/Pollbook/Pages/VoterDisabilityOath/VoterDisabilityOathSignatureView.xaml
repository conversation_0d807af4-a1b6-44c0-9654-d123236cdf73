<Page x:Class="Pollbook.Pages.VoterDisabilityOath.VoterDisabilityOathSignatureView"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:Pollbook.Pages.VoterDisabilityOath"
      xmlns:uiCore="clr-namespace:Pollbook.UICore"
      xmlns:userControls="clr-namespace:Pollbook.UserControls"
      mc:Ignorable="d" 
      DataContext="{Binding VoterDisabilityOathSignature, Source={StaticResource Locator}}"
      d:DesignHeight="1000" d:DesignWidth="1600"
      Background="{StaticResource Gray8Brush}" 
      Loaded="Page_Loaded"
      Initialized="Page_InitializedAsync">
    <Page.Resources>
        <Style TargetType="Label" BasedOn="{StaticResource StandardTitle}" />
        <Style TargetType="{x:Type TabPanel}">
            <Setter Property="Background" Value="Black"></Setter>
        </Style>

        <uiCore:BooleanToVisibilityConverter x:Key="BooleanToVisibility" True="Visible" False="Hidden" />
        <uiCore:BooleanToVisibilityConverter x:Key="CalloutVisibilityConverter" True="Visible" False="Collapsed" />

        <Style x:Key="RotateButton" TargetType="{x:Type Button}">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type Button}">
                        <Border Name="Border" CornerRadius="2" BorderThickness="1" Background="Transparent" BorderBrush="Transparent">
                            <ContentPresenter Margin="2"
                         HorizontalAlignment="Center"
                         VerticalAlignment="Center" />
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsKeyboardFocused" Value="true">
                                <Setter TargetName="Border" Property="BorderBrush" Value="Transparent" />
                            </Trigger>
                            <Trigger Property="IsDefaulted" Value="true">
                                <Setter TargetName="Border" Property="BorderBrush" Value="Transparent" />
                            </Trigger>
                            <Trigger Property="IsMouseOver" Value="true">
                                <Setter TargetName="Border" Property="Background" Value="Transparent" />
                            </Trigger>
                            <Trigger Property="IsPressed" Value="true">
                                <Setter TargetName="Border" Property="Background" Value="Transparent" />
                                <Setter TargetName="Border" Property="BorderBrush" Value="Transparent" />
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="false">
                                <Setter TargetName="Border" Property="Background" Value="Transparent" />
                                <Setter TargetName="Border" Property="BorderBrush" Value="Transparent" />
                                <Setter Property="Foreground" Value="Transparent"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Page.Resources>

    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="0.25*"/>
            <ColumnDefinition Width="3*"/>
            <ColumnDefinition Width="0.05*"/>
            <ColumnDefinition Width="3*"/>
            <ColumnDefinition Width="0.25*"/>
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="198*" />
            <RowDefinition Height="240"/>
        </Grid.RowDefinitions>

        <userControls:LanguageSelector Grid.Row="0" Grid.Column="0" Grid.ColumnSpan="4" VerticalAlignment="Center" HorizontalAlignment="Right" Margin="0,0,50,0" Visibility="Visible" />
        
        <Grid Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="5">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>

            <StackPanel Grid.Row="1" Orientation="Vertical">
                <Label Style="{StaticResource Display2BoldFont}" Content="{Binding OathHeader}" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                <ScrollViewer
                    MaxHeight="75"
                    Name="tbOathScroller"
                    CanContentScroll="False"
                    HorizontalScrollBarVisibility="Disabled"
                    VerticalScrollBarVisibility="Auto"
                    PanningMode="VerticalOnly">
                    <ScrollViewer.Resources>
                        <Style TargetType="ScrollBar" BasedOn="{StaticResource CustomVerticalScrollBarStyle}"/>
                    </ScrollViewer.Resources>

                    <TextBlock Name="tbOathText"
                       Padding="0,0,10,10"
                       Text="{Binding OathText}"
                       Margin="20,0,20,0"
                       Height="auto"
                       TextWrapping="Wrap"
                       HorizontalAlignment="Center"
                       Style="{StaticResource InstructionalTextBlock}" />
                </ScrollViewer>
            </StackPanel>
        </Grid>

        <Grid Grid.Row="2" Grid.Column="1" Margin="0, 10, 0, 0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>

            <StackPanel Grid.Column="0">
                <Label Style="{StaticResource Body1SemiBoldLight2Font}" Content="Voter Name" />
                <Label Style="{StaticResource Body1BoldFont}">
                    <Label.Content>
                        <TextBlock>
                            <TextBlock.Text>
                                <Binding Path="Voter.FullName" />
                            </TextBlock.Text>
                        </TextBlock>
                    </Label.Content>
                </Label>
            </StackPanel>
            <StackPanel Grid.Column="1">
                <Label Style="{StaticResource Body1SemiBoldLight2Font}" Content="Voter ID" />
                <Label Style="{StaticResource Body1BoldFont}">
                    <Label.Content>
                        <TextBlock>
                            <TextBlock.Text>
                                <Binding Path="Voter.VoterKey" />
                            </TextBlock.Text>
                        </TextBlock>
                    </Label.Content>
                </Label>
            </StackPanel>
            <StackPanel Grid.Column="2">
                <Label Style="{StaticResource Body1SemiBoldLight2Font}" Content="County" />
                <Label Style="{StaticResource Body1BoldFont}">
                    <Label.Content>
                        <TextBlock>
                            <TextBlock.Text>
                                <Binding Path="Voter.CountyName" />
                            </TextBlock.Text>
                        </TextBlock>
                    </Label.Content>
                </Label>
            </StackPanel>
        </Grid>

        <StackPanel Grid.Row="3" Grid.Column="1" Background="{StaticResource Gray7Brush}">
            <Label Style="{StaticResource Body1SemiBoldFont}" Content="My reason(s) for requiring assistance" Margin="10,5" />
            <StackPanel Orientation="Horizontal">
                <Button HorizontalAlignment="Left"
                        Margin="10,5"
                        userControls:ButtonHelper.DisableMultipleClicks="True"
                        Command="{Binding ChooseReasons}" >
                    <Button.Template>
                        <ControlTemplate TargetType="Button">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="10"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <ContentPresenter Grid.Column="0" />

                                <Viewbox Grid.Column="2" Stretch="Uniform" Width="24" Height="36" Margin="0,2,0,0">
                                    <Path Style="{StaticResource LinkArrowIcon}" Fill="{StaticResource SecondaryMediumBlueBrush}" />
                                </Viewbox>
                            </Grid>
                        </ControlTemplate>
                    </Button.Template>
                    <TextBlock Text="{Binding ChooseReasonsLabelText}" Foreground="{StaticResource SecondaryMediumBlueBrush}" Height="36" Margin="0,1,0,0" VerticalAlignment="Top">
                        <TextBlock.Style>
                            <Style TargetType="TextBlock" BasedOn="{StaticResource BoldTextBlock}" />
                        </TextBlock.Style>
                    </TextBlock>
                </Button>
                <Label Style="{StaticResource Body1SemiBoldFont}" Content="{Binding DisabilityReasonsLabelText}" Margin="10,5" />
            </StackPanel>

            <StackPanel Name="spDisabilityOathSignature" Visibility="{Binding DisabilityOathSignatureVisibility, Converter={StaticResource CalloutVisibilityConverter}}" Margin="10 5">
            </StackPanel>

            <Grid Margin="10,5">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>
                <Label Style="{StaticResource Body1SemiBoldFont}" Content="{Binding PersonAssistingLabelText}" HorizontalAlignment="Left" VerticalAlignment="Top" Grid.Row="0" Grid.Column="0" Grid.ColumnSpan="2" />
                <StackPanel Grid.Row="1" Orientation="Horizontal" >
                    <Button VerticalAlignment="Top"
                            HorizontalAlignment="Left"
                            Margin="5,5"
                            userControls:ButtonHelper.DisableMultipleClicks="True"
                            Command="{Binding EnterHere}" >
                        <Button.Template>
                            <ControlTemplate TargetType="Button">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="10"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <ContentPresenter Grid.Column="0" />

                                    <Viewbox Grid.Column="2" Stretch="Uniform" Width="24" Height="36" Margin="0,2,0,0">
                                        <Path Style="{StaticResource LinkArrowIcon}" Fill="{StaticResource SecondaryMediumBlueBrush}" />
                                    </Viewbox>
                                </Grid>
                            </ControlTemplate>
                        </Button.Template>
                        <TextBlock Text="{Binding PersonAssistingVoterButtonLabel, FallbackValue='Enter Here'}" Foreground="{StaticResource SecondaryMediumBlueBrush}" Height="36">
                            <TextBlock.Style>
                                <Style TargetType="TextBlock" BasedOn="{StaticResource BoldTextBlock}" />
                            </TextBlock.Style>
                        </TextBlock>
                    </Button>
                    <TextBlock Text="{Binding VoterAssistantLabelText}" VerticalAlignment="Top" Style="{StaticResource InstructionalTextBlock}" Margin="10,5,0,0" Visibility="{Binding VoterAssistantLabelVisible, Converter={StaticResource BooleanToVisibility}}" TextWrapping="Wrap"/>
                </StackPanel>
            </Grid>
        </StackPanel>

        <StackPanel Name="spAdditionalSignatures" Visibility="{Binding AdditionalSignatureVisibility, Converter={StaticResource CalloutVisibilityConverter}}" Grid.Row="3" Grid.Column="3" Margin="10,0,10,0">
        </StackPanel>

        <!-- Bottom Row with navigational buttons-->
        <Grid Grid.Row="4" Grid.Column="0" Grid.ColumnSpan="5" Background="#ffffff">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="auto" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="auto" />
            </Grid.ColumnDefinitions>
            <Button Content="{Binding BackLabel}"
                    Margin="40"
                    HorizontalAlignment="Left"
                    VerticalAlignment="Bottom"
                    userControls:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding BackCommand}">
                <Button.Style>
                    <Style TargetType="Button" BasedOn="{StaticResource SecondaryLargeButton}" />
                </Button.Style>
            </Button>

            <Button Grid.Column="1"
                    userControls:ButtonHelper.DisableMultipleClicks="True"
                    Click="Flip_Click"
                    Width="140"
                    Height="150"
                    BorderBrush="Transparent"
                    Background="Transparent"
                    Style="{StaticResource RotateButton}"
                    Visibility="{Binding ShowFlipScreen, Converter={StaticResource BooleanToVisibility}}">
                <StackPanel Orientation="Vertical" HorizontalAlignment="Center" VerticalAlignment="Center" Width="175">
                    <Viewbox Stretch="Uniform" VerticalAlignment="Center" HorizontalAlignment="Center" Width="100" Margin="0">
                        <Path>
                            <Path.Style>
                                <Style TargetType="{x:Type Path}" BasedOn="{StaticResource FlipScreenIcon}" />
                            </Path.Style>
                        </Path>
                    </Viewbox>
                    <Label Content="Flip Screen" HorizontalAlignment="Center" Margin="10" Style="{StaticResource FlipScreenFont}" />
                </StackPanel>
            </Button>

            <StackPanel x:Name="spNext" Orientation="Horizontal" Grid.Column="2" FlowDirection="RightToLeft" Background="#ffffff">
                <Button Content="{Binding NextLabel}"
                        Margin="40,40,20,40"
                        VerticalAlignment="Bottom"
                        userControls:ButtonHelper.DisableMultipleClicks="True"
                        Click="Next_Click"
                        IsEnabled="{Binding Path=IsNextButtonEnabled}"
                        Style="{StaticResource PrimaryLargeButton}"
                        Width="475" />
            </StackPanel>
        </Grid>
    </Grid>
</Page>
