using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.UICore;
using ESS.Pollbook.DynamicControls;
using ESS.Pollbook.ViewModel.VoterDisabilityOath;
using Pollbook.UICore;
using Pollbook.UserControls;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;

namespace Pollbook.Pages.VoterDisabilityOath
{
    /// <summary>
    /// Interaction logic for VoterDisabilityOathSignatureView.xaml
    /// </summary>
    public partial class VoterDisabilityOathSignatureView : Page, IContextView
    {
        private DebounceDispatcher _debounceTimer = new DebounceDispatcher();
        private VoterDisabilityOathSignatureViewModel Context = null;
        private List<DynamicControlsDto> _dynamicControls = null;

        public bool IsModal => false; 
        public SolidColorBrush PrimaryBackgroundBrush => (SolidColorBrush)Application.Current.FindResource("Gray8Brush");
        public Style PrimaryLargeButton => (Style)Application.Current.FindResource("PrimaryLargeButton");

        public VoterDisabilityOathSignatureView()
        {
            InitializeComponent();
            Context = (VoterDisabilityOathSignatureViewModel)DataContext;
        }

        #region DisabilityOathSignature Events

        private void ClearDisabilityOathSignature_Click(object sender, MouseButtonEventArgs e)
        {
            Context.HasDisabilityOathSignatureStrokes = false;
        }
        private void DisabilityOathSignature_OnStrokeCollected(object sender, InkCanvasStrokeCollectedEventArgs e)
        {
            Context.HasDisabilityOathSignatureStrokes = true;
            Context.DisabilityOathSignatureStrokes = ((InkCanvas)e.Source).Strokes;
        }
        #endregion

        #region AdditionalSignature1 Events
        private void ClearAdditionalSignature1_Click(object sender, MouseButtonEventArgs e)
        {
            Context.HasAdditionalSignature1Strokes = false;
        }
        private void AdditionalSignature1_OnStrokeCollected(object sender, InkCanvasStrokeCollectedEventArgs e)
        {
            Context.HasAdditionalSignature1Strokes = true;
            Context.AdditionalSignature1Strokes = ((InkCanvas)e.Source).Strokes;
        }
        #endregion

        #region AdditionalSignature2 Events
        private void ClearAdditionalSignature2_Click(object sender, MouseButtonEventArgs e)
        {
            Context.HasAdditionalSignature2Strokes = false;
        }
        private void AdditionalSignature2_OnStrokeCollected(object sender, InkCanvasStrokeCollectedEventArgs e)
        {
            Context.HasAdditionalSignature2Strokes = true;
            Context.AdditionalSignature2Strokes = ((InkCanvas)e.Source).Strokes;
        }
        #endregion

        private async void Next_Click(object sender, RoutedEventArgs e)
        {
            if (_dynamicControls != null)
            {
                Panel container = null;
                var dynamicSignatureControls = new List<SignatureCanvas>();

                var containerNames = _dynamicControls.Select(x => x.Container_Name).Distinct().ToList();
                foreach (string name in containerNames)
                {
                    container = GetContainerByName(name);

                    if (container != null)
                    {
                        foreach (var control in container.Children)
                        {
                            if (control.GetType() == typeof(SignatureCanvas))
                            {
                                dynamicSignatureControls.Add((SignatureCanvas)control);
                            }
                        }
                    }
                }

                Context.DisabilityOathSignatureBytes = ConvertInkToByteArray(dynamicSignatureControls.FirstOrDefault(x => x.Name == "DisabilityOathSignatureCanvas"));
                Context.AdditionalSignature1Bytes = ConvertInkToByteArray(dynamicSignatureControls.FirstOrDefault(x => x.Name == "JudgeSignature1Canvas"));
                Context.AdditionalSignature2Bytes = ConvertInkToByteArray(dynamicSignatureControls.FirstOrDefault(x => x.Name == "JudgeSignature2Canvas"));
            }

            await Context.NextAsync();
        }

        private byte[] ConvertInkToByteArray(SignatureCanvas signatureCanvas)
        {
            if (signatureCanvas == null)
                return null;

            RenderTargetBitmap rtb = new RenderTargetBitmap((int)signatureCanvas.SignatureInkCanvas.ActualWidth, (int)signatureCanvas.CanvasHeight, 96d, 96d, PixelFormats.Default);
            rtb.Render(signatureCanvas.SignatureInkCanvas);
            PngBitmapEncoder encoder = new PngBitmapEncoder();
            encoder.Frames.Add(BitmapFrame.Create(rtb));
            return Conversion.ConvertToGray(encoder, Conversion.GrayPixelFormatOptions.BlackWhite, (int)signatureCanvas.SignatureInkCanvas.ActualWidth, (int)signatureCanvas.CanvasHeight);
        }

        private void Page_InitializedAsync(object sender, EventArgs e)
        {
            ((VoterDisabilityOathSignatureViewModel)DataContext).PageIsInitialized();
        }

        private async void Page_Loaded(object sender, RoutedEventArgs e)
        {
            await Context.PageIsLoadedAsync();

            _dynamicControls = Context.GetDynamicControls.ToList();

            Panel container = null;
            var containerNames = _dynamicControls.Select(x => x.Container_Name).Distinct().ToList();
            foreach (string name in containerNames)
            {
                if (name.Equals("spDisabilityOathSignature") || name.Equals("spAdditionalSignatures"))
                {
                    container = GetContainerByName(name);

                    if (container != null)
                        container.Children.Clear();
                }
            }

            foreach (DynamicControlsDto dto in _dynamicControls)
            {
                container = GetContainerByName(dto.Container_Name);

                if (container != null)
                {
                    if (container.GetType() == typeof(StackPanel))
                    {
                        if (dto.Visible == 1)
                        {
                            foreach (UIElement element in DynamicControlsLib.Parse(dto))
                            {
                                if (element.GetType() == typeof(SignatureCanvas))
                                {
                                    Context.AddProperty(dto.Control_Name + "Content", Context.GetDefinedText(dto.Control_Pollbook_Defined_Text_Name, Context.SelectedLanguage));

                                    SignatureCanvas sc = (SignatureCanvas)element;
                                    sc.CanvasHeight = 204;

                                    if (container.Name == "spAdditionalSignatures")
                                    {
                                        Context.AdditionalSignatureVisibility = true;

                                        if (sc.Name == "JudgeSignature1Canvas")
                                        {
                                            if (Context.AdditionalSignature1Strokes?.Count > 0)
                                            {
                                                sc.SetStrokes(Context.AdditionalSignature1Strokes);
                                                Context.HasAdditionalSignature1Strokes = true;                                            
                                            }
                                            else
                                            {
                                                sc.SetStrokes(new StrokeCollection());
                                            }

                                            Binding binding = new Binding();
                                            binding.Path = new PropertyPath("HasAdditionalSignature1Strokes");
                                            binding.Source = Context;
                                            sc.InitializeClearButtonBinding(binding);

                                            sc.Signature_OnStrokeCollected += AdditionalSignature1_OnStrokeCollected;
                                            sc.ClearSignature_Clicked += ClearAdditionalSignature1_Click;
                                        }
                                        else if (sc.Name == "JudgeSignature2Canvas")
                                        {
                                            if (Context.AdditionalSignature2Strokes?.Count > 0)
                                            {
                                                sc.SetStrokes(Context.AdditionalSignature2Strokes);
                                                Context.HasAdditionalSignature2Strokes = true;
                                            }
                                            else
                                            {
                                                sc.SetStrokes(new StrokeCollection());
                                            }

                                            Binding binding = new Binding();
                                            binding.Path = new PropertyPath("HasAdditionalSignature2Strokes");
                                            binding.Source = Context;
                                            sc.InitializeClearButtonBinding(binding);

                                            sc.Signature_OnStrokeCollected += AdditionalSignature2_OnStrokeCollected;
                                            sc.ClearSignature_Clicked += ClearAdditionalSignature2_Click;
                                        }
                                    }

                                    if (container.Name == "spDisabilityOathSignature")
                                    {
                                        Context.DisabilityOathSignatureVisibility = true;

                                        if (sc.Name == "DisabilityOathSignatureCanvas")
                                        {
                                            if (Context.DisabilityOathSignatureStrokes?.Count > 0)
                                            {
                                                sc.SetStrokes(Context.DisabilityOathSignatureStrokes);
                                                Context.HasDisabilityOathSignatureStrokes = true;
                                            }
                                            else
                                            {
                                                sc.SetStrokes(new StrokeCollection());
                                            }

                                            Binding binding = new Binding();
                                            binding.Path = new PropertyPath("HasDisabilityOathSignatureStrokes");
                                            binding.Source = Context;
                                            sc.InitializeClearButtonBinding(binding);

                                            sc.ShowDateStamp = true;
                                            sc.Signature_OnStrokeCollected += DisabilityOathSignature_OnStrokeCollected;
                                            sc.ClearSignature_Clicked += ClearDisabilityOathSignature_Click;
                                        }
                                    }

                                    if (!container.Children.Contains(element))
                                    {
                                        container.Children.Insert(dto.Sort_Order, sc);
                                    }
                                }

                                if (element.GetType() == typeof(Label))
                                {
                                    Context.AddProperty(dto.Control_Name + "Content", Context.GetDefinedText(dto.Control_Pollbook_Defined_Text_Name, Context.SelectedLanguage));
                                    ((Label)element).Content = Context.GetDefinedText(dto.Control_Pollbook_Defined_Text_Name, Context.SelectedLanguage);
                                    ((Label)element).SetBinding(Label.ContentProperty, "DisabilityOathDynamicControls." + dto.Control_Name + "Content");

                                    if (!container.Children.Contains(element))
                                    {
                                        container.Children.Insert(dto.Sort_Order, element);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        private Panel GetContainerByName(string name)
        {
            Panel container = null;

            // find a stackpanel
            if (container == null)
                container = DynamicControlsLib.FindChild<StackPanel>(Application.Current.MainWindow, name);

            return container;
        }

        private void Flip_Click(object sender, RoutedEventArgs e)
        {
            Flipper.EnableTheFlip(sender, false);
            ScreenDisplayFlip.ToggleDisplay();
            _debounceTimer.Debounce(700, _ => Flipper.EnableTheFlip(sender, true));
        }
    }
}
