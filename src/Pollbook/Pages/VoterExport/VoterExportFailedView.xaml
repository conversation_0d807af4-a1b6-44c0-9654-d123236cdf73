<Page x:Class="Pollbook.Pages.VoterExport.VoterExportFailedView"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:Pollbook.Pages.VoterExport"
      xmlns:userControls="clr-namespace:Pollbook.UserControls"
      mc:Ignorable="d" 
      d:DesignHeight="1200" d:DesignWidth="1920"
      DataContext="{Binding VoterExportFailed, Source={StaticResource Locator}}"
      Title="VoterExportFailedView"
      Background="{StaticResource Gray8Brush}">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*" />
            <RowDefinition Height="auto" />
        </Grid.RowDefinitions>

        <!-- Body -->
        <Grid Grid.Row="0">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <Viewbox Height="100" Width="100" Stretch="Uniform" HorizontalAlignment="Center" Margin="0,0,0,60">
                    <Path Style="{StaticResource ErrorIcon}"/>
                </Viewbox>
                <TextBlock Text="{Binding FailedText}" HorizontalAlignment="Center" Margin="0,0,0,40" Style="{StaticResource Display1TextBlock}"/>
                <TextBlock Text= "Please check USB media." HorizontalAlignment="Center" Style="{StaticResource InstructionalSemiBoldTextBlock}"/>
            </StackPanel>
        </Grid>

        <!-- Buttons -->
        <Grid Grid.Row="2" Background="White">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="auto" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <Button Grid.Column="0"
                    Content="{Binding BackLabel}"
                    Style="{StaticResource SecondaryLargeButton}"
                    Margin="40"
                    userControls:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding GoBackCommand}" />
        </Grid>
    </Grid>
</Page>
