using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace Pollbook.Pages.VoterExport
{
    /// <summary>
    /// Interaction logic for IncrementalUpdatesLoadingView.xaml
    /// </summary>
    public partial class VoterExportLoadingView : UserControl,IContextView
    {
        public VoterExportLoadingView()
        {
            InitializeComponent();
        }

        public bool IsModal => false;
        public SolidColorBrush PrimaryBackgroundBrush => (SolidColorBrush)Application.Current.FindResource("Gray8Brush");
    }
}
