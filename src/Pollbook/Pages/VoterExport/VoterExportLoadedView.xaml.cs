using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace Pollbook.Pages.VoterExport
{
    /// <summary>
    /// Interaction logic for IncrementalUpdatesLoadedView.xaml
    /// </summary>
    public partial class VoterExportLoadedView : UserControl,IContextView
    {
        public VoterExportLoadedView()
        {
            InitializeComponent();
        }

        public bool IsModal => false;
        public SolidColorBrush PrimaryBackgroundBrush => (SolidColorBrush)Application.Current.FindResource("Gray8Brush");
    }
}
