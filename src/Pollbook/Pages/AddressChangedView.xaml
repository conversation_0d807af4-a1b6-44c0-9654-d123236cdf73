<Page x:Class="Pollbook.Pages.AddressChangedView"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:Pollbook.Pages"
      xmlns:uiCore="clr-namespace:Pollbook.UICore"
      xmlns:button="clr-namespace:Pollbook.UserControls"
      mc:Ignorable="d" 
      DataContext="{Binding AddressChanged, Source={StaticResource Locator}}"
      d:DesignHeight="800" d:DesignWidth="1600">
    <Page.Resources>
        <uiCore:BooleanToVisibilityConverter x:Key="BooleanToVisibility" True="Visible" False="Hidden" />
        <uiCore:BooleanToVisibilityConverter x:Key="InverseBooleanToVisibility" True="Hidden" False="Visible" />
    </Page.Resources>
    

    <Grid Background="White">
        <Grid.RowDefinitions>
            <RowDefinition Height="auto" />
            <RowDefinition Height="175" />
        </Grid.RowDefinitions>

        <StackPanel Margin="0,10,0,80">
            <TextBlock Style="{StaticResource StandardTextBlock}" Foreground="{StaticResource Gray2Brush}" Text="Verify Address" Margin="50,25,0,0" TextAlignment="Left" TextWrapping="Wrap" Visibility="{Binding EnableVoterAddressChange, Converter={StaticResource BooleanToVisibility}}"/>
            <Viewbox Stretch="Uniform" VerticalAlignment="Center" HorizontalAlignment="Center" Width="100" Margin="0,0,0,60" Visibility="{Binding EnableVoterAddressChange, Converter={StaticResource InverseBooleanToVisibility}}">
                <Path Style="{StaticResource WarningIconBlue}" Stretch="Fill" />
            </Viewbox>
            <TextBlock Style="{StaticResource Display1TextBlock}" Margin="0,0,0,30" Text="{Binding Title}" HorizontalAlignment="Center" TextAlignment="Center" TextWrapping="Wrap" />
            <TextBlock Style="{StaticResource StandardTextBlock}" Foreground="{StaticResource Gray2Brush}" Text="{Binding Message}" HorizontalAlignment="Center" TextAlignment="Center" TextWrapping="Wrap" Visibility="{Binding EnableVoterAddressChange, Converter={StaticResource InverseBooleanToVisibility}}"/>
            <StackPanel HorizontalAlignment="Center" Visibility="{Binding EnableVoterAddressChange, Converter={StaticResource BooleanToVisibility}}">
                <TextBlock Style="{StaticResource StandardTextBlock}" Foreground="{StaticResource Gray2Brush}" Text="{Binding VoterName}" TextAlignment="Left" TextWrapping="Wrap"/>
                <TextBlock Style="{StaticResource StandardTextBlock}" Foreground="{StaticResource Gray2Brush}" Text="{Binding VoterAddress}" TextAlignment="Left" TextWrapping="Wrap"/>
                <TextBlock Style="{StaticResource StandardTextBlock}" Foreground="{StaticResource Gray2Brush}" Text="{Binding VoterDOB}" TextAlignment="Left" TextWrapping="Wrap"/>
            </StackPanel>                        
        </StackPanel>

        <!-- Buttons -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="40" />
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <Button Grid.Column="0"
                    Style="{StaticResource SecondaryLargeButton}"
                    Content="{Binding AddressChangedLabel}"
                    button:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding YesCommand}"
                    HorizontalAlignment="Right"
                    Width="500"/>
            <Button Grid.Column="2"
                    Style="{StaticResource PrimaryLargeButton}"
                    Content="{Binding AddressUnchangedLabel}"
                    button:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding NoCommand}"
                    HorizontalAlignment="Left"
                    Width="500"/>
        </Grid>
    </Grid>
</Page>
