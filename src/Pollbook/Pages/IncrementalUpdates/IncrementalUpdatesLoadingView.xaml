<UserControl x:Class="Pollbook.Pages.IncrementalUpdates.IncrementalUpdatesLoadingView"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:Pollbook.Pages.IncrementalUpdates"
      xmlns:userControls="clr-namespace:Pollbook.UserControls"
      mc:Ignorable="d"
      DataContext="{Binding IncrementalUpdatesLoading, Source={StaticResource Locator}}"
      d:DesignHeight="1200" d:DesignWidth="1920">

    <Grid Background="{StaticResource Gray7Brush}">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="*" />
                <RowDefinition Height="2*" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>

            <!-- Top -->
            <Grid Grid.Row="0" Margin="120,70,120,0">
                <!-- Top Left -->
                <Grid HorizontalAlignment="Left" VerticalAlignment="Top">
                    <!-- View Title -->
                    <TextBlock Text="{Binding Updates}">
                        <TextBlock.Style>
                            <Style TargetType="TextBlock" BasedOn="{StaticResource Display2SemiBoldTextBlock}" />
                        </TextBlock.Style>
                    </TextBlock>
                </Grid>

                <!-- Top Right -->
                <Grid HorizontalAlignment="Right" VerticalAlignment="Top" Margin="0,90,0,0"></Grid>
            </Grid>

            <!-- Bottom -->
            <Grid Grid.Row="2" Margin="80,0,80,80"></Grid>
        </Grid>

        <!-- Body -->
        <StackPanel HorizontalAlignment="Center" VerticalAlignment="Top" Margin="0,250,0,0">
            <userControls:Spinner />
            <TextBlock Text="{Binding IncrementalUpdateApplying}" Style="{StaticResource Display2SemiBoldTextBlock}" HorizontalAlignment="Center" Margin="0,0,0,30" />
            <TextBlock Style="{StaticResource StandardTextBlock}" HorizontalAlignment="Center" Margin="0,0,0,30">
                <!--<TextBlock.Text>
                    <MultiBinding StringFormat="{}{0}% Complete...">
                        <Binding Path="PercentComplete" FallbackValue="100" />
                    </MultiBinding>
                </TextBlock.Text>-->
            </TextBlock>
        </StackPanel>
    </Grid>
</UserControl>
