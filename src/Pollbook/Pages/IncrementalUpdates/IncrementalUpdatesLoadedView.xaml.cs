using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace Pollbook.Pages.IncrementalUpdates
{
    /// <summary>
    /// Interaction logic for IncrementalUpdatesLoadedView.xaml
    /// </summary>
    public partial class IncrementalUpdatesLoadedView : UserControl,IContextView
    {
        public IncrementalUpdatesLoadedView()
        {
            InitializeComponent();
        }

        public bool IsModal => false;
        public SolidColorBrush PrimaryBackgroundBrush => (SolidColorBrush)Application.Current.FindResource("Gray8Brush");
    }
}
