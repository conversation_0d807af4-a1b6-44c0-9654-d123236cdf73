using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using ESS.Pollbook.ViewModel;

namespace Pollbook.Pages
{
    /// <summary>
    /// Interaction logic for MonitorPoll.xaml
    /// </summary>
    public partial class MonitorPollView : Page, IContextView
    {
        public bool IsModal => false;
        public SolidColorBrush PrimaryBackgroundBrush => (SolidColorBrush)Application.Current.FindResource("BackgroundBrush");

        public MonitorPollView()
        {
            InitializeComponent();
        }

        private async void Page_Loaded(object sender, RoutedEventArgs e)
        {
            await ((MonitorPollViewModel)DataContext).PageLoadedAsync();
            CheckProgressBarValueVisibility();
        }

        private void Page_Unloaded(object sender, RoutedEventArgs routedEventArgs)
        {
            ((MonitorPollViewModel)DataContext).PageUnloaded();
        }

        private void TabControl_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (sender is TabControl tab)
                LastSyncDate.Visibility = tab.SelectedIndex == 3 ? Visibility.Visible : Visibility.Collapsed;
        }

        private void DownloadProgressBar_OnValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
        {
            if (!(sender is ProgressBar)) return;
            var isDownloadVisible = ((MonitorPollViewModel)DataContext).IsDownloadPercentageVisible;
            DownloadProgressBar.Background = isDownloadVisible ? PrimaryBackgroundBrush : Brushes.Transparent;
        }

        private void AppliedProgressBar_OnValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
        {
            if (!(sender is ProgressBar)) return;
            var isAppliedVisible = ((MonitorPollViewModel)DataContext).IsAppliedPercentageVisible;
            AppliedProgressBar.Background = isAppliedVisible ? PrimaryBackgroundBrush : Brushes.Transparent;
        }

        private void UploadProgressBar_OnValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
        {
            if (!(sender is ProgressBar)) return;
            var isUploadVisible = ((MonitorPollViewModel)DataContext).IsUploadPercentageVisible;
            UploadProgressBar.Background = isUploadVisible ? PrimaryBackgroundBrush : Brushes.Transparent;
        }

        private void CheckProgressBarValueVisibility()
        {
            var isDownloadVisible = ((MonitorPollViewModel)DataContext).IsDownloadPercentageVisible;
            DownloadProgressBar.Background = isDownloadVisible ? PrimaryBackgroundBrush : Brushes.Transparent;

            var isAppliedVisible = ((MonitorPollViewModel)DataContext).IsAppliedPercentageVisible;
            AppliedProgressBar.Background = isAppliedVisible ? PrimaryBackgroundBrush : Brushes.Transparent;

            var isUploadVisible = ((MonitorPollViewModel)DataContext).IsUploadPercentageVisible;
            UploadProgressBar.Background = isUploadVisible ? PrimaryBackgroundBrush : Brushes.Transparent;
        }
    }
}
