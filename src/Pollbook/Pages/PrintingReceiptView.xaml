<Page x:Class="Pollbook.Pages.PrintingReceiptView"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:Pollbook.Pages"
      xmlns:ui="clr-namespace:Pollbook.UserControls"
      mc:Ignorable="d" 
      DataContext="{Binding PrintingReceipt, Source={StaticResource Locator}}"
      Background="{StaticResource Gray8Brush}"
      d:DesignHeight="1200" d:DesignWidth="1920"
      Title="PrintingReceiptView">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="232" />
        </Grid.RowDefinitions>
        <Grid Grid.Row="0">
            <Label Content="Printing Receipt" Style="{StaticResource Display2SemiBoldFont}" HorizontalAlignment="Left" VerticalAlignment="Top" Margin="107,96,0,0" />
        </Grid>
        <Grid Grid.Row="1">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ui:Spinner />
            </StackPanel>
        </Grid>
        <Grid Grid.Row="2" Background="White">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="auto" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>
            
        </Grid>
    </Grid>
</Page>
