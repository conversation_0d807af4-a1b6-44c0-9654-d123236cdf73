using ESS.Pollbook.ViewModel.VerifyVoterId;
using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace Pollbook.Pages.VerifyVoterId
{
    /// <summary>
    /// Interaction logic for VerifyVoterIdQuestionView.xaml
    /// </summary>
    public partial class VerifyVoterIdReasonableImpedimentDeclarationView : Page, IContextView
    {
        public bool IsModal => false;
        public SolidColorBrush PrimaryBackgroundBrush => (SolidColorBrush)Application.Current.FindResource("Gray8Brush");
        public VerifyVoterIdReasonableImpedimentDeclarationView()
        {
            InitializeComponent();
        }

        private void VerifyVoterIdReasonableImpedimentDeclarationView_OnInitialized(object sender, EventArgs e)
        {
            var viewModel = (VerifyVoterIdReasonableImpedimentDeclarationViewModel)DataContext;
            viewModel.PageInitialized();
        }
    }
}
