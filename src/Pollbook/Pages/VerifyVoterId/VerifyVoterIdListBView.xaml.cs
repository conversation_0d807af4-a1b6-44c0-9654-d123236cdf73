using ESS.Pollbook.ViewModel.VerifyVoterId;
using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace Pollbook.Pages.VerifyVoterId
{
    /// <summary>
    /// Interaction logic for VerifyVoterIdQuestionView.xaml
    /// </summary>
    public partial class VerifyVoterIdListBView : Page, IContextView
    {
        public bool IsModal => false;
        public SolidColorBrush PrimaryBackgroundBrush => (SolidColorBrush)Application.Current.FindResource("Gray8Brush");
        public VerifyVoterIdListBView()
        {
            InitializeComponent();
        }
        private void Page_Initialized(object sender, EventArgs e)
        {
            var viewModel = (VerifyVoterIdListBViewModel)DataContext;
            viewModel.PageInitialized();
        }
    }
}
