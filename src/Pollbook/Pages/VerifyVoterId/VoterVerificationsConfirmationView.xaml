<Page x:Class="Pollbook.Pages.VerifyVoterId.VoterVerificationsConfirmationView"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:Pollbook.Pages.VerifyVoterId"
      xmlns:userControls="clr-namespace:Pollbook.UserControls"
      mc:Ignorable="d" 
      DataContext="{Binding VoterVerificationsConfirmation, Source={StaticResource Locator}}"
      d:DesignHeight="800" d:DesignWidth="1600"
      Title="VoterVerificationsConfirmationView">

    <Grid Background="White" Margin="100,0">
        <Grid.RowDefinitions>
            <RowDefinition Height="auto" />
            <RowDefinition Height="175" />
        </Grid.RowDefinitions>

        <StackPanel Margin="0,0,0,80">
            <Viewbox Stretch="Uniform" VerticalAlignment="Center" HorizontalAlignment="Center" Width="100" Margin="0,0,0,60">
                <Path Style="{StaticResource InformationIcon}" Stretch="Fill" />
            </Viewbox>
            <TextBlock Style="{StaticResource Display2TextBlock}" Margin="0,0,0,30" Text="{Binding Title}" HorizontalAlignment="Center" TextAlignment="Center" TextWrapping="Wrap" />
            <TextBlock Style="{StaticResource InstructionalLargeTextBlock}" Text="{Binding Message}" HorizontalAlignment="Center" TextAlignment="Center" TextWrapping="Wrap" />
        </StackPanel>

        <!-- Buttons -->
        <StackPanel Orientation="Vertical" Grid.Row="1">
            <Button Grid.Column="0"
                    Style="{StaticResource PrimaryLargeButton}"
                    Content="{Binding CompleteLabel}"
                    userControls:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding CompleteCommand}"
                    HorizontalAlignment="Center"
                    Width="500"/>
        </StackPanel>
    </Grid>
</Page>
