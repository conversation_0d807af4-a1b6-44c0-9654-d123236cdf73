using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace Pollbook.Pages
{
    /// <summary>
    /// Interaction logic for VoterCheckInCompleteView.xaml
    /// </summary>
    public partial class VoterCheckInCompleteView : Page, IContextView
    {
        public bool IsModal => false;
        public SolidColorBrush PrimaryBackgroundBrush => (SolidColorBrush)Application.Current.FindResource("Gray8Brush");

        public VoterCheckInCompleteView()
        {
            InitializeComponent();
        }
    }
}
