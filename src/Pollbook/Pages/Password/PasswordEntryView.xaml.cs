using CommonServiceLocator;
using ESS.Pollbook.ViewModel.Infrastructure;
using ESS.Pollbook.ViewModel.Password;
using Pollbook.UserControls.Textbox;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;

namespace Pollbook.Pages.Password
{
	/// <summary>
	/// Interaction logic for PasswordEntryView.xaml
	/// </summary>
	public partial class PasswordEntryView : Page
	{
		private readonly IKeyboardService _keyboardService;

		public PasswordEntryView()
        {
            InitializeComponent();
            _keyboardService = ServiceLocator.Current.GetInstance<IKeyboardService>();
            PasswordBox.InputBindings.Add(new KeyBinding { Command = ((PasswordViewModel)DataContext).SubmitCommand, Key = Key.Return });
        }

		private async void Page_Loaded(object sender, RoutedEventArgs e)
        {
	        PasswordBox.PasswordChanged += PasswordBox_PasswordChanged;
	        NavigationCommands.BrowseBack.InputGestures.Clear();
        }

        private void PasswordBox_PasswordChanged(object sender, RoutedEventArgs e)
        {
	        if (DataContext != null && sender is PasswordBoxWithCaret passwordBox)
		        ((dynamic)DataContext).Password = passwordBox.Password;
        }

        private void Button_Click(object sender, RoutedEventArgs e)
        {
            PasswordBox.txtPassword.Focus();
        }

        private void Page_Unloaded(object sender, RoutedEventArgs e)
        {
	        PasswordBox.PasswordChanged -= PasswordBox_PasswordChanged;
	        _keyboardService.TextDeactivated();
        }
    }
}
