using System;
using ESS.Pollbook.ViewModel.Infrastructure;
using Pollbook.UserControls.Textbox;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using ESS.Pollbook.ViewModel;
using CommonServiceLocator;

namespace Pollbook.Pages
{
    /// <summary>
    /// Interaction logic for ManageDeviceLoginView.xaml
    /// </summary>
    public partial class ManageDeviceLoginView : Page, IDisposable
    {
        private readonly IKeyboardService _keyboardService;

        public ManageDeviceLoginView()
        {
            InitializeComponent();
            _keyboardService = ServiceLocator.Current.GetInstance<IKeyboardService>();
            ManageDevicePasswordBox.InputBindings?.Add(new KeyBinding { Command = ((ManageDeviceLoginViewModel)DataContext).SubmitCommand, Key = Key.Return });
            ManageDevicePasswordBox.PasswordChanged += PasswordBox_PasswordChanged;
        }

        private void PasswordBox_PasswordChanged(object sender, RoutedEventArgs e)
        {
            if (this.DataContext != null && sender is PasswordBoxWithCaret passwordBox)
            {
                ((dynamic)this.DataContext).AuthenticationQuery = passwordBox.Password;
            }
        }

        private void Button_Click(object sender, RoutedEventArgs e)
        {
            ManageDevicePasswordBox.txtPassword.Focus();
        }

        private void Page_Loaded(object sender, RoutedEventArgs e)
        {
            ((ManageDeviceLoginViewModel)DataContext).PageLoaded();
        }

        private void Page_Unloaded(object sender, RoutedEventArgs e)
        {
            _keyboardService.TextDeactivated();
        }

        public void Dispose()
        {
            ManageDevicePasswordBox = null;
        }
    }
}
