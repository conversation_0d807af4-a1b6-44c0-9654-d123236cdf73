<Page x:Class="Pollbook.Pages.Maintenance.ConfigureElectionLanding"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"  
      xmlns:uiCore="clr-namespace:Pollbook.UICore"
      xmlns:userControls="clr-namespace:Pollbook.UserControls"
      mc:Ignorable="d" 
      DataContext="{Binding ConfigureElectionLanding, Source={StaticResource Locator}}"
      Loaded="ConfigureElectionLanding_OnLoaded"
      d:DesignHeight="1200" d:DesignWidth="1920"
      Background="{StaticResource Gray8Brush}"
      Title="Configure Election Landing">

    <Page.Resources>
        <uiCore:BoolInverterConverter x:Key="BoolInverterConverter" />
    </Page.Resources>

    <Grid >
        <Grid.RowDefinitions>
            <RowDefinition Height="*" />
            <RowDefinition Height="auto" />
        </Grid.RowDefinitions>
        <StackPanel Grid.Row="0" Margin="120,0,120,0">
            <Grid HorizontalAlignment="Stretch" Margin="0,20,0,0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                <Label Grid.Column="0" Content="{Binding ConfigureElectionLabel, FallbackValue='Configure Election Title (unbound)'}"
                       Style="{StaticResource StandardTitle}" />
                <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Right" VerticalAlignment="Center">
                    <TextBlock Text="{Binding TestModeLabel}" 
                               Margin="0,4,20,0" 
                               Style="{StaticResource TextBlockStandardLightLabel}"/>
                    <ToggleButton 
                        Height="50"
                        IsChecked="{Binding EnableTestMode, Mode=TwoWay}" 
                        HorizontalAlignment="Right"/>
                </StackPanel>
            </Grid>
            <StackPanel>
                <Button Style="{StaticResource PrimaryLargeButton}" 
                        userControls:ButtonHelper.DisableMultipleClicks="True"
                        Command="{Binding ConfigureElectionCommand}"
                        Width="745" 
                        Content="{Binding ConfigureElectionFeaturesLabel, FallbackValue='Configure ExpressPoll Features (unbound)'}"
                        Margin="0,200,0,30" />
                <Button Style="{StaticResource PrimaryLargeButton}"
                        userControls:ButtonHelper.DisableMultipleClicks="True"
                        Command="{Binding ConfigurePrintingCommand}"
                        Width="745"
                        Content="{Binding ConfigurePrintingLabel,  FallbackValue='Configure Print Features (unbound)'}" />
            </StackPanel>
        </StackPanel>
        <Grid Grid.Row="1" Background="White">
            <Button Content="{Binding BackLabel, FallbackValue='Back (unbound)'}"
                    Style="{StaticResource SecondaryLargeButton}"
                    HorizontalAlignment="Left"
                    Margin="40"
                    userControls:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding BackCommand}" />
        </Grid>
    </Grid>
</Page>
