using ESS.Pollbook.ViewModel.Maintenance;
using System.Windows;
using System.Windows.Controls;


namespace Pollbook.Pages.Maintenance
{
    /// <summary>
    /// Interaction logic for WifiNetworkPropertiesView.xaml
    /// </summary>
    public partial class WifiNetworkPropertiesView : Page 
    {
        public WifiNetworkPropertiesView()
        {
            InitializeComponent();
        }

        public void OnPageLoaded(object sender, RoutedEventArgs e)
        {
            var context = DataContext as WifiNetworkPropertiesViewModel;
            context.OnPageLoaded();
        }
    }
}
