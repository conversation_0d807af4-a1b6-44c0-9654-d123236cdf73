using ESS.Pollbook.ViewModel.Maintenance;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace Pollbook.Pages.Maintenance
{
    public partial class TransactionsClearConfirmationView : Page, IContextView
    {
        public TransactionsClearConfirmationView()
        {
            InitializeComponent();
        }

        private void Page_Loaded(object sender, RoutedEventArgs e)
        {
            ((TransactionsClearConfirmationViewModel)DataContext).PageIsLoaded();
        }

        public bool IsModal => true;

        public SolidColorBrush PrimaryBackgroundBrush =>
            (SolidColorBrush)Application.Current.FindResource("WhiteBrush");
    }
}
