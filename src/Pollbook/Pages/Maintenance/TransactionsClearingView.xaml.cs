using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace Pollbook.Pages.Maintenance
{
    /// <summary>
    /// Interaction logic for TransactionsClearingView.xaml
    /// </summary>
    public partial class TransactionsClearingView : Page, IContextView
    {
        public bool IsModal => true;
        public SolidColorBrush PrimaryBackgroundBrush => (SolidColorBrush)Application.Current.FindResource("Gray8Brush");

        public TransactionsClearingView()
        {
            InitializeComponent();
        }
    }
}
