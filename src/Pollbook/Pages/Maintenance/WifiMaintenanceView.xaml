<Page x:Class="Pollbook.Pages.Maintenance.WifiMaintenanceView"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:Pollbook.Pages.Maintenance"
      xmlns:core="clr-namespace:Pollbook.UICore"
      xmlns:usercontrols="clr-namespace:Pollbook.UserControls"
      mc:Ignorable="d" 
      DataContext="{Binding WifiMaintenance, Source={StaticResource Locator}}"
      Background="{StaticResource WhiteBrush}"
      Loaded="OnPageLoadedAsync"
      Unloaded="OnPageUnloadedAsync"
      d:DesignHeight="1200" d:DesignWidth="1800"
      Title="WifiMaintenanceView">

    <Page.Resources>
        <SolidColorBrush x:Key="Signal.Foreground" Color="RoyalBlue" Opacity="1.0"/>
        <SolidColorBrush x:Key="Signal.Background" Color="Gray" Opacity="0.25"/>

        <core:BooleanToVisibilityConverter x:Key="OnVisibilityConverter" True="Visible" False="Collapsed" />
        <core:BooleanToVisibilityConverter x:Key="OnVisibilityReverseConverter" True="Collapsed" False="Visible" />

        <!-- SignalProgressBar -->
        <Style x:Key="ArcStyle" TargetType="{x:Type core:SignalProgressBar}">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type core:SignalProgressBar}">
                        <Grid Width="36" Height="36">
                            <Path x:Name="PART_LevelOne"
							  Stroke="DarkGray" StrokeThickness="5">
                                <Path.Data>
                                    <PathGeometry>
                                        <PathFigure StartPoint="32,36">
                                            <ArcSegment Point="36,32"
													Size="8,8"
													IsLargeArc="False" SweepDirection="Clockwise"/>
                                        </PathFigure>
                                    </PathGeometry>
                                </Path.Data>
                            </Path>

                            <Path x:Name="PART_LevelTwo"
							  Stroke="DarkGray" StrokeThickness="2.5">
                                <Path.Data>
                                    <PathGeometry>
                                        <PathFigure StartPoint="24,36">
                                            <ArcSegment Point="36,24"
													Size="12,12"
													IsLargeArc="False" SweepDirection="Clockwise"/>
                                        </PathFigure>
                                    </PathGeometry>
                                </Path.Data>
                            </Path>

                            <Path x:Name="PART_LevelThree"
							  Stroke="DarkGray" StrokeThickness="2.5">
                                <Path.Data>
                                    <PathGeometry>
                                        <PathFigure StartPoint="16,36">
                                            <ArcSegment Point="36,16"
													Size="20,20"
													IsLargeArc="False" SweepDirection="Clockwise"/>
                                        </PathFigure>
                                    </PathGeometry>
                                </Path.Data>
                            </Path>

                            <Path x:Name="PART_LevelFour"
							  Stroke="DarkGray" StrokeThickness="2.5">
                                <Path.Data>
                                    <PathGeometry>
                                        <PathFigure StartPoint="8,36">
                                            <ArcSegment Point="36,8"
													Size="28,28"
													IsLargeArc="False" SweepDirection="Clockwise"/>
                                        </PathFigure>
                                    </PathGeometry>
                                </Path.Data>
                            </Path>

                            <Path x:Name="PART_LevelFive"
							  Stroke="DarkGray" StrokeThickness="2.5">
                                <Path.Data>
                                    <PathGeometry>
                                        <PathFigure StartPoint="0,36">
                                            <ArcSegment Point="36,0"
													Size="36,36"
                                                        RotationAngle="45"
													IsLargeArc="False" SweepDirection="Clockwise"/>
                                        </PathFigure>
                                    </PathGeometry>
                                </Path.Data>
                            </Path>
                        </Grid>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Page.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*" />
            <RowDefinition Height="auto" />
        </Grid.RowDefinitions>

        <TabControl Grid.Row="0" SelectionChanged="TabControl_SelectionChanged" Margin="120,30,120,0" BorderThickness="0 2 0 0">
            <TabControl.Resources>
                <Style TargetType="TabItem">
                    <Setter Property="Template">
                        <Setter.Value>
                            <ControlTemplate TargetType="TabItem">
                                <Border Name="Panel" CornerRadius="3, 3, 0, 0" Width="410" Height="100" Margin="0,0,10,0" Background="White">
                                    <StackPanel VerticalAlignment="Bottom">
                                        <ContentPresenter x:Name="ContentSite" VerticalAlignment="Center" HorizontalAlignment="Left" Margin="0">
                                            <ContentPresenter.Content>
                                                <ContentControl x:Name="ContentCtl" Content="{TemplateBinding Header}" Style="{StaticResource Body1BoldFont}" Margin="0 0 0 20"/>
                                            </ContentPresenter.Content>
                                        </ContentPresenter>
                                        <Rectangle Fill="{StaticResource SecondaryDarkBlueBrush}" Height="6" Width="{Binding ActualWidth, ElementName=ContentCtl}" HorizontalAlignment="Left" VerticalAlignment="Bottom">
                                            <Rectangle.Style>
                                                <Style TargetType="Rectangle">
                                                    <Setter Property="Visibility" Value="Hidden"/>
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type TabItem}}, Path=IsSelected}" Value="True">
                                                            <Setter Property="Visibility" Value="Visible"/>
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </Rectangle.Style>
                                        </Rectangle>
                                    </StackPanel>
                                </Border>
                                <ControlTemplate.Triggers>
                                    <Trigger Property="IsSelected" Value="True">
                                        <Setter TargetName="Panel" Property="Background" Value="White" />
                                        <Setter TargetName="ContentCtl" Property="Foreground" Value="{StaticResource SecondaryDarkBlueBrush}"/>
                                    </Trigger>
                                    <Trigger Property="IsSelected" Value="False">
                                        <Setter TargetName="Panel" Property="Background" Value="White" />
                                        <Setter TargetName="ContentCtl" Property="Foreground" Value="{StaticResource Gray4Brush}"/>
                                    </Trigger>
                                    <Trigger Property="TabIndex" Value="0">
                                        <Setter TargetName="Panel" Property="Margin" Value="0,0,10,0" />
                                    </Trigger>
                                </ControlTemplate.Triggers>
                            </ControlTemplate>
                        </Setter.Value>
                    </Setter>
                </Style>
            </TabControl.Resources>

            <!-- Available Radios -->
            <TabItem Header="Available Networks" TabIndex="0" >
                <Grid Margin="0,0,0,0">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="auto" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>

                    <!-- List count-->
                    <StackPanel Grid.Row="0" >
                        <TextBlock 
                            Text="{Binding Path=AvailableRadios.Count, StringFormat={}{0} Networks}" 
                            FontSize="24" 
                            FontWeight="Bold" 
                            Margin="0,59,0,25"
                            Foreground="{StaticResource Gray1Brush}" />
                    </StackPanel>

                    <Button Grid.Row="0" 
                            FontSize="14" 
                            usercontrols:ButtonHelper.DisableMultipleClicks="True"
                            Command="{Binding RescanCommand}" 
                            Margin="0,-80,66,0" 
                            IsEnabled="{Binding ScanInProgress}"
                            HorizontalAlignment="Right">
                        <Button.Template>
                            <ControlTemplate TargetType="Button">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="10"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <ContentPresenter Grid.Column="0" />
                                </Grid>
                            </ControlTemplate>
                        </Button.Template>
                        <TextBlock Visibility="Collapsed" Text="{Binding RescanButton, FallbackValue='Rescan'}" Foreground="{StaticResource BluetifulBrush}">
                            <TextBlock.Style>
                                <Style TargetType="TextBlock" BasedOn="{StaticResource StandardTextBlock}" />
                            </TextBlock.Style>
                        </TextBlock>
                    </Button>



                    <!-- List of Available Radios -->
                    <ListView Grid.Row="1"
                              ItemsSource="{Binding AvailableRadios, IsAsync=True}" 
                              ScrollViewer.CanContentScroll="True" 
                              ScrollViewer.VerticalScrollBarVisibility="Auto" 
                              ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                              SelectionChanged="AvailableRadios_Selected"  
                              Background="White"
                              BorderBrush="{StaticResource Gray5Brush}"
                              BorderThickness="2"
                              VirtualizingPanel.IsVirtualizing="True"
                              VirtualizingPanel.VirtualizationMode="Recycling" 
                              VirtualizingPanel.CacheLengthUnit="Item"
                              VirtualizingPanel.CacheLength="8096"
                              SelectionMode="Single" 
                              Padding="0" 
                              HorizontalContentAlignment="Stretch" 
                              Grid.IsSharedSizeScope="True" Grid.ColumnSpan="2"
                              Margin="0 0 0 90">
                        <ListView.Resources>
                            <Style TargetType="ScrollBar" BasedOn="{StaticResource CustomVerticalScrollBarStyle}"/>
                        </ListView.Resources>
                        <ListView.ItemTemplate>
                            <DataTemplate>
                                <ContentControl>
                                    <StackPanel Orientation="Horizontal" Height="132">
                                        <Grid Margin="10">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="100" />
                                                <!-- Image-->
                                                <ColumnDefinition Width="500" />
                                                <!-- Profile Name-->
                                                <ColumnDefinition Width="250" />
                                                <!-- Connected-->
                                                <ColumnDefinition Width="50" />
                                                <!-- Signal Strength %-->
                                                <ColumnDefinition Width="10" />
                                                <!-- Band-->
                                                <ColumnDefinition Width="190" />
                                                <!-- SecurityEnabled-->
                                                <ColumnDefinition Width="600*" />
                                                <!-- Edit-->
                                            </Grid.ColumnDefinitions>

                                            <!-- Signal Graphic -->
                                            <core:SignalProgressBar Grid.Column="0" Margin="0,0,0,0" VerticalAlignment="Center" Background="{StaticResource Signal.Background}" Foreground="{StaticResource Signal.Foreground}" Value="{Binding SignalQuality}" Style="{StaticResource ArcStyle}" />

                                            <!-- Profile name {Sssid for Radio} -->
                                            <TextBlock Grid.Column="1" Margin="10,0" Style="{StaticResource Display3BoldTextBlock}" Text="{Binding Name}" VerticalAlignment="Center" />

                                            <!-- Profile isConnected -->
                                            <TextBlock Grid.Column="2" Margin="4,0,0,0" VerticalAlignment="Center" Foreground="Black" FontSize="30" FontWeight="Bold">
                                                <TextBlock.Style>
                                                    <Style TargetType="TextBlock">
                                                        <Style.Triggers>
                                                            <DataTrigger Binding="{Binding IsConnected}" Value="True">
                                                                <Setter Property="Text" Value="Connected" />
                                                            </DataTrigger>
                                                            <DataTrigger Binding="{Binding IsConnected}" Value="False">
                                                                <Setter Property="Text" Value="" />
                                                            </DataTrigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </TextBlock.Style>
                                            </TextBlock>

                                            <!-- Secured -->
                                            <TextBlock Grid.Column="5" Margin="10,0,0,0" FontSize="30" FontWeight="Bold" VerticalAlignment="Center">
                                                <TextBlock.Style>
                                                    <Style TargetType="TextBlock">
                                                        <Style.Triggers>
                                                            <DataTrigger Binding="{Binding IsSecurityEnabled}" Value="True">
                                                                <Setter Property="Text" Value="Secured" />
                                                            </DataTrigger>
                                                            <DataTrigger Binding="{Binding IsSecurityEnabled}" Value="False">
                                                                <Setter Property="Text" Value="Not Secured" />
                                                            </DataTrigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </TextBlock.Style>
                                            </TextBlock>

                                            <!-- Connection Link -->
                                            <StackPanel Grid.Column="6" 
                                                        Visibility="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type ListViewItem}}, Path=IsSelected, Converter={StaticResource OnVisibilityConverter}}"
                                                        HorizontalAlignment="Right"
                                                        VerticalAlignment="Center"
                                                        Width="550">

                                                <Button FontSize="36"
                                                        Width="320"
                                                        Visibility="{Binding IsConnected, Converter={StaticResource OnVisibilityReverseConverter}}" 
                                                        usercontrols:ButtonHelper.DisableMultipleClicks="True"    
                                                        Command="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Page}}, Path=DataContext.ConnectNetworkCommand}" HorizontalAlignment="Right" VerticalAlignment="Center">
                                                    <Button.Template>
                                                        <ControlTemplate TargetType="Button">
                                                            <Grid>
                                                                <Grid.ColumnDefinitions>
                                                                    <ColumnDefinition Width="*"/>
                                                                    <ColumnDefinition Width="10"/>
                                                                    <ColumnDefinition Width="*"/>
                                                                </Grid.ColumnDefinitions>
                                                                <ContentPresenter Grid.Column="0" />
                                                            </Grid>
                                                        </ControlTemplate>
                                                    </Button.Template>
                                                    <TextBlock Text="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Page}}, Path=DataContext.ConnectButton}"
                                                                Foreground="{StaticResource BluetifulBrush}" 
                                                                Height="61"  
                                                                HorizontalAlignment="Right"
                                                                Style="{StaticResource BoldTextBlock}"/>
                                                </Button>

                                                <Button FontSize="36"
                                                        Width="320"
                                                        Visibility="{Binding IsConnected, Converter={StaticResource OnVisibilityConverter}}"
                                                        usercontrols:ButtonHelper.DisableMultipleClicks="True"
                                                        Click="DisconnectFromAvailableNetworkItem"
                                                        HorizontalAlignment="Right"
                                                        VerticalAlignment="Center">
                                                    <Button.Template>
                                                        <ControlTemplate TargetType="Button">
                                                            <Grid>
                                                                <Grid.ColumnDefinitions>
                                                                    <ColumnDefinition Width="*"/>
                                                                    <ColumnDefinition Width="10"/>
                                                                    <ColumnDefinition Width="*"/>
                                                                </Grid.ColumnDefinitions>

                                                                <ContentPresenter Grid.Column="0" />
                                                            </Grid>
                                                        </ControlTemplate>
                                                    </Button.Template>
                                                    <TextBlock Text="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Page}}, Path=DataContext.DisconnectButton}" Foreground="{StaticResource BluetifulBrush}" Height="61"  HorizontalAlignment="Right">
                                                        <TextBlock.Style>
                                                            <Style TargetType="TextBlock" BasedOn="{StaticResource BoldTextBlock}" />
                                                        </TextBlock.Style>
                                                    </TextBlock>
                                                </Button>
                                            </StackPanel>
                                        </Grid>
                                    </StackPanel>
                                </ContentControl>
                            </DataTemplate>
                        </ListView.ItemTemplate>
                    </ListView>
                    <usercontrols:Spinner Grid.Row="0" Grid.RowSpan="2"  RenderTransformOrigin="1.421,1.144" Margin="0,10,0,0" 
                                          Visibility="{Binding ScanInProgress, Converter={StaticResource OnVisibilityConverter}, FallbackValue=Hidden}"/>
                </Grid>
            </TabItem>

            <!-- Manage Networks -->
            <TabItem Header="Manage Networks" TabIndex="1" Height="100" VerticalAlignment="Top">
                <Grid Margin="0,0,0,0">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="auto" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>

                    <Grid Grid.Row="0">
                        <!-- List count-->
                        <TextBlock  Grid.Row="0"
                                    Text="{Binding Path=ManageProfiles.Count, StringFormat={}{0} Networks}" 
                                    FontSize="24" 
                                    FontWeight="Bold" 
                                    Foreground="{StaticResource Gray1Brush}" 
                                    Margin="0,59,0,25"/>


                    </Grid>


                    <!-- List of Managed Network Profiles -->
                    <ListView Name="LvManageProfiles"  
                              Grid.Row="1"
                              ItemsSource="{Binding ManageProfiles}" 
                              ScrollViewer.CanContentScroll="False" 
                              ScrollViewer.VerticalScrollBarVisibility="Auto" 
                              ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                              Background="White"
                              BorderBrush="{StaticResource Gray5Brush}"
                              BorderThickness="2"
                              SelectionChanged="ManageProfiles_Selected" Grid.ColumnSpan="2"
                              Margin="0 0 0 90">
                        <ListView.Resources>
                            <Style TargetType="ScrollBar" BasedOn="{StaticResource CustomVerticalScrollBarStyle}"/>
                        </ListView.Resources>
                        <ListView.ItemTemplate>
                            <DataTemplate>
                                <ContentControl>
                                    <StackPanel Orientation="Horizontal" Height="auto" MinHeight="132">
                                        <Grid Margin="10">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="600" />
                                                <!-- Profile Name-->
                                                <ColumnDefinition Width="250" />
                                                <!-- Connected-->
                                                <ColumnDefinition Width="50" />
                                                <!-- SecurityEnabled-->
                                                <ColumnDefinition Width="100" />
                                                <!-- Security Type-->
                                                <ColumnDefinition Width="50" />
                                                <!-- Method-->
                                                <ColumnDefinition Width="110" />
                                                <!-- Spacer -->
                                                <ColumnDefinition Width="auto" />
                                                <!-- Buttons -->
                                            </Grid.ColumnDefinitions>

                                            <!-- Profile name -->
                                            <TextBlock Grid.Column="0" Margin="20,0" VerticalAlignment="Center" Style="{StaticResource Display3BoldTextBlock}" Text="{Binding Name}" />

                                            <!-- Profile isConnected -->
                                            <TextBlock Grid.Column="1" Margin="4,0,0,0" VerticalAlignment="Center" FontSize="30" FontWeight="Bold">
                                                <TextBlock.Style>
                                                    <Style TargetType="TextBlock">
                                                        <Style.Triggers>
                                                            <DataTrigger Binding="{Binding IsConnected}" Value="True">
                                                                <Setter Property="Text" Value="Connected" />
                                                                <Setter Property="Foreground" Value="Black" />
                                                            </DataTrigger>
                                                            <DataTrigger Binding="{Binding IsConnected}" Value="False">
                                                                <Setter Property="Text" Value="" />
                                                            </DataTrigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </TextBlock.Style>
                                            </TextBlock>

                                            <!-- Buttons -->
                                            <StackPanel Grid.Column="6" 
                                                        Orientation="Horizontal" 
                                                        Visibility="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type ListViewItem}}, Path=IsSelected, Converter={StaticResource OnVisibilityConverter}}">

                                                <StackPanel Grid.Column="0" Orientation="Vertical" VerticalAlignment="Center">
                                                    <StackPanel.Style>
                                                        <Style TargetType="StackPanel">
                                                            <Style.Triggers>
                                                                <DataTrigger Binding="{Binding IsConnected}" Value="true">
                                                                    <Setter Property="Visibility" Value="Visible" />
                                                                </DataTrigger>
                                                                <DataTrigger Binding="{Binding IsConnected}" Value="false">
                                                                    <Setter Property="Visibility" Value="Hidden" />
                                                                </DataTrigger>
                                                            </Style.Triggers>
                                                        </Style>
                                                    </StackPanel.Style>
                                                    <!-- Edit button-->
                                                    <Button FontSize="36"
                                                            usercontrols:ButtonHelper.DisableMultipleClicks="True"
                                                            Command="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Page}}, Path=DataContext.EditNetworkCommand}">
                                                        <Button.Template>
                                                            <ControlTemplate TargetType="Button">
                                                                <Grid>
                                                                    <Grid.ColumnDefinitions>
                                                                        <ColumnDefinition Width="*"/>
                                                                        <ColumnDefinition Width="10"/>
                                                                        <ColumnDefinition Width="*"/>
                                                                    </Grid.ColumnDefinitions>
                                                                    <ContentPresenter Grid.Column="0" />
                                                                </Grid>
                                                            </ControlTemplate>
                                                        </Button.Template>
                                                        <TextBlock Text="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Page}}, Path=DataContext.PropertiesButton}" 
                                                                       Foreground="{StaticResource SecondaryDarkBlueBrush}" 
                                                                       VerticalAlignment="Center" >
                                                            <TextBlock.Style>
                                                                <Style TargetType="TextBlock" BasedOn="{StaticResource BoldTextBlock}" />
                                                            </TextBlock.Style>
                                                        </TextBlock>
                                                    </Button>
                                                </StackPanel>

                                                <StackPanel Grid.Column="2" 
                                                                VerticalAlignment="Center" 
                                                                HorizontalAlignment="Right"
                                                                Margin="20,0,20,0">

                                                    <!-- Forget Me button-->
                                                    <Button FontSize="36" 
                                                            Margin="150,0,0,0" 
                                                            usercontrols:ButtonHelper.DisableMultipleClicks="True"
                                                            Command="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Page}}, Path=DataContext.DeleteNetworkCommand}">
                                                        <Button.Template>
                                                            <ControlTemplate TargetType="Button">
                                                                <Grid>
                                                                    <Grid.ColumnDefinitions>
                                                                        <ColumnDefinition Width="*"/>
                                                                        <ColumnDefinition Width="10"/>
                                                                        <ColumnDefinition Width="*"/>
                                                                    </Grid.ColumnDefinitions>
                                                                    <ContentPresenter Grid.Column="0" />
                                                                </Grid>
                                                            </ControlTemplate>
                                                        </Button.Template>
                                                        <TextBlock Text="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Page}}, Path=DataContext.DeleteButton}" 
                                                                       Foreground="{StaticResource SecondaryDarkRedBrush}" 
                                                                       VerticalAlignment="Center"
                                                                       Style="{StaticResource BoldTextBlock}">
                                                        </TextBlock>
                                                    </Button>
                                                </StackPanel>
                                            </StackPanel>
                                        </Grid>
                                    </StackPanel>
                                </ContentControl>
                            </DataTemplate>
                        </ListView.ItemTemplate>
                    </ListView>
                    <usercontrols:Spinner Grid.Row="0" Grid.RowSpan="2"  RenderTransformOrigin="1.421,1.144" Margin="0,10,0,0" 
                                          Visibility="{Binding ManageNetworkInProcess, Converter={StaticResource OnVisibilityConverter}, FallbackValue=Hidden}"/>
                </Grid>
            </TabItem>
        </TabControl>
        <Button Grid.Column="0" 
                Grid.Row="0" 
                x:Name="ClearSelectionButton"
                FontSize="14" 
                usercontrols:ButtonHelper.DisableMultipleClicks="True"
                Click="ClearSelection" 
                VerticalAlignment="Top"
                HorizontalAlignment="Right"
                Margin="0,50,190,0" 
                Height="36">
            <Button.Template>
                <ControlTemplate TargetType="Button">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="10"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <ContentPresenter Grid.Column="0" />
                    </Grid>
                </ControlTemplate>
            </Button.Template>
            <TextBlock Text="Clear Selection"  Foreground="{StaticResource BluetifulBrush}">
                <TextBlock.Style>
                    <Style TargetType="TextBlock" BasedOn="{StaticResource StandardTextBlock}" />
                </TextBlock.Style>
            </TextBlock>
        </Button>
        <Button Grid.Column="0" 
                Grid.Row="0" 
                x:Name="RescanButton"
                FontSize="14" 
                usercontrols:ButtonHelper.DisableMultipleClicks="True"
                Command="{Binding RescanCommand}" 
                VerticalAlignment="Top"
                HorizontalAlignment="Right"
                Margin="0,50,190,0" 
                Height="36"
                IsEnabled="{Binding RescanEnabled}">
            <Button.Template>
                <ControlTemplate TargetType="Button">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="10"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <ContentPresenter Grid.Column="0" />
                    </Grid>
                </ControlTemplate>
            </Button.Template>
            <TextBlock Text="{Binding RescanButton}">
                <TextBlock.Style>
                    <Style TargetType="TextBlock" BasedOn="{StaticResource StandardTextBlock}" >
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding ScanInProgress}" Value="True">
                                <Setter Property="Foreground" Value="{StaticResource Gray5Brush}"/>
                                <Setter Property="IsEnabled" Value="False"/>
                            </DataTrigger>
                            <DataTrigger Binding="{Binding ScanInProgress}" Value="False">
                                <Setter Property="Foreground" Value="{StaticResource BluetifulBrush}"/>
                                <Setter Property="IsEnabled" Value="True"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </TextBlock.Style>
            </TextBlock>
        </Button>

        <!-- Buttons -->
        <Grid Grid.Row="1" Background="White">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="auto" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <Button Grid.Column="0"
                    Content="{Binding BackButton}"
                    Style="{StaticResource SecondaryLargeButton}"
                    Margin="40"
                    Width="420"
                    usercontrols:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding BackCommand}" />
            <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Right">
                <Button Name="AddNetworkButton"
                        Margin="40,0"
                        Content="{Binding AddNetworkButton}"
                        Style="{StaticResource PrimaryLargeButton}"
                        usercontrols:ButtonHelper.DisableMultipleClicks="True"
                        Command="{Binding AddNetworkCommand}"/>
            </StackPanel>
        </Grid>
    </Grid>
</Page>
