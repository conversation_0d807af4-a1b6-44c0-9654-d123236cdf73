using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace Pollbook.Pages.Maintenance
{
    /// <summary>
    /// Interaction logic for ElectionLoadingView.xaml
    /// </summary>
    public partial class ElectionLoadingView : Page, IContextView
    {
        public bool IsModal => true;
        public SolidColorBrush PrimaryBackgroundBrush => (SolidColorBrush)Application.Current.FindResource("Gray8Brush");

        public ElectionLoadingView()
        {
            InitializeComponent();
        }
    }
}
