<Page x:Class="Pollbook.Pages.Maintenance.WifiKeyEntryView"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:core="clr-namespace:Pollbook.UICore"
      xmlns:usercontrols="clr-namespace:Pollbook.UserControls"
      xmlns:textbox="clr-namespace:Pollbook.UserControls.Textbox"
      xmlns:local="clr-namespace:Pollbook.Pages.Maintenance"
      mc:Ignorable="d" 
      DataContext="{Binding WifiKeyEntry, Source={StaticResource Locator}}"
      Loaded="PageLoaded"
      Unloaded="PageUnloaded"
      d:DesignHeight="450" d:DesignWidth="840"
      Title="WifiKeyEntryView"
      Background="White">

    <Page.Resources>
        <core:BooleanToVisibilityConverter x:Key="OnVisibilityConverter" True="Visible" False="Collapsed" />
    </Page.Resources>

    <Grid Margin="80,50,80,34">
        <Grid Background="White">
            <StackPanel>
                <Label HorizontalAlignment="Left" VerticalAlignment="Top" Style="{StaticResource LargeBoldLabel}" 
                       Content="{Binding WindowTitle}"/>
                
                <Label Content="Password" Margin="0,10,0,0" Style="{StaticResource StandardLabel}" />
                
                <textbox:PasswordBoxWithCaret x:Name="PasswordBox" TakeKeyboardFocusOnLoad="True"/>
              
                <!-- Messages -->
                <StackPanel Orientation="Horizontal">
                    <StackPanel.Style>
                        <Style TargetType="StackPanel">
                            <Setter Property="Visibility" Value="Collapsed"/>
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding IsAuthenticationValid}" Value="False">
                                    <Setter Property="Visibility" Value="Visible"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </StackPanel.Style>
                    <Label Content="Error:" Style="{StaticResource ErrorBoldFont}" />
                    <Label Content="Please correct password" Style="{StaticResource ErrorFont}" />
                </StackPanel>
            </StackPanel>
            <Button Content="{Binding SubmitButton}" 
                    Style="{StaticResource PrimarySmallButton}"
                    usercontrols:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding SubmitCommand}" 
                    IsEnabled="{Binding SubmitEnabled}" 
                    HorizontalAlignment="Right" 
                    VerticalAlignment="Bottom" 
                    Width="179" 
                    Click="Button_Click" />

        </Grid>
        <usercontrols:Spinner  RenderTransformOrigin="1.421,1.144" Margin="0,10,0,0" 
                               Visibility="{Binding InProcess, Converter={StaticResource OnVisibilityConverter}}"/>
    </Grid>
</Page>
