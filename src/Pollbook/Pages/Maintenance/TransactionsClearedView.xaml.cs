using ESS.Pollbook.ViewModel.Maintenance;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace Pollbook.Pages.Maintenance
{
    /// <summary>
    /// Interaction logic for TransactionsClearedView.xaml
    /// </summary>
    public partial class TransactionsClearedView : Page, IContextView
    {
        public bool IsModal => true;
        public SolidColorBrush PrimaryBackgroundBrush => (SolidColorBrush)Application.Current.FindResource("Gray8Brush");

        public TransactionsClearedView()
        {
            InitializeComponent();
        }

        private void TransactionsClearedView_OnLoaded(object sender, RoutedEventArgs e)
        {
            ((TransactionsClearedViewModel)DataContext).PageIsLoaded();
        }
    }
}
