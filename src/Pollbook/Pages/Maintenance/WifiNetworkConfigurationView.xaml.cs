using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using CommonServiceLocator;
using ESS.Pollbook.ViewModel.Infrastructure;
using ESS.Pollbook.ViewModel.Maintenance;
using Pollbook.UserControls.Textbox;

namespace Pollbook.Pages.Maintenance
{
    /// <summary>
    /// Interaction logic for WifiNetworkConfigurationView.xaml
    /// </summary>
    public partial class WifiNetworkConfigurationView : Page, IContextView
    {
        private readonly IKeyboardService _keyboardService;
        public bool IsModal => true;
        public SolidColorBrush PrimaryBackgroundBrush => (SolidColorBrush)Application.Current.FindResource("Gray8Brush");

        public WifiNetworkConfigurationView()
        {
            InitializeComponent();
            _keyboardService = ServiceLocator.Current.GetInstance<IKeyboardService>();
            txtNetworkPasswordBox.InputBindings?.Add(new KeyBinding { Command = ((WifiNetworkConfigurationViewModel)DataContext).SaveCommand, Key = Key.Return });
            txtNetworkPasswordBox.PasswordChanged += PasswordBox_PasswordChanged;
        }

        private void PasswordBox_PasswordChanged(object sender, RoutedEventArgs e)
        {
            var passwordBox = sender as PasswordBoxWithCaret;
            if (this.DataContext != null && passwordBox != null)
            {
                ((dynamic)this.DataContext).Key = passwordBox.Password;
            }
        }

        private void OnPageLoaded(object sender, RoutedEventArgs e)
        {
            ((WifiNetworkConfigurationViewModel)DataContext).OnPageLoaded();
        }

        private void Page_Unloaded(object sender, RoutedEventArgs e)
        {
            _keyboardService.TextDeactivated();
        }
    }
}
