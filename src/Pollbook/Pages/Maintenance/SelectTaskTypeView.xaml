<Page x:Class="Pollbook.Pages.Maintenance.SelectTaskTypeView"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:userControls="clr-namespace:Pollbook.UserControls"
      mc:Ignorable="d" 
      DataContext="{Binding SelectTaskTypeViewModel, Source={StaticResource Locator}}"
      Background="{StaticResource Gray8Brush}"
      d:DesignHeight="1200" d:DesignWidth="1920">
    <Page.Resources>

    </Page.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="auto" />
        </Grid.RowDefinitions>

        <!-- Top -->
        <Grid Grid.Row="0">
            <Grid Margin="124,70,120,0">
                <!-- Top Left -->
                <Grid HorizontalAlignment="Left" VerticalAlignment="Top">
                    <!-- View Title -->
                    <TextBlock Text="{Binding TitleText}">
                        <TextBlock.Style>
                            <Style TargetType="TextBlock" BasedOn="{StaticResource Display2SemiBoldTextBlock}" />
                        </TextBlock.Style>
                    </TextBlock>
                </Grid>

                <!-- Top Right -->
                <Grid HorizontalAlignment="Right" VerticalAlignment="Top" Margin="0,90,0,0"></Grid>
            </Grid>
        </Grid>
        
        <!-- Body -->
      <Grid Grid.Row="1">
         <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,-144,0,0">
            <Border Height="450" Margin="0,0,0,0">
               <Grid>
                  <Viewbox Stretch="Uniform" VerticalAlignment="Center" HorizontalAlignment="Center" Width="180" Margin="0,0,0,40">
                     <Path Style="{StaticResource UsbDriveIcon}"/>
                  </Viewbox>
                  <Button Content="{Binding FromUsbDriveLabel}"
                          VerticalAlignment="Bottom" Style="{StaticResource PrimaryLargeButton}"
                          userControls:ButtonHelper.DisableMultipleClicks="True"
                          Command="{Binding SelectTypeCommand}"
                          CommandParameter="USB"
                          Width="419"/>
               </Grid>
            </Border>
         </StackPanel>
      </Grid>

      <!-- Buttons -->
        <Grid Grid.Row="2" Background="White">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="auto" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <Button Grid.Column="0"
                    Content="{Binding BackLabel}"
                    Style="{StaticResource SecondaryLargeButton}"
                    Margin="40"
                    userControls:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding BackCommand}" />
        </Grid>
    </Grid>
</Page>
