<Page x:Class="Pollbook.Pages.LoadElectionConfirmationView"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:userControls="clr-namespace:Pollbook.UserControls"
      mc:Ignorable="d" 
      DataContext="{Binding LoadElectionConfirmation, Source={StaticResource Locator}}"
      d:DesignHeight="800" d:DesignWidth="1600">

    <Grid Background="White" Margin="100,0">
        <Grid.RowDefinitions>
            <RowDefinition Height="auto" />
            <RowDefinition Height="175" />
        </Grid.RowDefinitions>

        <StackPanel Margin="0,0,0,80">
            <Viewbox Stretch="Uniform" VerticalAlignment="Center" HorizontalAlignment="Center" Width="100" Margin="0,0,0,60">
                <Path Style="{StaticResource LargeBallotIssuedIcon}" Stretch="Fill" />
            </Viewbox>
            <TextBlock Style="{StaticResource Display1TextBlock}" Margin="0,0,0,30" Text="Transactions Pending Upload" HorizontalAlignment="Center" 
                       TextAlignment="Center" TextWrapping="Wrap" />
            <TextBlock Style="{StaticResource StandardTextBlock}" Foreground="{StaticResource Gray2Brush}" Text="Are you sure you want to load the election? &#10; By selecting &quot;Yes, Load Election&quot; below, any existing records will be replaced." 
                       HorizontalAlignment="Center" TextAlignment="Center" TextWrapping="Wrap" />
        </StackPanel>

        <!-- Buttons -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="600" />
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            <Button Grid.Column="1"
                    Style="{StaticResource PrimaryLargeButton}"
                    Content="{Binding YesLabel}"
                    userControls:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding YesCommand}" />
        </Grid>
    </Grid>
</Page>
