<Page x:Class="Pollbook.Pages.Maintenance.BackupTransactionsLoadedView"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:userControls="clr-namespace:Pollbook.UserControls"
      mc:Ignorable="d" 
      DataContext="{Binding BackupTransactionsLoaded, Source={StaticResource Locator}}"
      Background="{StaticResource Gray7Brush}"
      d:DesignHeight="1200" d:DesignWidth="1920"
      Title="BackupTransactionsLoadedView">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*" />
            <RowDefinition Height="auto" />
        </Grid.RowDefinitions>
        <Grid Grid.Row="0" Background="{StaticResource Gray7Brush}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Top" Margin="0,250,0,0">
                <Viewbox Height="120" Width="120" Stretch="Uniform" HorizontalAlignment="Center">
                    <Path>
                        <Path.Style>
                            <Style TargetType="{x:Type Path}" BasedOn="{StaticResource SuccessIcon}" />
                        </Path.Style>
                    </Path>
                </Viewbox>
                <TextBlock Text="Transactions Successfully Loaded to USB" HorizontalAlignment="Center" Margin="0,60,0,00">
                    <TextBlock.Style>
                        <Style TargetType="TextBlock" BasedOn="{StaticResource Display1TextBlock}" />
                    </TextBlock.Style>
                </TextBlock>
                
                <Button Content="{Binding DoneLabel}" 
                        Style="{StaticResource PrimaryLargeButton}" 
                        HorizontalAlignment="Center" 
                        Margin="40" 
                        Width="420" 
                        userControls:ButtonHelper.DisableMultipleClicks="True"
                        Command="{Binding DoneCommand}" />
            </StackPanel>
        </Grid>

    </Grid>
</Page>
