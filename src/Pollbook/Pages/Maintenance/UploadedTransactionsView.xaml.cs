using ESS.Pollbook.ViewModel.Maintenance;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace Pollbook.Pages.Maintenance
{
    /// <summary>
    /// Interaction logic for UploadedTransactionsView.xaml
    /// </summary>
    public partial class UploadedTransactionsView : Page, IContextView
    {
        public bool IsModal => true;
        public SolidColorBrush PrimaryBackgroundBrush => (SolidColorBrush)Application.Current.FindResource("Gray8Brush");
        public UploadedTransactionsView()
        {
            InitializeComponent();
        }

        private async void Page_Loaded(object sender, RoutedEventArgs e)
        {
            await ((UploadedTransactionsViewModel)DataContext).PageIsLoaded();
        }
    }
}
