<Page x:Class="Pollbook.Pages.Maintenance.ManageElectionView"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:Pollbook.Pages.Maintenance" 
      xmlns:uiCore="clr-namespace:Pollbook.UICore"
      xmlns:userControls="clr-namespace:Pollbook.UserControls"
      mc:Ignorable="d" 
      Loaded="Page_Loaded"
      DataContext="{Binding ManageElection, Source={StaticResource Locator}}"
      Background="{StaticResource Gray8Brush}"
      d:DesignHeight="1200" d:DesignWidth="1920">
    <Page.Resources>
        <uiCore:BooleanToVisibilityConverter x:Key="OnVisibilityConverter" True="Visible" False="Collapsed" />
        <uiCore:BooleanToVisibilityConverter x:Key="OffVisibilityConverter" True="Collapsed" False="Visible" />
    </Page.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="auto" />
        </Grid.RowDefinitions>

        <!-- Top -->
        <Grid Grid.Row="0">
            <Grid Margin="120,70,120,0">
                <!-- Top Left -->
                <Grid HorizontalAlignment="Left" VerticalAlignment="Top">
                    <!-- View Title -->
                    <TextBlock Text="Manage Election">
                        <TextBlock.Style>
                            <Style TargetType="TextBlock" BasedOn="{StaticResource Display2SemiBoldTextBlock}" />
                        </TextBlock.Style>
                    </TextBlock>
                </Grid>

                <!-- Top Right -->
                <Grid HorizontalAlignment="Right" VerticalAlignment="Top"></Grid>
            </Grid>
            <Grid HorizontalAlignment="Right" VerticalAlignment="Bottom" Margin="0,0,180,0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="auto" />
                    <ColumnDefinition Width="auto" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <TextBlock Grid.Column="0" Text="Enhanced Logging" Style="{StaticResource TextBlockStandardLightLabel}" VerticalAlignment="Center"/>

                <ToggleButton Grid.Column="1" 
                              IsChecked="{Binding LoggingDebugMode, Mode=TwoWay}" 
                              Margin="20,0,0,0"/>
            </Grid>
        </Grid>

        <!-- Body -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="500" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="500" />
            </Grid.ColumnDefinitions>
            <StackPanel Grid.Column="1" Orientation="Vertical" HorizontalAlignment="Center">
                <Button Style="{StaticResource PrimaryLargeButton}"
                        userControls:ButtonHelper.DisableMultipleClicks="True"
                        Command="{Binding SmartUpdateCommand}"
                        Width="600"
                        Content="{Binding IncrementalUpdatesLabel}"
                        Margin="0,50,0,0" />
                <Button Style="{StaticResource PrimaryLargeButton}"
                        userControls:ButtonHelper.DisableMultipleClicks="True"
                        Command="{Binding BackupTransactionsCommand}"
                        Width="600"
                        Content="{Binding BackupTransactionsLabel}"
                        Margin="0,25,0,0"/>
                <Button Style="{StaticResource PrimaryLargeButton}"
                        userControls:ButtonHelper.DisableMultipleClicks="True"
                        Command="{Binding ClearTransactionsCommand}"
                        Width="600"
                        Content="{Binding ClearTransactionsLabel}"
                        Margin="0,25,0,0"/>
            </StackPanel>
            <Grid Grid.Column="2" VerticalAlignment="Bottom" Margin="0,0,0,20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="auto" />
                    <ColumnDefinition Width="auto" />
                </Grid.ColumnDefinitions>
                <StackPanel Grid.Column="0" VerticalAlignment="Bottom" >
                    <TextBlock Style="{StaticResource BoldTextBlockWithHeight}" Foreground="#55636b" FontSize="24px" >
                        <TextBlock.Text>
                            <MultiBinding StringFormat="Upload Sync Status: {0}%">
                                <Binding Path="UploadSyncStatus" FallbackValue="Unknown" />
                            </MultiBinding>
                        </TextBlock.Text>
                    </TextBlock>
                </StackPanel>
                <Button BorderThickness="0"
                        Background="Transparent"
                        VerticalAlignment="Bottom"
                        Margin="20,0,0,15"
                        Grid.Column="1"
                        IsEnabled="{Binding EnableUploadToHost}"
                        userControls:ButtonHelper.DisableMultipleClicks="True"
                        Command="{Binding UploadTransactionsToHostCommand}" >
                    <Path Height="90" Stretch="Fill" Width="90">
                        <Path.Style>
                            <Style TargetType="{x:Type Path}" BasedOn="{StaticResource UploadIcon}">
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding IsEnabled, RelativeSource={RelativeSource AncestorType=Button}}" Value="True">
                                        <Setter Property="Fill" Value="{StaticResource SecondaryDarkBlueBrush}"/>
                                    </DataTrigger>
                                    <DataTrigger Binding="{Binding IsEnabled, RelativeSource={RelativeSource AncestorType=Button}}" Value="False">
                                        <Setter Property="Fill" Value="{StaticResource Gray5Brush}"/>
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </Path.Style>
                    </Path>
                </Button>
            </Grid>

        </Grid>

        <!-- Buttons -->
        <Grid Grid.Row="2" Background="White">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="auto" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <Button Grid.Column="0"
                    Content="{Binding BackLabel}"
                    Style="{StaticResource SecondaryLargeButton}"
                    Margin="40"
                    Width="420"
                    userControls:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding BackCommand}" />
        </Grid>
    </Grid>
</Page>
