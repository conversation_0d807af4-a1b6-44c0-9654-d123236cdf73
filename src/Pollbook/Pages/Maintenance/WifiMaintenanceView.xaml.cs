using ESS.Pollbook.Core.Model;
using ESS.Pollbook.ViewModel.Maintenance;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace Pollbook.Pages.Maintenance
{
    /// <summary>
    /// Interaction logic for WifiMaintenanceView.xaml
    /// </summary>
    public partial class WifiMaintenanceView : Page, IContextView
    {
        public bool IsModal => false;
        public SolidColorBrush PrimaryBackgroundBrush => (SolidColorBrush)Application.Current.FindResource("BackgroundBrush");
        private readonly WifiMaintenanceViewModel _viewModel;

        public WifiMaintenanceView()
        {
            InitializeComponent();
            _viewModel = (WifiMaintenanceViewModel)DataContext;
        }

        private async void OnPageLoadedAsync(object sender, RoutedEventArgs e)
        {
            await ((WifiMaintenanceViewModel)DataContext).OnPageLoadedAsync();
        }
        
        private void OnPageUnloadedAsync(object sender, RoutedEventArgs e)
        {
            ((WifiMaintenanceViewModel)DataContext).OnPageUnloadedAsync();
        }

        private void AvailableRadios_Selected(object sender, RoutedEventArgs e)
        {
            var lv = (ListView)sender;
            _viewModel.SelectedRadioItem = (RadioItem)lv.SelectedItem;
        }

        private void ManageProfiles_Selected(object sender, RoutedEventArgs e)
        {
            var lv = (ListView)sender;
            _viewModel.SelectedManageItem = (ProfileItem)lv.SelectedItem;
        }

        private void TabControl_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            var tab = (TabControl)sender;
            if (tab.SelectedIndex == 1)
            {
                AddNetworkButton.Visibility = Visibility.Visible;
                //Rescan and clear button can only be clicked if outside the tab control
                //visibility must be controlled manually
                RescanButton.Visibility = Visibility.Collapsed;
                ClearSelectionButton.Visibility = Visibility.Visible;
            }
            else
            {
                AddNetworkButton.Visibility = Visibility.Collapsed;
                RescanButton.Visibility = Visibility.Visible;
                ClearSelectionButton.Visibility = Visibility.Collapsed;
            }
        }

        private void ClearSelection(object sender, RoutedEventArgs e)
        {
            LvManageProfiles.SelectedItem = null;
        }

        private void DisconnectFromAvailableNetworkItem(object sender, RoutedEventArgs e)
        {
            _viewModel.DisconnectNetwork();
        }
    }
}
