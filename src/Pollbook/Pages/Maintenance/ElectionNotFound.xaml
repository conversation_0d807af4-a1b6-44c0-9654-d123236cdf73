<Page x:Class="Pollbook.Pages.Maintenance.ElectionNotFound"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:Pollbook.Pages.Maintenance"
      xmlns:userControls="clr-namespace:Pollbook.UserControls"
      mc:Ignorable="d" 
      DataContext="{Binding ElectionNotFound, Source={StaticResource Locator}}"
      Background="{StaticResource Gray8Brush}"
      d:DesignHeight="1200" d:DesignWidth="1920"
      Title="ElectionNotFound">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="auto" />
        </Grid.RowDefinitions>

        <!-- Top -->
        <Grid Grid.Row="0">
            <Grid Margin="120,70,120,0">
                <!-- Top Left -->
                <Grid HorizontalAlignment="Left" VerticalAlignment="Top">
                    <!-- View Title -->
                    <TextBlock Text="{Binding Title}" Style="{StaticResource Display2SemiBoldTextBlock}"/>
                </Grid>

                <!-- Top Right -->
                <Grid HorizontalAlignment="Right" VerticalAlignment="Top" Margin="0,90,0,0"></Grid>
            </Grid>
        </Grid>

        <!-- Body -->
        <Grid Grid.Row="1">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Top" Margin="0,160,0,0">
                <Viewbox Height="100" Width="100" Stretch="Uniform" HorizontalAlignment="Center">
                    <Path>
                        <Path.Style>
                            <Style TargetType="{x:Type Path}" BasedOn="{StaticResource ErrorIcon}" />
                        </Path.Style>
                    </Path>
                </Viewbox>
                <TextBlock Text="No Election Found" HorizontalAlignment="Center" Margin="0,60,0,0" Style="{StaticResource Display1TextBlock}"/>
                <TextBlock Text="{Binding ErrorMessage}" HorizontalAlignment="Center" Margin="0,40,0,0" Style="{StaticResource InstructionalSemiBoldTextBlock}"/>
            </StackPanel>
        </Grid>

        <!-- Buttons -->
        <Grid Grid.Row="2" Background="White">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="auto" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <Button Grid.Column="0"
                    Content="{Binding BackLabel}"
                    Style="{StaticResource SecondaryLargeButton}"
                    Margin="40"
                    Width="420"
                    userControls:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding BackCommand}" />
        </Grid>
    </Grid>
</Page>
