using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using ESS.Pollbook.ViewModel;

namespace Pollbook.Pages
{
    /// <summary>
    /// Interaction logic for ManageDeviceView.xaml
    /// </summary>
    public partial class ManageDeviceView : IContextView
    {

        public bool IsModal => true;
        public SolidColorBrush PrimaryBackgroundBrush => (SolidColorBrush)Application.Current.FindResource("BackgroundBrush");
        public ManageDeviceView()
        {
            InitializeComponent();
        }

        private async void Page_Loaded(object sender, RoutedEventArgs e)
        {
            ((ManageDeviceViewModel)DataContext).ProgressBarLength = (float)ProgressBarCanvas.Width;
            await ((ManageDeviceViewModel)DataContext).PageLoadedAsync();
        }

        private void Page_Unloaded(object sender, RoutedEventArgs e)
        {
            ((ManageDeviceViewModel)DataContext).PageUnloaded();
        }

        private void TabControl_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (sender is TabControl tab)
                LastSyncDate.Visibility = tab.SelectedIndex == 1 ? Visibility.Visible : Visibility.Collapsed;
        }
    }
}
