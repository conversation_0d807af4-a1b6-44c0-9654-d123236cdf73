<Page x:Class="Pollbook.Pages.ShutDownView"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:Pollbook.Pages"
      xmlns:userControls="clr-namespace:Pollbook.UserControls"
      mc:Ignorable="d" 
      DataContext="{Binding ShutDown, Source={StaticResource Locator}}"
      d:DesignHeight="800" d:DesignWidth="1600">

    <Grid Background="White" Margin="100,0" Width="auto">
        <Grid.RowDefinitions>
            <RowDefinition Height="auto" />
            <RowDefinition Height="175" />
        </Grid.RowDefinitions>

        <StackPanel Width="auto" Margin="0,0,0,80" HorizontalAlignment="Center">
            <Viewbox Stretch="Uniform" VerticalAlignment="Center" HorizontalAlignment="Center" Width="100" Margin="0,0,0,60">
                <Path Style="{StaticResource LargeBallotIssuedIcon}" Stretch="Fill" />
            </Viewbox>
            <TextBlock Style="{StaticResource Display1TextBlock}" Margin="0,0,0,30" Text="{Binding Title}" HorizontalAlignment="Center" TextAlignment="Left" TextWrapping="Wrap" />
            <TextBlock Style="{StaticResource StandardTextBlock}" Foreground="{StaticResource Gray2Brush}" Text="{Binding Message}" HorizontalAlignment="Center" TextAlignment="Center" TextWrapping="Wrap" />
        </StackPanel>

        <!-- Buttons -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition />
                <ColumnDefinition Width="40" />
                <ColumnDefinition />
            </Grid.ColumnDefinitions>

            <Button Grid.Column="0"
                    Width="420" Style="{StaticResource SecondaryLargeButton}"
                    Content="{Binding NoLabel}"
                    userControls:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding NoCommand}"
                    HorizontalAlignment="Right" />

            <Button Grid.Column="2"
                    Width="420"
                    Style="{StaticResource PrimaryLargeButton}"
                    Content="{Binding YesLabel}"
                    userControls:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding YesCommand}"
                    IsEnabled="{Binding Path=CanExecuteYesCommand}"
                    HorizontalAlignment="Left" />
        </Grid>
    </Grid>
</Page>
