<Page x:Class="Pollbook.Pages.Voter.CaptureIdVoterPresentFormView"
     xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:uiCore="clr-namespace:Pollbook.UICore"
      xmlns:userControls="clr-namespace:Pollbook.UserControls"
      mc:Ignorable="d" 
      DataContext="{Binding CaptureIdVoterPresentForm, Source={StaticResource Locator}}"
      Initialized="Page_Loaded"
      d:DesignHeight="800" d:DesignWidth="1600">

    <Page.Resources>
        <uiCore:BoolInverterConverter x:Key="BoolInverterConverter" />
    </Page.Resources>

    <Grid Background="White" Margin="100,0" Width="1490">
        <Grid.RowDefinitions>
            <RowDefinition Height="auto" />
            <RowDefinition Height="175" />
        </Grid.RowDefinitions>

        <StackPanel Margin="30,0,30,30" Width="1200">
            <Viewbox Stretch="Uniform" VerticalAlignment="Center" HorizontalAlignment="Center" Width="100" Margin="0,0,0,60">
                <Path Style="{StaticResource InformationIcon}" Stretch="Fill" />
            </Viewbox>
            <TextBlock Style="{StaticResource Display2TextBlock}" Margin="0,0,0,30" Text="{Binding Instructions}" HorizontalAlignment="Center" TextAlignment="Center" TextWrapping="Wrap" Width="900" />
            <TextBlock Style="{StaticResource StandardTextBlock}" Foreground="{StaticResource Gray2Brush}" Text="{Binding PresentedIdForm}" HorizontalAlignment="Center" TextAlignment="Center" TextWrapping="Wrap" />
        </StackPanel>

        <!-- Buttons -->
        <Grid Grid.Row="1">
            <Button Style="{StaticResource PrimaryLargeButton}"
                    Content="{Binding CompleteLabel}"
                    userControls:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding CompleteCommand}"
                    HorizontalAlignment="Center"
                    IsEnabled="{Binding SignOutInProgress, Converter={StaticResource BoolInverterConverter}}"/>
        </Grid>
    </Grid>
</Page>
