using ESS.Pollbook.ViewModel.Voter;
using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
namespace Pollbook.Pages.Voter
{
    /// <summary>
    /// Interaction logic for CancelBallotView.xaml
    /// </summary>
    public partial class CancelBallotView : Page, IContextView
    {
        public bool IsModal => true; // false = show or true = hide the back button Proposed fix.
        public SolidColorBrush PrimaryBackgroundBrush => (SolidColorBrush)Application.Current.FindResource("Gray8Brush");
        public CancelBallotView()
        {
            InitializeComponent();
        }

        private void CancelBallotView_OnInitialized(object sender, EventArgs e)
        {
            ((CancelBallotViewModel)DataContext).PageInitialized();
        }
    }
}
