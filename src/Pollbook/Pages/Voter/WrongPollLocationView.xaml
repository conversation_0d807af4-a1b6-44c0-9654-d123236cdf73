<Page x:Class="Pollbook.Pages.Voter.WrongPollLocationView"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:uiCore="clr-namespace:Pollbook.UICore"
      xmlns:userControls="clr-namespace:Pollbook.UserControls"
      mc:Ignorable="d"
      DataContext="{Binding WrongPollLocation, Source={StaticResource Locator}}"
      d:DesignHeight="1083" d:DesignWidth="1920">

    <Page.Resources>
        <uiCore:BinaryImageConverter x:Key="ImgConverter" />
        <uiCore:BooleanToVisibilityConverter x:Key="OnVisibilityConverter" True="Visible" False="Collapsed" />
    </Page.Resources>

    <Grid Background="White" Margin="100,0" Width="1490">
        <Grid.RowDefinitions>
            <RowDefinition Height="1*" />
            <RowDefinition Height="2*" />
            <RowDefinition Height="1*" />
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="10" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="10" />
        </Grid.ColumnDefinitions>

        <StackPanel Grid.Row="0" Grid.Column="1" Grid.ColumnSpan="2">
            <ContentControl Template="{StaticResource LocationErrorIcon}" HorizontalAlignment="Center"
                            VerticalAlignment="Bottom" />
            <TextBlock Style="{StaticResource Display1TextBlock}" Margin="0,60,0,40"
                       Text="Voter must vote in his/her designated poll place." HorizontalAlignment="Center"
                       TextAlignment="Center" TextWrapping="Wrap" VerticalAlignment="Bottom" />
        </StackPanel>

        <!--Printable information-->
        <Border Grid.Row="1" Grid.Column="1" Width="548" Height="396" CornerRadius="10" BorderThickness="2"
                BorderBrush="#e5ebf0" HorizontalAlignment="Right">
            <Grid>
                <StackPanel Margin="40,30,0,0">
                    <Label Content="Designated Poll Place" Margin="-5,0,0,0">
                        <Label.Style>
                            <Style TargetType="Label" BasedOn="{StaticResource StandardLightLabel}" />
                        </Label.Style>
                    </Label>
                    <TextBlock TextWrapping="WrapWithOverflow" Margin="0,0,100,0" Text="{Binding LocationInfo}">
                        <TextBlock.Style>
                            <Style TargetType="TextBlock" BasedOn="{StaticResource Display3BoldTextBlock}" />
                        </TextBlock.Style>
                    </TextBlock>

                </StackPanel>
            </Grid>
        </Border>

        <!--Poll place map-->
        <Border CornerRadius="10" Grid.Row="1" Grid.Column="2" Width="548" Height="396" HorizontalAlignment="Left"
                Margin="20,0,0,0" BorderBrush="#e5ebf0" BorderThickness="2">
            <Border.Style>
                <Style TargetType="Border">
                    <Setter Property="Background">
                        <Setter.Value>
                            <ImageBrush Stretch="UniformToFill"
                                        ImageSource="{Binding VotersPollPlace.PollingPlaceImage, Converter={StaticResource ImgConverter}}" />
                        </Setter.Value>
                    </Setter>
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding VotersPollPlace.PollingPlaceImage}" Value="{x:Null}">
                            <Setter Property="Background">
                                <Setter.Value>
                                    <ImageBrush Stretch="Uniform"
                                                ImageSource="/Styles/Images/official-polling-place.png" />
                                </Setter.Value>
                            </Setter>
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </Border.Style>
        </Border>

        <Border Grid.Row="2" Grid.Column="1" Margin="0,0,0,0" CornerRadius="10">
            <Button Width="724"
                    Margin="20,20,0,0"
                    Style="{StaticResource SecondaryLargeButton}"
                    Content="{Binding PrintDetailsLabel}"
                    userControls:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding PrintDetailsCommand}" />
        </Border>

        <Border Grid.Row="2" Grid.Column="2" Margin="0,0,0,0" CornerRadius="10">
            <Button Width="724"
                    Margin="20,20,20,0"
                    Style="{StaticResource PrimaryLargeButton}"
                    Content="{Binding SendTextLabel}"
                    userControls:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding SendTextCommand}"
                    Visibility="{Binding IsSendTextVisible, Converter={StaticResource OnVisibilityConverter}}" />
        </Border>
    </Grid>
</Page>