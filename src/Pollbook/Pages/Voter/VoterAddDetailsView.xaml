<Page x:Class="Pollbook.Pages.Voter.VoterAddDetailsView"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:Pollbook.Pages.Voter"
      xmlns:userControls="clr-namespace:Pollbook.UserControls"
      mc:Ignorable="d" 
      d:DesignHeight="1200" d:DesignWidth="1920"
      DataContext="{Binding VoterAddDetails, Source={StaticResource Locator}}">

    <Grid  Background="{StaticResource Gray8Brush}">
        <Grid.RowDefinitions>
            <RowDefinition Height="0.6*"></RowDefinition>
            <RowDefinition Height="3*"></RowDefinition>
            <RowDefinition Height="*"></RowDefinition>
        </Grid.RowDefinitions>

        <Label Content="{Binding Title}"  Style="{StaticResource StandardTitle}" Margin="120,40,0,20"></Label>

        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="0.5*"></ColumnDefinition>
                <ColumnDefinition Width="*"></ColumnDefinition>
            </Grid.ColumnDefinitions>

            <!--Time and Location-->
            <Border Grid.Column="0" Background="White" CornerRadius="3" Margin="120,0,10,56">
                <WrapPanel Margin="50,0,0,0" Orientation="Vertical" VerticalAlignment="Top" HorizontalAlignment="Left" Width="460" Height="717">
                    <Label Content="Time and Location" Style="{StaticResource LargeBoldLabel}" Margin="0,40,0,0"></Label>

                    <Label Content="Timestamp" Style="{StaticResource StandardLightLabel}"></Label>
                    <TextBlock Margin="5,-10" Text="{Binding Timestamp}" Style="{StaticResource Display3BoldTextBlock}"></TextBlock>

                    <Label Margin="0,10" Content="Poll Place" Style="{StaticResource StandardLightLabel}"></Label>
                    <TextBlock Margin="5,-25" Text="{Binding Pollplace}" Style="{StaticResource Display3BoldTextBlock}" TextWrapping="WrapWithOverflow"></TextBlock>

                    <Label Margin="0,20" Content="Device Name" Style="{StaticResource StandardLightLabel}"></Label>
                    <TextBlock Margin="5,-35" Text="{Binding SystemId}" Style="{StaticResource Display3BoldTextBlock}"></TextBlock>

                    <Label Margin="0,10" Content="Serial Number" Style="{StaticResource StandardLightLabel}"></Label>
                    <TextBlock Margin="5,-25" Text="{Binding SerialNumber}" Style="{StaticResource Display3BoldTextBlock}"></TextBlock>

                    <Label Margin="0,20" Content="Username" Style="{StaticResource StandardLightLabel}"></Label>
                    <TextBlock Margin="5,-35" Text="{Binding Username}" Style="{StaticResource Display3BoldTextBlock}"></TextBlock>
                </WrapPanel>
            </Border>

            <!--Status Details-->
            <Border Grid.Column="1" Background="White" CornerRadius="3"  Margin="10,0,120,56">
                <StackPanel Margin="50,0,0,0">
                    <Label Content="{Binding DetailsTitle}" Style="{StaticResource LargeBoldLabel}" Margin="0,40,0,0"></Label>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"></ColumnDefinition>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*"></RowDefinition>
                            <RowDefinition Height="*"></RowDefinition>
                        </Grid.RowDefinitions>

                        <StackPanel Grid.Row="0" Grid.Column="0">
                            <Label Content="Added Voter" Style="{StaticResource StandardLightLabel}"></Label>
                            <TextBlock  Text="{Binding Name}" Style="{StaticResource Display3BoldTextBlock}" Margin="5,-10,0,0"></TextBlock>
                        </StackPanel>
                        <StackPanel Grid.Row="1" Grid.Column="0">
                            <Label Content="VoterKey" Style="{StaticResource StandardLightLabel}"></Label>
                            <TextBlock  Text="{Binding SourceKey}" Style="{StaticResource Display3BoldTextBlock}" Margin="5,-10,0,0"></TextBlock>
                        </StackPanel>
                    </Grid>
                </StackPanel>
            </Border>

        </Grid>

        <Grid Grid.Row="2" Background="White">
            <Button Grid.Column="0"
                    Content="Back"
                    Style="{StaticResource SecondaryLargeButton}"
                    Margin="40,40,0,40"
                    userControls:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding BackCommand}"
                    HorizontalAlignment="Left"
                    VerticalAlignment="Bottom" />
        </Grid>

    </Grid>
</Page>
