<UserControl x:Class="Pollbook.Pages.Voter.CaptureIdVoterVerificationView"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:userControls="clr-namespace:Pollbook.UserControls"
      mc:Ignorable="d" 
      d:DesignHeight="1000" d:DesignWidth="1600"
      DataContext="{Binding CaptureIdVoterVerification, Source={StaticResource Locator}}"
      Loaded="Page_Loaded"
     
      Background="{StaticResource Gray8Brush}">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="auto" />
        </Grid.RowDefinitions>

        <!-- Header - <PERSON> 0 -->
        <Grid Grid.Row="0">
            <Label Content="{Binding LabelContent}" HorizontalAlignment="Left" VerticalAlignment="Top" Margin="107,40,0,0" Style="{StaticResource Body1SemiBoldFont}"/>
            <Label Content="{Binding SubLabelContent}" HorizontalAlignment="Left" VerticalAlignment="Top" Margin="107,96,0,-0.2" Style="{StaticResource Display2SemiBoldFont}"/>
        </Grid>

        <Grid Grid.Row="1" Margin="170,0,100,0">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="auto" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>

                <Grid Grid.Row="0" Margin="0,40,0,0">
                    <Label Content="{Binding CaptureIdTypeStatement}" Style="{StaticResource Body1SemiBoldFont}"/>
                </Grid>
                <Grid Grid.Row="1" Margin="0,-20,0,0">
                    <ListBox Name="CaptureIdTypes"  
                             Style="{StaticResource RadioListBox}"
                             ItemsSource="{Binding Path=VoterCaptureTypes, Mode=TwoWay}"   
                             SelectedValuePath="Capture_Voter_ID_Type_Name" 
                             ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                             SelectedItem ="{Binding SelectedVerificationType, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" >
                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding Capture_Voter_ID_Type_Name}" TextWrapping="WrapWithOverflow" Margin="0,0,50,0"  Width="400"/>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                        <ListBox.ItemsPanel>
                            <ItemsPanelTemplate>
                                <UniformGrid Rows="5" Columns="3" />
                            </ItemsPanelTemplate>
                        </ListBox.ItemsPanel>
                    </ListBox>

                </Grid>
            </Grid>
        </Grid>
 
        <!-- Row 2 of the Grid for the Buttons -->
        <Grid Grid.Row="2" Background="White">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="auto" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <Button Grid.Column="0"
                    Content="{Binding BackLabel}"
                    Margin="40"
                    userControls:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding BackCommand}">
                <Button.Style>
                    <Style TargetType="Button" BasedOn="{StaticResource SecondaryLargeButton}" />
                </Button.Style>
            </Button>

            <Button Grid.Column="1"
                    Content="{Binding NextLabel}"
                    HorizontalAlignment="Right"
                    Margin="40"
                    userControls:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding NextCommand}"
                    IsEnabled="{Binding Path= HasVerificationTypeSelection}">
                <Button.Style>
                    <Style TargetType="Button" BasedOn="{StaticResource PrimaryLargeButton}">
                    </Style>
                </Button.Style>
            </Button>
        </Grid>
    </Grid>
</UserControl>
