using ESS.Pollbook.ViewModel.Affidavit;
using ESS.Pollbook.ViewModel.Voter;
using System.Text.RegularExpressions;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;

namespace Pollbook.Pages.Voter
{
    /// <summary>
    /// Interaction logic for VoterEditView.xaml
    /// </summary>
    public partial class VoterEditView : Page, IContextView
    {
        public VoterEditView()
        {
            InitializeComponent();
        }

        public bool IsModal => ((VoterEditViewModel)DataContext).IsModal;

        public SolidColorBrush PrimaryBackgroundBrush =>
            (SolidColorBrush)Application.Current.FindResource("WhiteBrush");

        private void Page_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Back)
            {
                e.Handled = true;
            }
        }

        private void Page_Loaded(object sender, RoutedEventArgs e)
        {
            ((VoterEditViewModel)DataContext).PageIsLoaded();
        }

        private void HouseNumberFractionTextBox_OnPreviewTextInput(object sender, TextCompositionEventArgs e)
        {
            // Allow only alphanumeric, "-", "/", "(", ")"
            Regex regex = new Regex(@"^[a-zA-Z0-9\-/()]+$");
            e.Handled = !regex.IsMatch(e.Text);
        }
    }
}
