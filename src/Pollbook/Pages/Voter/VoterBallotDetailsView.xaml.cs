using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.ViewModel.Voter;
using ESS.Pollbook.DynamicControls;
using CommonServiceLocator;
using ESS.Pollbook.Core.Logging;

namespace Pollbook.Pages.Voter
{
    /// <summary>
    /// Interaction logic for VoterBallotDetailsView.xaml
    /// </summary>
    public partial class VoterBallotDetailsView : UserControl
    {
        public VoterBallotDetailsView()
        {
            InitializeComponent();
        }
        public void VoterBallotDetails_OnLoaded(object sender, RoutedEventArgs e)
        {
            var viewModel = ((VoterBallotDetailsViewModel)DataContext);

            viewModel.PageIsLoaded();

            try
            {
                int maxCol = 2; // starts at 0, count=2

                int currRow = 0;
                int currCol = 0;
                Grid container = null;
                CheckBox checkBox = null;

                foreach (DynamicControlsDto dto in viewModel.GetDynamicControl.ToList<DynamicControlsDto>().Where(n => n.Visible == 1).OrderBy(n => n.Sort_Order))
                {
                    if (container == null || !container.Name.Equals(dto.Container_Name))
                        container = DynamicControlsLib.FindChild<Grid>(Application.Current.MainWindow, dto.Container_Name);

                    if (container != null)
                    {
                        foreach (UIElement element in DynamicControlsLib.Parse(dto))
                        {
                            if (element.GetType() == typeof(CheckBox))
                            {
                                checkBox = (CheckBox)element;
                                checkBox.Style = Application.Current.FindResource("ShadowCheckbox") as Style;
                                Grid.SetRow(element, currRow);
                                Grid.SetColumn(element, currCol);

                                container.Children.Add(element);
                            }
                            else
                            {
                                Grid.SetRow(element, currRow);
                                Grid.SetColumn(element, currCol);

                                container.Children.Add(element);
                            }
                        }

                        if (currCol == maxCol - 1)
                        {
                            currCol = 0;
                            currRow++;
                        }
                        else
                            currCol++;
                    }
                }
            }
            catch (Exception ex)
            {
                var essLogger = ServiceLocator.Current.GetInstance<IEssLogger>();
                Dictionary<string, string> logProps = new Dictionary<string, string> { { "Action", "VoterBallotDetailsView.VoterBallotDetails_OnLoaded" } };
                essLogger.LogError(ex, logProps);
            }
        }
    }
}
