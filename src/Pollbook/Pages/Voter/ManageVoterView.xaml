<Page x:Class="Pollbook.Pages.Voter.ManageVoterView"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:uicore="clr-namespace:Pollbook.UICore"
      xmlns:userControls="clr-namespace:Pollbook.UserControls"
      mc:Ignorable="d" 
      d:DesignHeight="1200" d:DesignWidth="1920"
      DataContext="{Binding ManageVoter, Source={StaticResource Locator}}">

    <Page.Resources>

        <!--Drop down labels-->
        <Style x:Key="labelStyle" TargetType="Label">
            <Setter Property="FontFamily" Value="Noto Sans Display"></Setter>
            <Setter Property="FontSize" Value="30"></Setter>
            <Style.Triggers>
                <DataTrigger Binding="{Binding CanChangeStatuses}" Value="False">
                    <Setter Property="Foreground" Value="{StaticResource Gray4Brush}"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding CanChangeStatuses}" Value="True">
                    <Setter Property="Foreground" Value="{StaticResource Gray1Brush}"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </Page.Resources>

    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="1.8*" />
            <ColumnDefinition Width="*" />
        </Grid.ColumnDefinitions>

        <Label Grid.Column="0" Content="Manage Voter Status" Style="{StaticResource StandardBoldLabel}" Margin="60,50,0,0" />

        <StackPanel Grid.Column="1" Margin="0,200,0,23" >

            <StackPanel Orientation="Horizontal">
                <Label  Content="Voter name: " Style="{StaticResource labelStyle}" Foreground="{StaticResource Gray1Brush}"  Margin="0,0,0,0"/>
                <Label  Content="{Binding VoterFullName}" Style="{StaticResource labelStyle}" Foreground="{StaticResource Gray1Brush}"/>
            </StackPanel>

            <Label  Content="Voter Status" Margin="0,20,0,20" Style="{StaticResource StandardLabel}"/>
            <uicore:WatermarkComboBox x:Name="VoterStatus"
                              Height="106"
                              ItemsSource="{Binding Path=VoterStatuses}"
                              SelectedValue="{Binding StatusId, Mode=TwoWay}"   
                              DisplayMemberPath="JurisdictionEnumerationValueDescription" 
                              SelectedValuePath="JurisdictionEnumerationValueId"
                              SelectedItem="{Binding SelectedVoterStatus}"
                              WatermarkText="Select"
                              MaxDropDownHeight="440" 
                              IsEnabled="{Binding CanChangeStatuses}"
                              Style="{StaticResource {x:Type ComboBox}}">
            </uicore:WatermarkComboBox>

            <Label  Content="Absentee Status" Margin="0,35,0,20" Style="{StaticResource StandardLabel}"/>
            <uicore:WatermarkComboBox x:Name="AbsenteeStatusesComboBox" Margin="0,0,0,0" 
                          Height="106"
                          ItemsSource="{Binding Path=AbsenteeStatuses, Mode=TwoWay}"
                          DisplayMemberPath="JurisdictionEnumerationValueDescription"
                          SelectedValuePath="JurisdictionEnumerationValueId"
                          SelectedItem="{Binding Path=SelectedAbsenteeStatus, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                          IsEnabled="{Binding CanChangeStatuses}"
                          WatermarkText="Select"
                          MaxDropDownHeight="440"
                          Style="{StaticResource {x:Type ComboBox}}">
            </uicore:WatermarkComboBox>

            <Label  Content="ID Required" Margin="0,35,0,20" Style="{StaticResource StandardLabel}"/>
            <uicore:WatermarkComboBox x:Name="IdRequiredComboBox" Margin="0,0,0,0" 
                      Height="106"
                      ItemsSource="{Binding Path=IdRequiredStatuses, Mode=TwoWay}"
                      DisplayMemberPath="JurisdictionEnumerationValueDescription"
                      SelectedValuePath="JurisdictionEnumerationValueId"
                      SelectedItem="{Binding Path=SelectedIdRequiredStatus, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                      IsEnabled="{Binding CanChangeStatuses}"
                      WatermarkText="Select"
                      MaxDropDownHeight="440"
                      Style="{StaticResource {x:Type ComboBox}}">
            </uicore:WatermarkComboBox>
            <Button Margin="0,60,0,20"
                    Style="{StaticResource PrimarySmallButton}"
                    Content="{Binding CancelBallotLabel}"
                    HorizontalAlignment="Left"
                    userControls:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding CancelBallotCommand}"
                    IsEnabled="{Binding IsCancelBallotButtonEnabled}"/>

        </StackPanel>

        <StackPanel Grid.Column="2" Margin="0,40,40,0" Orientation="Horizontal" HorizontalAlignment="Right" VerticalAlignment="Top">
            <Button Content="{Binding CancelLabel}"
                    Style="{StaticResource SecondarySmallButton}"
                    Width="180"
                    userControls:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding CancelCommand}"
                    Margin="0,0,19,0" />
            <Button Content="{Binding SaveLabel}"
                    Style="{StaticResource PrimarySmallButton}"
                    Width="180"
                    userControls:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding SaveCommand}"
                    IsEnabled="{Binding CanChangeStatuses}"/>
        </StackPanel>

    </Grid>
</Page>