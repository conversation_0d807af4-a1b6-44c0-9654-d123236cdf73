using ESS.Pollbook.ViewModel.Voter;
using System;
using System.Windows;
using System.Windows.Media;

namespace Pollbook.Pages.Voter
{
	public partial class CaptureIdVoterConfirmationView : IContextView
	{
        public bool IsModal => false;

        public SolidColorBrush PrimaryBackgroundBrush => (SolidColorBrush)Application.Current.FindResource("Gray8Brush");

        public CaptureIdVoterConfirmationView()
        {
	        InitializeComponent();
        }

        private void Page_Loaded(object sender, EventArgs eventArgs)
        {
	        ((CaptureIdVoterConfirmationViewModel)DataContext).PageLoaded();
        }
	}
}
