using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace Pollbook.Pages.Voter
{
	public partial class CaptureIdVoterPresentFormView : Page, IContextView
	{
        public bool IsModal => true;
        public SolidColorBrush PrimaryBackgroundBrush => (SolidColorBrush)Application.Current.FindResource("Gray8Brush");

        public CaptureIdVoterPresentFormView()
        {
	        InitializeComponent();
        }

        private void Page_Loaded(object sender, EventArgs eventArgs)
        {
	        if (Application.Current.MainWindow == null) // makes resharper happy.
		        return;

	        // unrolls any possible combo boxes
	        foreach (var x in UiUtilities.FindVisualChildren<ComboBox>(Window.GetWindow(Application.Current.MainWindow)))
		        x.IsDropDownOpen = false;

	        ((ESS.Pollbook.ViewModel.Voter.CaptureIdVoterPresentFormViewModel)DataContext).PageLoaded();
        }
	}
}
