<UserControl x:Class="Pollbook.Pages.Voter.VoterBallotDetailsView"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:uiCore="clr-namespace:Pollbook.UICore"
      xmlns:userControls="clr-namespace:Pollbook.UserControls"
      mc:Ignorable="d" 
      Loaded="VoterBallotDetails_OnLoaded"
      DataContext="{Binding VoterBallotDetails, Source={StaticResource Locator}}"
      d:DesignHeight="1200" d:DesignWidth="1920">
   <UserControl.Resources>
      <uiCore:NullImageConverter x:Key="ImageConversion"/>
      <uiCore:BooleanToVisibilityConverter x:Key="CallOutVisibilityConverter" True="Visible" False="Collapsed" />
      <uiCore:BooleanToVisibilityConverter x:Key="AddressChangedVisibilityConverter" True="Visible" False="Hidden" />
   </UserControl.Resources>
   <!--Main container. Holds the information areas and the navigation buttons-->
   <Grid Background="{StaticResource Gray8Brush}">

      <Grid.RowDefinitions>
         <RowDefinition Height="0.6*"></RowDefinition>
         <RowDefinition Height="3.6*"></RowDefinition>
         <RowDefinition Height="1*"></RowDefinition>
      </Grid.RowDefinitions>

      <Label Grid.Row="0" Grid.Column="0" Content="{Binding DetailHeaderCopy}" Style="{StaticResource Display2SemiBoldFont}" HorizontalAlignment="Left" Margin="140,50,0,0" VerticalAlignment="Top"/>

      <!--Information Area containers-->
      <Grid Grid.Row="1" Margin="120,0,120,0">

         <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"></ColumnDefinition>
            <ColumnDefinition Width="1.4*"></ColumnDefinition>
         </Grid.ColumnDefinitions>

         <!--Time and Location-->
         <Border Background="White" Grid.Column="0" Margin="20,20,20,20" CornerRadius="8">
            <WrapPanel Orientation="Vertical" VerticalAlignment="Top" HorizontalAlignment="Left" Height="750" Margin="50,10,0,0">
               <Label Content="Time and Location" Style="{StaticResource Display3BoldFont}" HorizontalAlignment="Left" VerticalAlignment="Top"/>

               <Label Content="Timestamp" Style="{StaticResource Body1SemiBoldLightGray2Font}"/>
               <TextBlock Text="{Binding Timestamp}"  Style="{StaticResource Display3BoldTextBlock}" Uid="txtTimestamp" AutomationProperties.AutomationId="AAtxtTimestamp" AutomationProperties.Name="AAtxtTimestampName"/>

               <Label Content="Poll Place" Style="{StaticResource Body1SemiBoldLightGray2Font}"/>
               <TextBlock Text="{Binding Pollplace}" ToolTip="{Binding Pollplace}" Style="{StaticResource Display3BoldTextBlock}" Uid="txtPollplace" AutomationProperties.AutomationId="AAtxtPollplace" AutomationProperties.Name="AAtxtPollplaceName" TextWrapping="WrapWithOverflow" TextTrimming="CharacterEllipsis" Height="100"/>

               <Label Content="Device ID" Style="{StaticResource Body1SemiBoldLightGray2Font}"/>
               <TextBlock Text="{Binding SystemId}"  Style="{StaticResource Display3BoldTextBlock}" Uid="txtSystemId" AutomationProperties.AutomationId="AAtxtSystemId" AutomationProperties.Name="AAtxtSystemIdName"/>

               <Label Content="Serial Number" Style="{StaticResource Body1SemiBoldLightGray2Font}"/>
               <TextBlock Text="{Binding SerialNumber}"  Style="{StaticResource Display3BoldTextBlock}" Uid="txtSerialNumber" AutomationProperties.AutomationId="AAtxtSerialNumber" AutomationProperties.Name="AAtxtSerialNumberName"/>

               <Label Content="Username" Style="{StaticResource Body1SemiBoldLightGray2Font}"/>
               <TextBlock Text="{Binding Username}"  Style="{StaticResource Display3BoldTextBlock}" Uid="txtUsername" AutomationProperties.AutomationId="AAtxtUsername" AutomationProperties.Name="AAtxtUsernameName"/>
            </WrapPanel>
         </Border>

         <!--Issued Details-->
         <Border Background="White" Grid.Column="1" Margin="20,20,20,20" CornerRadius="8">
            <Grid>
               <Grid.RowDefinitions>
                  <RowDefinition Height="240"/>
                  <RowDefinition Height="120"/>
                  <RowDefinition Height="120"/>
                  <RowDefinition Height="*"/>
                  <RowDefinition Height="*"/>
               </Grid.RowDefinitions>

               <Grid Grid.Row="0" Margin="0,10,20,0">

                  <Grid.RowDefinitions>
                     <RowDefinition Height="50"></RowDefinition>
                     <RowDefinition Height="50"></RowDefinition>
                     <RowDefinition Height="50"></RowDefinition>
                     <RowDefinition Height="50"></RowDefinition>
                  </Grid.RowDefinitions>

                  <Grid.ColumnDefinitions>
                     <ColumnDefinition Width="100*" />
                     <ColumnDefinition Width="100*" />

                  </Grid.ColumnDefinitions>

                  <Label Content="Ballot Style" Style="{StaticResource Body1SemiBoldLightGray2Font}" HorizontalAlignment="Left" Margin="20,0,0,0" VerticalAlignment="Top" Grid.Row="0" Grid.Column="0" Width="169"/>
                  <TextBlock Margin="24,-10,0,0" Text="{Binding BallotStyleDescription}"  Style="{StaticResource Display3BoldTextBlock}" VerticalAlignment="Top" Grid.Row="1" Grid.Column="0" Uid="txtBallotstyleDescription" AutomationProperties.AutomationId="AAtxtBallotstyleDescription" AutomationProperties.Name="AAtxtBallotstyleDescriptionName"/>

                  <Label Content="Ballot Type" Style="{StaticResource Body1SemiBoldLightGray2Font}" HorizontalAlignment="Left" Margin="20,0,0,0" VerticalAlignment="Top" Grid.Row="0" Grid.Column="1"/>
                  <TextBlock Margin="24,-10,0,0" Text="{Binding BallotType}"  Style="{StaticResource Display3BoldTextBlock}" VerticalAlignment="Top" Grid.Row="1" Grid.Column="1" Uid="txtBallotType" AutomationProperties.AutomationId="AAtxtBallotType" AutomationProperties.Name="AAtxtBallotTypeName"/>

                  <Label Content="Ballot Party" Style="{StaticResource Body1SemiBoldLightGray2Font}" HorizontalAlignment="Left" Margin="20,0,0,0" VerticalAlignment="Top" Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="2"/>
                  <TextBlock Margin="24,-10,0,0" Text="{Binding BallotParty}"  Style="{StaticResource Display3BoldTextBlock}" 
                            VerticalAlignment="Top" 
                            Grid.Row="3" Grid.Column="0" Grid.ColumnSpan="2"  
                            Uid="txtBallotParty" AutomationProperties.AutomationId="AAtxtBallotParty" 
                            AutomationProperties.Name="AAtxtBallotPartyName" Height="100" TextWrapping="NoWrap" TextTrimming="CharacterEllipsis"/>
               </Grid>


               <Grid Grid.Row="1" Margin="0,0,0,0">

                  <Grid.RowDefinitions>
                     <RowDefinition></RowDefinition>
                     <RowDefinition></RowDefinition>
                  </Grid.RowDefinitions>

                  <Grid.ColumnDefinitions>
                     <ColumnDefinition Width="100*" />
                     <ColumnDefinition Width="100*" />
                     <ColumnDefinition Width="100*" />
                  </Grid.ColumnDefinitions>

                  <Label Content="Voting Method" Style="{StaticResource Body1SemiBoldLightGray2Font}" HorizontalAlignment="Left" Margin="20,0,0,0" VerticalAlignment="Top" Grid.Row="0" Grid.Column="0"/>
                  <TextBlock Text="{Binding VotingMethod}"  Style="{StaticResource Display3BoldTextBlock}" VerticalAlignment="Top"  Margin="24,-10,0,0" Grid.Row="1" Grid.Column="0" Uid="txtVotingMethod" AutomationProperties.AutomationId="AAtxtVotingMethod" AutomationProperties.Name="AAtxtVotingMethodName"/>

                  <Label Content="{Binding ProvisionalIdLabel}" Style="{StaticResource Body1SemiBoldLightGray2Font}" HorizontalAlignment="Left" Margin="20,0,0,0" VerticalAlignment="Top" Grid.Row="0" Grid.Column="1" Visibility="{Binding ProvisionalIdVisibility, Converter={StaticResource CallOutVisibilityConverter}}" />
                  <TextBlock Text="{Binding ProvisionalId}"  Style="{StaticResource Display3BoldTextBlock}" Margin="24,-10,0,0" VerticalAlignment="Top" Grid.Row="1" Grid.Column="1" Grid.ColumnSpan="2" Uid="txtProvisionalId" Visibility= "{Binding ProvisionalIdVisibility, Converter={StaticResource CallOutVisibilityConverter}}"/>

                  <Label Content="Ballot Number" Style="{StaticResource Body1SemiBoldLightGray2Font}" HorizontalAlignment="Left" Margin="20,0,0,0" VerticalAlignment="Top" Grid.Row="0" Grid.Column="1" Visibility="{Binding CapturedBallotNumberVisibility, Converter={StaticResource CallOutVisibilityConverter}}" />
                  <TextBlock Text="{Binding CapturedBallotNumber}"  Style="{StaticResource Display3BoldTextBlock}" Margin="24,-10,0,0" VerticalAlignment="Top" Grid.Row="1" Grid.Column="1" Visibility="{Binding CapturedBallotNumberVisibility, Converter={StaticResource CallOutVisibilityConverter}}" />
               </Grid>

               <!--Voter Signatures-->
               <Grid Grid.Row="2" Margin="0,0,0,0">
                  <Grid.ColumnDefinitions>
                     <ColumnDefinition Width="*"></ColumnDefinition>
                     <ColumnDefinition Width="*"></ColumnDefinition>
                     <ColumnDefinition Width="*"></ColumnDefinition>
                  </Grid.ColumnDefinitions>

                  <!--Signature on transaction-->
                  <StackPanel  Grid.Row="0" Grid.Column="0" Margin="20,10,0,0" Visibility="{Binding ShowSignature, Converter={StaticResource AddressChangedVisibilityConverter}}">
                     <Label Content="Signature Captured" Style="{StaticResource Body1SemiBoldLightGray2Font}" VerticalAlignment="Bottom" HorizontalAlignment="Left"/>
                     <Image Name="SignatureOnTransaction" Margin="10,0,0,0" HorizontalAlignment="Left" Source="{Binding SignatureOnTransaction, Converter={StaticResource ImageConversion}}" Visibility="{Binding ShowSignatureCaptured, Converter={StaticResource CallOutVisibilityConverter}}"
                                   Stretch="Uniform" MaxHeight="45" RenderOptions.BitmapScalingMode="HighQuality" />
                     <TextBlock Style="{StaticResource Display3BoldTextBlock}" Margin="10,14,0,0" HorizontalAlignment="Left" Text="Not Available" Visibility="{Binding ShowSignatureOnPaper, Converter={StaticResource CallOutVisibilityConverter}}" Uid="txtEnteredonPaper" AutomationProperties.AutomationId="AAtxtEnteredonPaper" AutomationProperties.Name="AAtxtEnteredonPaperName"/>
                  </StackPanel>

                  <!--Signature on file-->
                  <StackPanel  Grid.Row="0" Grid.Column="1" Margin="0,10,0,0" Visibility="{Binding ShowSignatureOnFile, Converter={StaticResource AddressChangedVisibilityConverter}}">
                     <Label Content="Signature on File" Style="{StaticResource Body1SemiBoldLightGray2Font}" HorizontalAlignment="Left" Margin="50,0,0,0" VerticalAlignment="Bottom"/>
                     <Image Name="SignatureOnFile" Opacity=".30" Margin="50,0,0,0" HorizontalAlignment="Left" Source="{Binding SignatureOnFile}" 
                                   Stretch="Uniform" MaxHeight="45" RenderOptions.BitmapScalingMode="HighQuality" />
                  </StackPanel>

                  <StackPanel Grid.Row="0" Grid.Column="2" Margin="0,10,0,0" Visibility="{Binding ShowAddressChanged, Converter={StaticResource CallOutVisibilityConverter}}">
                     <Label Content="{Binding AddressChangedLabel}" Style="{StaticResource Body1SemiBoldLightGray2Font}" HorizontalAlignment="Left" Margin="20,0,0,10" VerticalAlignment="Top" />
                     <TextBlock Text="{Binding AddressChanged}" Style="{StaticResource Display3BoldTextBlock}" HorizontalAlignment="Left" Margin="24,-10,0,0" VerticalAlignment="Top" Uid="txtAddressChanged" AutomationProperties.AutomationId="AAtxtAddressChanged" AutomationProperties.Name="AAtxtAddressChangedName"/>
                  </StackPanel>
               </Grid>

               <!-- Voter Verifications -->
               <Grid Grid.Row="3" Name="VoterVerificationGrid" Margin="24,0,0,0" Grid.RowSpan="2" >
                  <Grid.ColumnDefinitions>
                     <ColumnDefinition Width=".5*" />
                     <ColumnDefinition Width=".5*" />
                  </Grid.ColumnDefinitions>
                  <Grid.RowDefinitions>
                     <RowDefinition Height="auto" />
                     <RowDefinition Height="auto" />
                     <RowDefinition Height="auto" />
                  </Grid.RowDefinitions>
               </Grid>
            </Grid>
         </Border>
      </Grid>

      <Grid Grid.Row="2" Grid.Column="0" Background="White">
         <Button Grid.Column="0"
                 Content="{Binding BackLabel}"
                 userControls:ButtonHelper.DisableMultipleClicks="True"
                 Command="{Binding BackCommand}"
                 HorizontalAlignment="Left"
                 Margin="40,3,40,10">
            <Button.Style>
               <Style TargetType="Button" BasedOn="{StaticResource SecondaryLargeButton}" />
            </Button.Style>
         </Button>
         <Button Grid.Column="0"
                 Content="{Binding PrintLabel}"
                 userControls:ButtonHelper.DisableMultipleClicks="True"
                 Command="{Binding PrintCommand}"
                 IsEnabled="{Binding PrintEnabled}"
                 Visibility="{Binding PrintVisible, Converter={StaticResource CallOutVisibilityConverter}}"
                 HorizontalAlignment="Right"
                 Margin="40,06,40,10">
            <Button.Style>
               <Style TargetType="Button" BasedOn="{StaticResource PrimaryLargeButton}" />
            </Button.Style>
         </Button>
      </Grid>

   </Grid>


</UserControl>
