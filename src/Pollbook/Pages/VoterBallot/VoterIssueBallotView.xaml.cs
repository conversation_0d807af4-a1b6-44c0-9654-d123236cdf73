using ESS.Pollbook.ViewModel.VoterBallot;
using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace Pollbook.Pages.VoterBallot
{
    /// <summary>
    /// Interaction logic for VoterIssueBallotView.xaml
    /// </summary>
    public partial class VoterIssueBallotView : UserControl, IContextView
    {
        public VoterIssueBallotView()
        {
            InitializeComponent();
        }

        //Don't need "X" button on this page
        public bool IsModal => true;

        public SolidColorBrush PrimaryBackgroundBrush => (SolidColorBrush)Application.Current.FindResource("Gray8Brush");

        private void VoterIssueBallotView_OnInitialized(object sender, EventArgs e)
        {
            ((VoterIssueBallotViewModel)DataContext).PageInitialized();
        }
    }
}
