<UserControl x:Class="Pollbook.Pages.SelectReissueBallotReason.SelectReissueBallotReasonView"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:userControls="clr-namespace:Pollbook.UserControls"
      mc:Ignorable="d" 
      d:DesignHeight="1000" d:DesignWidth="1600"
      DataContext="{Binding ReissueBallotReason, Source={StaticResource Locator}}"
      Loaded="SelectReissueBallotReasonView_OnLoad"
      Background="{StaticResource Gray8Brush}">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="auto" />
        </Grid.RowDefinitions>

        <!-- Header -->
        <Grid Grid.Row="0">
            <Label Content="Reissue Ballot" HorizontalAlignment="Left" VerticalAlignment="Top" Margin="107,40,0,0" Style="{StaticResource Body1SemiBoldFont}"/>
            <Label Content="Reissue Ballot Reason" HorizontalAlignment="Left" VerticalAlignment="Top" Margin="107,96,0,-0.2" Style="{StaticResource Display2SemiBoldFont}"/>
        </Grid>

        <Grid Grid.Row="1" Margin="270,0,270,0">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="auto"></RowDefinition>
                    <RowDefinition Height="*"></RowDefinition>
                </Grid.RowDefinitions>
                <Grid Grid.Row="1" HorizontalAlignment="Center" VerticalAlignment="Top">
                    <StackPanel  Margin="350,50,0,0">
                        <Label Content="Select Reissue Reason" Style="{StaticResource Body1SemiBoldFont}"/>
                        <ListBox Style="{StaticResource RadioListBox}" 
                                 ItemsSource="{Binding Path=BallotReissueReasons, Mode=TwoWay}"   
                                 SelectedValuePath="JurisdictionEnumerationValueId"
                                 ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                                 SelectedItem ="{Binding SelectedBallotReissueReason, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" 
                                 Uid="txtReissueReason" AutomationProperties.AutomationId="AAReissueReason" 
                                 AutomationProperties.Name="AAReissueReasonList">
                            <ListBox.ItemTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding JurisdictionEnumerationValueDescription}" TextWrapping="WrapWithOverflow" Margin="0,0,60,0"  Width="545" >
                                    </TextBlock>
                                </DataTemplate>
                            </ListBox.ItemTemplate>
                            <ListBox.ItemsPanel>
                                <ItemsPanelTemplate>
                                    <WrapPanel Orientation="Vertical" Margin="0,20,100,0"/>
                                </ItemsPanelTemplate>
                            </ListBox.ItemsPanel>
                        </ListBox>
                      
                        <StackPanel.Style>
                            <Style TargetType="StackPanel">
                                <Setter Property="Visibility" Value="Visible"/>
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding Parties.Count}" Value="0">
                                        <Setter Property="Visibility" Value="Hidden"/>
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </StackPanel.Style>
                    </StackPanel>

                </Grid>
            </Grid>
        </Grid>

        <!-- Buttons -->
        <Grid Grid.Row="2" Background="White">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="auto" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <Button Grid.Column="0"
                    Content="{Binding BackLabel}"
                    Margin="40"
                    userControls:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding BackCommand}">
                <Button.Style>
                    <Style TargetType="Button" BasedOn="{StaticResource SecondaryLargeButton}" />
                </Button.Style>
            </Button>

            <Button Grid.Column="1"
                    Content="{Binding NextLabel}"
                    HorizontalAlignment="Right"
                    Margin="40"
                    userControls:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding NextCommand}"
                    IsEnabled="{Binding Path= HasBallotReissueReasonSelection}">
                <Button.Style>
                    <Style TargetType="Button" BasedOn="{StaticResource PrimaryLargeButton}">
                    </Style>
                </Button.Style>
            </Button>
        </Grid>
    </Grid>
</UserControl>
