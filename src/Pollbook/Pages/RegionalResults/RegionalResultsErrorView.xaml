<Page x:Class="Pollbook.Pages.RegionalResults.RegionalResultsErrorView"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:i="http://schemas.microsoft.com/expression/2010/interactivity"
      xmlns:uiCore="clr-namespace:Pollbook.UICore"
      xmlns:usercontrols="clr-namespace:Pollbook.UserControls"
      mc:Ignorable="d"
      d:DesignHeight="1200" d:DesignWidth="1920"
      DataContext="{Binding RegionalResultsError, Source={StaticResource Locator}}"
      Title="RegionalResultsErrorView">
    <Page.Resources>
        <uiCore:BooleanToVisibilityConverter x:Key="VisibilityConverter" True="Hidden" False="Visible" />
    </Page.Resources>

    <Grid Background="{StaticResource Gray7Brush}">
        <Grid.RowDefinitions>
            <RowDefinition Height="150"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Title -->
        <Grid Grid.Row="0"  Background="{StaticResource Gray7Brush}" Margin="0,0,50,0">
            <Label HorizontalAlignment="Left" VerticalAlignment="Top" Margin="120,60,0,0" Content="{Binding PageTitle}" >
                <Label.Style>
                    <Style TargetType="Label" BasedOn="{StaticResource Body1SemiBoldFont}">
                    </Style>
                </Label.Style>
            </Label>
        </Grid>

        <!-- No Regional Results Notice! -->
        <Grid Grid.Row="1" Background="White" Margin="120,0,121,150">
            <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center" Margin="0,0,0,0">
                <Viewbox Height="120" Width="120" Stretch="Uniform" HorizontalAlignment="Center">
                    <Path>
                        <Path.Style>
                            <Style TargetType="{x:Type Path}" BasedOn="{StaticResource ErrorIcon}" />
                        </Path.Style>
                    </Path>
                </Viewbox>
                <TextBlock Text="{Binding RegionalResultsUnavailable}" HorizontalAlignment="Center" Margin="0,30,0,0">
                    <TextBlock.Style>
                        <Style TargetType="TextBlock" BasedOn="{StaticResource Display1TextBlock}" />
                    </TextBlock.Style>
                </TextBlock>
                <TextBlock Text="{Binding ContactInfo}" HorizontalAlignment="Center" Margin="0,-5,0,0">
                    <TextBlock.Style>
                        <Style TargetType="TextBlock" BasedOn="{StaticResource Display1TextBlock}" />
                    </TextBlock.Style>
                </TextBlock>
            </StackPanel>
        </Grid>
    </Grid>
</Page>
