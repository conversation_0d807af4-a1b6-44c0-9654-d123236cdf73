using ESS.Pollbook.ViewModel.RegionalResults;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace Pollbook.Pages.RegionalResults
{
	/// <summary>
	/// Interaction logic for RegionalResultsTransmitResultsView.xaml
	/// </summary>
	public partial class RegionalResultsTransmitResultsView : Page, IContextView
	{
		public bool IsModal => false;
		public SolidColorBrush PrimaryBackgroundBrush => (SolidColorBrush)Application.Current.FindResource("BackgroundBrush");

		public RegionalResultsTransmitResultsView()
		{
			InitializeComponent();
		}

		private async void Page_Loaded(object sender, RoutedEventArgs e)
		{
			await ((RegionalResultsTransmitResultsViewModel)DataContext).PageLoadedAsync();
		}
	}
}
