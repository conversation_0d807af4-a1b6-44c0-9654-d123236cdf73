<Page x:Class="Pollbook.Pages.RegionalResults.RegionalResultsTransmitResultsView"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:local="clr-namespace:Pollbook.UICore"
      xmlns:userControls="clr-namespace:Pollbook.UserControls"
      xmlns:gif="https://github.com/XamlAnimatedGif/XamlAnimatedGif"
      mc:Ignorable="d"
      d:DesignHeight="1200" d:DesignWidth="1920"
      Title="RegionalResultsTransmitResultsView"
      Background="{StaticResource BackgroundBrush}"
      Loaded="Page_Loaded"
      DataContext="{Binding RegionalResultsTransmitResults, Source={StaticResource Locator}}">

    <Page.Resources>
        <local:BooleanToVisibilityConverter x:Key="OnVisibilityConverter" True="Visible" False="Collapsed" />
        <local:UtcToLocalDateTimeConverter x:Key="UtcToLocalDateTimeConverter" />
        <Style x:Key="MediaCountPanelLabelText" TargetType="TextBlock">
            <Setter Property="Foreground" Value="#343741" />
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="HorizontalAlignment" Value="Left" />
            <Setter Property="FontFamily" Value="Noto Sans Display" />
            <Setter Property="FontWeight" Value="Normal" />
            <Setter Property="FontSize" Value="20" />
            <Setter Property="Margin" Value="20 0 0 0" />
        </Style>
        <Style x:Key="MediaCountPanelCountText" TargetType="TextBlock">
            <Setter Property="Foreground" Value="#343741" />
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="HorizontalAlignment" Value="Center" />
            <Setter Property="FontFamily" Value="Noto Sans Display" />
            <Setter Property="FontWeight" Value="Bold" />
            <Setter Property="FontSize" Value="30" />
        </Style>
        <Style x:Key="WidgetActiveTitle" TargetType="TextBlock">
            <Setter Property="Foreground" Value="{StaticResource Gray2Brush}" />
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="HorizontalAlignment" Value="Left" />
            <Setter Property="FontFamily" Value="Noto Sans Display" />
            <Setter Property="FontWeight" Value="Medium" />
            <Setter Property="FontSize" Value="30" />
            <Setter Property="Margin" Value="0 0 0 5" />
        </Style>
        <Style x:Key="WidgetActiveSubTitle" TargetType="TextBlock">
            <Setter Property="Foreground" Value="{StaticResource Gray1Brush}" />
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="HorizontalAlignment" Value="Left" />
            <Setter Property="FontFamily" Value="Noto Sans Display" />
            <Setter Property="FontWeight" Value="Normal" />
            <Setter Property="FontSize" Value="24" />
        </Style>
        <Style x:Key="WidgetInitialTitle" TargetType="TextBlock">
            <Setter Property="Foreground" Value="{StaticResource Gray4Brush}" />
            <Setter Property="VerticalAlignment" Value="Top"/>
            <Setter Property="HorizontalAlignment" Value="Left" />
            <Setter Property="FontFamily" Value="Noto Sans Display" />
            <Setter Property="FontWeight" Value="Medium" />
            <Setter Property="FontSize" Value="30" />
            <Setter Property="Margin" Value="20 10" />
        </Style>
        <Style x:Key="WidgetCompleteTitle" TargetType="TextBlock">
            <Setter Property="Foreground" Value="{StaticResource Gray4Brush}" />
            <Setter Property="VerticalAlignment" Value="Top"/>
            <Setter Property="HorizontalAlignment" Value="Left" />
            <Setter Property="FontFamily" Value="Noto Sans Display" />
            <Setter Property="FontWeight" Value="Medium" />
            <Setter Property="FontSize" Value="30" />
            <Setter Property="Margin" Value="20 10" />
        </Style>
        <Style x:Key="WidgetIconViewbox" TargetType="Viewbox">
            <Setter Property="VerticalAlignment" Value="Top"/>
            <Setter Property="HorizontalAlignment" Value="Center" />
            <Setter Property="Width" Value="50" />
            <Setter Property="Stretch" Value="Uniform" />
        </Style>
    </Page.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!--Header-->
        <Grid Grid.Row="0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="1.6*"/>
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition Height="*" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>

            <StackPanel Grid.Column="0" Grid.Row="0" Grid.RowSpan="2" Margin="120,40,10,0">
                <StackPanel Orientation="Horizontal">
                    <Label Content="{Binding PageLabel}" Style="{StaticResource LargeTitle}"/>
                </StackPanel>
                <!--Election Name-->
                <Label Content="{Binding ElectionName}" Style="{StaticResource StandardTitleSmall}" Foreground="{StaticResource Gray2Brush}" />
                <!--Election Date-->
                <Label Content="{Binding ElectionDate}" Style="{StaticResource StandardLightLabel}"/>
            </StackPanel>
            <StackPanel Grid.Row="0" Grid.Column="1" Orientation="Horizontal" Margin="50,40,100,0">
                <Grid Width="320" Margin="0 0 20 0">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="62" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="2*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <Grid Grid.Column="0" Background="{StaticResource ExpressGreenLightBrush}">
                        <TextBlock Text="{Binding ExpectedMedia}" Style="{StaticResource MediaCountPanelLabelText}"/>
                    </Grid>
                    <Grid Grid.Column="1" Background="{StaticResource ExpressGreenMediumBrush}">
                        <TextBlock Text="{Binding ExpectedMediaCount}" Style="{StaticResource MediaCountPanelCountText}"/>
                    </Grid>
                </Grid>
                <Grid Width="320" Margin="0 0 20 0">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="62" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="2*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <Grid Grid.Column="0" Background="{StaticResource SecondaryLightBlueBrush2}">
                        <TextBlock Text="{Binding ReadMedia}" Style="{StaticResource MediaCountPanelLabelText}"/>
                    </Grid>
                    <Grid Grid.Column="1" Background="{StaticResource SecondaryLightBlueBrush}">
                        <TextBlock Text="{Binding ReadMediaCount}" Style="{StaticResource MediaCountPanelCountText}"/>
                    </Grid>
                </Grid>
                <Grid Width="320">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="62" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="2*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <Grid Grid.Column="0" Background="{StaticResource SecondaryLightYellowBrush}">
                        <TextBlock Text="{Binding SentMedia}" Style="{StaticResource MediaCountPanelLabelText}"/>
                    </Grid>
                    <Grid Grid.Column="1" Background="{StaticResource GoldenRodMediumBrush}">
                        <TextBlock Text="{Binding SentMediaCount}" Style="{StaticResource MediaCountPanelCountText}"/>
                    </Grid>
                </Grid>
            </StackPanel>
        </Grid>
        <!--Tabs-->
        <TabControl Grid.Row="1" BorderThickness="0 2 0 0" Margin="120 0 120 0"
                    SelectedIndex="{Binding SelectedTab, Mode=TwoWay}" >
            <TabControl.Resources>
                <Style TargetType="TabItem">
                    <Setter Property="Template">
                        <Setter.Value>
                            <ControlTemplate TargetType="TabItem">
                                <Border Name="Panel" CornerRadius="3, 3, 0, 0" Width="285" Height="100" Background="White">
                                    <StackPanel Orientation="Vertical" VerticalAlignment="Bottom">
                                        <ContentPresenter x:Name="ContentSite" VerticalAlignment="Center" HorizontalAlignment="Left" Margin="0 0 0 30">
                                            <ContentPresenter.Content>
                                                <ContentControl x:Name="ContentCtl" Content="{TemplateBinding Header}" Style="{StaticResource Body1BoldFont}"/>
                                            </ContentPresenter.Content>
                                        </ContentPresenter>
                                        <Rectangle Fill="{StaticResource SecondaryDarkBlueBrush}" Height="6" Width="{Binding ActualWidth, ElementName=ContentCtl}" VerticalAlignment="Bottom" HorizontalAlignment="Left">
                                            <Rectangle.Style>
                                                <Style TargetType="Rectangle">
                                                    <Setter Property="Visibility" Value="Hidden"/>
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type TabItem}}, Path=IsSelected}" Value="True">
                                                            <Setter Property="Visibility" Value="Visible"/>
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </Rectangle.Style>
                                        </Rectangle>
                                    </StackPanel>
                                </Border>
                                <ControlTemplate.Triggers>
                                    <Trigger Property="IsSelected" Value="True">
                                        <Setter TargetName="Panel" Property="Background" Value="White" />
                                        <Setter TargetName="ContentCtl" Property="Foreground" Value="{StaticResource SecondaryDarkBlueBrush}"/>
                                    </Trigger>
                                    <Trigger Property="IsSelected" Value="False">
                                        <Setter TargetName="Panel" Property="Background" Value="White" />
                                        <Setter TargetName="ContentCtl" Property="Foreground" Value="{StaticResource Gray4Brush}"/>
                                    </Trigger>
                                    <Trigger Property="TabIndex" Value="0">
                                        <Setter TargetName="Panel" Property="Margin" Value="0,0,10,0" />
                                    </Trigger>
                                </ControlTemplate.Triggers>
                            </ControlTemplate>
                        </Setter.Value>
                    </Setter>
                </Style>
            </TabControl.Resources>
            <!--Transmit Results Tab-->
            <TabItem Header="Transmit Results">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="auto" />
                        <RowDefinition Height="auto" />
                    </Grid.RowDefinitions>
                    <Border Grid.Row="0" BorderBrush="{StaticResource Gray5Brush}" BorderThickness="1" Margin="0 20 0 0">
                        <Grid Margin="5">
                            <!--Widgets-->
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition>
                                    <ColumnDefinition.Style>
                                        <Style TargetType="{x:Type ColumnDefinition}">
                                            <Setter Property="Width" Value="*" />
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding InsertStatus}" Value="Current">
                                                    <Setter Property="Width" Value="3*" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </ColumnDefinition.Style>
                                </ColumnDefinition>
                                <ColumnDefinition>
                                    <ColumnDefinition.Style>
                                        <Style TargetType="{x:Type ColumnDefinition}">
                                            <Setter Property="Width" Value="*" />
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding ValidateStatus}" Value="Current">
                                                    <Setter Property="Width" Value="3*" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding ValidateStatus}" Value="Error">
                                                    <Setter Property="Width" Value="3*" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </ColumnDefinition.Style>
                                </ColumnDefinition>
                                <ColumnDefinition>
                                    <ColumnDefinition.Style>
                                        <Style TargetType="{x:Type ColumnDefinition}">
                                            <Setter Property="Width" Value="*" />
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding SendStatus}" Value="Current">
                                                    <Setter Property="Width" Value="3*" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding SendStatus}" Value="Error">
                                                    <Setter Property="Width" Value="3*" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </ColumnDefinition.Style>
                                </ColumnDefinition>
                                <ColumnDefinition>
                                    <ColumnDefinition.Style>
                                        <Style TargetType="{x:Type ColumnDefinition}">
                                            <Setter Property="Width" Value="*" />
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding CompleteStatus}" Value="Complete">
                                                    <Setter Property="Width" Value="3*" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </ColumnDefinition.Style>
                                </ColumnDefinition>
                            </Grid.ColumnDefinitions>
                            <Grid Grid.Column="0" Margin="15 10">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="150" />
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="2*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.Style>
                                    <Style TargetType="{x:Type Grid}">
                                        <Setter Property="Background" Value="{StaticResource Gray8Brush}" />
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding InsertStatus}" Value="Current">
                                                <Setter Property="Background" Value="{StaticResource SecondaryLightYellowBrush}" />
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </Grid.Style>
                                <StackPanel Grid.Column="0" Orientation="Vertical" Margin="20 10">
                                    <StackPanel.Style>
                                        <Style TargetType="{x:Type StackPanel}">
                                            <Setter Property="Visibility" Value="Collapsed" />
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding InsertStatus}" Value="Current">
                                                    <Setter Property="Visibility" Value="Visible" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </StackPanel.Style>
                                    <TextBlock Text="Insert a USB Flash Drive to Begin" Style="{StaticResource WidgetActiveTitle}"/>
                                    <TextBlock Text="This process cannot be cancelled." Style="{StaticResource WidgetActiveSubTitle}"/>
                                </StackPanel>
                                <StackPanel Grid.Column="1">
                                    <StackPanel.Style>
                                        <Style TargetType="{x:Type StackPanel}">
                                            <Setter Property="Visibility" Value="Collapsed" />
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding InsertStatus}" Value="Current">
                                                    <Setter Property="Visibility" Value="Visible" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </StackPanel.Style>
                                    <Image gif:AnimationBehavior.SourceUri="/Styles/Images/Insert-Usb-Animation.gif" Margin="0 -60 0 0" />
                                </StackPanel>
                                <StackPanel Grid.Column="0" Grid.ColumnSpan="2" Orientation="Vertical">
                                    <StackPanel.Style>
                                        <Style TargetType="{x:Type StackPanel}">
                                            <Setter Property="Visibility" Value="Collapsed" />
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding InsertStatus}" Value="Complete">
                                                    <Setter Property="Visibility" Value="Visible" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </StackPanel.Style>
                                    <TextBlock Text="Insert" Style="{StaticResource WidgetCompleteTitle}"/>
                                    <Viewbox Style="{StaticResource WidgetIconViewbox}">
                                        <Path Style="{StaticResource SuccessIcon}" Stretch="Fill" />
                                    </Viewbox>
                                </StackPanel>
                            </Grid>
                            <Grid Grid.Column="1"  Margin="15 10">
                                <!--<TextBlock Text="test" FontSize="40"></TextBlock>-->
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="150" />
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.Style>
                                    <Style TargetType="{x:Type Grid}">
                                        <Setter Property="Background" Value="{StaticResource Gray8Brush}" />
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding ValidateStatus}" Value="Current">
                                                <Setter Property="Background" Value="{StaticResource SecondaryLightYellowBrush}" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding ValidateStatus}" Value="Error">
                                                <Setter Property="Background" Value="{StaticResource SecondaryLightYellowBrush}" />
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </Grid.Style>
                                <StackPanel Grid.Column="0" Orientation="Vertical" Margin="20 10" >
                                    <StackPanel.Style>
                                        <Style TargetType="{x:Type StackPanel}">
                                            <Setter Property="Visibility" Value="Collapsed" />
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding ValidateStatus}" Value="Current">
                                                    <Setter Property="Visibility" Value="Visible" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </StackPanel.Style>
                                    <TextBlock Text="Validating Results" Style="{StaticResource WidgetActiveTitle}"/>
                                    <StackPanel Orientation="Horizontal">
                                        <Viewbox Style="{StaticResource WidgetIconViewbox}" Width="55" Margin="0 10 5 0">
                                            <Path Style="{StaticResource ImportantIcon}" Stretch="Fill" />
                                        </Viewbox>
                                        <StackPanel Orientation="Vertical">
                                            <TextBlock Text="Do not remove the USB flash drive." Style="{StaticResource WidgetActiveSubTitle}"/>
                                            <Image gif:AnimationBehavior.SourceUri="/Styles/Images/Regional-Results-Progress-Bar.gif" Margin="10 0" />
                                        </StackPanel>
                                    </StackPanel>
                                </StackPanel>
                                <StackPanel Grid.Column="0" Orientation="Vertical" Margin="20 10" >
                                    <StackPanel.Style>
                                        <Style TargetType="{x:Type StackPanel}">
                                            <Setter Property="Visibility" Value="Collapsed" />
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding ValidateStatus}" Value="Error">
                                                    <Setter Property="Visibility" Value="Visible" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </StackPanel.Style>
                                    <TextBlock Text="Validating Results" Style="{StaticResource WidgetActiveTitle}" Margin="10 0"/>
                                    <StackPanel Orientation="Horizontal">
                                        <Viewbox Style="{StaticResource WidgetIconViewbox}"  Width="55" Margin="0 15 5 0">
                                            <Path Style="{StaticResource WarningIconGoldenRod}" Stretch="Fill" />
                                        </Viewbox>
                                        <StackPanel Orientation="Vertical">
                                            <TextBlock Text="Invalid Poll Media." Style="{StaticResource WidgetActiveSubTitle}" Margin="10 0"/>
                                            <Image gif:AnimationBehavior.SourceUri="/Styles/Images/Regional-Results-Progress-Bar-warning.gif" Margin="10 0" />
                                        </StackPanel>
                                    </StackPanel>
                                </StackPanel>
                                <StackPanel Grid.Column="0" Orientation="Vertical">
                                    <StackPanel.Style>
                                        <Style TargetType="{x:Type StackPanel}">
                                            <Setter Property="Visibility" Value="Collapsed" />
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding ValidateStatus}" Value="Initial">
                                                    <Setter Property="Visibility" Value="Visible" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </StackPanel.Style>
                                    <TextBlock Text="Validate" Style="{StaticResource WidgetInitialTitle}"/>
                                    <Viewbox Style="{StaticResource WidgetIconViewbox}">
                                        <Path Style="{StaticResource SuccessIconGray}" Stretch="Fill" />
                                    </Viewbox>
                                </StackPanel>
                                <StackPanel Grid.Column="0" Orientation="Vertical">
                                    <StackPanel.Style>
                                        <Style TargetType="{x:Type StackPanel}">
                                            <Setter Property="Visibility" Value="Collapsed" />
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding ValidateStatus}" Value="Complete">
                                                    <Setter Property="Visibility" Value="Visible" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </StackPanel.Style>
                                    <TextBlock Text="Validate" Style="{StaticResource WidgetCompleteTitle}"/>
                                    <Viewbox Style="{StaticResource WidgetIconViewbox}">
                                        <Path Style="{StaticResource SuccessIcon}" Stretch="Fill" />
                                    </Viewbox>
                                </StackPanel>
                            </Grid>
                            <Grid Grid.Column="2"  Margin="15 10">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="150" />
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.Style>
                                    <Style TargetType="{x:Type Grid}">
                                        <Setter Property="Background" Value="{StaticResource Gray8Brush}" />
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding SendStatus}" Value="Current">
                                                <Setter Property="Background" Value="{StaticResource SecondaryLightYellowBrush}" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding SendStatus}" Value="Error">
                                                <Setter Property="Background" Value="{StaticResource SecondaryLightYellowBrush}" />
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </Grid.Style>
                                <StackPanel Grid.Column="0" Orientation="Vertical" Margin="20 10" >
                                    <StackPanel.Style>
                                        <Style TargetType="{x:Type StackPanel}">
                                            <Setter Property="Visibility" Value="Collapsed" />
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding SendStatus}" Value="Current">
                                                    <Setter Property="Visibility" Value="Visible" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </StackPanel.Style>
                                    <TextBlock Text="Sending Results" Style="{StaticResource WidgetActiveTitle}"/>
                                    <StackPanel Orientation="Horizontal">
                                        <Viewbox Style="{StaticResource WidgetIconViewbox}" Width="55" Margin="0 10 5 0">
                                            <Path Style="{StaticResource ImportantIcon}" Stretch="Fill" />
                                        </Viewbox>
                                        <StackPanel Orientation="Vertical">
                                            <TextBlock Text="Do not remove the USB flash drive." Style="{StaticResource WidgetActiveSubTitle}"/>
                                            <Image gif:AnimationBehavior.SourceUri="/Styles/Images/Regional-Results-Progress-Bar.gif" Margin="10 0" />
                                        </StackPanel>
                                    </StackPanel>
                                </StackPanel>
                                <StackPanel Grid.Column="0" Orientation="Vertical" Margin="20 10" >
                                    <StackPanel.Style>
                                        <Style TargetType="{x:Type StackPanel}">
                                            <Setter Property="Visibility" Value="Collapsed" />
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding SendStatus}" Value="Error">
                                                    <Setter Property="Visibility" Value="Visible" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </StackPanel.Style>
                                    <TextBlock Text="Sending Results" Style="{StaticResource WidgetActiveTitle}" Margin="10 0"/>
                                    <StackPanel Orientation="Horizontal">
                                        <Viewbox Style="{StaticResource WidgetIconViewbox}"  Width="55" Margin="0 15 5 0">
                                            <Path Style="{StaticResource WarningIconGoldenRod}" Stretch="Fill" />
                                        </Viewbox>
                                        <StackPanel Orientation="Vertical">
                                            <TextBlock Text="Unable to send results." Style="{StaticResource WidgetActiveSubTitle}" Margin="10 0"/>
                                            <Image gif:AnimationBehavior.SourceUri="/Styles/Images/Regional-Results-Progress-Bar-warning.gif" Margin="10 0" />
                                        </StackPanel>
                                    </StackPanel>
                                </StackPanel>
                                <StackPanel Grid.Column="0" Orientation="Vertical">
                                    <StackPanel.Style>
                                        <Style TargetType="{x:Type StackPanel}">
                                            <Setter Property="Visibility" Value="Collapsed" />
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding SendStatus}" Value="Initial">
                                                    <Setter Property="Visibility" Value="Visible" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </StackPanel.Style>
                                    <TextBlock Text="Send" Style="{StaticResource WidgetInitialTitle}"/>
                                    <Viewbox Style="{StaticResource WidgetIconViewbox}">
                                        <Path Style="{StaticResource FileCircleIcon}" Stretch="Fill" />
                                    </Viewbox>
                                </StackPanel>
                                <StackPanel Grid.Column="0" Orientation="Vertical">
                                    <StackPanel.Style>
                                        <Style TargetType="{x:Type StackPanel}">
                                            <Setter Property="Visibility" Value="Collapsed" />
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding SendStatus}" Value="Complete">
                                                    <Setter Property="Visibility" Value="Visible" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </StackPanel.Style>
                                    <TextBlock Text="Send" Style="{StaticResource WidgetCompleteTitle}"/>
                                    <Viewbox Style="{StaticResource WidgetIconViewbox}">
                                        <Path Style="{StaticResource SuccessIcon}" Stretch="Fill" />
                                    </Viewbox>
                                </StackPanel>
                            </Grid>
                            <Grid Grid.Column="3" Margin="15 10">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="150" />
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.Style>
                                    <Style TargetType="{x:Type Grid}">
                                        <Setter Property="Background" Value="{StaticResource Gray8Brush}" />
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding CompleteStatus}" Value="Complete">
                                                <Setter Property="Background" Value="{StaticResource SecondaryLightYellowBrush}" />
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </Grid.Style>
                                <StackPanel Grid.Column="0" Orientation="Vertical" Margin="20 10">
                                    <StackPanel.Style>
                                        <Style TargetType="{x:Type StackPanel}">
                                            <Setter Property="Visibility" Value="Collapsed" />
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding CompleteStatus}" Value="Complete">
                                                    <Setter Property="Visibility" Value="Visible" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </StackPanel.Style>
                                    <TextBlock Text="Transmission Complete" Style="{StaticResource WidgetActiveTitle}"/>
                                    <StackPanel Orientation="Horizontal">
                                        <Viewbox Style="{StaticResource WidgetIconViewbox}" Width="55" Margin="0 10 5 0">
                                            <Path Style="{StaticResource StarIconComplete}" Stretch="Fill" />
                                        </Viewbox>
                                        <TextBlock Text="It is safe to remove the USB Flash drive." Style="{StaticResource WidgetActiveSubTitle}" Margin="10 -10 0 10"/>
                                    </StackPanel>
                                </StackPanel>
                                <StackPanel Grid.Column="0" Orientation="Vertical">
                                    <StackPanel.Style>
                                        <Style TargetType="{x:Type StackPanel}">
                                            <Setter Property="Visibility" Value="Collapsed" />
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding CompleteStatus}" Value="Initial">
                                                    <Setter Property="Visibility" Value="Visible" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </StackPanel.Style>
                                    <TextBlock Text="Complete" Style="{StaticResource WidgetInitialTitle}"/>
                                    <Viewbox Style="{StaticResource WidgetIconViewbox}">
                                        <Path Style="{StaticResource StarIcon}" Stretch="Fill" />
                                    </Viewbox>
                                </StackPanel>
                            </Grid>
                        </Grid>
                    </Border>
                    <Grid Grid.Row="1" Margin="0,20,0,20">
                        <!--Media Activity-->
                        <Grid.RowDefinitions>
                            <RowDefinition Height="auto" />
                            <RowDefinition Height="*" />
                        </Grid.RowDefinitions>
                        <Grid Margin="0,10,0,0" Grid.Row="1">
                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                <StackPanel.Style>
                                    <Style TargetType="StackPanel">
                                        <Setter Property="Visibility" Value="Collapsed" />
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding LoadingResults}" Value="True">
                                                <Setter Property="Visibility" Value="Visible" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding LoadingResults}" Value="False">
                                                <Setter Property="Visibility" Value="Collapsed" />
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </StackPanel.Style>
                                <userControls:Spinner/>
                            </StackPanel>
                            <Grid Name="ResultsGridTest">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="*" />
                                    <RowDefinition Height="*" />
                                </Grid.RowDefinitions>
                                <Grid.Style>
                                    <Style TargetType="Grid">
                                        <Setter Property="Visibility" Value="Collapsed" />
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding LoadingResults}" Value="True">
                                                <Setter Property="Visibility" Value="Collapsed" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding LoadingResults}" Value="False">
                                                <Setter Property="Visibility" Value="Visible" />
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </Grid.Style>
                                <!-- Rows -->
                                <ListView Grid.Column="0" Grid.Row="0"
                                          ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                                          ScrollViewer.VerticalScrollBarVisibility="Auto"
                                          ScrollViewer.CanContentScroll="False"
                                          ItemsSource="{Binding ActivityList,IsAsync=True}"
                                          BorderThickness="0"
                                          Grid.ColumnSpan="2">
                                    <ListView.Resources>
                                        <Style TargetType="ScrollBar"
                                               BasedOn="{StaticResource CustomVerticalScrollBarStyle}" />
                                    </ListView.Resources>
                                    <ListView.View>
                                        <GridView>
                                            <GridViewColumn Header="Poll Place" DisplayMemberBinding="{Binding PollPlace}"
                                                            Width="400" />
                                            <GridViewColumn Header="Media Id"
                                                            DisplayMemberBinding="{Binding MediaId}"
                                                            Width="200" />
                                            <GridViewColumn Header="Status"
                                                            DisplayMemberBinding="{Binding Status}"
                                                            Width="400" />
                                            <GridViewColumn Header="Equipment"
                                                            DisplayMemberBinding="{Binding Equipment}"
                                                            Width="200" />
                                            <GridViewColumn Header="Load Time"
                                                            DisplayMemberBinding="{Binding LoadTime, Converter={StaticResource UtcToLocalDateTimeConverter}, StringFormat=MM-dd-yyyy h:mmtt}"
                                                            Width="400" />
                                            <GridView.ColumnHeaderContainerStyle>
                                                <Style TargetType="GridViewColumnHeader">
                                                    <Setter Property="Foreground" Value="{StaticResource Gray1Brush}" />
                                                    <Setter Property="FontFamily" Value="Noto Sans Display" />
                                                    <Setter Property="FontSize" Value="30" />
                                                    <Setter Property="FontWeight" Value="600" />
                                                    <Setter Property="Height" Value="60" />
                                                    <Setter Property="Margin" Value="30,20,0,0" />
                                                    <Setter Property="HorizontalContentAlignment" Value="Stretch" />
                                                    <Setter Property="VerticalContentAlignment" Value="Stretch" />
                                                    <Setter Property="Template">
                                                        <Setter.Value>
                                                            <ControlTemplate TargetType="{x:Type GridViewColumnHeader}">
                                                                <TextBlock Text="{TemplateBinding Content}" Padding="5"
                                                                           Width="{TemplateBinding Width}"
                                                                           TextAlignment="Left" />
                                                            </ControlTemplate>
                                                        </Setter.Value>
                                                    </Setter>
                                                </Style>
                                            </GridView.ColumnHeaderContainerStyle>
                                        </GridView>
                                    </ListView.View>

                                    <ListView.ItemContainerStyle>
                                        <Style TargetType="ListViewItem"
                                               BasedOn="{StaticResource ReportListViewItemStyle}">
                                            <Setter Property="BorderBrush" Value="Transparent" />
                                            <Setter Property="BorderThickness" Value="0" />
                                            <Setter Property="Background" Value="White" />
                                            <Setter Property="Focusable" Value="false" />
                                            <Setter Property="Foreground" Value="{StaticResource Gray1Brush}" />
                                            <Setter Property="FontWeight" Value="Normal" />
                                            <Setter Property="FontFamily" Value="Noto Sans Display" />
                                            <Setter Property="FontSize" Value="30" />
                                            <Setter Property="Height" Value="85" />
                                        </Style>
                                    </ListView.ItemContainerStyle>
                                </ListView>
                                <Grid Grid.Column="0" Grid.ColumnSpan="2" Background="{StaticResource Gray8Brush}" Grid.Row="1" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" Visibility="{Binding NoResults, Converter={StaticResource OnVisibilityConverter}}">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="80" />
                                        <RowDefinition Height="*" />
                                        <RowDefinition Height="*" />
                                        <RowDefinition Height="*" />
                                        <RowDefinition Height="100" />
                                    </Grid.RowDefinitions>
                                    <Viewbox Grid.Row="1" Style="{StaticResource WidgetIconViewbox}">
                                        <Path Style="{StaticResource UsbIcon}" Stretch="Fill" />
                                    </Viewbox>
                                    <Label Grid.Row="2" Content="No Media" HorizontalAlignment="Center" Style="{StaticResource StandardBoldLabel}" Foreground="{StaticResource Gray2Brush}" />
                                    <Label Grid.Row="3" Style="{StaticResource StandardLightLabel}" Foreground="{StaticResource SecondaryMediumBlueBrush}" HorizontalAlignment="Center">
                                        <TextBlock TextDecorations="Underline">
                                            Insert a USB Flash Drive to begin import
                                        </TextBlock>
                                    </Label>
                                </Grid>
                            </Grid>
                        </Grid>
                    </Grid>
                </Grid>
            </TabItem>
            <!--Media Activity Tab-->
            <TabItem Header="Media Status" Margin="30 0 0 0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="auto" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>
                    <Grid Margin="0,10,0,0" Grid.Row="1" Height="1100">
                        <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                            <StackPanel.Style>
                                <Style TargetType="StackPanel">
                                    <Setter Property="Visibility" Value="Collapsed" />
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding LoadingResults}" Value="True">
                                            <Setter Property="Visibility" Value="Visible" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding LoadingResults}" Value="False">
                                            <Setter Property="Visibility" Value="Collapsed" />
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </StackPanel.Style>
                            <userControls:Spinner />
                        </StackPanel>
                        <Grid Name="MediaActivityGrid">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*" />
                                <RowDefinition Height="*" />
                            </Grid.RowDefinitions>
                            <Grid.Style>
                                <Style TargetType="Grid">
                                    <Setter Property="Visibility" Value="Collapsed" />
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding LoadingResults}" Value="True">
                                            <Setter Property="Visibility" Value="Collapsed" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding LoadingResults}" Value="False">
                                            <Setter Property="Visibility" Value="Visible" />
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </Grid.Style>
                            <!-- Rows -->
                            <ListView Grid.Column="0" Grid.Row="0"
                                      ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                                      ScrollViewer.VerticalScrollBarVisibility="Auto"
                                      ScrollViewer.CanContentScroll="False"
                                      ItemsSource="{Binding MediaActivityList,IsAsync=True}"
                                      BorderThickness="0"
                                      Grid.ColumnSpan="2" Grid.RowSpan="2">
                                <ListView.Resources>
                                    <Style TargetType="ScrollBar"
                                           BasedOn="{StaticResource CustomVerticalScrollBarStyle}" />
                                    <Style TargetType="TextBlock">
                                        <Setter Property="TextWrapping" Value="Wrap"/>
                                    </Style>
                                </ListView.Resources>
                                <ListView.View>
                                    <GridView>
                                        <GridViewColumn Header="Region" DisplayMemberBinding="{Binding RegionName}"
                                                        Width="150" />
                                        <GridViewColumn Header="Poll Place"
                                                        DisplayMemberBinding="{Binding PollPlaceName}"
                                                        Width="300" />
                                        <GridViewColumn Header="Media Id"
                                                        DisplayMemberBinding="{Binding MediaId}"
                                                        Width="200" />
                                        <GridViewColumn Header="Equipment"
                                                        DisplayMemberBinding="{Binding Equipment}"
                                                        Width="200" />
                                        <GridViewColumn Header="Status"
                                                        DisplayMemberBinding="{Binding Status}"
                                                        Width="350" />
                                        <GridViewColumn Header="Time Read"
                                                        DisplayMemberBinding="{Binding TimeRead, Converter={StaticResource UtcToLocalDateTimeConverter}, StringFormat=MM-dd-yyyy h:mmtt}"
                                                        Width="250" />
                                        <GridViewColumn Header="Time Sent"
                                                        DisplayMemberBinding="{Binding TimeSent, Converter={StaticResource UtcToLocalDateTimeConverter}, StringFormat=MM-dd-yyyy h:mmtt}"
                                                        Width="250" />
                                        <GridView.ColumnHeaderContainerStyle>
                                            <Style TargetType="GridViewColumnHeader">
                                                <Setter Property="Foreground" Value="{StaticResource Gray1Brush}" />
                                                <Setter Property="FontFamily" Value="Noto Sans Display" />
                                                <Setter Property="FontSize" Value="30" />
                                                <Setter Property="FontWeight" Value="600" />
                                                <Setter Property="Height" Value="60" />
                                                <Setter Property="Margin" Value="30,20,0,0" />
                                                <Setter Property="HorizontalContentAlignment" Value="Stretch" />
                                                <Setter Property="VerticalContentAlignment" Value="Stretch" />
                                                <Setter Property="Template">
                                                    <Setter.Value>
                                                        <ControlTemplate TargetType="{x:Type GridViewColumnHeader}">
                                                            <TextBlock Text="{TemplateBinding Content}" Padding="5"
                                                                       Width="{TemplateBinding Width}"
                                                                       TextAlignment="Left" />
                                                        </ControlTemplate>
                                                    </Setter.Value>
                                                </Setter>
                                            </Style>
                                        </GridView.ColumnHeaderContainerStyle>
                                    </GridView>
                                </ListView.View>

                                <ListView.ItemContainerStyle>
                                    <Style TargetType="ListViewItem"
                                           BasedOn="{StaticResource ReportListViewItemStyle}">
                                        <Setter Property="BorderBrush" Value="Transparent" />
                                        <Setter Property="BorderThickness" Value="0" />
                                        <Setter Property="Background" Value="White" />
                                        <Setter Property="Focusable" Value="false" />
                                        <Setter Property="Foreground" Value="{StaticResource Gray1Brush}" />
                                        <Setter Property="FontWeight" Value="Normal" />
                                        <Setter Property="FontFamily" Value="Noto Sans Display" />
                                        <Setter Property="FontSize" Value="30" />
                                        <Setter Property="Height" Value="85" />
                                    </Style>
                                </ListView.ItemContainerStyle>
                            </ListView>

                        </Grid>
                    </Grid>
                </Grid>
            </TabItem>
            <!--Log Viewer Tab-->
            <TabItem Header="Log Viewer" Margin="35 0 0 0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="auto" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>
                    <Grid Margin="0,10,0,0" Grid.Row="1" Height="1100">
                        <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                            <StackPanel.Style>
                                <Style TargetType="StackPanel">
                                    <Setter Property="Visibility" Value="Collapsed" />
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding LoadingResults}" Value="True">
                                            <Setter Property="Visibility" Value="Visible" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding LoadingResults}" Value="False">
                                            <Setter Property="Visibility" Value="Collapsed" />
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </StackPanel.Style>
                            <userControls:Spinner />
                        </StackPanel>
                        <Grid Name="LogViewerGrid">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*" />
                                <RowDefinition Height="*" />
                            </Grid.RowDefinitions>
                            <Grid.Style>
                                <Style TargetType="Grid">
                                    <Setter Property="Visibility" Value="Collapsed" />
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding LoadingResults}" Value="True">
                                            <Setter Property="Visibility" Value="Collapsed" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding LoadingResults}" Value="False">
                                            <Setter Property="Visibility" Value="Visible" />
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </Grid.Style>
                            <!-- Rows -->
                            <ListView Grid.Column="0" Grid.Row="0"
                                      ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                                      ScrollViewer.VerticalScrollBarVisibility="Auto"
                                      ScrollViewer.CanContentScroll="False"
                                      ItemsSource="{Binding LogViewerList,IsAsync=True}"
                                      BorderThickness="0"
                                      Grid.ColumnSpan="2">
                                <ListView.Resources>
                                    <Style TargetType="ScrollBar"
                                           BasedOn="{StaticResource CustomVerticalScrollBarStyle}" />
                                </ListView.Resources>
                                <ListView.View>
                                    <GridView>
                                        <GridViewColumn Header="Time"
                                                        DisplayMemberBinding="{Binding Time, Converter={StaticResource UtcToLocalDateTimeConverter}, StringFormat=MM-dd-yyyy h:mmtt}"
                                                        Width="400" />
                                        <GridViewColumn Header="Log Id" DisplayMemberBinding="{Binding LogId}"
                                                        Width="200" />
                                        <GridViewColumn Header="Event Type"
                                                        DisplayMemberBinding="{Binding EventType}"
                                                        Width="200" />
                                        <GridViewColumn Header="Message"
                                                        DisplayMemberBinding="{Binding Message}"
                                                        Width="780" />
                                        <GridView.ColumnHeaderContainerStyle>
                                            <Style TargetType="GridViewColumnHeader">
                                                <Setter Property="Foreground" Value="{StaticResource Gray1Brush}" />
                                                <Setter Property="FontFamily" Value="Noto Sans Display" />
                                                <Setter Property="FontSize" Value="30" />
                                                <Setter Property="FontWeight" Value="600" />
                                                <Setter Property="Height" Value="60" />
                                                <Setter Property="Margin" Value="30,20,0,0" />
                                                <Setter Property="HorizontalContentAlignment" Value="Stretch" />
                                                <Setter Property="VerticalContentAlignment" Value="Stretch" />
                                                <Setter Property="Template">
                                                    <Setter.Value>
                                                        <ControlTemplate TargetType="{x:Type GridViewColumnHeader}">
                                                            <TextBlock Text="{TemplateBinding Content}" Padding="5"
                                                                       Width="{TemplateBinding Width}"
                                                                       TextAlignment="Left" />
                                                        </ControlTemplate>
                                                    </Setter.Value>
                                                </Setter>
                                            </Style>
                                        </GridView.ColumnHeaderContainerStyle>
                                    </GridView>
                                </ListView.View>

                                <ListView.ItemContainerStyle>
                                    <Style TargetType="ListViewItem"
                                           BasedOn="{StaticResource ReportListViewItemStyle}">
                                        <Setter Property="BorderBrush" Value="Transparent" />
                                        <Setter Property="BorderThickness" Value="0" />
                                        <Setter Property="Background" Value="White" />
                                        <Setter Property="Focusable" Value="false" />
                                        <Setter Property="Foreground" Value="{StaticResource Gray1Brush}" />
                                        <Setter Property="FontWeight" Value="Normal" />
                                        <Setter Property="FontFamily" Value="Noto Sans Display" />
                                        <Setter Property="FontSize" Value="30" />
                                        <Setter Property="Height" Value="85" />
                                    </Style>
                                </ListView.ItemContainerStyle>
                            </ListView>

                        </Grid>
                    </Grid>
                </Grid>
            </TabItem>
        </TabControl>
        <!--Footer-->
        <Grid Grid.Row="2" Background="{StaticResource WhiteBrush}">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="2*" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>
            <StackPanel Grid.Column="0" Orientation="Horizontal" HorizontalAlignment="Left">
                <Button Content="{Binding BackLabel}"
                        userControls:ButtonHelper.DisableMultipleClicks="True"
                        Command="{Binding BackCommand}"
                        Style="{StaticResource SecondaryLargeButton}"
                        Margin="40"/>
            </StackPanel>
            <StackPanel Grid.Column="1"  Orientation="Horizontal" HorizontalAlignment="Right" Visibility="{Binding IsButtonVisible, Converter={StaticResource OnVisibilityConverter}}" Margin="0 40 40 40">
                <Button Content="{Binding ResetAllLabel}"
                        userControls:ButtonHelper.DisableMultipleClicks="True"
                        Command="{Binding ResetCommand}"
                        Style="{StaticResource TertiaryLargeButton}"  />
            </StackPanel>
            <StackPanel Grid.Column="2"  Orientation="Horizontal" HorizontalAlignment="Right" Visibility="{Binding IsButtonVisible, Converter={StaticResource OnVisibilityConverter}}">
                <Button Content="{Binding ExportLogsLabel}"
                        userControls:ButtonHelper.DisableMultipleClicks="True"
                        Command="{Binding ExportCommand}"
                        Style="{StaticResource PrimaryLargeButton}"
                        Margin="0 40 40 40" />
            </StackPanel>
        </Grid>
    </Grid>
</Page>
