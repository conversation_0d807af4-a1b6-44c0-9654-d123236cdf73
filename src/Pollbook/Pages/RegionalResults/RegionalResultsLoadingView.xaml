<Page x:Class="Pollbook.Pages.RegionalResults.RegionalResultsLoadingView"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:core="clr-namespace:Pollbook.UICore"
      xmlns:usercontrols="clr-namespace:Pollbook.UserControls"
      mc:Ignorable="d"
      Loaded="RegionalResultsLoadingView_OnLoaded"
      DataContext="{Binding RegionalResultsLoading, Source={StaticResource Locator}}"
      Background="{StaticResource Gray8Brush}"
      d:DesignHeight="600" d:DesignWidth="800"
      Title="RegionalResultsLoadingView">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <Grid Grid.Row="1">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Top">
                <usercontrols:Spinner />
                <TextBlock Text="{Binding ValidateConnection}" Style="{StaticResource Display2SemiBoldTextBlock}" HorizontalAlignment="Center" />
            </StackPanel>
        </Grid>
    </Grid>
</Page>
