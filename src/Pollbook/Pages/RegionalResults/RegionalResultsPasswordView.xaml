<Page x:Class="Pollbook.Pages.RegionalResults.RegionalResultsPasswordView"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:textbox="clr-namespace:Pollbook.UserControls.Textbox"
      xmlns:userControls="clr-namespace:Pollbook.UserControls"
      mc:Ignorable="d"
      d:DesignHeight="450" d:DesignWidth="840"
      Unloaded="Page_Unloaded"
      DataContext="{Binding RegionalResultsPassword, Source={StaticResource Locator}}"
      Title="RegionalResultsPasswordView"
      Background="White">

    <Grid Margin="80,50,80,34">
        <StackPanel >
            <Label Style="{StaticResource LargeBoldLabel}" Content="{Binding WindowTitle}"/>
            <Label Content="Password"
                   Style="{StaticResource StandardLabel}"
                   Margin="0,10,0,0"/>

            <textbox:PasswordBoxWithCaret x:Name="PasswordBox" TakeKeyboardFocusOnLoad="True"/>

            <!-- Messages -->
            <StackPanel Orientation="Horizontal">
                <StackPanel.Style>
                    <Style TargetType="StackPanel">
                        <Setter Property="Visibility" Value="Hidden"/>
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding IsAuthenticationValid}" Value="False">
                                <Setter Property="Visibility" Value="Visible"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </StackPanel.Style>
                <Label Content="Error:" Style="{StaticResource ErrorBoldFont}" />
                <Label Content="Please correct password" Style="{StaticResource ErrorFont}" />
            </StackPanel>
        </StackPanel>

        <Button Content="{Binding SubmitLabel}"
                Style="{StaticResource PrimarySmallButton}"
                Margin="0,0,0,0"
                userControls:ButtonHelper.DisableMultipleClicks="True"
                Command="{Binding SubmitCommand}"
                IsEnabled="{Binding SubmitEnabled}"
                HorizontalAlignment="Right"
                VerticalAlignment="Bottom"
                Width="179"
                Click="Button_Click" />
    </Grid>
</Page>
