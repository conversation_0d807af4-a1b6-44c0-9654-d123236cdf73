<Page x:Class="Pollbook.Pages.PrintPollPlaceDetailsFailedView"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:Pollbook.Pages"
      xmlns:userControls="clr-namespace:Pollbook.UserControls"
      mc:Ignorable="d" 
      DataContext="{Binding PrintPollPlaceDetailsFailed, Source={StaticResource Locator}}"
      Background="{StaticResource Gray7Brush}"
      d:DesignHeight="450" d:DesignWidth="800"
      Title="PrintPollPlaceDetailsFailedView">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="auto" />
        </Grid.RowDefinitions>
        <Grid Grid.Row="0" Margin="120,70,120,0">
            <TextBlock Text="Print Poll Place Details" Style="{StaticResource Display2SemiBoldTextBlock}"/>
        </Grid>
        <!-- Body -->
        <Grid Grid.Row="1" Background="{StaticResource Gray7Brush}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Top" Margin="0,200,0,0">
                <Viewbox Height="120" Width="120" Stretch="Uniform" HorizontalAlignment="Center">
                    <Path>
                        <Path.Style>
                            <Style TargetType="{x:Type Path}" BasedOn="{StaticResource ErrorIcon}" />
                        </Path.Style>
                    </Path>
                </Viewbox>
                <TextBlock Text="Unable to Print the Poll Place Details" HorizontalAlignment="Center" Margin="0,30,0,0">
                    <TextBlock.Style>
                        <Style TargetType="TextBlock" BasedOn="{StaticResource Display1TextBlock}" />
                    </TextBlock.Style>
                </TextBlock>
                <TextBlock Text="Please check the printer or call the Elections Office for support." HorizontalAlignment="Center" Margin="0,-5,0,0">
                    <TextBlock.Style>
                        <Style TargetType="TextBlock" BasedOn="{StaticResource StandardTextBlock}" />
                    </TextBlock.Style>
                </TextBlock>
            </StackPanel>
        </Grid>

        <!-- Buttons -->
        <Grid Grid.Row="2" Background="White">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="auto" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <Button Grid.Column="0"
                    Content="{Binding BackLabel}"
                    Style="{StaticResource SecondaryLargeButton}"
                    Margin="40"
                    Width="540"
                    userControls:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding BackCommand}" />

            <Button Grid.Column="1"
                    Content="{Binding RetryLabel}"
                    Style="{StaticResource PrimaryLargeButton}"
                    HorizontalAlignment="Right"
                    Margin="40"
                    Width="420"
                    userControls:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding RetryCommand}" />
        </Grid>
    </Grid>
</Page>
