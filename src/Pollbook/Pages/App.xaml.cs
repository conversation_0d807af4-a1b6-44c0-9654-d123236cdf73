using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Security;
using System.Threading;
using System.Windows;
using System.Windows.Threading;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.ViewModel;
using GalaSoft.MvvmLight.Messaging;

namespace Pollbook.Pages
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App
    {
        private readonly IMessenger _messenger;
        private readonly IEssLogger _logger;
        private bool _dispatcherException;
        private static Exception globalException  = null;

        public App()
        {
            _messenger = Messenger.Default;

            //Log Example: We have to manually instantiate the logger here.
            _logger = new EssLogger();
        }

        protected override void OnStartup(StartupEventArgs e)
        {
            //Get Reference to the current Process
            Process thisProc = Process.GetCurrentProcess();

            // Check how many total processes have the same name as the current one
            if (Process.GetProcessesByName(thisProc.ProcessName).Length > 1)
            {
                // If there is more than one, than it is already running.
                Current.Shutdown();
                return;
            }

            AppDomain.CurrentDomain.UnhandledException += OnUnhandledExceptionHandler;

            Dispatcher.UnhandledException += OnDispatcherUnhandledException;

            base.OnStartup(e);
        }

        // This ensures it handles any uncaught exceptions that happen during UI operations (e.g., button click events, input errors).

        private void OnDispatcherUnhandledException(object sender, DispatcherUnhandledExceptionEventArgs e)
        {
            if (e.Exception.InnerException is SecurityException)
            {
                _messenger.Send(new NotificationMessage<NotificationTypeEnum>(NotificationTypeEnum.Error, "You are not authorized to perform that action."));

                _logger.LogError(e.Exception, new Dictionary<string, string>
                {
                    { "Action", "Unauthorized access attempt" },
                    { "ExceptionMessage", e.Exception.Message }
                });

                e.Handled = true;

                return;
            }

            _dispatcherException = true;
            
            // Log the exception with more properties
            var logProps = new Dictionary<string, string>
            {
                { "Action", "Trapping unhandled exception" },
                { "Timestamp", DateTime.UtcNow.ToString("o") },
                { "Thread", Thread.CurrentThread.ManagedThreadId.ToString() },
                { "ExceptionType", e.Exception.GetType().ToString() },
                { "ExceptionMessage", e.Exception.Message }
            };
            _logger.LogError(e.Exception, logProps);
            
            globalException = e.Exception;
            RestartMessage();
        }

        private void OnUnhandledExceptionHandler(object sender, UnhandledExceptionEventArgs e)
        {
            if (_dispatcherException)
            {
                return;
            }

            if (!(e.ExceptionObject is Exception exception))
            {
                _logger.LogError("An unhandled exception occurred, but it could not be converted to type Exception.");
            }
            else
            {
                Dictionary<string, string> logProps = new Dictionary<string, string> { { "Action", "Trapping unhandled exception" } };
                _logger.LogError(exception, logProps);
            }


            globalException = e.ExceptionObject as Exception;
            Dispatcher.Invoke(DispatcherPriority.Loaded, new Action(RestartMessage));
        }

        private static void RestartMessage()
        {
            var userFriendlyMessage = "An unexpected error occurred, and the application needs to restart.";
            var details = globalException != null
                ? $"{globalException.Message}\n\n{globalException.InnerException?.Message}"
                : "No further details are available.";

// #if DEBUG
//             // Provide detailed error for debugging purposes
//             MessageBox.Show(
//                 $"{globalException?.Message}\n=============\n{globalException?.InnerException?.Message ?? "No Inner Exception"}",
//                 "Unhandled Exception (Debug Mode)",
//                 MessageBoxButton.OK, 
//                 MessageBoxImage.Error);
//
// #else
 // User-friendly error for production
    MessageBox.Show(
        $"{userFriendlyMessage}\n\n{details}\nPlease click OK to restart.",
        "Application Error",
        MessageBoxButton.OK,
        MessageBoxImage.Error);

// #endif
            
            // Restart the application safely after showing a message
            RestartApplication();
        }
        
        private static void RestartApplication()
        {
            try
            {
                var processInfo = new ProcessStartInfo
                {
                    FileName = Process.GetCurrentProcess().MainModule.FileName,
                    Arguments = string.Join(" ", Environment.GetCommandLineArgs()),
                    WindowStyle = ProcessWindowStyle.Hidden
                };

                Process.Start(processInfo);
                Current.Shutdown(); // Shut down the existing application safely
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"Failed to restart the application. Please restart manually.\n\n{ex.Message}",
                    "Restart Failed",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
        }

    }
}
