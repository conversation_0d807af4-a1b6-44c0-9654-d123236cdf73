using CommonServiceLocator;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.ViewModel;
using ESS.Pollbook.ViewModel.Infrastructure;
using Pollbook.UserControls.Textbox;
using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;

namespace Pollbook.Pages
{
    /// <summary>
    /// Interaction logic for Login.xaml
    /// </summary>
    public partial class Login : UserControl
    {
        private readonly IKeyboardService _keyboardService;

        public Login()
        {
            InitializeComponent();
            _keyboardService = ServiceLocator.Current.GetInstance<IKeyboardService>();
            LoginPasswordBox.InputBindings?.Add(new KeyBinding
                { Command = ((LoginViewModel)DataContext).LoginCommand, Key = Key.Return });
            LoginPasswordBox.PasswordChanged += PasswordBox_PasswordChanged;

            txtUserName.SelectionChanged += (sender, e) => TextboxUserControlHelper.MoveCustomCaret(txtUserName, Caret);
            txtUserName.LostFocus += (sender, e) => Caret.Visibility = Visibility.Collapsed;
            txtUserName.GotFocus += (sender, e) => Caret.Visibility = Visibility.Visible;

            ((LoginViewModel)DataContext).ResetLoginForm = obj =>
            {
                txtUserName.Text = string.Empty;
                LoginPasswordBox.txtPassword.Password = string.Empty;
                LoginPasswordBox.txtPlainText.Text = string.Empty;
                if (LoginPasswordBox.IsPasswordVisible)
                {
                    LoginPasswordBox.TogglePasswordVisibility();
                }

                txtUserName.Focus();
            };

            try
            {
	            // Pre-loads the ViewModel that listens for messages
	            ViewModelLocator locator = new ViewModelLocator();
                var dashboard = locator.Dashboard;
                var monitorPoll = locator.MonitorPoll;
                var voter = locator.Voter;
                var cancelBallot = locator.CancelBallot;
                var wrongPollLocation = locator.WrongPollLocation;
                var voterBallotDetailsViewModel = locator.VoterBallotDetails;
                var configureElectionViewModel = locator.ConfigureElection;
                var manageVoterViewModel = locator.ManageVoter;
                var voterEditDetailsViewModel = locator.VoterEditDetails;
                var loadElectionFailed = locator.ElectionLoadingFailed;
                var electionLoaded = locator.ElectionLoaded;
                var incrementalUpdatedFailed = locator.IncrementalUpdatesFailed;
                var incrementalUpdateSucceed = locator.IncrementalUpdatesLoaded;
                var ballotCancelDetailsViewModel = locator.BallotCancelDetails;
                var voterAddDetailsViewModel = locator.VoterAddDetails;
                var optionsViewModel = locator.Options;
                var voterEditViewModel = locator.VoterEdit;
                var voterAddViewModel = locator.VoterAdd;
                var voterIssueBallotViewModel = locator.VoterIssueBallot;
                var voterVerificationViewModel = locator.VoterVerification;
                var pollingPlaceVoterListReportViewModel = locator.PollingPlaceVoterListReport;
                var fullVoterListReportViewModel = locator.FullVotedListReport;
                var ballotReissueReportViewModel = locator.BallotReIssuedReport;
                var spoiledBallotReportViewModel = locator.SpoiledBallotReport;
                var ballotTotalsReportViewModel = locator.BallotTotalsReport;
                var verifyVoterIdViewModel = locator.VerifyVoterId;
                var verifyVoterIdListAViewModel = locator.VerifyVoterIdListA;
                var verifyVoterIdListBViewModel = locator.VerifyVoterIdListB;
                var verifyVoterIdReasonableImpedimentDeclarationViewModel = locator.VerifyVoterIdReasonableImpedimentDeclaration;
                var VoterVerificationConfirmationViewModel = locator.VoterVerificationsConfirmation;
                var verifyVoterIdSignatureViewModel = locator.VerifyVoterIdSignature;
                var selectReissueBallotReasonViewModel = locator.ReissueBallotReason;
                var selectProvisionalReasonAndIDViewModel = locator.ProvisionalReasonAndId;
                var voterReasonableImpedimentViewModel = locator.VoterReasonableImpediment;
                var voterReasonableImpedimentReasonsViewModel = locator.VoterReasonableImpedimentReasons;
                var conversationViewModel = locator.Conversation;
                var alertViewModel = locator.Alert;
                var genericAlertViewModel = locator.GenericAlert;
                var printWaitTimeTokenViewModel = locator.PrintWaitTimeToken;
                var printWaitTimeTokenFailedViewModel = locator.PrintWaitTimeTokenFailed;
                var enterWaitTimeTokenViewModel = locator.EnterWaitTimeToken;
                var cancelWaitTimeTokenViewModel = locator.CancelWaitTimeToken;
                var affidavitTemplateViewModel = locator.AffidavitTemplate;
                var affidavitCaptureNameAddress = locator.AffidavitCaptureNameAddress;
                var affidavitConfirmation = locator.AffidavitConfirmation;
                var printingAffidavitReportFail = locator.PrintingAffidavitReportFailed;
                var voterSignature = locator.VoterSignature;
                var selectSkipSignatureReasonViewModel = locator.SelectSkipSignatureReason;
                var deviceLockedViewModel = locator.DeviceLocked;
                var selectPartyViewModel = locator.SelectParty;
                var sdCardCopying = locator.SdCardCopying;
                var advancedVoterSearch = locator.AdvancedVoterSearch;
                var addressChange = locator.AddressChanged;
                var affirmationOfResidenceAffidavit = locator.AffirmationOfResidenceAffidavit;
                var voterNameVerification = locator.VoterNameVerification;
            }
            catch (Exception ex)
            {
                var essLogger = ServiceLocator.Current.GetInstance<IEssLogger>();
                var logProps = new Dictionary<string, string>();
                logProps.Add("Action", "Login.Login");
                essLogger.LogError(ex, logProps);
            }
        }

        private void PasswordBox_PasswordChanged(object sender, RoutedEventArgs e)
        {
            var passwordBox = sender as PasswordBoxWithCaret;
            if (DataContext != null && passwordBox != null) ((dynamic)DataContext).Password = passwordBox.Password;
        }

        private async void Login_OnLoaded(object sender, RoutedEventArgs e)
        {
            _keyboardService.TextActivated(txtUserName);

            await ((LoginViewModel)DataContext).PageLoaded();
        }

        private void Login_Unloaded(object sender, RoutedEventArgs e)
        {
            ((LoginViewModel)DataContext).PageUnloaded();
        }
    }
}