<Page x:Class="Pollbook.Pages.HelpCenter.HelpCenterView"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" xmlns:i="http://schemas.microsoft.com/expression/2010/interactivity"
      xmlns:uiCore="clr-namespace:Pollbook.UICore"
      xmlns:usercontrols="clr-namespace:Pollbook.UserControls"
      mc:Ignorable="d" 
      d:DesignHeight="1200" d:DesignWidth="1920"
      Loaded="Page_IsLoaded"
      DataContext="{Binding HelpCenter, Source={StaticResource Locator}}">

    <Page.Resources>
        <uiCore:BooleanToVisibilityConverter x:Key="VisibilityConverter" True="Hidden" False="Visible" />
    </Page.Resources>

    <Grid Background="{StaticResource Gray7Brush}">
        <Grid.RowDefinitions>
            <RowDefinition Height="150"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Title -->
        <Grid Grid.Row="0"  Background="{StaticResource Gray7Brush}" Margin="0,0,50,0">
            <Label HorizontalAlignment="Left" VerticalAlignment="Top" Margin="120,60,0,0" Content="{Binding PageTitle}" >
                <Label.Style>
                    <Style TargetType="Label" BasedOn="{StaticResource Body1SemiBoldFont}">
                    </Style>
                </Label.Style>
            </Label>
        </Grid>

        <!-- No Help Notice! -->
        <Grid Grid.Row="1" Visibility="{Binding NoHelpVisible}" Background="White" Margin="120,0,121,150">
            <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center" Margin="0,0,0,0">
                <Viewbox Height="120" Width="120" Stretch="Uniform" HorizontalAlignment="Center">
                    <Path>
                        <Path.Style>
                            <Style TargetType="{x:Type Path}" BasedOn="{StaticResource ErrorIcon}" />
                        </Path.Style>
                    </Path>
                </Viewbox>
                <TextBlock Text="{Binding HelpUnavailable}" HorizontalAlignment="Center" Margin="0,30,0,0">
                    <TextBlock.Style>
                        <Style TargetType="TextBlock" BasedOn="{StaticResource Display1TextBlock}" />
                    </TextBlock.Style>
                </TextBlock>
                <TextBlock Text="{Binding ContactInfo}" HorizontalAlignment="Center" Margin="0,-5,0,0">
                    <TextBlock.Style>
                        <Style TargetType="TextBlock" BasedOn="{StaticResource Display1TextBlock}" />
                    </TextBlock.Style>
                </TextBlock>
            </StackPanel>
        </Grid>

        <StackPanel Grid.Row="1" Visibility="{Binding HelpCenterVisible}" Background="White" Margin="120,0,121,155">

            <ListView 
                ItemsSource="{Binding HelpCenterItems, IsAsync=True}"
                SelectionMode="Single" 
                Padding="0" 
                Margin="0" 
                BorderThickness="0"
                x:Name="HelpCenterListView"
                ScrollViewer.VerticalScrollBarVisibility="Auto"
                ScrollViewer.HorizontalScrollBarVisibility="Hidden"
                ScrollViewer.CanContentScroll="False"
                
                VirtualizingPanel.IsVirtualizing="True"
                VirtualizingPanel.VirtualizationMode="Recycling" 
                VirtualizingPanel.CacheLengthUnit="Item"
                VirtualizingPanel.CacheLength="8096"
                MaxHeight="730"
                >
                <ListView.Resources>
                    <Style TargetType="ScrollBar" BasedOn="{StaticResource CustomVerticalScrollBarStyle}"/>
                </ListView.Resources>
                <ListView.ItemsPanel>
                    <ItemsPanelTemplate>
                        <UniformGrid Columns="2"/>
                    </ItemsPanelTemplate>
                </ListView.ItemsPanel>
                
                <ListView.ItemTemplate>
                    <DataTemplate>
                        <Grid Margin="30">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="10"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="{Binding Title}" Foreground="{StaticResource SecondaryDarkBlueBrush}" Style="{StaticResource BoldTextBlock}" />
                            <Viewbox Grid.Column="2" Stretch="Uniform" Width="24" Height="24" Margin="0,0,0,0" >
                                <Path Style="{StaticResource LinkArrowIcon}" Fill="{StaticResource SecondaryDarkBlueBrush}" />
                            </Viewbox>
                        </Grid>
                    </DataTemplate>
                </ListView.ItemTemplate>

                <i:Interaction.Triggers>
                    <i:EventTrigger EventName="SelectionChanged">
                        <i:InvokeCommandAction Command="{Binding Path=ItemSelected}" CommandParameter="{Binding ElementName=HelpCenterListView, Path=SelectedItem}"/>
                    </i:EventTrigger>
                </i:Interaction.Triggers>
            </ListView>
        </StackPanel>
        <usercontrols:Spinner Grid.Row="0" Grid.RowSpan="2" RenderTransformOrigin="1.421,1.144" Margin="0,10,0,0" 
                               Visibility="{Binding HideSpinner, Converter={StaticResource VisibilityConverter}}"/>
    </Grid>
</Page>
