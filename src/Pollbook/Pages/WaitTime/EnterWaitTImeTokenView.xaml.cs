using ESS.Pollbook.ViewModel.WaitTime;
using Pollbook.UserControls.Textbox;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;

namespace Pollbook.Pages.WaitTime
{
    /// <summary>
    /// Interaction logic for EnterWaitTImeTokenView.xaml
    /// </summary>
    public partial class EnterWaitTImeTokenView : Page
    {
        public EnterWaitTImeTokenView()
        {
            InitializeComponent();

            tokenTextBox.SelectionChanged += (sender, e) => TextboxUserControlHelper.MoveCustomCaret(tokenTextBox, Caret);
            tokenTextBox.LostFocus += (sender, e) => Caret.Visibility = Visibility.Collapsed;
            tokenTextBox.GotFocus += (sender, e) => Caret.Visibility = Visibility.Visible;
        }

        private void Page_Loaded(object sender, RoutedEventArgs e)
        {
            ((EnterWaitTimeTokenViewModel)DataContext).PageIsLoaded();
        }

        // Allow only digit characters (and the backspace).
        private void tokenTextBox_PreviewKeyDown(object sender, KeyEventArgs e)
        {
            if ((e.Key < Key.D0 || e.Key > Key.D9) && (e.Key != Key.Back))
            {
                e.Handled = true;
            }
        }
    }
}
