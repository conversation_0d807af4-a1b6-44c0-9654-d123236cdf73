using ESS.Pollbook.ViewModel.WaitTime;
using System.Windows;
using System.Windows.Controls;

namespace Pollbook.Pages.WaitTime
{
    /// <summary>
    /// Interaction logic for DisplayWaitTimeView.xaml
    /// </summary>
    public partial class DisplayWaitTimeView : Page
    {
        public DisplayWaitTimeView()
        {
            InitializeComponent();
        }

        private void Page_Loaded(object sender, RoutedEventArgs e)
        {
            ((DisplayWaitTimeViewModel)DataContext).PageIsLoaded();
        }
    }
}
