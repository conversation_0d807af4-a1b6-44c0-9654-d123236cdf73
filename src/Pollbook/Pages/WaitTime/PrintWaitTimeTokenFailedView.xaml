<Page x:Class="Pollbook.Pages.WaitTime.PrintWaitTimeTokenFailedView"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:Pollbook.Pages.WaitTime"
      xmlns:userControls="clr-namespace:Pollbook.UserControls"
      mc:Ignorable="d" 
      DataContext="{Binding PrintWaitTimeTokenFailed, Source={StaticResource Locator}}"
      d:DesignHeight="1450" d:DesignWidth="1800"
      Title="PrintFailedView"
      Background="{StaticResource Gray8Brush}">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="auto" />
        </Grid.RowDefinitions>
        <Label Grid.Row="0" Content="Generate Token" Style="{StaticResource Display2SemiBoldFont}" HorizontalAlignment="Left" VerticalAlignment="Top" Margin="120,65,0,0" />
        <!-- Body -->

        <StackPanel Grid.Row="1" VerticalAlignment="Center">
            <Viewbox Height="100" Width="100" Stretch="Uniform" HorizontalAlignment="Center">
                <Path>
                    <Path.Style>
                        <Style TargetType="{x:Type Path}" BasedOn="{StaticResource ErrorIcon}" />
                    </Path.Style>
                </Path>
            </Viewbox>
            <TextBlock Text="Unable to Print the Wait Time token" HorizontalAlignment="Center" Margin="0,60,0,0" Style="{StaticResource Display1TextBlock}" />
            <TextBlock Text="Please check the printer or call the Elections Office for support." HorizontalAlignment="Center" Margin="0,40,0,80" Style="{StaticResource InstructionalSemiBoldTextBlock}" />
        </StackPanel>

        <!-- Buttons -->
        <Grid Grid.Row="2" Background="White">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="auto" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <Button Grid.Column="0"
                    Content="{Binding BackLabel}"
                    Style="{StaticResource SecondaryLargeButton}"
                    Margin="40"
                    Width="420"
                    userControls:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding BackCommand}" />
            <Button Grid.Column="1"
                    Content="{Binding RetryLabel}"
                    Style="{StaticResource PrimaryLargeButton}"
                    HorizontalAlignment="Right"
                    Margin="40"
                    Width="420"
                    userControls:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding RetryCommand}" />
        </Grid>
    </Grid>
</Page>
