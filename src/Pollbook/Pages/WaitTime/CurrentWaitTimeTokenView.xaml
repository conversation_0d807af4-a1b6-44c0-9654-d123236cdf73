<Page x:Class="Pollbook.Pages.WaitTime.CurrentWaitTimeTokenView"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:Pollbook.Pages.WaitTime"
      xmlns:local1="clr-namespace:Pollbook.UICore"
      xmlns:userControls="clr-namespace:Pollbook.UserControls"
      mc:Ignorable="d"
      Loaded="Page_Loaded"
      DataContext="{Binding CurrentWaitTimeToken, Source={StaticResource Locator}}"
      d:DesignHeight="1450" d:DesignWidth="1800"
      Title="CurrentWaitTimeTokenView"
      Background="{StaticResource Gray8Brush}">
    <Page.Resources>
        <local1:BooleanToVisibilityConverter x:Key="CalloutVisibilityConverter" True="Visible" False="Collapsed" />
    </Page.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="auto" />
            <RowDefinition Height="*"/>
            <RowDefinition Height="auto" />
        </Grid.RowDefinitions>
        <Label Grid.Row="0" Content="Generate Token" Style="{StaticResource Display2SemiBoldFont}" HorizontalAlignment="Left" VerticalAlignment="Top" Margin="120,65,0,0" />

        <StackPanel Grid.Row="1" VerticalAlignment="Center">
            <Viewbox Grid.Row="1" Height="100" Width="100" Stretch="Uniform" HorizontalAlignment="Center">
                <Path Style="{StaticResource ImportantIcon}" Stretch="Fill" />
            </Viewbox>
            <TextBlock Grid.Row="2" Style="{StaticResource Display1TextBlock}" HorizontalAlignment="Center" Margin="0,60,0,0">
                <Run Text="Current Token: "/><Run 
                    Text="{Binding WaitTimeToken}" />
            </TextBlock>
            <TextBlock Grid.Row="3" Style="{StaticResource InstructionalSemiBoldTextBlock}" HorizontalAlignment="Center" Margin="0,40,0,80">
                <Run Text="Wait time token was already issued at " /><Run 
                    Text="{Binding TokenGeneratedTime}" />
            </TextBlock>
        </StackPanel>

        <Grid Grid.Row="2" Background="White">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="1*" />
                <ColumnDefinition Width="auto" />
                <ColumnDefinition Width="auto" />
            </Grid.ColumnDefinitions>
            <Button Grid.Column="0"
                    Content="{Binding BackLabel}"
                    Style="{StaticResource SecondaryLargeButton}"
                    Margin="40,40,0,40"
                    userControls:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding BackCommand}"
                    HorizontalAlignment="Left" />
            <Button Grid.Column="1"
                    Content="{Binding CancelCurrentLabel}"
                    Style="{StaticResource PrimaryLargeButton}"
                    Margin="0,40,50,40"
                    userControls:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding CancelCurrentCommand}"
                    HorizontalAlignment="Right" />
            <Button Grid.Column="2"
                    Content="{Binding PrintLabel}"
                    Style="{StaticResource PrimaryLargeButton}"
                    Margin="-20,40,50,40"
                    userControls:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding PrintCommand}"
                    HorizontalAlignment="Right"
                    Visibility="{Binding IsPrintButtonVisible, Converter={StaticResource CalloutVisibilityConverter}}"/>
        </Grid>
    </Grid>
</Page>
