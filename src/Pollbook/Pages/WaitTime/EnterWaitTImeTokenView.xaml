<Page x:Class="Pollbook.Pages.WaitTime.EnterWaitTImeTokenView"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:Pollbook.Pages.WaitTime" xmlns:uiCore="clr-namespace:Pollbook.UICore"
      xmlns:userControls="clr-namespace:Pollbook.UserControls"
      mc:Ignorable="d" 
      DataContext="{Binding EnterWaitTimeToken, Source={StaticResource Locator}}"
      Loaded="Page_Loaded"
      d:DesignHeight="1450" d:DesignWidth="1800"
      Title="EnterWaitTImeTokenView"
      Background="{StaticResource Gray8Brush}">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="auto" />
        </Grid.RowDefinitions>
        <Label Grid.Row="0" Content="Enter Token" Style="{StaticResource Display2SemiBoldFont}" HorizontalAlignment="Left" VerticalAlignment="Top" Margin="120,65,0,0" />
        <Grid Grid.Row="1" VerticalAlignment="Top" Margin="600, 100, 600, 0" >
            <Grid.RowDefinitions>
                <RowDefinition Height="auto"/>
                <RowDefinition Height="auto"/>
                <RowDefinition Height="auto"/>
            </Grid.RowDefinitions>
            <TextBlock Grid.Row="0" Style="{StaticResource Display3BoldTextBlock}" Text="Enter Token (4 digits):" HorizontalAlignment="Left" Margin="0,0,0,30"/>
            <uiCore:KBTextBox Grid.Row="1" CaretBrush="Transparent" x:Name="tokenTextBox" PreviewKeyDown="tokenTextBox_PreviewKeyDown" Text="{Binding EnteredWaitTimeToken, UpdateSourceTrigger=PropertyChanged}">
                <uiCore:KBTextBox.Style>
                    <Style TargetType="{x:Type uiCore:KBTextBox}" BasedOn="{StaticResource KBTextBox}">
                        <Setter Property="MaxLength" Value="4" />
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding IsEnteredTokenValid}" Value="False">
                                <Setter Property="Background" Value="White" />
                                <Setter Property="BorderBrush" Value="{StaticResource RedBrush}" />
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </uiCore:KBTextBox.Style>
                <uiCore:KBTextBox.InputBindings>
                    <KeyBinding Command="{Binding Path=NextCommand}" Key="Return"/>
                </uiCore:KBTextBox.InputBindings>
            </uiCore:KBTextBox>
            <Canvas Grid.Row="1">
                <Border x:Name="Caret" Visibility="Collapsed" Canvas.Left="0" Canvas.Top="0" Width="2" Background="Black" >
                    <Border.Triggers>
                        <EventTrigger RoutedEvent="Border.Loaded">
                            <BeginStoryboard>
                                <Storyboard x:Name="CaretStoryBoard" RepeatBehavior="Forever">
                                    <ColorAnimationUsingKeyFrames Storyboard.TargetProperty="Background.Color" Duration="0:0:0:1" FillBehavior="HoldEnd">
                                        <ColorAnimationUsingKeyFrames.KeyFrames>
                                            <DiscreteColorKeyFrame KeyTime="0:0:0.500" Value="Transparent" />
                                            <DiscreteColorKeyFrame KeyTime="0:0:0.000" Value="Black" />
                                        </ColorAnimationUsingKeyFrames.KeyFrames>
                                    </ColorAnimationUsingKeyFrames>
                                </Storyboard>
                            </BeginStoryboard>
                        </EventTrigger>
                    </Border.Triggers>
                </Border>
            </Canvas>
            <StackPanel Grid.Row="2" Orientation="Horizontal">
                <StackPanel.Style>
                    <Style TargetType="StackPanel">
                        <Setter Property="Visibility" Value="Collapsed"/>
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding IsEnteredTokenValid}" Value="False">
                                <Setter Property="Visibility" Value="Visible"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </StackPanel.Style>
                <Label Content="Error:" Style="{StaticResource ErrorBoldFont}" />
                <Label Content="Please verify token and try again." Style="{StaticResource ErrorFont}" />
            </StackPanel>
        </Grid>

        <Grid Grid.Row="2" Background="White">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="1*" />
                <ColumnDefinition Width="1*" />
            </Grid.ColumnDefinitions>
            <Button Grid.Column="0"
                    Content="{Binding BackLabel}"
                    Style="{StaticResource SecondaryLargeButton}"
                    Margin="40"
                    userControls:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding BackCommand}"
                    HorizontalAlignment="Left" />
            <Button Grid.Column="1"
                    Content="{Binding NextLabel}"
                    Style="{StaticResource PrimaryLargeButton}"
                    Margin="40"
                    userControls:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding NextCommand}"
                    IsEnabled="{Binding IsNextEnabled}"
                    HorizontalAlignment="Right" />
        </Grid>
    </Grid>
</Page>
