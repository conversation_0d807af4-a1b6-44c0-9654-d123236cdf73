<Page x:Class="Pollbook.Pages.WaitTime.DisplayWaitTimeView"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:Pollbook.Pages.WaitTime"
      xmlns:userControls="clr-namespace:Pollbook.UserControls"
      mc:Ignorable="d" 
      DataContext="{Binding DisplayWaitTime, Source={StaticResource Locator}}"
      Loaded="Page_Loaded"
      d:DesignHeight="1450" d:DesignWidth="1800"
      Title="DisplayWaitTimeView"
      Background="{StaticResource Gray8Brush}">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="auto" />
        </Grid.RowDefinitions>
        <Label Grid.Row="0" Content="Enter Token" Style="{StaticResource Display2SemiBoldFont}" HorizontalAlignment="Left" VerticalAlignment="Top" Margin="120,65,0,0" />
        <StackPanel Grid.Row="1" VerticalAlignment="Center">
            <TextBlock Style="{StaticResource Display1TextBlock}" Text="Wait time:" HorizontalAlignment="Center"/>
            <TextBlock Style="{StaticResource Display5BoldTextBlock}" Text="{Binding WaitTime}" HorizontalAlignment="Center" Margin="0,12,0,52"/>
        </StackPanel>

        <Grid Grid.Row="2" Background="White">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="1*" />
                <ColumnDefinition Width="1*" />
            </Grid.ColumnDefinitions>
            <Button Grid.Column="1"
                    Content="{Binding DoneLabel}"
                    Style="{StaticResource PrimaryLargeButton}"
                    Margin="40"
                    userControls:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding DoneCommand}"
                    HorizontalAlignment="Right" />
        </Grid>
    </Grid>
</Page>
