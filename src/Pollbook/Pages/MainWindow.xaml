<Window x:Class="Pollbook.Pages.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:core="clr-namespace:Pollbook.UICore"
        xmlns:virtualkeyboard="clr-namespace:Pollbook.OnScreenKeyboard"
        xmlns:userControls="clr-namespace:Pollbook.UserControls"
        mc:Ignorable="d"
        Name="myMainWindow"
        Activated="myMainWindow_Activated"
        MouseDown="myMainWindow_MouseDown"
        DataContext="{Binding MainWindowView, Source={StaticResource Locator}}"
        d:DesignHeight="1200" d:DesignWidth="1800" 
        Width="1200"
        Height="839"
        Loaded="myMainWindow_Loaded"
        Closing ="MainWindow_OnClosing">
   <Window.Resources>
      <Color x:Key="SystemStatusColor" R="255" G="255" B="255" A="255" />
      <Color x:Key="SystemStatusLightColor" R="255" G="255" B="255" A="51" />

      <SolidColorBrush x:Key="SystemStatusBrush" Color="{StaticResource SystemStatusColor}" />
      <SolidColorBrush x:Key="SystemStatusLightBrush" Color="{StaticResource SystemStatusLightColor}" />

      <Color x:Key="ModalOverlayColor" R="21" G="32" B="37" A="166" />

      <SolidColorBrush x:Key="ModalOverlayBrush" Color="{StaticResource ModalOverlayColor}" />

      <core:BooleanToVisibilityConverter x:Key="StatusBarVisibilityConverter" True="Visible" False="Collapsed" />
      <core:BooleanToVisibilityConverter x:Key="OnVisibilityConverter" True="Visible" False="Collapsed" />
      <core:BooleanToVisibilityConverter x:Key="OffVisibilityConverter" True="Collapsed" False="Visible" />
      <core:BooleanToVisibilityConverter x:Key="BoolToVisibilityConverter" True="Visible" False="Collapsed"/>
      <core:BoolToObjectConverter x:Key="PrinterColorConverter" 
                                    TrueObject="{StaticResource PrimaryGreenBrush}" 
                                    FalseObject="{StaticResource PrimaryRedBrush}" 
                                    NullObject="{StaticResource PrimaryYellowBrush}"/>
   </Window.Resources>
   <Grid x:Name="KeyboardGrid">
      <Grid x:Name="MainGrid">
         <Frame Name="MainFrame" NavigationUIVisibility="Hidden">
            <Frame.Template>
               <ControlTemplate TargetType="Frame">
                  <Grid Name="MainGrid" SizeChanged="MainGrid_SizeChanged">
                     <Grid.LayoutTransform>
                        <ScaleTransform
                            CenterX="0"
                            CenterY="0"
                            ScaleX="{Binding ElementName=myMainWindow, Path=ScaleValue}"
                            ScaleY="{Binding ElementName=myMainWindow, Path=ScaleValue}" />
                     </Grid.LayoutTransform>
                     <ContentPresenter />
                  </Grid>
               </ControlTemplate>
            </Frame.Template>
         </Frame>

         <Grid Name="SystemStatus" Height="66" VerticalAlignment="Top">

            <Grid.LayoutTransform>
               <ScaleTransform
                            CenterX="0"
                            CenterY="0"
                            ScaleX="{Binding ElementName=myMainWindow, Path=ScaleValue}"
                            ScaleY="{Binding ElementName=myMainWindow, Path=ScaleValue}" />
            </Grid.LayoutTransform>

            <Grid.RowDefinitions>
               <RowDefinition Height="35" />
               <RowDefinition Height="*" />
            </Grid.RowDefinitions>

            <Grid.ColumnDefinitions>
               <ColumnDefinition Width="*"/>
               <ColumnDefinition Width="auto"/>
               <ColumnDefinition Width="auto"/>
            </Grid.ColumnDefinitions>

            <TextBlock Text="{Binding PollPlaceDetails.PollingPlaceDisplayName, StringFormat='{} '}" Grid.Column="0" Style="{StaticResource StandardTextBlock}" 
                           HorizontalAlignment="Left" Foreground="{StaticResource SystemStatusBrush}" VerticalAlignment="Center" Margin="20,0,0,0"
                           Visibility="{Binding IsStatusBarElectionInfoVisible, Converter={StaticResource StatusBarVisibilityConverter}}"
                           TextTrimming="CharacterEllipsis">
            </TextBlock>

            <StackPanel Grid.Row="0" Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Center" Margin="12,0,-12,0" Visibility="{Binding IsStatusBarElectionInfoVisible, Converter={StaticResource StatusBarVisibilityConverter}}">
               <TextBlock>
                  <TextBlock.Style >
                     <Style TargetType="TextBlock" BasedOn="{StaticResource StandardTextBlock}">
                        <Style.Setters>
                           <Setter Property="Text" Value="{Binding SystemStats.BallotsIssued, StringFormat=' {0:#,0} Issued'}"></Setter>
                           <Setter Property="Foreground" Value="{StaticResource SystemStatusBrush}"></Setter>
                           <Setter Property="VerticalAlignment" Value="Center"></Setter>
                        </Style.Setters>
                     </Style>
                  </TextBlock.Style>
               </TextBlock>
               <TextBlock>
                  <TextBlock.Style >
                     <Style TargetType="TextBlock" BasedOn="{StaticResource StandardTextBlock}">
                        <Style.Setters>
                           <Setter Property="Text" Value="{Binding SystemStats.BallotsReissued, StringFormat=' | {0:#,0} Reissued'}"></Setter>
                           <Setter Property="Foreground" Value="{StaticResource SystemStatusBrush}"></Setter>
                           <Setter Property="VerticalAlignment" Value="Center"></Setter>
                        </Style.Setters>
                     </Style>
                  </TextBlock.Style>
               </TextBlock>
               <TextBlock>
                  <TextBlock.Style >
                     <Style TargetType="TextBlock" BasedOn="{StaticResource StandardTextBlock}">
                        <Style.Setters>
                           <Setter Property="Text" Value="{Binding SystemStats.VotersCheckedIn, StringFormat=' | {0:#,0} Checked In'}"></Setter>
                           <Setter Property="Foreground" Value="{StaticResource SystemStatusBrush}"></Setter>
                           <Setter Property="VerticalAlignment" Value="Center"></Setter>
                        </Style.Setters>
                     </Style>
                  </TextBlock.Style>
               </TextBlock>
            </StackPanel>

            <StackPanel Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="2" Name="spCustomLocationInfo" Orientation="Horizontal" HorizontalAlignment="Left" Margin="20,0,20,0" Visibility="{Binding IsStatusBarElectionInfoVisible, Converter={StaticResource StatusBarVisibilityConverter}}" />

            <Border Grid.Row="1" Grid.Column="2" Visibility="{Binding IsStatusBarElectionInfoVisible, Converter={StaticResource StatusBarVisibilityConverter}}" Margin="10,0,20,0">
               <Border  Visibility="{Binding TestModeVisible}" Background="{StaticResource PrimaryYellowBrush}" HorizontalAlignment="Stretch">
                  <TextBlock Text="{Binding TestModeLabel}" Style="{StaticResource StandardTextBlock}" TextAlignment="Center" VerticalAlignment="Center" LineHeight="20" />
               </Border>
            </Border>

            <StackPanel Grid.Row="0" Grid.Column="2"  Orientation="Horizontal" HorizontalAlignment="Right" Margin="20,0,0,0" Visibility="{Binding IsStatusBarSystemInfoVisible, Converter={StaticResource StatusBarVisibilityConverter}}"
                            ZIndex="1">

               <Viewbox Stretch="Uniform" VerticalAlignment="Bottom" Width="36" Margin="10,5,10,5" Visibility="{Binding SystemStats.IsDownloadIconVisible, Converter={StaticResource OnVisibilityConverter}}" >
                  <Grid>
                     <Path Style="{StaticResource Downloading}" Fill="{StaticResource SecondaryDarkYellowBrush}" />
                  </Grid>
               </Viewbox>

               <Viewbox Stretch="Uniform" VerticalAlignment="Bottom" Width="36" Margin="10,5,10,5" Visibility="{Binding SystemStats.IsPrinterIconVisible, Converter={StaticResource OnVisibilityConverter}}" >
                  <Grid>
                     <Path Style="{StaticResource PrinterIcon}" Fill="{Binding SystemStats.PrinterIconColor, Converter={StaticResource PrinterColorConverter}}"/>
                  </Grid>
               </Viewbox>

               <Viewbox Stretch="Uniform" VerticalAlignment="Bottom" Width="36" Margin="10,5,10,5" Visibility="{Binding SystemStats.IsInstantMessageIconVisible, Converter={StaticResource OnVisibilityConverter}}" >
                  <Grid>
                     <Path Style="{StaticResource LiveChatNotification}" Fill="{StaticResource SecondaryDarkYellowBrush}" />
                  </Grid>
               </Viewbox>

               <Viewbox Stretch="Uniform" VerticalAlignment="Bottom" Width="36" Margin="10,5,10,5">
                  <Grid>
                     <Path Style="{StaticResource OnIcon}" Fill="{StaticResource PrimaryGreenBrush}"  Visibility="{Binding IsNetWorkAttached, Converter={StaticResource OnVisibilityConverter}}" />
                     <Path Style="{StaticResource OffIcon}" Fill="{StaticResource PrimaryRedBrush}"  Visibility="{Binding IsNetWorkAttached, Converter={StaticResource OffVisibilityConverter}}"/>
                  </Grid>
               </Viewbox>

               <!-- Host Connection Icons -->
               <Viewbox Stretch="Uniform" VerticalAlignment="Bottom" Width="36" Margin="10,5,10,5" Visibility="{Binding SystemStats.IsHostViewBoxVisible, Converter={StaticResource OnVisibilityConverter}}">
                  <Grid>
                     <ContentControl Visibility="{Binding SystemStats.IsHostOnIconVisible, Converter={StaticResource OnVisibilityConverter}}" Template="{StaticResource HostConnected}"/>
                     <ContentControl Visibility="{Binding SystemStats.IsHostSlowIconVisible, Converter={StaticResource OnVisibilityConverter}}" Template="{StaticResource HostConnectedSlow}"/>
                     <ContentControl Visibility="{Binding SystemStats.IsHostOffIconVisible, Converter={StaticResource OnVisibilityConverter}}" Template="{StaticResource HostDisconnected}"/>
                  </Grid>
               </Viewbox>

               <!-- Peer Connection Icons -->
               <Viewbox Stretch="Uniform" VerticalAlignment="Bottom" Width="36" Margin="10,5,10,5" Visibility="{Binding SystemStats.IsPeerToPeerViewBoxVisible, Converter={StaticResource OnVisibilityConverter}}" >
                  <Grid>
                     <ContentControl Visibility="{Binding SystemStats.IsPeerToPeerOffIconVisible, Converter={StaticResource OnVisibilityConverter}}" Template="{StaticResource PeerDisconnected}"/>
                     <ContentControl Visibility="{Binding SystemStats.IsPeerToPeerOnIconVisible, Converter={StaticResource OnVisibilityConverter}}" Template="{StaticResource PeerConnected}"/>
                  </Grid>
               </Viewbox>
               <TextBlock Text="{Binding SystemStats.SystemDateTime, StringFormat='{}ddd MMM dd  h:mm tt'}" Style="{StaticResource StandardTextBlock}" Foreground="{StaticResource SystemStatusBrush}" VerticalAlignment="Center" Margin="20,0,20,0"></TextBlock>
               <Button
                   IsEnabled="{Binding SystemStats.IsBatteryTimeEnabled}"
                   userControls:ButtonHelper.DisableMultipleClicks="True"
                   Command="{Binding SystemStats.ShowTimeRemainingCommand}"
                   HorizontalAlignment="Right"
                   VerticalAlignment="Top"
                   Margin="0,-3,5,0">
                   <Button.Template>
                       <ControlTemplate TargetType="Button">
                           <ContentPresenter />
                       </ControlTemplate>
                   </Button.Template>
                   <StackPanel Orientation="Horizontal">
                       <TextBlock VerticalAlignment="Center" Margin="0,0,5,0" Text="{Binding SystemStats.BatteryLifeStatusText}" Style="{StaticResource StandardTextBlock}" Foreground="{StaticResource SystemStatusBrush}"/>
                            <Viewbox Stretch="Uniform" VerticalAlignment="Center" Width="36" Margin="10,0,22,4">
                                <Viewbox.Resources>
                                    <Path x:Key="Battery00" Fill="{StaticResource PrimaryRedBrush}">
                                        <Path.Style>
                                            <Style TargetType="Path" BasedOn="{StaticResource Battery00To03Icon}" />
                                        </Path.Style>
                                    </Path>
                                    <Path x:Key="Battery04" Fill="{StaticResource PrimaryRedBrush}">
                                        <Path.Style>
                                            <Style TargetType="Path" BasedOn="{StaticResource Battery04To08Icon}" />
                                        </Path.Style>
                                    </Path>
                                    <Path x:Key="Battery09" Fill="{StaticResource PrimaryRedBrush}">
                                        <Path.Style>
                                            <Style TargetType="Path" BasedOn="{StaticResource Battery09To15Icon}" />
                                        </Path.Style>
                                    </Path>
                                    <Path x:Key="Battery16" Fill="{StaticResource PrimaryRedBrush}">
                                        <Path.Style>
                                            <Style TargetType="Path" BasedOn="{StaticResource Battery16To22Icon}" />
                                        </Path.Style>
                                    </Path>
                                    <Path x:Key="Battery23" Fill="{StaticResource PrimaryRedBrush}">
                                        <Path.Style>
                                            <Style TargetType="Path" BasedOn="{StaticResource Battery23To30Icon}" />
                                        </Path.Style>
                                    </Path>
                                    <Path x:Key="Battery31" Fill="{StaticResource SystemStatusBrush}">
                                        <Path.Style>
                                            <Style TargetType="Path" BasedOn="{StaticResource Battery31To36Icon}" />
                                        </Path.Style>
                                    </Path>
                                    <Path x:Key="Battery37" Fill="{StaticResource SystemStatusBrush}">
                                        <Path.Style>
                                            <Style TargetType="Path" BasedOn="{StaticResource Battery37To43Icon}" />
                                        </Path.Style>
                                    </Path>
                                    <Path x:Key="Battery44" Fill="{StaticResource SystemStatusBrush}">
                                        <Path.Style>
                                            <Style TargetType="Path" BasedOn="{StaticResource Battery44To50Icon}" />
                                        </Path.Style>
                                    </Path>
                                    <Path x:Key="Battery51" Fill="{StaticResource SystemStatusBrush}">
                                        <Path.Style>
                                            <Style TargetType="Path" BasedOn="{StaticResource Battery51To57Icon}" />
                                        </Path.Style>
                                    </Path>
                                    <Path x:Key="Battery58" Fill="{StaticResource SystemStatusBrush}">
                                        <Path.Style>
                                            <Style TargetType="Path" BasedOn="{StaticResource Battery58To64Icon}" />
                                        </Path.Style>
                                    </Path>
                                    <Path x:Key="Battery65" Fill="{StaticResource SystemStatusBrush}">
                                        <Path.Style>
                                            <Style TargetType="Path" BasedOn="{StaticResource Battery65To71Icon}" />
                                        </Path.Style>
                                    </Path>
                                    <Path x:Key="Battery72" Fill="{StaticResource SystemStatusBrush}">
                                        <Path.Style>
                                            <Style TargetType="Path" BasedOn="{StaticResource Battery72To78Icon}" />
                                        </Path.Style>
                                    </Path>
                                    <Path x:Key="Battery79" Fill="{StaticResource SystemStatusBrush}">
                                        <Path.Style>
                                            <Style TargetType="Path" BasedOn="{StaticResource Battery79To85Icon}" />
                                        </Path.Style>
                                    </Path>
                                    <Path x:Key="Battery86" Fill="{StaticResource SystemStatusBrush}">
                                        <Path.Style>
                                            <Style TargetType="Path" BasedOn="{StaticResource Battery86To92Icon}" />
                                        </Path.Style>
                                    </Path>
                                    <Path x:Key="Battery93" Fill="{StaticResource SystemStatusBrush}">
                                        <Path.Style>
                                            <Style TargetType="Path" BasedOn="{StaticResource Battery93To100Icon}" />
                                        </Path.Style>
                                    </Path>
                                    <Path x:Key="ChargingLow" Fill="{StaticResource PrimaryRedBrush}">
                                        <Path.Style>
                                            <Style TargetType="Path" BasedOn="{StaticResource BatteryChargingIcon}" />
                                        </Path.Style>
                                    </Path>
                                    <Path x:Key="ChargingHigh" Fill="{StaticResource SystemStatusBrush}">
                                        <Path.Style>
                                            <Style TargetType="Path" BasedOn="{StaticResource BatteryChargingIcon}" />
                                        </Path.Style>
                                    </Path>
                                </Viewbox.Resources>
                                <ContentControl>
                                    <ContentControl.Style>
                                        <Style TargetType="{x:Type ContentControl}">
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding SystemStats.BatteryLifeKey}" Value="Battery00">
                                                    <Setter Property="Content" Value="{StaticResource Battery00}"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding SystemStats.BatteryLifeKey}" Value="Battery04">
                                                    <Setter Property="Content" Value="{StaticResource Battery04}"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding SystemStats.BatteryLifeKey}" Value="Battery09">
                                                    <Setter Property="Content" Value="{StaticResource Battery09}"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding SystemStats.BatteryLifeKey}" Value="Battery16">
                                                    <Setter Property="Content" Value="{StaticResource Battery16}"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding SystemStats.BatteryLifeKey}" Value="Battery23">
                                                    <Setter Property="Content" Value="{StaticResource Battery23}"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding SystemStats.BatteryLifeKey}" Value="Battery31">
                                                    <Setter Property="Content" Value="{StaticResource Battery31}"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding SystemStats.BatteryLifeKey}" Value="Battery37">
                                                    <Setter Property="Content" Value="{StaticResource Battery37}"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding SystemStats.BatteryLifeKey}" Value="Battery44">
                                                    <Setter Property="Content" Value="{StaticResource Battery44}"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding SystemStats.BatteryLifeKey}" Value="Battery51">
                                                    <Setter Property="Content" Value="{StaticResource Battery51}"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding SystemStats.BatteryLifeKey}" Value="Battery58">
                                                    <Setter Property="Content" Value="{StaticResource Battery58}"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding SystemStats.BatteryLifeKey}" Value="Battery65">
                                                    <Setter Property="Content" Value="{StaticResource Battery65}"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding SystemStats.BatteryLifeKey}" Value="Battery72">
                                                    <Setter Property="Content" Value="{StaticResource Battery72}"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding SystemStats.BatteryLifeKey}" Value="Battery79">
                                                    <Setter Property="Content" Value="{StaticResource Battery79}"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding SystemStats.BatteryLifeKey}" Value="Battery86">
                                                    <Setter Property="Content" Value="{StaticResource Battery86}"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding SystemStats.BatteryLifeKey}" Value="Battery93">
                                                    <Setter Property="Content" Value="{StaticResource Battery93}"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding SystemStats.BatteryLifeKey}" Value="ChargingLow">
                                                    <Setter Property="Content" Value="{StaticResource ChargingLow}"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding SystemStats.BatteryLifeKey}" Value="ChargingHigh">
                                                    <Setter Property="Content" Value="{StaticResource ChargingHigh}"/>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </ContentControl.Style>
                                </ContentControl>
                            </Viewbox>
                   </StackPanel>
               </Button>
            </StackPanel>
         </Grid>

         <Frame Name="ContextFrame" NavigationUIVisibility="Hidden">
            <Frame.Style>
               <Style TargetType="Frame">
                  <Style.Setters>
                     <Setter Property="Visibility" Value="Visible"></Setter>
                  </Style.Setters>
                  <Style.Triggers>
                     <Trigger Property="Source" Value="{x:Null}">
                        <Setter Property="Visibility" Value="Hidden"/>
                     </Trigger>
                  </Style.Triggers>
               </Style>
            </Frame.Style>
            <Frame.Template>
               <ControlTemplate TargetType="Frame">
                  <Grid Name="ContextGrid" SizeChanged="MainGrid_SizeChanged">
                     <Grid.LayoutTransform>
                        <ScaleTransform
                            CenterX="0"
                            CenterY="0"
                            ScaleX="{Binding ElementName=myMainWindow, Path=ScaleValue}"
                            ScaleY="{Binding ElementName=myMainWindow, Path=ScaleValue}" />
                     </Grid.LayoutTransform>

                     <ContentPresenter Margin="0,110,0,0" />

                     <!-- Close Button -->
                     <Button x:Name="CloseButton"
                             Width="90"
                             Height="90"
                             Background="White"
                             HorizontalAlignment="Right"
                             VerticalAlignment="Top"
                             Margin="0,65,30,0"
                             userControls:ButtonHelper.DisableMultipleClicks="True"
                             Click="CloseContext_Click">

                        <Button.Template>
                           <ControlTemplate>
                              <Grid>
                                 <Ellipse Width="90" Height="90" Fill="{TemplateBinding Background}" />
                                 <Viewbox Stretch="Uniform" Width="32" HorizontalAlignment="Center" VerticalAlignment="Center" Margin="0,-10,2,0">
                                    <Path Style="{StaticResource CloseIcon}" Fill="{StaticResource PrimaryNavyBrush}" />
                                 </Viewbox>
                              </Grid>
                           </ControlTemplate>
                        </Button.Template>
                     </Button>


                  </Grid>
               </ControlTemplate>
            </Frame.Template>
         </Frame>

         <Frame Name="ModalFrame" NavigationUIVisibility="Hidden">
            <Frame.Style>
               <Style TargetType="Frame">
                  <Style.Setters>
                     <Setter Property="Visibility" Value="Visible"></Setter>
                  </Style.Setters>
                  <Style.Triggers>
                     <Trigger Property="Source" Value="{x:Null}">
                        <Setter Property="Visibility" Value="Hidden"/>
                     </Trigger>
                  </Style.Triggers>
               </Style>
            </Frame.Style>
            <Frame.Template>
               <ControlTemplate TargetType="Frame">
                  <Grid Name="ContextGrid" SizeChanged="MainGrid_SizeChanged">
                     <Grid.LayoutTransform>
                        <ScaleTransform
                            CenterX="0"
                            CenterY="0"
                            ScaleX="{Binding ElementName=myMainWindow, Path=ScaleValue}"
                            ScaleY="{Binding ElementName=myMainWindow, Path=ScaleValue}" />
                     </Grid.LayoutTransform>
                     <Grid.RowDefinitions>
                        <RowDefinition Height="auto" MinHeight="400" />
                        <RowDefinition Height="*" />
                     </Grid.RowDefinitions>

                     <Grid Background="White">
                        <ContentPresenter Margin="100,140,100,100" />
                     </Grid>
                     <Canvas Grid.Row="1"  />

                     <!-- Close Button -->

                     <Button x:Name="CloseButton"
                             HorizontalAlignment="Right"
                             VerticalAlignment="Top"
                             Margin="0,23,29,0"
                             Height="90"
                             Width="90"
                             userControls:ButtonHelper.DisableMultipleClicks="True"
                             Click="CloseModal_Click">
                         <Button.Template>
                             <ControlTemplate>
                                 <Grid Background="White">
                                     <Viewbox Stretch="Uniform" Width="32" HorizontalAlignment="Center" VerticalAlignment="Center" Margin="0,-8,2,0">
                                         <Path Style="{StaticResource CloseIcon}" Fill="{StaticResource SecondaryDarkBlueBrush}" />
                                     </Viewbox>
                                 </Grid>
                             </ControlTemplate>
                         </Button.Template>
                     </Button>

                  </Grid>
               </ControlTemplate>
            </Frame.Template>
         </Frame>

         <Frame Name="ModalDialogFrame" NavigationUIVisibility="Hidden">
            <Frame.Style>
               <Style TargetType="Frame">
                  <Style.Setters>
                     <Setter Property="Visibility" Value="Visible"></Setter>
                  </Style.Setters>
                  <Style.Triggers>
                     <Trigger Property="Source" Value="{x:Null}">
                        <Setter Property="Visibility" Value="Collapsed"/>
                     </Trigger>
                  </Style.Triggers>
               </Style>
            </Frame.Style>
            <Frame.Template>
               <ControlTemplate TargetType="Frame">
                  <Grid Name="ContextGrid" SizeChanged="MainGrid_SizeChanged">
                     <Grid.LayoutTransform>
                        <ScaleTransform
                                CenterX="0"
                                CenterY="0"
                                ScaleX="{Binding ElementName=myMainWindow, Path=ScaleValue}"
                                ScaleY="{Binding ElementName=myMainWindow, Path=ScaleValue}" />
                     </Grid.LayoutTransform>

                     <Grid.RowDefinitions>
                        <RowDefinition Height="0.5*" />
                        <RowDefinition Height="490"/>
                        <RowDefinition Height="5*" />
                     </Grid.RowDefinitions>

                     <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="1*" />
                        <ColumnDefinition Width="1.75*" MinWidth="400" />
                        <ColumnDefinition Width="1*" />
                     </Grid.ColumnDefinitions>

                     <Canvas Grid.Row="0" Grid.RowSpan="3" Grid.Column="0" Grid.ColumnSpan="3" Background="{StaticResource ModalOverlayBrush}" />
                     <Border Grid.Row="1" Grid.Column ="1" Background="White" BorderBrush="Transparent" BorderThickness="1" CornerRadius="3">
                        <Grid >
                           <ContentPresenter Margin="0,10,0,10" />
                        </Grid>
                     </Border>

                     <!-- Close Button -->

                     <Button x:Name="CloseButton"
                             Grid.Row="1"
                             Grid.Column="1"
                             HorizontalAlignment="Right"
                             VerticalAlignment="Top"
                             Margin="0,20,20,0"
                             userControls:ButtonHelper.DisableMultipleClicks="True"
                             Click="CloseModalDialog_Click"
                             Height="80"
                             Width="80">
                        <Button.Template>
                           <ControlTemplate>
                              <Grid Background="White">
                                 <Viewbox Stretch="Uniform" Width="30" Height="32" HorizontalAlignment="Center" VerticalAlignment="Center" Margin="0,-10,2,0">
                                    <Path Style="{StaticResource CloseIcon}" Fill="{StaticResource SecondaryDarkBlueBrush}" />
                                 </Viewbox>
                              </Grid>
                           </ControlTemplate>
                        </Button.Template>
                     </Button>

                  </Grid>
               </ControlTemplate>
            </Frame.Template>
         </Frame>

         <Frame Name="OptionsFrame" NavigationUIVisibility="Hidden">
            <Frame.Style>
               <Style TargetType="Frame">
                  <Style.Setters>
                     <Setter Property="Visibility" Value="Visible"></Setter>
                  </Style.Setters>
                  <Style.Triggers>
                     <Trigger Property="Source" Value="{x:Null}">
                        <Setter Property="Visibility" Value="Hidden"/>
                     </Trigger>
                  </Style.Triggers>
               </Style>
            </Frame.Style>
            <Frame.Template>
               <ControlTemplate TargetType="Frame">
                  <Grid Name="OptionsGrid" SizeChanged="MainGrid_SizeChanged">
                     <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="auto" MinWidth="300" />
                     </Grid.ColumnDefinitions>
                     <ContentPresenter Grid.Column="1" />
                  </Grid>
               </ControlTemplate>
            </Frame.Template>
         </Frame>

         <Grid Name="Notifications" VerticalAlignment="Top" Visibility="Visible">
            <Grid.LayoutTransform>
               <ScaleTransform
                            CenterX="0"
                            CenterY="0"
                            ScaleX="{Binding ElementName=myMainWindow, Path=ScaleValue}"
                            ScaleY="{Binding ElementName=myMainWindow, Path=ScaleValue}" />
            </Grid.LayoutTransform>

            <StackPanel Name="NotificationPanel" Margin="0" />
         </Grid>
      </Grid>
      <virtualkeyboard:OnScreenKeyboard
            HorizontalAlignment="Stretch"
            VerticalAlignment="Bottom"
            x:Name="Keyboard"
            ToggleButtonStyle="{StaticResource DefaultTouchToggleButtonStyle}"
            Background="#E5EBF0"
            Height="240"     
            Visibility="Hidden" />
   </Grid>
</Window>
