<Page x:Class="Pollbook.Pages.Affidavit.AffidavitConfirmationView"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:Pollbook.Pages.Affidavit"
      xmlns:button="clr-namespace:Pollbook.UserControls"
      mc:Ignorable="d" 
       DataContext="{Binding AffidavitConfirmation, Source={StaticResource Locator}}"
      d:DesignHeight="800" d:DesignWidth="1600"
      Title="AffidavitConfirmationView">

    <Grid Background="White" Margin="100,0">
        <Grid.RowDefinitions>
            <RowDefinition Height="auto" />
            <RowDefinition Height="175" />
        </Grid.RowDefinitions>

        <StackPanel Margin="0,0,0,80">
            <Viewbox Stretch="Uniform" VerticalAlignment="Center" HorizontalAlignment="Center" Width="100" Margin="0,0,0,60">
                <Path Style="{StaticResource InformationIcon}" Stretch="Fill" />
            </Viewbox>
            <TextBlock Style="{StaticResource Display1TextBlock}" Margin="0,0,0,30" Text="{Binding Title}" HorizontalAlignment="Center" TextAlignment="Center" TextWrapping="Wrap" />
            <TextBlock Style="{StaticResource InstructionalSemiBoldTextBlock}" Text="{Binding Message}" HorizontalAlignment="Center" TextAlignment="Center" TextWrapping="Wrap" />
        </StackPanel>

        <!-- Buttons -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="40" />
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <Button Grid.Column="0"
                    Style="{StaticResource PrimaryLargeButton}"
                    Content="{Binding NoButtonLabel}"
                    button:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding NoCommand}"
                    HorizontalAlignment="Right"
                    Width="500"/>

            <Button Grid.Column="2"
                    Style="{StaticResource PrimaryLargeButton}"
                    Content="{Binding YesButtonLabel}"
                    button:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding YesCommand}"
                    HorizontalAlignment="Left"
                    Width="500"/>
        </Grid>
    </Grid>
</Page>
