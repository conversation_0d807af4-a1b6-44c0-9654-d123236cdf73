using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.UICore;
using ESS.Pollbook.ViewModel.Affidavit;
using Pollbook.UICore;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;

namespace Pollbook.Pages.Affidavit
{
    public partial class AffirmationOfResidenceAffidavitView : Page, IContextView
    {
        private readonly DebounceDispatcher _debounceTimer = new DebounceDispatcher();

        public bool IsModal => true;
        public SolidColorBrush PrimaryBackgroundBrush => (SolidColorBrush)Application.Current.FindResource("WhiteBrush");

        public AffirmationOfResidenceAffidavitView()
        {
            InitializeComponent();
        }

        private async void Page_Loaded(object sender, RoutedEventArgs e)
        {
            await ((AffirmationOfResidenceAffidavitViewModel)DataContext).PageIsLoaded();
        }

        private void Page_Unloaded(object sender, RoutedEventArgs e)
        {
            this.SignatureCanvas.Strokes?.Clear();
            ((AffirmationOfResidenceAffidavitViewModel)DataContext).HasStrokes = false;
        }

        private void ClearSignature_MouseDown(object sender, MouseButtonEventArgs e)
        {
            this.SignatureCanvas.Strokes?.Clear();
            ((AffirmationOfResidenceAffidavitViewModel)DataContext).HasStrokes = false;
        }

        private void Flip_Click(object sender, RoutedEventArgs e)
        {
            Flipper.EnableTheFlip(sender, false);

            ScreenDisplayFlip.ToggleDisplay();

            _debounceTimer.Debounce(700, (p) =>
            {
                Flipper.EnableTheFlip(sender, true);
            });
        }

        private void SignatureCanvas_OnStrokeCollected(object sender, InkCanvasStrokeCollectedEventArgs e)
        {
            ((AffirmationOfResidenceAffidavitViewModel)DataContext).HasStrokes = true;
        }

        private void DoneSigning_Clicked(object sender, RoutedEventArgs e)
        {
            var rtb = new RenderTargetBitmap((int)SignatureCanvas.ActualWidth, (int)SignatureCanvas.ActualHeight, 96d, 96d, PixelFormats.Default);
            rtb.Render(SignatureCanvas);
            var encoder = new PngBitmapEncoder();
            encoder.Frames.Add(BitmapFrame.Create(rtb));
            var bytes = Conversion.ConvertToGray(encoder, Conversion.GrayPixelFormatOptions.BlackWhite);

            ((AffirmationOfResidenceAffidavitViewModel)DataContext).NextCommand.ExecuteAsync(bytes).ConfigureAwait(false);
        }
    }
}
