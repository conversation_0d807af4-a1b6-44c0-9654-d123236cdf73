<Page x:Class="Pollbook.Pages.Affidavit.PrintingAffidavitReportView"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:Pollbook.Pages.Affidavit"
      xmlns:core="clr-namespace:Pollbook.UICore"
      xmlns:usercontrols="clr-namespace:Pollbook.UserControls"
      mc:Ignorable="d" 
      DataContext="{Binding PrintingAffidavitReport, Source={StaticResource Locator}}"
      Background="{StaticResource Gray8Brush}"
      d:DesignHeight="1200" d:DesignWidth="1920"
      Title="PrintingAffidavitReportView">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*" />
            <RowDefinition Height="2*" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>
        <Grid Grid.Row="0">
            <Label Content="{Binding Title}" Style="{StaticResource Display2SemiBoldFont}" HorizontalAlignment="Left" VerticalAlignment="Top" Margin="100,120,0,0" />
        </Grid>
        <Grid Grid.Row="1">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Top" Margin="0,20,0,0">
                <usercontrols:Spinner />
            </StackPanel>
        </Grid>
        <Grid Grid.Row="2" Background="White">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="auto" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>
        </Grid>
    </Grid>
</Page>
