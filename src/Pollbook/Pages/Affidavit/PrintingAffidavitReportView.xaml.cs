using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace Pollbook.Pages.Affidavit
{
    /// <summary>
    /// Interaction logic for PrintingReceiptView.xaml
    /// </summary>
    public partial class PrintingAffidavitReportView : Page, IContextView
    {
        public bool IsModal => true;
        public SolidColorBrush PrimaryBackgroundBrush => (SolidColorBrush)Application.Current.FindResource("Gray8Brush");
        public PrintingAffidavitReportView()
        {
            InitializeComponent();
        }
    }
}
