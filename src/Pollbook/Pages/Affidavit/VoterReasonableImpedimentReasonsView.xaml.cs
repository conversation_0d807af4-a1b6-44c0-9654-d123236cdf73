using CommonServiceLocator;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.DynamicControls;
using ESS.Pollbook.ViewModel.Affidavit;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using Windows.UI.Xaml.Data;
using VerticalAlignment = System.Windows.VerticalAlignment;

namespace Pollbook.Pages.Affidavit
{
   /// <summary>
   /// Interaction logic for VoterReasonableImpedimentReasonsView.xaml
   /// </summary>
   public partial class VoterReasonableImpedimentReasonsView: IContextView
   {
      public bool IsModal => false; 
      public SolidColorBrush PrimaryBackgroundBrush => (SolidColorBrush)Application.Current.FindResource("WhiteBrush");

      /// <summary>
      /// View Model that we are working with
      /// </summary>
      private readonly VoterReasonableImpedimentReasonsViewModel _viewModel;
      private Button refuseButton = new Button();

      public Style SecondaryLargeButton => (Style)Application.Current.FindResource("SecondaryLargeButton");

      public VoterReasonableImpedimentReasonsView()
      {
         InitializeComponent();
         _viewModel = (VoterReasonableImpedimentReasonsViewModel)DataContext; // Must be after Initialize else DataContext is not set.

      }

      /// <summary>
      /// RoutedEvent that fires where the dialog has the "X" at top right clicked.
      /// </summary>
      /// <param name="sender"></param>
      /// <param name="e"></param>
      private void CloseModalReasonsView_Click(object sender, RoutedEventArgs e)
      {
         _viewModel.ReasonStateControl_Revert();
         e.Handled = true;
      }

      /// <summary>
      /// Event to handle a checkbox click.
      /// </summary>
      /// <param name="sender"></param>
      /// <param name="e"></param>
      private void DynamicCheckBox_Click(object sender, RoutedEventArgs e)
      {
         try
         {
            var checkbox = (CheckBox)sender;
            _viewModel.DynamicCheckBox_Click(checkbox.Name, checkbox.IsChecked != null && checkbox.IsChecked == true);
            e.Handled = true;
         }
         catch (Exception ex)
         {
            ServiceLocator.Current.GetInstance<IEssLogger>().LogError(ex,
                new Dictionary<string, string> { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
         }
      }

      /// <summary>
      /// Fired once when the page loads.
      /// </summary>
      /// <param name="sender"></param>
      /// <param name="e"></param>
      private void Page_Loaded(object sender, RoutedEventArgs e)
      {
         try
         {
            _viewModel.PageIsLoaded();
            ((MainWindow)Application.Current.MainWindow).OnClickedBtnClose_Clicked += CloseModalReasonsView_Click;

            int reasonCnt = 0;
            foreach (var control in _viewModel.GetReasonsControls.Where(x => x.Container_Name.Equals(gdReasons.Name)))
            {
               var isChecked = _viewModel.GetCheckBoxControlStateValue(control.Control_Name);

               _viewModel.AddProperty(control.Control_Name + "Content", _viewModel.GetDefinedText(control.Control_Pollbook_Defined_Text_Name, _viewModel.SelectedLanguage));
               _viewModel.AddProperty(control.Control_Name + "IsChecked", isChecked);
               // Revert is here to maintain the "defaults" that the Modal was loaded with.  If the person 
               // clicks "Ok" it serves no purpose.  If the user Clicks X, then we must reset IsChecked to what InitialChecked is.
               // Note that InitialChecked does not have bindings and is loaded once when SetSelectedControls() is executed.
               _viewModel.AddProperty(control.Control_Name + "Revert", isChecked);
               reasonCnt++;

               // Protect from an exception and get only the checkboxes
               foreach (var cb in DynamicControlsLib.Parse(control).OfType<CheckBox>())
               {
                  if (control.Control_Text_Wrap == true)
                  {
                     var tb = new TextBlock
                     {
                        Width = control.Control_Max_Text_Length ?? ESS.Pollbook.Core.DynamicControls.DynamicControlsLib.DefaultMaxTextLength,
                        TextWrapping = TextWrapping.Wrap,
                        HorizontalAlignment = HorizontalAlignment.Left,
                        VerticalAlignment = VerticalAlignment.Top
                     };
                     tb.SetBinding(TextBlock.TextProperty, "DynamicReasons." + control.Control_Name + "Content");
                     cb.Content = tb;
                  }
                  else
                  {
                     cb.SetBinding(ContentControl.ContentProperty, "DynamicReasons." + control.Control_Name + "Content");
                  }
                  cb.SetBinding(System.Windows.Controls.Primitives.ToggleButton.IsCheckedProperty, "DynamicReasons." + control.Control_Name + "IsChecked");
                  cb.Click += DynamicCheckBox_Click;
                  cb.IsChecked = isChecked;

                  int col = reasonCnt % 2 == 0 ? 1 : 0;
                  if (col == 0)
                     gdReasons.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

                  Grid.SetRow(cb, (int)Math.Floor((double)(reasonCnt - 1) / 2));
                  Grid.SetColumn(cb, col);
                  gdReasons.Children.Add(cb);
               }
            }
         }
         catch (Exception ex)
         {
            ServiceLocator.Current.GetInstance<IEssLogger>().LogError(ex,
                new Dictionary<string, string> { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });
         }
      }
   }
}
