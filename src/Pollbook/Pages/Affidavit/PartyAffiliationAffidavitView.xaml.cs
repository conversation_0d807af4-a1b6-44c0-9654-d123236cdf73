using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.UICore;
using ESS.Pollbook.ViewModel.Affidavit;
using Pollbook.UICore;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;

namespace Pollbook.Pages.Affidavit
{
   /// <summary>
   /// Interaction logic for PartyAffiliationAffidavitView.xaml
   /// </summary>
   public partial class PartyAffiliationAffidavitView : Page, IContextView
   {
      private DebounceDispatcher _debounceTimer = new DebounceDispatcher();

      public bool IsModal => true; 

      public SolidColorBrush PrimaryBackgroundBrush => (SolidColorBrush)Application.Current.FindResource("WhiteBrush");

      public PartyAffiliationAffidavitView()
      {
         InitializeComponent();
      }

      private async void Page_Loaded(object sender, RoutedEventArgs e)
      {
         await ((PartyAffiliationAffidavitViewModel)DataContext).PageIsLoaded();
      }
      private void Page_Unloaded(object sender, RoutedEventArgs e)
      {
         this.SignatureCanvas.Strokes?.Clear();
         ((PartyAffiliationAffidavitViewModel)DataContext).HasStrokes = false;
        }
      private void ClearSignature_MouseDown(object sender, MouseButtonEventArgs e)
      {
         this.SignatureCanvas.Strokes?.Clear();
         ((PartyAffiliationAffidavitViewModel)DataContext).HasStrokes = false;
        }

      private void Flip_Click(object sender, RoutedEventArgs e)
      {
         Flipper.EnableTheFlip(sender, false);

         ScreenDisplayFlip.ToggleDisplay();

         _debounceTimer.Debounce(700, (p) =>
         {
            Flipper.EnableTheFlip(sender, true);
         });
      }
        
      private void SignatureCanvas_OnStrokeCollected(object sender, InkCanvasStrokeCollectedEventArgs e)
      {
          ((PartyAffiliationAffidavitViewModel)DataContext).HasStrokes = true;
      }
    }
}
