<Page x:Class="Pollbook.Pages.Affidavit.VoterNameVerificationView"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:Pollbook.Pages.Affidavit"
      xmlns:userControls="clr-namespace:Pollbook.UserControls"
      mc:Ignorable="d" 
      DataContext="{Binding VoterNameVerification, Source={StaticResource Locator}}"
      d:DesignHeight="800" d:DesignWidth="1600"
      Title="VoterNameVerificationView">

    <Grid Background="White" Margin="100,0">
        <Grid.RowDefinitions>
            <RowDefinition Height="auto" />
            <RowDefinition Height="175" />
        </Grid.RowDefinitions>

        <StackPanel Margin="0,0,0,80">
            <Viewbox Stretch="Uniform" VerticalAlignment="Center" HorizontalAlignment="Center" Width="100" Margin="0,0,0,60">
                <Path Style="{StaticResource InformationIcon}" Stretch="Fill" />
            </Viewbox>
            <TextBlock Style="{StaticResource Display1TextBlock}" Margin="0,0,0,30" Text="{Binding Title}" HorizontalAlignment="Center" TextAlignment="Center" TextWrapping="Wrap" />
            <StackPanel HorizontalAlignment="Center" Margin="0, 0, 0, 30">
                <StackPanel Orientation="Horizontal">
                    <TextBlock  Text="{Binding VoterFullNameLabel}" Style="{StaticResource StandardTextBlock}"/>
                    <TextBlock Grid.Row="0" Text="{Binding VoterFullName}" Style="{StaticResource BoldTextBlock}" />
                </StackPanel>

                <StackPanel Orientation="Horizontal">
                    <TextBlock Grid.Row="1" Grid.Column="0" Text="{Binding VoterAddressLabel}" Style="{StaticResource StandardTextBlock}"/>
                    <TextBlock Grid.Row="1" Grid.Column="0" Text="{Binding VoterAddress}" Style="{StaticResource BoldTextBlock}" />
                </StackPanel>

                <StackPanel Orientation="Horizontal">
                    <TextBlock Grid.Row="2" Grid.Column="0" Text="{Binding VoterDateOfBirthLabel}" Style="{StaticResource StandardTextBlock}"/>
                    <TextBlock Grid.Row="2" Grid.Column="0" Text="{Binding VoterDateOfBirth}" Style="{StaticResource BoldTextBlock}" />
                </StackPanel>
            </StackPanel>
            <TextBlock Style="{StaticResource StandardTextBlock}" Text="{Binding Message}" HorizontalAlignment="Center" TextAlignment="Center" TextWrapping="Wrap" Margin="250 0" />
        </StackPanel>

        <Button Grid.Row="1"
                Style="{StaticResource PrimaryLargeButton}"
                Content="{Binding OkCaption}"
                userControls:ButtonHelper.DisableMultipleClicks="True"
                Command="{Binding OkCommand}"
                HorizontalAlignment="Center" Width="500"/>
    </Grid>
</Page>
