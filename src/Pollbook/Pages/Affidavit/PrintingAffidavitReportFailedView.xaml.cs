using ESS.Pollbook.ViewModel.Affidavit;
using System.Windows;
using System.Windows.Controls;

namespace Pollbook.Pages.Affidavit
{
    /// <summary>
    /// Interaction logic for PrintAuthDocFailedView.xaml
    /// </summary>
    public partial class PrintingAffidavitReportFailedView : Page
    {
        public PrintingAffidavitReportFailedView()
        {
            InitializeComponent();
        }
        private void Page_Loaded(object sender, RoutedEventArgs e)
        {
            var viewModel = (PrintingAffidavitReportFailedViewModel)DataContext;
            viewModel.PageIsLoaded();
        }
    }
}
