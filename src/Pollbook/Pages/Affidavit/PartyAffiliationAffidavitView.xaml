<Page x:Class="Pollbook.Pages.Affidavit.PartyAffiliationAffidavitView"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:uiCore="clr-namespace:Pollbook.UICore"
      xmlns:usercontrols="clr-namespace:Pollbook.UserControls"
      mc:Ignorable="d" 
      Background="White"
      DataContext="{Binding PartyAffiliationAffidavit, Source={StaticResource Locator}}"
      Loaded="Page_Loaded"
      Unloaded="Page_Unloaded"
      d:DesignHeight="1000" d:DesignWidth="1600"
      Title="AffidavitTemplateView">

   <Page.Resources>
      <Style TargetType="Label" BasedOn="{StaticResource StandardTitle}" />
      <uiCore:SignatureConverter x:Key="SignatureConverter" />
      <BooleanToVisibilityConverter x:Key="booleanToVisibility" />
      <uiCore:BooleanToVisibilityConverter x:Key="OnVisibilityConverter" True="Visible" False="Collapsed" />
      <uiCore:BooleanToVisibilityConverter x:Key="CalloutVisibilityConverter" True="Visible" False="Collapsed" />
      <uiCore:BooleanToVisibilityConverter x:Key="CalloppositeVisibilityConverter" True="Collapsed" False="Visible" />
      <Style x:Key="RotateButton" TargetType="{x:Type Button}">
         <Setter Property="Template">
            <Setter.Value>
               <ControlTemplate TargetType="{x:Type Button}">
                  <Border Name="Border" CornerRadius="2" BorderThickness="1" Background="Transparent" BorderBrush="Transparent">
                     <ContentPresenter Margin="2" 
                                 HorizontalAlignment="Center"
                                 VerticalAlignment="Center" />
                  </Border>
                  <ControlTemplate.Triggers>
                     <Trigger Property="IsKeyboardFocused" Value="true">
                        <Setter TargetName="Border" Property="BorderBrush" Value="Transparent" />
                     </Trigger>
                     <Trigger Property="IsDefaulted" Value="true">
                        <Setter TargetName="Border" Property="BorderBrush" Value="Transparent" />
                     </Trigger>
                     <Trigger Property="IsMouseOver" Value="true">
                        <Setter TargetName="Border" Property="Background" Value="Transparent" />
                     </Trigger>
                     <Trigger Property="IsPressed" Value="true">
                        <Setter TargetName="Border" Property="Background" Value="Transparent" />
                        <Setter TargetName="Border" Property="BorderBrush" Value="Transparent" />
                     </Trigger>
                     <Trigger Property="IsEnabled" Value="false">
                        <Setter TargetName="Border" Property="Background" Value="Transparent" />
                        <Setter TargetName="Border" Property="BorderBrush" Value="Transparent" />
                        <Setter Property="Foreground" Value="Transparent"/>
                     </Trigger>
                  </ControlTemplate.Triggers>
               </ControlTemplate>
            </Setter.Value>
         </Setter>
      </Style>
   </Page.Resources>

   <Grid Background="White" >
      <Grid.RowDefinitions>
         <RowDefinition Height=".1*" />
         <RowDefinition Height="1*" />
         <RowDefinition Height=".5*" />
      </Grid.RowDefinitions>

      <!-- Language Support -->
      <usercontrols:LanguageSelector Grid.Row="0" VerticalAlignment="Center" HorizontalAlignment="Right" Margin="0,0,50,0" />

      <!-- Main Grid -->
      <Grid Grid.Row="1">
         <Grid.RowDefinitions>
            <RowDefinition Height=".5*" />
            <RowDefinition Height=".5*" />
         </Grid.RowDefinitions>

         <!-- Oath -->
         <StackPanel Grid.Row="0" Orientation="Vertical" Margin="50,40,100,40" HorizontalAlignment="Center" >
            <Label Content="{Binding OathHeader}" Margin="0,10,0,10" HorizontalAlignment="Center" Style="{StaticResource StandardTitle}"/>
            <TextBlock Text="{Binding OathText}" Margin="300,0" TextWrapping="Wrap" HorizontalAlignment="Center" Style="{StaticResource DataGridSmallTextBlock}" />
         </StackPanel>

         <!-- Signature Blocks -->
         <Grid Grid.Row="1" Margin="0" Background="White">

            <Grid.ColumnDefinitions>
               <ColumnDefinition Width="1*" />
               <ColumnDefinition Width="2*" />
            </Grid.ColumnDefinitions>

            <StackPanel Grid.Column="0" Margin="300,0,0,0" VerticalAlignment="Top">
               <TextBlock Text="{Binding RegisteredPartyLabel}" Style="{StaticResource StandardTextBlock}" TextWrapping="WrapWithOverflow"/>
               <TextBlock Text="{Binding RegisteredParty}" Style="{StaticResource Display3BoldTextBlock}" TextWrapping="WrapWithOverflow" />

               <TextBlock Text="{Binding VotedPartyLabel}" Style="{StaticResource StandardTextBlock}" TextWrapping="WrapWithOverflow"/>
               <TextBlock Text="{Binding VotedParty}" Style="{StaticResource Display3BoldTextBlock}" TextWrapping="WrapWithOverflow" />
            </StackPanel>

            <!-- Voter Signature -->
            <Grid Grid.Column="1"  Margin="0,0,40,0" Background="White">
               <!-- Signature -->
               <StackPanel Margin="0,0,100,0" VerticalAlignment="Top" Orientation="Vertical">
                  <!-- Signature Panel -->
                  <Border BorderThickness="4" BorderBrush="#cfd8de" Height="204" Width="1050">
                     <DockPanel x:Name="sigPanel" Grid.Row="0" Height="204">
                        <InkCanvas x:Name="SignatureCanvas" StrokeCollected="SignatureCanvas_OnStrokeCollected" Height="{Binding Height, ElementName=sigPanel}">
                           <InkCanvas.DefaultDrawingAttributes>
                              <DrawingAttributes Width="6" Height="6"/>
                           </InkCanvas.DefaultDrawingAttributes>
                        </InkCanvas>
                     </DockPanel>
                  </Border>
                  <!-- Grid for labels underneath signature block -->
                  <Grid>
                     <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="1*" />
                        <ColumnDefinition Width="1*" />
                     </Grid.ColumnDefinitions>
                     <TextBlock Grid.Column="0" Text="{Binding VoterFullName}" Style="{StaticResource Display3BoldTextBlock}" Margin="40,0" TextAlignment="Left" />
                     <!-- Clear Signature -->
                     <Label Grid.Column="1" Name="clearVoterCanvas" Content="{Binding ClearSignature}" MouseLeftButtonDown="ClearSignature_MouseDown" HorizontalAlignment="Right" VerticalAlignment="Top" Margin="40,0">
                        <Label.Style>
                           <Style TargetType="Label" BasedOn="{StaticResource LinkLabel}">
                              <Style.Triggers>
                                 <DataTrigger Binding="{Binding Path=HasStrokes}" Value="False">
                                    <Setter Property="Foreground" Value="{StaticResource Gray5Brush}" />
                                 </DataTrigger>
                              </Style.Triggers>
                           </Style>
                        </Label.Style>
                     </Label>
                  </Grid>
               </StackPanel>
            </Grid>

         </Grid>
      </Grid>
      <!-- Navigation Buttons -->
      <Grid Grid.Row="2" Background="White" VerticalAlignment="Bottom">
         <Grid.ColumnDefinitions>
            <ColumnDefinition Width="auto" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="auto" />
         </Grid.ColumnDefinitions>

         <!-- Back -->
         <Button Grid.Column="0"
                    Content="{Binding BackLabel}"
                    Margin="40"
                    usercontrols:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding BackCommand}"
                    Style="{StaticResource SecondaryLargeButton}"
                    HorizontalAlignment="Left" />
         <Button Grid.Column="1"
                    Click="Flip_Click"
                    Width="140"
                    Height="150"
                    BorderBrush="Transparent"
                    Background="Transparent"
                    Style="{StaticResource RotateButton}"
                    usercontrols:ButtonHelper.DisableMultipleClicks="True"
                    Visibility="{Binding ShowFlipScreen, Converter={StaticResource OnVisibilityConverter}}">
            <StackPanel Orientation="Vertical" HorizontalAlignment="Center" VerticalAlignment="Center" Width="175">
               <Viewbox Stretch="Uniform" VerticalAlignment="Center" HorizontalAlignment="Center" Width="100" Margin="0">
                  <Path>
                     <Path.Style>
                        <Style TargetType="{x:Type Path}" BasedOn="{StaticResource FlipScreenIcon}" />
                     </Path.Style>
                  </Path>
               </Viewbox>
               <Label Content="Flip Screen" HorizontalAlignment="Center" Margin="10" Style="{StaticResource FlipScreenFont}" />
            </StackPanel>
         </Button>
         <StackPanel Grid.Column="2" Orientation="Horizontal">
            <!-- Refused to Sign -->
            <Button Content="{Binding RefusedButtonLabel }"
                       HorizontalAlignment="Left" Margin="40"
                       Command="{Binding RefusedToSignCommand}"
                       Style="{StaticResource SecondaryLargeButton}"
                       IsEnabled="{Binding IsRefusedButtonEnabled}"
                       Visibility="{Binding IsRefusedButtonEnabled, Converter={StaticResource OnVisibilityConverter}}"/>

            <!-- Next -->
            <Button Content="{Binding DoneSigning}" 
                       HorizontalAlignment="Right" Margin="40" 
                       Command="{Binding NextCommand}" 
                       Style="{StaticResource PrimaryLargeButton}" 
                       IsEnabled="{Binding IsNextEnabled}">
               <Button.CommandParameter>
                  <MultiBinding Converter="{StaticResource SignatureConverter}">
                     <Binding ElementName="SignatureCanvas" />
                  </MultiBinding>
               </Button.CommandParameter>
            </Button>
         </StackPanel>
      </Grid>
   </Grid>
</Page>
