<Page x:Class="Pollbook.Pages.ConfigurationUpdateAlertView"
     xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:uiCore="clr-namespace:Pollbook.UICore"
      xmlns:userControls="clr-namespace:Pollbook.UserControls"
      mc:Ignorable="d" 
      DataContext="{Binding ConfigurationUpdateAlert, Source={StaticResource Locator}}"
      d:DesignHeight="800" d:DesignWidth="1600">
    <Page.Resources>
        <uiCore:BoolInverterConverter x:Key="BoolInverterConverter" />
    </Page.Resources>

    <Grid Background="White" Margin="100,0" Width="1490">
        <Grid.RowDefinitions>
            <RowDefinition Height="auto" />
            <RowDefinition Height="175" />
        </Grid.RowDefinitions>

        <StackPanel Width="920" Margin="0,0,0,80">
            <Viewbox Stretch="Uniform" VerticalAlignment="Center" HorizontalAlignment="Center" Width="100" Margin="0,0,0,60">
                <Path Style="{StaticResource WarningIcon}" Stretch="Fill" />
            </Viewbox>
            <TextBlock Style="{StaticResource Display1TextBlock}" Margin="0,0,0,30" Text="{Binding Title}" HorizontalAlignment="Center" TextAlignment="Center" TextWrapping="Wrap" />
            <TextBlock Style="{StaticResource StandardTextBlock}" Foreground="{StaticResource Gray2Brush}" Text="{Binding Message}" HorizontalAlignment="Center" TextAlignment="Center" TextWrapping="Wrap" />
        </StackPanel>

        <!-- Buttons -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition />
                <ColumnDefinition Width="40" />
                <ColumnDefinition />
            </Grid.ColumnDefinitions>

            <Button Grid.Column="0"
                    Style="{StaticResource SecondaryLargeButton}"
                    Content="{Binding DelayChangesLabel}"
                    userControls:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding DelayChangesCommand}"
                    HorizontalAlignment="Right" />
            <Button Grid.Column="2"
                    Style="{StaticResource PrimaryLargeButton}"
                    Content="{Binding ApplyChangesLabel}"
                    userControls:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding ApplyChangesCommand}"
                    HorizontalAlignment="Left"
                    IsEnabled="{Binding SignOutInProgress, Converter={StaticResource BoolInverterConverter}}"/>
        </Grid>
    </Grid>
</Page>
