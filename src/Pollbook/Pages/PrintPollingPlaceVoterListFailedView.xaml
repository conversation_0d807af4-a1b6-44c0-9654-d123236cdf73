<Page x:Class="Pollbook.Pages.PrintPollingPlaceVoterListFailedView"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:Pollbook.Pages"
      xmlns:userControls="clr-namespace:Pollbook.UserControls"
      mc:Ignorable="d" 
      DataContext="{Binding PrintPollingPlaceVoterListFailed, Source={StaticResource Locator}}"
      Background="{StaticResource Gray8Brush}"
      d:DesignHeight="450" d:DesignWidth="800"
      Title="PrintPollingPlaceVoterListFailedView">


    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="auto" />
        </Grid.RowDefinitions>
        <Grid Grid.Row="0" Margin="120,70,120,0">
            <TextBlock Text="Print Voter List" Style="{StaticResource Display2SemiBoldTextBlock}"/>
        </Grid>
        <!-- Body -->
        <Grid Grid.Row="1">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <Viewbox Height="100" Width="100" Stretch="Uniform" HorizontalAlignment="Center" Margin="0,0,0,60">
                    <Path Style="{StaticResource ErrorIcon}"/>
                </Viewbox>
                <TextBlock Text="Unable to Print the Voter List" HorizontalAlignment="Center" Margin="0,0,0,40" Style="{StaticResource Display1TextBlock}"/>
                <TextBlock Text="Please check the printer or call the Elections Office for support." HorizontalAlignment="Center" Style="{StaticResource InstructionalSemiBoldTextBlock}"/>
            </StackPanel>
        </Grid>

        <!-- Buttons -->
        <Grid Grid.Row="2" Background="White">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="auto" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <Button Grid.Column="0"
                    Content="{Binding BackLabel}"
                    Style="{StaticResource SecondaryLargeButton}"
                    Margin="40"
                    userControls:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding BackCommand}" />

            <Button Grid.Column="1"
                    Content="{Binding RetryLabel}"
                    Style="{StaticResource PrimaryLargeButton}"
                    HorizontalAlignment="Right"
                    Margin="40"
                    userControls:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding RetryCommand}" />
        </Grid>
    </Grid>
</Page>