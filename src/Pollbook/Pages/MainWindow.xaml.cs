using CommonServiceLocator;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.Messaging;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.DynamicControls;
using ESS.Pollbook.ViewModel;
using ESS.Pollbook.ViewModel.Infrastructure;
using GalaSoft.MvvmLight.Messaging;
using Pollbook.UserControls;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;

namespace Pollbook.Pages
{
    /// <summary>
    ///     Auto-adjust logic
    ///     https://stackoverflow.com/questions/3193339/tips-on-developing-resolution-independent-application/5000120#5000120
    /// </summary>
    public partial class MainWindow
    {
        public static readonly RoutedEvent OnModalCloseButtonClicked =
            EventManager.RegisterRoutedEvent("btnClose_Click", RoutingStrategy.Direct, typeof(RoutedEventHandler),
                typeof(Button));

        private static readonly RoutedEvent OnContextCloseButtonClicked =
            EventManager.RegisterRoutedEvent("btnContextClose_Click", RoutingStrategy.Direct, typeof(RoutedEventHandler),
                typeof(Button));

        private readonly IMessenger _messenger = Messenger.Default;
        private IKeyboardService _keyboardService;

        public MainWindow()
        {
            InitializeComponent();
            ContextFrame.ContentRendered += ContextFrame_ContentRendered;
            ModalFrame.ContentRendered += ModalFrame_ContentRendered;
            ModalDialogFrame.ContentRendered += ModalFrame_ContentRendered;

            Thread.CurrentThread.Priority = ThreadPriority.Highest;

            if (RunningInKioskMode())
            {
                SystemDetails.IsKioskMode = true;
                Thread.CurrentThread.Priority = ThreadPriority.Highest;
#if DEBUG
                WindowStyle = WindowStyle.None;
                ResizeMode = ResizeMode.NoResize;
#endif
            }
        }

        //If you are using ModelFrame and if you want to show the close button
        //then we should set IsModal => false otherwise set IsModal => true;
        private void ModalFrame_ContentRendered(object sender, EventArgs e)
        {
            if (sender is Frame frame && frame.Content is IContextView context)
            {
                // Close Button
                var temp = frame.Template.FindName("CloseButton", frame);

                if (temp is Button button)
                {
                    button.Background = context.PrimaryBackgroundBrush;
                    button.Visibility = context.IsModal ? Visibility.Collapsed : Visibility.Visible;
                }
            }
        }

        private bool RunningInKioskMode()
        {
            var args = Environment.GetCommandLineArgs();
            return args.Any(a => a.ToLower() == "kiosk");
        }

        private void ContextFrame_ContentRendered(object sender, EventArgs e)
        {
            if (!(sender is Frame frame) || !(frame.Content is IContextView context)) return;

            // Close Button
            var temp = frame.Template.FindName("CloseButton", frame);

            if (!(temp is Button button)) return;

            button.Background = context.PrimaryBackgroundBrush;
            button.Visibility = context.IsModal ? Visibility.Collapsed : Visibility.Visible;
        }

        protected override void OnInitialized(EventArgs e)
        {
            base.OnInitialized(e);

            _keyboardService = ServiceLocator.Current.GetInstance<IKeyboardService>();

            _messenger.Register<NotificationMessage<NotificationTypeEnum>>(this, NotifyMe);
            _messenger.Register<DynamicFormControlsMessage>(this, DynamicFormControlsMessaging);

            MainFrame.Source = new Uri("/Pages/Launch.xaml", UriKind.Relative);
        }

        private void NotifyMe(NotificationMessage<NotificationTypeEnum> notificationMessage)
        {
            if (notificationMessage.Content == NotificationTypeEnum.Alert)
            {
                // Do nothing - handled by MainWindowViewModel
            }
            else
            {
                NotificationPanel.Children.Add(new NotificationBar(NotificationPanel)
                    { NotificationType = notificationMessage.Content, Message = notificationMessage.Notification });
            }
        }

        private void MainGrid_SizeChanged(object sender, EventArgs e)
        {
            CalculateScale();
        }

        private void CalculateScale()
        {
            var yScale = ActualHeight / 1200f;
            var xScale = ActualWidth / 1920f;
            var value = Math.Min(xScale, yScale);

            ScaleValue = (double)OnCoerceScaleValue(myMainWindow, value);
        }

        private void CloseContext_Click(object sender, RoutedEventArgs e)
        {
            var vm = (MainWindowViewModel)DataContext;
            vm.ContextFrameClosed();
            RaiseEvent(new RoutedEventArgs(OnContextCloseButtonClicked));
        }

        private void CloseModal_Click(object sender, RoutedEventArgs e)
        {
            var vm = (MainWindowViewModel)DataContext;
            vm.ModalFrameClosed();
            RaiseEvent(new RoutedEventArgs(OnModalCloseButtonClicked));
        }

        private void CloseModalDialog_Click(object sender, RoutedEventArgs e)
        {
           if (!(sender is Button closeButton)) return;

           var frame = closeButton.FindParent<Frame>();
            if (frame == null) return;

            frame.Source = null;
        }

        private void myMainWindow_Loaded(object sender, RoutedEventArgs e)
        {
            ((MainWindowViewModel)DataContext).MainWindowLoaded();
            var locator = new ViewModelLocator();
            var launch = locator.Launch;
        }

        private void myMainWindow_Activated(object sender, EventArgs e)
        {
            if (SystemDetails.IsKioskMode)
            {
                // Now set it to full screen. This works around a rendering problem on the Toshiba Encore 10.
                WindowStyle = WindowStyle.None;
                WindowState = WindowState.Maximized;
            }
        }

        // Remove the keyboard anytime we receive a mouse click or touch. This works only because we don't receive this event when a text field is clicked/touched
        // or when the keyboard is clicked/touched, since these controls handle the event and don't allow it to bubble up to here. For pages that have controls that
        // can receive focus other than text boxes, we must continue to use a GotFocus handler to ensure the keyboard is removed when these controls get focus, since
        // they will not allow the mouse event to bubble up to here.
        private void myMainWindow_MouseDown(object sender, MouseButtonEventArgs e)
        {
            _keyboardService.TextDeactivated();
        }

        private void CustomLocationInfo_Update()
        {
            try
            {
                Panel sp = null;

                var dynamicFormControls = ((MainWindowViewModel)DataContext).DynamicFormControls
                    .Where(n => n.Visible == 1).OrderBy(n => n.Sort_Order).ToList();

                if (dynamicFormControls.Any())
                {
                    var lastElement = dynamicFormControls[dynamicFormControls.Count - 1];

                    var containers = dynamicFormControls.Select(x => x.Container_Name).Distinct().ToList();
                    if (containers.Count > 0)
                        foreach (var container in containers)
                        {
                            sp = DynamicControlsLib.FindChild<StackPanel>(Application.Current.MainWindow, container);
                            sp.Children.Clear();
                        }

                    foreach (var dto in dynamicFormControls)
                    {
                        if (containers.Count > 1)
                            sp = DynamicControlsLib.FindChild<StackPanel>(Application.Current.MainWindow,
                                dto.Container_Name);

                        if (sp != null)
                            foreach (var element in DynamicControlsLib.Parse(dto))
                                if (element.GetType() == typeof(TextBlock))
                                {
                                    var tb = (TextBlock)element;
                                    tb.Foreground = new SolidColorBrush(Color.FromArgb(191, 255, 255, 255));
                                    tb.FontSize = 24;
                                    tb.Margin = new Thickness(0, 0, 5, 0);
                                    sp.Children.Add(tb);
                                }
                                else
                                {
                                    sp.Children.Add(element);
                                }

                        // adding the pipe separator if we aren't on the last control
                        if (dto == lastElement || sp == null) continue;
                        // tried using Separator but it never appears right, using a Grid instead
                        var separator = new Grid
                        {
                            Background =
                                (SolidColorBrush)Application.Current
                                    .FindResource("Gray4Brush"), // "#aebbc4" -> Gray4Color
                            Height = 25,
                            Width = 2,
                            Margin = new Thickness(12, 0, 12, 0)
                        };
                        sp.Children.Add(separator);
                    }
                }
            }
            catch (Exception ex)
            {
                var essLogger = ServiceLocator.Current.GetInstance<IEssLogger>();
                var logProps = new Dictionary<string, string> { { "Action", "MainWindow.CustomLocationInfo_Update" } };
                essLogger.LogError(ex, logProps);
            }
        }

        private async void DynamicFormControlsMessaging(DynamicFormControlsMessage msg)
        {
            await ((MainWindowViewModel)DataContext).GetDynamicFormControls();
            CustomLocationInfo_Update();
        }

        // expose our event
        public event RoutedEventHandler OnClickedBtnClose_Clicked
        {
            add => AddHandler(OnModalCloseButtonClicked, value);
            remove => RemoveHandler(OnModalCloseButtonClicked, value);
        }

        // expose our event
        public event RoutedEventHandler OnClickedBtnCloseContextWindow_Clicked
        {
            add => AddHandler(OnContextCloseButtonClicked, value);
            remove => RemoveHandler(OnContextCloseButtonClicked, value);
        }

        private void MainWindow_OnClosing(object sender, CancelEventArgs e)
        {
            Environment.Exit(0);
        }

        #region ScaleValue Depdency Property

        public static readonly DependencyProperty ScaleValueProperty = DependencyProperty.Register("ScaleValue",
            typeof(double), typeof(MainWindow), new UIPropertyMetadata(1.0, OnScaleValueChanged, OnCoerceScaleValue));

        private static object OnCoerceScaleValue(DependencyObject o, object value)
        {
            var mainWindow = o as MainWindow;

            return mainWindow?.OnCoerceScaleValue((double)value) ?? value;
        }

        private static void OnScaleValueChanged(DependencyObject o, DependencyPropertyChangedEventArgs e)
        {
            if (o is MainWindow mainWindow)
                mainWindow.OnScaleValueChanged((double)e.OldValue, (double)e.NewValue);
        }

        private double OnCoerceScaleValue(double value)
        {
            if (double.IsNaN(value)) return 1.0f;

            value = Math.Max(0.01, value);

            return value;
        }

        protected virtual void OnScaleValueChanged(double oldValue, double newValue)
        {
        }

        public double ScaleValue
        {
            get => (double)GetValue(ScaleValueProperty);
            set => SetValue(ScaleValueProperty, value);
        }

        #endregion
    }
}