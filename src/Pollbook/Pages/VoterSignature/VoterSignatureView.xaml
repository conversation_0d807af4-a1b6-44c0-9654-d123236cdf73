<Page x:Class="Pollbook.Pages.VoterSignature.VoterSignatureView"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:uiCore="clr-namespace:Pollbook.UICore"
      xmlns:usercontrols="clr-namespace:Pollbook.UserControls"
      mc:Ignorable="d"
      DataContext="{Binding VoterSignature, Source={StaticResource Locator}}"
      Initialized="Page_Initialized"
      Unloaded="Page_Unloaded"
      Background="White"
      d:DesignHeight="1000" d:DesignWidth="1600">
    <Page.Resources>
        <Style TargetType="Label" BasedOn="{StaticResource StandardTitle}"/>
        <Style TargetType="{x:Type TabPanel}">
            <Setter Property="Background" Value="Black"></Setter>
        </Style>

        <uiCore:MultiNameTextConverter x:Key="MultiNameTextConverter" />
        <uiCore:AddressLine1Converter x:Key="AddressLine1Converter" />
        <uiCore:AddressLine2Converter x:Key="AddressLine2Converter" />

        <uiCore:BooleanToVisibilityConverter x:Key="OnVisibilityConverter" True="Visible" False="Collapsed" />

        <Style x:Key="RotateButton" TargetType="{x:Type Button}">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type Button}">
                        <Border Name="Border" CornerRadius="2" BorderThickness="1" Background="Transparent" BorderBrush="Transparent">
                            <ContentPresenter Margin="2" 
                                 HorizontalAlignment="Center"
                                 VerticalAlignment="Center" />
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsKeyboardFocused" Value="true">
                                <Setter TargetName="Border" Property="BorderBrush" Value="Transparent" />
                            </Trigger>
                            <Trigger Property="IsDefaulted" Value="true">
                                <Setter TargetName="Border" Property="BorderBrush" Value="Transparent" />
                            </Trigger>
                            <Trigger Property="IsMouseOver" Value="true">
                                <Setter TargetName="Border" Property="Background" Value="Transparent" />
                            </Trigger>
                            <Trigger Property="IsPressed" Value="true">
                                <Setter TargetName="Border" Property="Background" Value="Transparent" />
                                <Setter TargetName="Border" Property="BorderBrush" Value="Transparent" />
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="false">
                                <Setter TargetName="Border" Property="Background" Value="Transparent" />
                                <Setter TargetName="Border" Property="BorderBrush" Value="Transparent" />
                                <Setter Property="Foreground" Value="Transparent"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Page.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="0.3*" />
            <RowDefinition Height="1.4*" />
            <RowDefinition Height="1.8*" />
            <RowDefinition Height="0.9*" />
            <RowDefinition Height="1.2*" />
        </Grid.RowDefinitions>
        <!-- Header -->
        <usercontrols:LanguageSelector Grid.Row="0" VerticalAlignment="Center" HorizontalAlignment="Right" Margin="0,0,50,0" />

        <StackPanel Grid.Row="1">
            <Label Style="{StaticResource Display2SemiBoldFont}" Content="{Binding SignatureHeader}" HorizontalAlignment="Center" VerticalAlignment="Center"/>
            <TextBlock Style="{StaticResource DataGridSmallTextBlock}" TextAlignment="Left" TextWrapping="WrapWithOverflow" Text="{Binding OathText}" HorizontalAlignment="Left" VerticalAlignment="Center" Margin ="100,10,100,0"/>
        </StackPanel>

        <!-- Signature -->
        <Grid Grid.Row="2" Margin="100,0,100,20" VerticalAlignment="Bottom">
            <!-- Signature Panel -->
            <Border BorderThickness="4"
                    BorderBrush="#cfd8de" Height="304">
                <DockPanel Height="304">
                    <InkCanvas x:Name="SignatureCanvas"
                               StrokeCollected="SignatureCanvas_OnStrokeCollected">
                        <InkCanvas.DefaultDrawingAttributes>
                            <DrawingAttributes Width="8" Height="8" />
                        </InkCanvas.DefaultDrawingAttributes>
                    </InkCanvas>
                </DockPanel>
            </Border>
        </Grid>

        <!-- Voter Info -->
        <Grid Grid.Row="3" Background="White" VerticalAlignment="Top">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Voter personal info -->
            <Grid Grid.Column="0" HorizontalAlignment="Left">
                <Grid.RowDefinitions>
                    <RowDefinition Height="auto" />
                    <RowDefinition Height="auto" />
                    <RowDefinition Height="auto" />
                </Grid.RowDefinitions>

                <!-- Name -->
                <Label Grid.Row="0" Margin="100,0,0,0" Grid.RowSpan="2">
                    <Label.Style>
                        <Style TargetType="Label" BasedOn="{StaticResource Body1BoldFont}" />
                    </Label.Style>
                    <Label.Content>
                        <TextBlock>
                            <TextBlock.Text>
                                <Binding Path="Voter.FullName" />
                            </TextBlock.Text>
                        </TextBlock>
                    </Label.Content>
                </Label>

                <!-- Address -->
                <Label Grid.Row="1" Margin="100,10,0,0" Grid.RowSpan="2">
                    <Label.Style>
                        <Style TargetType="Label" BasedOn="{StaticResource Body1SemiBoldFont}"/>
                    </Label.Style>
                    <Label.Content>
                        <TextBlock>
                            <TextBlock.Text>
                                <Binding Path="Voter.AddressLineText" />
                            </TextBlock.Text>
                        </TextBlock>
                    </Label.Content>
                </Label>

                <!-- City, State ZIP -->
                <Label Grid.Row="2" Margin="100,20,0,0" HorizontalAlignment="Left">
                    <Label.Style>
                        <Style TargetType="Label" BasedOn="{StaticResource Body1SemiBoldFont}"/>
                    </Label.Style>
                    <Label.Content>
                        <TextBlock>
                            <TextBlock.Text>
                                <Binding Path="Voter.CityStateZipLineText" />
                            </TextBlock.Text>
                        </TextBlock>
                    </Label.Content>
                </Label>
            </Grid>

            <!-- Clear Signature -->
            <Label Grid.Column="2" Margin="0,0,100,34" Content="{Binding ClearSignature}" HorizontalAlignment="Right" VerticalAlignment="Top" MouseLeftButtonDown="clearSignature_mouseDown" Grid.RowSpan="2" Height="50">
                <Label.Style>
                    <Style TargetType="Label" BasedOn="{StaticResource LinkLabel}">
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding Path=HasStrokes}" Value="False">
                                <Setter Property="Foreground" Value="{StaticResource Gray5Brush}" />
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </Label.Style>
            </Label>
    </Grid>

        <!-- Buttons -->
        <Grid Grid.Row="4" Background="White" VerticalAlignment="Bottom">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="auto" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="auto" />
            </Grid.ColumnDefinitions>

            <!-- Back -->
            <Button Grid.Column="0"
                    Content="{Binding BackLabel}"
                    Margin="40,0,0,40"
                    usercontrols:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding BackCommand}"
                    Style="{StaticResource SecondaryLargeButton}"/>

            <Button Grid.Column="1"
                    usercontrols:ButtonHelper.DisableMultipleClicks="True"
                    Click="Flip_Click"
                    Width="140"
                    Height="150"
                    BorderBrush="Transparent"
                    Background="Transparent"
                    Style="{StaticResource RotateButton}"
                    Visibility="{Binding ShowFlipScreen, Converter={StaticResource OnVisibilityConverter}}">
            <StackPanel Orientation="Vertical" HorizontalAlignment="Center" VerticalAlignment="Center" Width="175">
                <Viewbox Stretch="Uniform" VerticalAlignment="Center" HorizontalAlignment="Center" Width="100" Margin="0">
                    <Path>
                        <Path.Style>
                            <Style TargetType="{x:Type Path}" BasedOn="{StaticResource FlipScreenIcon}" />
                        </Path.Style>
                    </Path>
                </Viewbox>
                <Label Content="Flip Screen" HorizontalAlignment="Center" Margin="10" Style="{StaticResource FlipScreenFont}" />
            </StackPanel>
        </Button>

            <StackPanel Grid.Column="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,0,40,40">

            <Button Command="{Binding SkipCommand}" 
                    usercontrols:ButtonHelper.DisableMultipleClicks="True"
                    Visibility="{Binding ShowSkipSignature, Converter={StaticResource OnVisibilityConverter}}"
                    Style="{StaticResource SecondaryLargeButton}">
                <Button.Content>
                    <TextBlock TextWrapping="Wrap" TextAlignment="Center" FontSize="36" Text="{Binding UnableToSignLabel}"/>
                </Button.Content>
            </Button>

            <Button Command="{Binding SignatureCapturedOnPaperCommand}"
                    usercontrols:ButtonHelper.DisableMultipleClicks="True"
                    Visibility="{Binding ShowSignatureCapturedOnPaper, Converter={StaticResource OnVisibilityConverter}}">
                <Button.Content>
                    <TextBlock TextWrapping="Wrap" TextAlignment="Center" FontSize="36" Text="{Binding SignatureCapturedOnPaper}"/>
                </Button.Content>
                <Button.Style>
                    <Style TargetType="Button" BasedOn="{StaticResource SecondaryLargeButton}" />
                </Button.Style>
            </Button>

            <!-- Done Delivering -->
            <Button  Content="{Binding DoneSigning}"
                     HorizontalAlignment="Right" 
                     usercontrols:ButtonHelper.DisableMultipleClicks="True"
                     Command="{Binding DoneDeliveringCommand}"
                     CommandParameter="{Binding ElementName=SignatureCanvas}"
                     IsEnabled="{Binding Path=HasStrokes}"
                     Margin="14,0,0,0"
                     Width="475"
                     Style="{StaticResource PrimaryLargeButton}"/>

        </StackPanel>
        </Grid>
    </Grid>
</Page>
