using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.UICore;
using ESS.Pollbook.ViewModel.VoterSignature;
using Pollbook.UICore;
using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;

namespace Pollbook.Pages.VoterSignature
{
    /// <summary>
    /// Interaction logic for VoterSignatureView.xaml
    /// </summary>
    public partial class VoterSignatureView : Page, IContextView
    {
        public bool IsModal => true;
        private VoterSignatureViewModel vm;

        public SolidColorBrush PrimaryBackgroundBrush =>
            (SolidColorBrush)Application.Current.FindResource("WhiteBrush");

        private DebounceDispatcher _debounceTimer = new DebounceDispatcher();

        public VoterSignatureView()
        {
            InitializeComponent();
        }

        private void Page_Initialized(object sender, EventArgs e)
        {
            vm = ((VoterSignatureViewModel)DataContext);
            vm.PageInitialized();
        }
        
        private void Page_Unloaded(object sender, EventArgs e)
        {
            SignatureCanvas.Strokes?.Clear();
        }

        private void clearSignature_mouseDown(object sender, MouseButtonEventArgs e)
        {
            SignatureCanvas.Strokes?.Clear();
            vm.ResetSignature();
        }

        private void Flip_Click(object sender, RoutedEventArgs e)
        {
            Flipper.EnableTheFlip(sender, false);

            ScreenDisplayFlip.ToggleDisplay();

            _debounceTimer.Debounce(700, (p) =>
            {
                Flipper.EnableTheFlip(sender, true);
            });
        }

        private void SignatureCanvas_OnStrokeCollected(object sender, InkCanvasStrokeCollectedEventArgs e)
        {
            vm.HasStrokes = true;
            e.Handled = true;
        }
    }
}
