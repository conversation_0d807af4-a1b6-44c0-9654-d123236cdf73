<Page x:Class="Pollbook.Pages.VoterSignature.SignatureComparisonView"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:uiCore="clr-namespace:Pollbook.UICore"
      xmlns:userControls="clr-namespace:Pollbook.UserControls"
      Initialized="Page_InitializedAsync"
      DataContext="{Binding SigComparisionViewModel, Source={StaticResource Locator}}"
      mc:Ignorable="d" 
      d:DesignHeight="1000" d:DesignWidth="1600"
      Background="{StaticResource Gray8Brush}" Loaded="Page_Loaded">
   <Page.Resources>
      <Style TargetType="Label" BasedOn="{StaticResource StandardTitle}" />
      <Style TargetType="{x:Type TabPanel}">
          <Setter Property="Background" Value="Black"></Setter>
      </Style>

      <uiCore:BooleanToVisibilityConverter x:Key="BooleanToVisibility" True="Visible" False="Hidden" />
      <uiCore:MultiNameTextConverter x:Key="MultiNameTextConverter" />
      <uiCore:AddressLine1Converter x:Key="AddressLine1Converter" />
      <uiCore:AddressLine2Converter x:Key="AddressLine2Converter" />
      <uiCore:ColumnSpanConverter x:Key="ColumnSpanConverter" />
   </Page.Resources>

   <Grid >
      <Grid.ColumnDefinitions>
         <ColumnDefinition Width="0.25*"/>
         <ColumnDefinition Width="3*"/>
         <ColumnDefinition Width="0.05*"/>
         <ColumnDefinition Width="3*"/>
         <ColumnDefinition Width="0.25*"/>
      </Grid.ColumnDefinitions>
      <Grid.RowDefinitions>
         <RowDefinition Height="Auto" />
         <RowDefinition Height="Auto" />
         <RowDefinition  Height="Auto" />
         <RowDefinition  Height="198*" />
         <RowDefinition Height="auto"/>
      </Grid.RowDefinitions>

      <Grid Grid.Row="1" Grid.Column="1" Grid.ColumnSpan="4">
         <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
         </Grid.RowDefinitions>
         <Label Grid.Row="0" HorizontalAlignment="Left" VerticalAlignment="Top" Style="{StaticResource Body1SemiBoldFont}" Content="{Binding LabelContent}" Margin="34,40,0,0" />
         <Label Grid.Row="1" Content="Accept Voter Signature" HorizontalAlignment="Left" VerticalAlignment="Top" Margin="34,10,0,0">
            <Label.Style>
               <Style TargetType="Label" BasedOn="{StaticResource Display2SemiBoldFont}" />
            </Label.Style>
         </Label>
         <Label Grid.Row="2" HorizontalAlignment="Left" VerticalAlignment="Bottom" Style="{StaticResource Body1SemiBoldFont}" Content="Compare Signatures:" Visibility="{Binding ShowSignatureOnFile, Converter={StaticResource BooleanToVisibility}}" Margin="34,10,0,0" />
      </Grid>

      <Grid Grid.Row="2" Grid.Column="1" Grid.ColumnSpan="{Binding ShowSignatureOnFile, Converter={StaticResource ColumnSpanConverter}}" VerticalAlignment="Center" Background="White" Margin="35,20,5,0" Height="210">
            <Rectangle Name="sigCapturedOnPaperRec" Fill="#e5ebf0" MaxHeight="210" Stroke="#e5ebf0" StrokeThickness="1.0" VerticalAlignment="Stretch" HorizontalAlignment="Stretch" />
            <Label Name="sigCaptureOnPaperLabel" Style="{StaticResource Body1SemiBoldLightGrayFont}" Content="{Binding SignatureCapturedOnPaperText}"  Margin="0,0,0,76" VerticalAlignment="Bottom" HorizontalAlignment="Center" Visibility="{Binding ShowSignatureCapturedOnPaperPlaceholder, Converter={StaticResource BooleanToVisibility}}" />
            <Label Name="unableToSignLabel" Style="{StaticResource Body1SemiBoldLightGrayFont}" Content="{Binding UnableToSignText}"  Margin="0,0,0,76" VerticalAlignment="Bottom" HorizontalAlignment="Center" Visibility="{Binding ShowUnableToSignPlaceholder, Converter={StaticResource BooleanToVisibility}}" />
            <Image Name="currentSignature"  MaxHeight="{Binding SignatureMaxHeight}" Source="{Binding Voter.VoterTransactionSignature}" Stretch="Fill" />
      </Grid>

      <Grid Visibility="{Binding ShowSignatureOnFile, Converter={StaticResource BooleanToVisibility}}" Grid.Row="2" Grid.Column="3">
         <Rectangle Name="noSigRec" Fill="#e5ebf0" MaxHeight="210" Margin="5,20,35,0" Stroke="#e5ebf0" StrokeThickness="1.0" VerticalAlignment="Stretch" HorizontalAlignment="Stretch" />
         <Label Name="noSigLabel" Style="{StaticResource Body1SemiBoldLightGrayFont}" Content="No Signature on File"  Margin="0,0,0,76" VerticalAlignment="Bottom" HorizontalAlignment="Center" Visibility="{Binding ShowNoSignatureLabel, Converter={StaticResource BooleanToVisibility}}" />
         <Image Name="signatureOnFile" Opacity=".30" MaxHeight="210" Margin="5,20,35,0" Stretch="Fill" Source="{Binding Voter.VoterVRSignatureImage}"/>
      </Grid>

        <Grid Grid.Row="3" Grid.Column="1" Grid.ColumnSpan="{Binding ShowSignatureOnFile, Converter={StaticResource ColumnSpanConverter}}">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="650" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="150" />
            </Grid.ColumnDefinitions>

            <Label Grid.Column="0" Margin="34,5,0,34" HorizontalAlignment="Left">
                <Label.Style>
                    <Style TargetType="Label" BasedOn="{StaticResource Body1BoldFont}" />
                </Label.Style>
                <Label.Content>
                    <TextBlock>
                        <TextBlock.Text>
                            <Binding Path="Voter.FullName" />
                        </TextBlock.Text>
                    </TextBlock>
                </Label.Content>
            </Label>

            <!-- Address -->
            <Label Grid.Column="0" Margin="34,40,0,34" HorizontalAlignment="Left">
                <Label.Style>
                    <Style TargetType="Label" BasedOn="{StaticResource Body1SemiBoldFont}"/>
                </Label.Style>
                <Label.Content>
                    <TextBlock>
                        <TextBlock>
                            <TextBlock.Text>
                                <Binding Path="Voter.AddressLineText" />
                            </TextBlock.Text>
                        </TextBlock>
                    </TextBlock>
                </Label.Content>
            </Label>

            <!-- City, State ZIP -->
            <Label Grid.Column="0" Margin="34,75,0,0" HorizontalAlignment="Left">
                <Label.Style>
                    <Style TargetType="Label" BasedOn="{StaticResource Body1SemiBoldFont}"/>
                </Label.Style>
                <Label.Content>
                    <TextBlock>
                        <TextBlock.Text>
                            <Binding Path="Voter.CityStateZipLineText" />
                        </TextBlock.Text>
                    </TextBlock>
                </Label.Content>
            </Label>

            <userControls:ButtonRotate Grid.Row="0" Grid.Column="2" SourceImageBytes="{Binding Voter.VoterTransactionSignature, Mode=TwoWay}" HorizontalAlignment="Right" Visibility="{Binding AllowSignatureRotation, Converter={StaticResource BooleanToVisibility}}" />
        </Grid>

        <Grid Grid.Row="3" Grid.Column="3" Margin="0,40,34,0" VerticalAlignment="Top">
         <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="*" />
         </Grid.ColumnDefinitions>

         <!-- Signature Panel -->
         <Grid Grid.Column="0" Visibility="{Binding IsAdditionalSignaturesVisible, Converter={StaticResource BooleanToVisibility}}" Margin="0,40,0,0">
            <Grid.RowDefinitions>
               <RowDefinition Height="80"/>
               <RowDefinition Height="*" />
               <RowDefinition Height="80"/>
            </Grid.RowDefinitions>
            <Grid.Style>
               <Style TargetType="Grid">
                  <Style.Triggers>
                     <DataTrigger Binding="{Binding IsSignature2Visible}" Value="False">
                        <Setter Property="Grid.ColumnSpan" Value="2" />
                     </DataTrigger>
                  </Style.Triggers>
               </Style>
            </Grid.Style>
            <Label Grid.Row="0" Style="{StaticResource Body1BoldFont}" VerticalAlignment="Bottom" Content="{Binding SignatureLabel1}" />
            <Border Grid.Row="1" Margin="0,0,10,0"
                        BorderThickness="4"
                        BorderBrush="#cfd8de" Height="210">
               <DockPanel Height="210">
                  <InkCanvas x:Name="signatureCanvas1" StrokeCollected="Signature1_StrokesCollected">
                     <InkCanvas.DefaultDrawingAttributes>
                        <DrawingAttributes Width="8" Height="8" />
                     </InkCanvas.DefaultDrawingAttributes>
                  </InkCanvas>
               </DockPanel>
            </Border>
            <Label Grid.Row="2" Margin="0,0,10,34" Content="{Binding ClearSignatureLabel}" HorizontalAlignment="Right" VerticalAlignment="Top" MouseLeftButtonDown="ClearSignature1_Click"  Grid.RowSpan="2" Height="50">
               <Label.Style>
                  <Style TargetType="Label" BasedOn="{StaticResource LinkLabel}">
                     <Style.Triggers>
                        <DataTrigger Binding="{Binding Path=HasStrokes1}" Value="False">
                           <Setter Property="Foreground" Value="{StaticResource Gray5Brush}" />
                        </DataTrigger>
                     </Style.Triggers>
                  </Style>
               </Label.Style>
            </Label>
         </Grid>
         <Grid Grid.Column="1" Visibility="{Binding IsSignature2Visible, Converter={StaticResource BooleanToVisibility}}" Margin="0,40,0,0">
            <Grid.RowDefinitions>
               <RowDefinition Height="80"/>
               <RowDefinition Height="*" />
               <RowDefinition Height="80"/>
            </Grid.RowDefinitions>
            <Label Grid.Row="0" Style="{StaticResource Body1BoldFont}" VerticalAlignment="Bottom" Content="{Binding SignatureLabel2}" />
            <Border Grid.Row="1" 
                    BorderThickness="4" Margin="0,0,10,0"
                    BorderBrush="#cfd8de" Height="210">
               <DockPanel Height="210">
                  <InkCanvas x:Name="signatureCanvas2" StrokeCollected="Signature2_StrokesCollected" VerticalAlignment="Center">
                     <InkCanvas.DefaultDrawingAttributes>
                        <DrawingAttributes Width="8" Height="8" />
                     </InkCanvas.DefaultDrawingAttributes>
                  </InkCanvas>
               </DockPanel>
            </Border>
            <Label Grid.Row="2" Margin="0,0,0,34" Content="{Binding ClearSignatureLabel}" HorizontalAlignment="Right" VerticalAlignment="Top" MouseLeftButtonDown="ClearSignature2_Click"  Grid.RowSpan="2" Height="50" >
               <Label.Style>
                  <Style TargetType="Label" BasedOn="{StaticResource LinkLabel}">
                     <Style.Triggers>
                        <DataTrigger Binding="{Binding Path=HasStrokes2}" Value="False">
                           <Setter Property="Foreground" Value="{StaticResource Gray5Brush}" />
                        </DataTrigger>
                     </Style.Triggers>
                  </Style>
               </Label.Style>
            </Label>
         </Grid>
      </Grid>

      <!-- Bottom Row with navigational buttons-->
        <Grid Grid.Row="4" Grid.Column="0" Grid.ColumnSpan="5" Background="#ffffff">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="auto" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="auto" />
            </Grid.ColumnDefinitions>
            <Button Content="{Binding BackLabel}"
                    Margin="40"
                    HorizontalAlignment="Left"
                    VerticalAlignment="Bottom"
                    userControls:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding BackCommand}">
                <Button.Style>
                    <Style TargetType="Button" BasedOn="{StaticResource SecondaryLargeButton}" />
                </Button.Style>
            </Button>

            <StackPanel x:Name="spAcceptReject" Orientation="Horizontal" Grid.Column="2" FlowDirection="RightToLeft" Background="#ffffff">
                <Button Content="{Binding AcceptLabel}"
                        Margin="40,40,20,40"
                        VerticalAlignment="Bottom"
                        userControls:ButtonHelper.DisableMultipleClicks="True"
                        Click="Accept_Click"
                        IsEnabled="{Binding Path=IsAcceptButtonEnabled}" 
                    Style="{StaticResource PrimaryLargeButton}" Width="475" />
            </StackPanel>
        </Grid>
    </Grid>
</Page>
