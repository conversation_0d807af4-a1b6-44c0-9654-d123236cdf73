<UserControl x:Class="Pollbook.Pages.SelectParty.SelectPartyView" KeyDown="Page_KeyDown"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:uiCore="clr-namespace:Pollbook.UICore"
      xmlns:usercontrols="clr-namespace:Pollbook.UserControls"
      mc:Ignorable="d" 
      xmlns:ui="clr-namespace:Pollbook.UserControls"
      d:DesignHeight="1000" d:DesignWidth="1600"
      Initialized="UserControl_Initialized"
      DataContext="{Binding SelectParty, Source={StaticResource Locator}}"
      Background="White">
    <UserControl.Resources>
        <uiCore:BooleanToVisibilityConverter x:Key="OnVisibilityConverter" True="Visible" False="Collapsed" />
        <uiCore:ZeroCollapsedNonZeroVisible x:Key="HideListBox" />
        <uiCore:StringToXamlConverter x:Key="StringToXamlConverter" />

        <Style x:Key="RotateButton" TargetType="{x:Type Button}">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type Button}">
                        <Border Name="Border" CornerRadius="2" BorderThickness="1" Background="Transparent" BorderBrush="Transparent">
                            <ContentPresenter Margin="2" 
                                 HorizontalAlignment="Center"
                                 VerticalAlignment="Center" />
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsKeyboardFocused" Value="true">
                                <Setter TargetName="Border" Property="BorderBrush" Value="Transparent" />
                            </Trigger>
                            <Trigger Property="IsDefaulted" Value="true">
                                <Setter TargetName="Border" Property="BorderBrush" Value="Transparent" />
                            </Trigger>
                            <Trigger Property="IsMouseOver" Value="true">
                                <Setter TargetName="Border" Property="Background" Value="Transparent" />
                            </Trigger>
                            <Trigger Property="IsPressed" Value="true">
                                <Setter TargetName="Border" Property="Background" Value="Transparent" />
                                <Setter TargetName="Border" Property="BorderBrush" Value="Transparent" />
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="false">
                                <Setter TargetName="Border" Property="Background" Value="Transparent" />
                                <Setter TargetName="Border" Property="BorderBrush" Value="Transparent" />
                                <Setter Property="Foreground" Value="Transparent"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>
    <Grid Background="{StaticResource Gray8Brush}">
        <Grid.RowDefinitions>
            <RowDefinition Height="auto" />
            <RowDefinition Height="auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="auto" />
        </Grid.RowDefinitions>

        <usercontrols:LanguageSelector x:Name="LanguageSelector" Grid.Row="0" VerticalAlignment="Center" HorizontalAlignment="Right" Margin="0,10,50,0" DataContext="{Binding Languages, Source={StaticResource Locator}}" Visibility="{Binding Languages.Count, Converter={StaticResource HideListBox}}"/>
        
        <!-- Header -->
        <Grid Grid.Row="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>
            <Label Grid.Row="0" HorizontalAlignment="Left" Content="{Binding LabelContent}" VerticalAlignment="Top">
                <Label.Style>
                    <Style TargetType="Label" BasedOn="{StaticResource Body1SemiBoldFont}">
                        <Setter Property="Margin" Value="107,40,0,2"/>
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding Visibility, ElementName=LanguageSelector}" Value="Visible">
                                <Setter Property="Margin" Value="107,-8,0,2"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </Label.Style>
            </Label>

            <Label Grid.Row="1" HorizontalAlignment="Left" VerticalAlignment="Top"  Margin="107,8,0,0" Style="{StaticResource Display2SemiBoldFont}" Content="{Binding Title}"/>
        </Grid>

        <StackPanel Grid.Row="2" Margin="250,0,250,0">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Top">
                <StackPanel Margin="60,0,0,0">
                    <StackPanel.Style>
                        <Style TargetType="StackPanel">
                            <Setter Property="Visibility" Value="Visible"/>
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding IsProvisional}" Value="true">
                                    <Setter Property="Visibility" Value="Visible"/>
                                </DataTrigger>
                                <DataTrigger Binding="{Binding IsProvisional}" Value="false">
                                    <Setter Property="Visibility" Value="Collapsed"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </StackPanel.Style>
                    <Label Content="{Binding PrecinctHeader}" Style="{StaticResource Body1SemiBoldFont}" Margin="0,28,0,10" />
                    <ui:PollPlaceSearchBox x:Name="searchBox" Height="106" Width="900"  Text="{Binding SearchTerm}" ClearCommand="{Binding ClearCommand}" NoResults="{Binding NoResults}">
                    </ui:PollPlaceSearchBox>
                </StackPanel>
            </StackPanel>

            <Canvas Panel.ZIndex="1">
                <Border Width="900" Canvas.Left="270" Canvas.Top="0">
                    <Border.Style>
                        <Style TargetType="Border">
                            <Setter Property="Visibility" Value="Collapsed"/>
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding DisplayResults}" Value="True">
                                    <Setter Property="Visibility" Value="Visible"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </Border.Style>

                    <ListBox ItemsSource="{Binding Results}" 
                             SelectedItem="{Binding SelectedPrecinctSplitItem, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" 
                             MaxHeight="330" x:Name="listbox" Uid="txtPrecinctSplitList" 
                             AutomationProperties.AutomationId="AAtxtPrecinctSplitList" AutomationProperties.Name="AAtxtPrecinctSplitList"
                             Style="{StaticResource ListBoxResultDropDown}">

                        <ListBox.ItemContainerStyle>
                            <Style TargetType="ListBoxItem" BasedOn="{StaticResource ListBoxContainer}"/>
                        </ListBox.ItemContainerStyle>

                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <ContentControl>
                                    <ContentControl.Content>
                                        <MultiBinding Converter="{StaticResource StringToXamlConverter}">
                                            <Binding Path="PrecinctSplitName" />
                                            <Binding Path="DataContext.SearchTerm" RelativeSource="{RelativeSource Mode=FindAncestor, AncestorType=UserControl}" />
                                        </MultiBinding>
                                    </ContentControl.Content>
                                </ContentControl>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                    </ListBox>
                </Border>
            </Canvas>

            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Top">
                <StackPanel Margin="60,0,0,0" 
                            Visibility ="{Binding IsPartyListVisible, Converter={StaticResource OnVisibilityConverter}}">
                    <Label Content="{Binding PartyHeader}"  Margin="0,28,0,10" Style="{StaticResource Body1SemiBoldFont}"/>
                    <uiCore:WatermarkComboBoxExtended x:Name="PartiesComboBox2" MinWidth="900" MaxWidth="1400" Height="106"
                                                      Style="{StaticResource {x:Type ComboBox}}"
                                                      ItemsSource="{Binding Path=PartyNames, Mode=TwoWay}"
                                                      DisplayMemberPath="PartyDisplayName"
                                                      SelectedValuePath="PartyId"
                                                      SelectedItem="{Binding Path=SelectedPartyName}"
                                                      WatermarkText="{Binding PartyWaterMark}"
                                                      MaxDropDownHeight="440"
                                                      IsEnabled="{Binding IsPartyEnabled}">
                    </uiCore:WatermarkComboBoxExtended>
                </StackPanel>
            </StackPanel>

            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Top">
                <StackPanel Margin="60,0,0,0" 
                            Visibility ="{Binding IsBallotStyleTypeVisible, Converter={StaticResource OnVisibilityConverter}}">
                    <Label Content="{Binding BallotStyleTypeHeader}"  Margin="0,28,0,10" Style="{StaticResource Body1SemiBoldFont}"/>
                    <uiCore:WatermarkComboBoxExtended x:Name="BallotStyleTypeNameComboBox" Width="900" Height="106"
                                                      Style="{StaticResource {x:Type ComboBox}}"
                                                      ItemsSource="{Binding Path=BallotStyleTypeNames, Mode=TwoWay}"
                                                      DisplayMemberPath="BallotStyleTypeName"
                                                      SelectedValuePath="BallotStyleTypeId"
                                                      SelectedItem="{Binding Path=SelectedBallotStyleTypeName}"
                                                      WatermarkText="{Binding BallotStyleWaterMark}"
                                                      MaxDropDownHeight="440"
                                                      IsEnabled="{Binding IsBallotStyleTypeEnabled}">
                    </uiCore:WatermarkComboBoxExtended>
                </StackPanel>
            </StackPanel>
        </StackPanel>

        <!-- Buttons -->
        <Grid Grid.Row="3" Background="White">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="auto" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="auto" />
            </Grid.ColumnDefinitions>

            <Button Grid.Column="0"
                    Content="{Binding BackLabel}"
                    Margin="40"
                    usercontrols:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding BackCommand}">
                <Button.Style>
                    <Style TargetType="Button" BasedOn="{StaticResource SecondaryLargeButton}" />
                </Button.Style>
            </Button>

            <Button Margin="0,40,70,0"
                    Grid.Column="1"
                    usercontrols:ButtonHelper.DisableMultipleClicks="True"
                    Click="Flip_Click"
                    Width="140"
                    Height="150"
                    BorderBrush="Transparent"
                    Background="Transparent"
                    Style="{StaticResource RotateButton}"
                    Visibility="{Binding ShowFlipScreen, Converter={StaticResource OnVisibilityConverter}}">
                <StackPanel Orientation="Vertical" HorizontalAlignment="Center" VerticalAlignment="Center" Width="175">
                    <Viewbox Stretch="Uniform" VerticalAlignment="Center" HorizontalAlignment="Center" Width="100" Margin="0">
                        <Path>
                            <Path.Style>
                                <Style TargetType="{x:Type Path}" BasedOn="{StaticResource FlipScreenIcon}" />
                            </Path.Style>
                        </Path>
                    </Viewbox>
                    <Label Content="Flip Screen" HorizontalAlignment="Center" Margin="10" Style="{StaticResource FlipScreenFont}" />
                </StackPanel>
            </Button>

            <Button Grid.Column="2"
                    Content="{Binding NextButtonText}"
                    HorizontalAlignment="Right"
                    Margin="40"
                    usercontrols:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding NextCommandAsync}"
                    IsEnabled="{Binding Path=NextEnabled}">
                <Button.Style>
                    <Style TargetType="Button" BasedOn="{StaticResource PrimaryLargeButton}" />
                </Button.Style>
            </Button>
        </Grid>
    </Grid>
</UserControl>
