<Page x:Class="Pollbook.Pages.MonitorPollView"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:local="clr-namespace:Pollbook.UICore"
      xmlns:userControls="clr-namespace:Pollbook.UserControls"
      mc:Ignorable="d" 
      Loaded="Page_Loaded"
      Unloaded="Page_Unloaded"
      DataContext="{Binding MonitorPoll, Source={StaticResource Locator}}"
      Background="{StaticResource BackgroundBrush}"
      d:DesignHeight="1200" d:DesignWidth="1920">

    <Page.Resources>
        <local:BooleanToVisibilityConverter x:Key="OnVisibilityConverter" True="Visible" False="Collapsed" />
        <local:BooleanToVisibilityConverter x:Key="OnVisibilityHiddenConverter" True="Visible" False="Hidden" />
        <local:BooleanToVisibilityConverter x:Key="InvertedBooleanToVisibilityConverter" True="Collapsed" False="Visible" />
        <Style x:Key="ReportsTextBlock" TargetType="TextBlock">
            <Setter Property="Width" Value="200"/>
            <Setter Property="Margin" Value="20 0 20 48"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="TextAlignment" Value="Center"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="30"/>
        </Style>

        <Style x:Key="LargeSemiBoldLabel" TargetType="Label">
            <Setter Property="TextBlock.Foreground" Value="{StaticResource Gray1Brush}" />
            <Setter Property="TextBlock.FontFamily" Value="Noto Sans Display"/>
            <Setter Property="TextBlock.FontWeight" Value="SemiBold" />
            <Setter Property="TextBlock.FontSize" Value="36" />
        </Style>
    </Page.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="auto" />
        </Grid.RowDefinitions>

        <!--Header-->
        <Grid Grid.Row="0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2.5*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            <StackPanel Grid.Column="0" Margin="120,40,54,0">
                <StackPanel Orientation="Horizontal">
                    <Label Content="{Binding ElectionName}" Style="{StaticResource LargeTitle}" FontSize="48"/>
                </StackPanel>
                <!--Polling Place-->
                <Label Content="{Binding PollLocationName}" Style="{StaticResource StandardLightLabel}" Foreground="{StaticResource Gray2Brush}" />
                <Label Content="{Binding PollLocationAddress}" Style="{StaticResource StandardLightLabel}"/>
                <!--Device Name-->
                <StackPanel Orientation="Horizontal">
                    <Label Content="Device Name: " Style="{StaticResource StandardLightLabel}"/>
                    <Label Content="{Binding SystemId}" Style="{StaticResource StandardLabel}"/>
                </StackPanel>
            </StackPanel>
            <Button Grid.Column="1"
                    userControls:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding ShowManageDeviceCommand}"
                    HorizontalAlignment="Right"
                    VerticalAlignment="Bottom"
                    Margin="0,0,120,30">
                <Button.Template>
                    <ControlTemplate TargetType="Button">
                        <ContentPresenter />
                    </ControlTemplate>
                </Button.Template>
                <Grid>
                    <StackPanel Orientation="Horizontal" Margin="92,0,0,0">
                        <TextBlock Text="{Binding ManageDeviceLabel}"  Margin="0,0,5,48" Foreground="{StaticResource BluetifulBrush}">
                            <TextBlock.Style>
                                <Style TargetType="{x:Type TextBlock}" BasedOn="{StaticResource Display4BoldTextBlock}"/>
                            </TextBlock.Style>
                        </TextBlock>
                        <StackPanel Margin="0 5 0 0">
                            <Viewbox Stretch="Uniform" VerticalAlignment="Top" HorizontalAlignment="Center" Width="16" Margin="0,0,50,120">
                                <Path>
                                    <Path.Style >
                                        <Style TargetType="{x:Type Path}" BasedOn="{StaticResource LinkArrowIcon}"/>
                                    </Path.Style>
                                </Path>
                            </Viewbox>
                        </StackPanel>
                    </StackPanel>
                </Grid>
                <Button.Style>
                    <Style TargetType="Button" BasedOn="{StaticResource PrimaryLargeButton}"/>
                </Button.Style>
            </Button>
            <StackPanel x:Name="LastSyncDate" Grid.Column="1" Orientation="Horizontal" Margin="0 185 0 0">
                <Rectangle Height="50" Width="4" Fill="{StaticResource SkyBlueBrush}" Margin="-305 0 0 0"/>
                <Border Background="{StaticResource AliceBlueBrush}" Height="50" Margin="-145 0 0 0" VerticalAlignment="Center">
                    <StackPanel Orientation="Horizontal" Margin="0 5 0 0">
                        <TextBlock Text="Last Sync Date: "  Style="{StaticResource StandardTextBlock}" Margin="10 0 0 0"/>
                        <TextBlock Text="{Binding LastSyncTimeLabel}"  Style="{StaticResource StandardTextBlock}" Margin="0 0 60 0"/>
                    </StackPanel>
                </Border>
            </StackPanel>
        </Grid>
        <!--Tabs-->
        <TabControl Grid.Row="1" 
                BorderThickness="0 2 0 0" 
                    Margin="120 0 120 0"
                SelectedIndex="{Binding SelectedTab, Mode=TwoWay}"
                SelectionChanged="TabControl_SelectionChanged">
            <TabControl.Resources>
                <Style TargetType="TabItem">
                    <Setter Property="Template">
                        <Setter.Value>
                            <ControlTemplate TargetType="TabItem">
                                <Border Name="Panel" CornerRadius="3, 3, 0, 0" Width="285" Height="100" Margin="-2,0,10,0" Background="White">
                                    <StackPanel Orientation="Vertical" VerticalAlignment="Bottom">
                                        <ContentPresenter x:Name="ContentSite" VerticalAlignment="Center" HorizontalAlignment="Left" Margin="0 0 0 30">
                                            <ContentPresenter.Content>
                                                <ContentControl x:Name="ContentCtl" Content="{TemplateBinding Header}" Style="{StaticResource Body1BoldFont}"/>
                                            </ContentPresenter.Content>
                                        </ContentPresenter>
                                        <Rectangle Fill="{StaticResource SecondaryDarkBlueBrush}" Height="6" Width="{Binding ActualWidth, ElementName=ContentCtl}" VerticalAlignment="Bottom" HorizontalAlignment="Left">
                                            <Rectangle.Style>
                                                <Style TargetType="Rectangle">
                                                    <Setter Property="Visibility" Value="Hidden"/>
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type TabItem}}, Path=IsSelected}" Value="True">
                                                            <Setter Property="Visibility" Value="Visible"/>
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </Rectangle.Style>
                                        </Rectangle>
                                    </StackPanel>
                                </Border>
                                <ControlTemplate.Triggers>
                                    <Trigger Property="IsSelected" Value="True">
                                        <Setter TargetName="Panel" Property="Background" Value="White" />
                                        <Setter TargetName="ContentCtl" Property="Foreground" Value="{StaticResource SecondaryDarkBlueBrush}"/>
                                    </Trigger>
                                    <Trigger Property="IsSelected" Value="False">
                                        <Setter TargetName="Panel" Property="Background" Value="White" />
                                        <Setter TargetName="ContentCtl" Property="Foreground" Value="{StaticResource Gray4Brush}"/>
                                    </Trigger>
                                    <Trigger Property="TabIndex" Value="0">
                                        <Setter TargetName="Panel" Property="Margin" Value="0,0,10,0" />
                                    </Trigger>
                                </ControlTemplate.Triggers>
                            </ControlTemplate>
                        </Setter.Value>
                    </Setter>
                </Style>
            </TabControl.Resources>
            <!--OVERVIEW TAB-->
            <TabItem Header="Overview">
                <Grid Margin="0 20 0 55">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="2*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <Border Grid.Column="0" Margin="0 0 20 0" BorderThickness="2" BorderBrush="{StaticResource Gray5Brush}" CornerRadius="3">
                        <Grid Margin="30">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="1.1*"/>
                                <RowDefinition Height="2*"/>
                            </Grid.RowDefinitions>

                            <StackPanel Grid.Row="0" Grid.ColumnSpan="3" Orientation="Vertical" >
                                <Label Content= "{Binding RegisteredVotersLabel}" Style="{StaticResource LargeBlueLabel}" Margin="0 0 20 0"/>
                                <TextBlock Text="{Binding RegisteredVoters, StringFormat='{}{0:#,0}'}" Style="{StaticResource Display3BoldTextBlock}" Margin="0 10 0 0"/>
                            </StackPanel>
                            <!--Voters at this Poll Place-->
                            <StackPanel Grid.Row="1" Grid.Column="0" >
                                <Label Content="{Binding VotersAtThisLabel}" Style="{StaticResource LargeBlueLabel}"/>
                                <Grid Margin="0 10 0 0">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width=".7*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="*"/>
                                        <RowDefinition Height="*"/>
                                    </Grid.RowDefinitions>
                                    <StackPanel Grid.Column="0" Grid.Row="0" Grid.RowSpan="2" Margin="0 0 0 10">
                                        <!--Checked In-->
                                        <StackPanel Margin="0 0 0 10">
                                            <TextBlock Text="{Binding VotersCheckedInCount, StringFormat='{}{0:#,0}'}" Style="{StaticResource Display3BoldTextBlock}" />
                                            <Label Content="Checked In" Style="{StaticResource StandardLightLabel}" Margin="0 0 0 0" Padding="0"/>
                                        </StackPanel>
                                        <!--Early Voting-->
                                        <StackPanel Visibility="{Binding ShowEarlyVotingCounts, Converter={StaticResource OnVisibilityConverter}}">
                                            <TextBlock Text="{Binding EarlyVoterCount, StringFormat='{}{0:#,0}'}" Style="{StaticResource Display3BoldTextBlock}" />
                                            <Label Content="Early Voting" Style="{StaticResource StandardLightLabel}" Margin="0 0 0 0" Padding="0"/>
                                        </StackPanel>
                                    </StackPanel>
                                    <StackPanel Grid.Column="1" Grid.Row="0" Grid.RowSpan="2" Margin="0 0 0 10">
                                        <!--Not in Roster-->
                                        <StackPanel Margin="0 0 0 10"
                                            Visibility="{Binding IsNewVotersCountVisible, Converter={StaticResource OnVisibilityConverter}}">
                                            <TextBlock Text="{Binding NewVotersCount, StringFormat='{}{0:#,0}'}" Style="{StaticResource Display3BoldTextBlock}"/>
                                            <Label Content="Not in Roster" Style="{StaticResource StandardLightLabel}" Margin="0 0 0 0" Padding="0"/>
                                        </StackPanel>
                                        <!--Absentee-->
                                        <StackPanel>
                                            <TextBlock Text="{Binding AbsenteeVoterCount, StringFormat='{}{0:#,0}'}" Style="{StaticResource Display3BoldTextBlock}"/>
                                            <Label Content="Absentee" Style="{StaticResource StandardLightLabel}" Margin="0 0 0 0" Padding="0"/>
                                        </StackPanel>
                                    </StackPanel>
                                </Grid>
                            </StackPanel>
                            <!--Voters on this Device-->
                            <StackPanel Grid.Column="1" Grid.Row="1">
                                <Label Content="Voters on this Device" Style="{StaticResource LargeBlueLabel}"/>
                                <Grid Margin="0 10 0 0">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width=".7*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <!--Checked In (On device)-->
                                    <StackPanel Grid.Column="0">
                                        <TextBlock Text="{Binding LocalVotersCheckedInCount, StringFormat='{}{0:#,0}'}" Style="{StaticResource Display3BoldTextBlock}" />
                                        <Label Content="Checked In" Style="{StaticResource StandardLightLabel}" Margin="0 0 0 0" Padding="0"/>
                                    </StackPanel>
                                    <!--Not in Roster-->
                                    <StackPanel Grid.Column="1" Visibility="{Binding IsNewVotersCountVisible, Converter={StaticResource OnVisibilityConverter}}">
                                        <TextBlock Text="{Binding LocalAddedVotersCount, StringFormat='{}{0:#,0}'}" Style="{StaticResource Display3BoldTextBlock}" />
                                        <Label Content="Not in Roster" Style="{StaticResource StandardLightLabel}" Margin="0 0 0 0" Padding="0"/>
                                    </StackPanel>
                                </Grid>
                            </StackPanel>
                        </Grid>
                    </Border>
                    <StackPanel Grid.Column="1" Grid.Row="0" HorizontalAlignment="Left">
                        <!--Poll Time-->
                        <Border CornerRadius="3"  Margin="0 0 0 20" BorderThickness="0" Width="550">
                            <Border.Style>
                                <Style TargetType="Border">
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding IsPollOpen}" Value="True">
                                            <Setter Property="Background" Value="{StaticResource SecondaryLightGreenBrush}"/>
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding IsPollOpen}" Value="False">
                                            <Setter Property="Background" Value="{StaticResource SecondaryLightRedBrush2}"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </Border.Style>
                            <Grid Margin="30 15 30 15">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="*"/>
                                    <RowDefinition Height="*"/>
                                </Grid.RowDefinitions>
                                <Label Grid.Column="0" Grid.ColumnSpan="2" Content="Poll Place" Style="{StaticResource LargeSemiBoldLabel}" Padding="0"/>
                                <StackPanel Grid.Column="0" Grid.Row="1" Orientation="Horizontal" Margin="0 10 0 0">
                                    <!--Open Time-->
                                    <StackPanel Margin="0 0 60 0">
                                        <TextBlock Text="{Binding  OpenTime, StringFormat='{}h:mm tt'}" Style="{StaticResource Display3BoldTextBlock}" />
                                        <Label Content="Open Time" Style="{StaticResource StandardLightLabel}" Margin="0 0 0 0" Padding="0"/>
                                    </StackPanel>
                                    <!--Close Time-->
                                    <StackPanel>
                                        <TextBlock Text="{Binding  CloseTime, StringFormat='{}h:mm tt'}" Style="{StaticResource Display3BoldTextBlock}" />
                                        <Label Content="Close Time" Style="{StaticResource StandardLightLabel}" Margin="0 0 0 0" Padding="0"/>
                                    </StackPanel>
                                </StackPanel>
                            </Grid>
                        </Border>
                        <!--Peripheral Status-->
                        <Border CornerRadius="3" Visibility="{Binding IsPrintingEnabled, Converter={StaticResource OnVisibilityConverter}}">
                            <Border.Resources>
                                <Path x:Key="IssueStandardRes">
                                    <Path.Style>
                                        <Style TargetType="Path" BasedOn="{StaticResource BallotStandardIcon}" />
                                    </Path.Style>
                                </Path>
                                <Path x:Key="IssueProvisionalRes">
                                    <Path.Style>
                                        <Style TargetType="Path" BasedOn="{StaticResource BallotProvisionalIcon}" />
                                    </Path.Style>
                                </Path>
                                <Path x:Key="BallotIssuedRes">
                                    <Path.Style>
                                        <Style TargetType="Path" BasedOn="{StaticResource BallotIssuedIcon}" />
                                    </Path.Style>
                                </Path>
                            </Border.Resources>
                            <Border.Style>
                                <Style TargetType="Border">
                                    <Setter Property="Background" Value="{StaticResource SecondaryLightGreenBrush}"/>
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding PrinterStatusColor}" Value="Green">
                                            <Setter Property="Background" Value="{StaticResource SecondaryLightGreenBrush}" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding PrinterStatusColor}" Value="Yellow">
                                            <Setter Property="Background" Value="{StaticResource SecondaryLightYellowBrush}" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding PrinterStatusColor}" Value="Red">
                                            <Setter Property="Background" Value="{StaticResource SecondaryLightRedBrush2}" />
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </Border.Style>
                            <Grid Margin="0,0,0,20">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="425*"/>
                                    <ColumnDefinition Width="94*"/>
                                </Grid.ColumnDefinitions>
                                <Viewbox Width="27" Height="27" HorizontalAlignment="Left" VerticalAlignment="Top" Margin="27,51,0,0">
                                    <ContentControl>
                                        <ContentControl.Style>
                                            <Style TargetType="{x:Type ContentControl}">
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding PrinterStatusColor}" Value="Green">
                                                        <Setter Property="Content" Value="{StaticResource IssueStandardRes}"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding PrinterStatusColor}" Value="Yellow">
                                                        <Setter Property="Content" Value="{StaticResource IssueProvisionalRes}"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding PrinterStatusColor}" Value="Red">
                                                        <Setter Property="Content" Value="{StaticResource BallotIssuedRes}"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </ContentControl.Style>
                                    </ContentControl>
                                </Viewbox>
                                <Label Margin="72,40,0,0" Grid.ColumnSpan="2" Grid.Column="0">
                                    <Label.Style>
                                        <Style TargetType="Label" BasedOn="{StaticResource LargeBoldLabel}">
                                            <Setter Property="Content" Value="{Binding PeripheralStatusLabel}"/>
                                        </Style>
                                    </Label.Style>
                                </Label>
                                <TextBlock Text="{Binding PeripheralStatusText}" TextWrapping="Wrap" Margin="78,90,0,10" Grid.ColumnSpan="2" Grid.Column="0">
                                    <TextBlock.Style>
                                        <Style TargetType="TextBlock" BasedOn="{StaticResource StandardTextBlock}"/>
                                    </TextBlock.Style>
                                </TextBlock>
                            </Grid>
                        </Border>
                    </StackPanel>
                </Grid>

            </TabItem>
            <!--BALLOTS TAB-->
            <TabItem Header="Ballots">
                <Grid Margin="0 20 0 55">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <!--Ballots by Poll Place-->
                    <Border BorderThickness="2" BorderBrush="{StaticResource Gray5Brush}" CornerRadius="3" Margin="0 0 30 0">
                        <Grid Margin="30 25 30 25">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="2*"/>
                                <ColumnDefinition Width="2*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>
                            <Label Content="Ballots by Poll Place" Style="{StaticResource LargeBlueLabel}" Grid.Column="0" Grid.ColumnSpan="2" Margin="0 0 0 20" Padding="0"/>
                            <StackPanel Orientation="Vertical" Grid.Column="0"  Grid.Row="1">
                                <StackPanel Margin="0 0 0 0">
                                    <!--Paper-->
                                    <StackPanel Margin="0 0 0 40" Visibility="{Binding IsPaperCountVisible, Converter={StaticResource OnVisibilityHiddenConverter}}">
                                        <TextBlock Text="{Binding StandardPaperCount, StringFormat='{}{0:#,0}'}" Style="{StaticResource Display3BoldTextBlock}" />
                                        <TextBlock Text="{Binding PaperLabel}" Style="{StaticResource TextBlockLightLabel}"/>
                                    </StackPanel>
                                    <!--ExpressVote-->
                                    <StackPanel Margin="0 0 0 40" Visibility="{Binding IsExpressVoteCountVisible, Converter={StaticResource OnVisibilityHiddenConverter}}">
                                        <TextBlock Text="{Binding StandardExpressCardCount, StringFormat='{}{0:#,0}'}" Style="{StaticResource Display3BoldTextBlock}" />
                                        <TextBlock Text="{Binding ExpressVoteLabel}" Style="{StaticResource TextBlockLightLabel}"/>
                                    </StackPanel>
                                    <!--Reissued-->
                                    <StackPanel Orientation="Vertical">
                                        <TextBlock Text="{Binding BallotsReissuedCount, StringFormat='{}{0:#,0}'}" Style="{StaticResource Display3BoldTextBlock}" Margin="0 -6 0 0"/>
                                        <TextBlock Text="Reissued" Style="{StaticResource TextBlockLightLabel}" Margin="0 0 5 0"/>
                                    </StackPanel>
                                </StackPanel>
                            </StackPanel>
                            <StackPanel Grid.Column="1" Grid.Row="1" Grid.ColumnSpan="2">
                                <!--Provisional Paper-->
                                <StackPanel Margin="0 0 0 40" Visibility="{Binding IsPaperProvisionalCountVisible, Converter={StaticResource OnVisibilityHiddenConverter}}">
                                    <TextBlock Text="{Binding ProvisionalPaperCount, StringFormat='{}{0:#,0}'}" Style="{StaticResource Display3BoldTextBlock}" />
                                    <TextBlock Text="{Binding ProvisionalPaperLabel}" Style="{StaticResource TextBlockLightLabel}" TextWrapping="Wrap" HorizontalAlignment="Left"/>
                                </StackPanel>
                                <!--Provisional ExpressVote-->
                                <StackPanel Margin="0 0 0 40" Visibility="{Binding IsExpressVoteProvisionalCountVisible, Converter={StaticResource OnVisibilityHiddenConverter}}">
                                    <TextBlock Text="{Binding ProvisionalExpressCardCount, StringFormat='{}{0:#,0}'}" Style="{StaticResource Display3BoldTextBlock}" />
                                    <TextBlock Text="{Binding ExpressVoteBallotLabel}" Style="{StaticResource TextBlockLightLabel}" TextWrapping="Wrap" HorizontalAlignment="Left"/>
                                </StackPanel>
                                <!--Canceled-->
                                <StackPanel Orientation="Vertical">
                                    <TextBlock Text="{Binding BallotsCanceledCount, StringFormat='{}{0:#,0}'}" Style="{StaticResource Display3BoldTextBlock}" Margin="0 -6 0 0"/>
                                    <TextBlock Text="Canceled" Style="{StaticResource TextBlockLightLabel}" Margin="0 0 5 0"/>
                                </StackPanel>
                            </StackPanel>
                        </Grid>
                    </Border>
                    <!--Ballots by Device-->
                    <Border Grid.Column="1" BorderThickness="2" BorderBrush="{StaticResource Gray5Brush}" CornerRadius="3" Margin="0 0 30 0">
                        <Grid Margin="30 25 30 25">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="2*"/>
                                <ColumnDefinition Width="2*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>
                            <Label Content="Ballots by Device" Style="{StaticResource LargeBlueLabel}" Grid.Column="0" Grid.ColumnSpan="2" Margin="0 0 0 20" Padding="0"/>
                            <StackPanel Orientation="Vertical" Grid.Column="0"  Grid.Row="1">
                                <StackPanel Margin="0 0 0 0">
                                    <!--Paper-->
                                    <StackPanel Margin="0 0 0 40" Visibility="{Binding IsPaperCountVisible, Converter={StaticResource OnVisibilityHiddenConverter}}">
                                        <TextBlock Text="{Binding LocalStandardPaperCount, StringFormat='{}{0:#,0}'}" Style="{StaticResource Display3BoldTextBlock}" />
                                        <TextBlock Text="{Binding PaperLabel}" Style="{StaticResource TextBlockLightLabel}"/>
                                    </StackPanel>
                                    <!--ExpressVote-->
                                    <StackPanel Margin="0 0 0 40" Visibility="{Binding IsExpressVoteCountVisible, Converter={StaticResource OnVisibilityHiddenConverter}}">
                                        <TextBlock Text="{Binding LocalStandardExpressCardCount, StringFormat='{}{0:#,0}'}" Style="{StaticResource Display3BoldTextBlock}" />
                                        <TextBlock Text="{Binding ExpressVoteLabel}" Style="{StaticResource TextBlockLightLabel}"/>
                                    </StackPanel>
                                    <!--Reissued-->
                                    <StackPanel Orientation="Vertical">
                                        <TextBlock Text="{Binding LocalBallotsReissuedCount, StringFormat='{}{0:#,0}'}" Style="{StaticResource Display3BoldTextBlock}" Margin="0 -6 0 0"/>
                                        <TextBlock Text="Reissued" Style="{StaticResource TextBlockLightLabel}" Margin="0 0 5 0"/>
                                    </StackPanel>
                                </StackPanel>
                            </StackPanel>
                            <StackPanel Grid.Column="1" Grid.Row="1" Grid.ColumnSpan="2">
                                <!--Provisional Paper-->
                                <StackPanel Margin="0 0 0 40" Visibility="{Binding IsPaperProvisionalCountVisible, Converter={StaticResource OnVisibilityHiddenConverter}}">
                                    <TextBlock Text="{Binding LocalProvisionalPaperCount, StringFormat='{}{0:#,0}'}" Style="{StaticResource Display3BoldTextBlock}" />
                                    <TextBlock Text="{Binding ProvisionalPaperLabel}" Style="{StaticResource TextBlockLightLabel}" TextWrapping="Wrap" HorizontalAlignment="Left"/>
                                </StackPanel>
                                <!--Provisional ExpressVote-->
                                <StackPanel Margin="0 0 0 40" Visibility="{Binding IsExpressVoteProvisionalCountVisible, Converter={StaticResource OnVisibilityHiddenConverter}}">
                                    <TextBlock Text="{Binding LocalProvisionalExpressCardCount, StringFormat='{}{0:#,0}'}" Style="{StaticResource Display3BoldTextBlock}" />
                                    <TextBlock Text="{Binding ExpressVoteBallotLabel}" Style="{StaticResource TextBlockLightLabel}" TextWrapping="Wrap" HorizontalAlignment="Left"/>
                                </StackPanel>
                                <!--Canceled-->
                                <StackPanel Orientation="Vertical">
                                    <TextBlock Text="{Binding LocalBallotsCanceledCount, StringFormat='{}{0:#,0}'}" Style="{StaticResource Display3BoldTextBlock}" Margin="0 -6 0 0"/>
                                    <TextBlock Text="Canceled" Style="{StaticResource TextBlockLightLabel}" Margin="0 0 5 0"/>
                                </StackPanel>
                            </StackPanel>
                        </Grid>
                    </Border>
                </Grid>
            </TabItem>
            <!--REPORTS TAB-->
            <TabItem Header="Reports">
                <StackPanel Orientation="Horizontal" VerticalAlignment="Top" HorizontalAlignment="Center" Margin="0 33 0 0">
                    <!--Ballot Totals Report-->
                    <Button VerticalContentAlignment="Bottom"
                            Style="{StaticResource PrimaryLargeButton}"
                            Margin="0 0 20 0"
                            Height="270"
                            Width="285"
                            userControls:ButtonHelper.DisableMultipleClicks="True"
                            Command="{Binding PrintBallotTotalsCommand}">
                        <StackPanel>
                            <Viewbox Height="60" Margin="0 0 0 30">
                                <Path>
                                    <Path.Style>
                                        <Style TargetType="Path" BasedOn="{StaticResource BallotListIcon}" />
                                    </Path.Style>
                                </Path>
                            </Viewbox>
                            <TextBlock Text="Ballot Totals Report" Style="{StaticResource ReportsTextBlock}" />
                        </StackPanel>
                    </Button>
                    <!--Voted List Report-->
                    
                    <Button VerticalContentAlignment="Bottom" 
                            Style="{StaticResource PrimaryLargeButton}"
                            Margin="0 0 20 0" Height="270" Width="285"
                            userControls:ButtonHelper.DisableMultipleClicks="True"
                            Command="{Binding ShowFullVotedListCommand}">
                        <StackPanel>
                            <Viewbox Height="60" Margin="0 0 0 30">
                                <Path>
                                    <Path.Style>
                                        <Style TargetType="Path" BasedOn="{StaticResource VotedListIcon}" />
                                    </Path.Style>
                                </Path>
                            </Viewbox>
                            <TextBlock Text="Voted List Report" Style="{StaticResource ReportsTextBlock}" />
                        </StackPanel>
                    </Button>
                    <!--Reissued Report-->
                    <Button VerticalContentAlignment="Bottom"
                            Style="{StaticResource PrimaryLargeButton}"
                            Margin="0 0 20 0"
                            Height="270"
                            Width="285"
                            userControls:ButtonHelper.DisableMultipleClicks="True"
                            Command="{Binding ViewBallotReissueCommand}">
                        <StackPanel>
                            <ContentControl Template="{StaticResource ReissuedReportIcon}"/>
                            <TextBlock Text="Reissued Report" Style="{StaticResource ReportsTextBlock}" />
                        </StackPanel>
                    </Button>
                    <!--Spoiled Ballot Report-->
                    <Button VerticalContentAlignment="Bottom"
                            Style="{StaticResource PrimaryLargeButton}"
                            Margin="0 0 20 0" Height="270"
                            Width="285"
                            userControls:ButtonHelper.DisableMultipleClicks="True"
                            Command="{Binding ShowSpoiledBallotCommand}">
                        <StackPanel>
                            <ContentControl Template="{StaticResource SpoiledBallotReportIcon}"/>
                            <TextBlock Text="Spoiled Ballot Report" Style="{StaticResource ReportsTextBlock}" />
                        </StackPanel>
                    </Button>

                    <!--Voter List Report-->
                    <Button VerticalContentAlignment="Bottom"
                            Visibility="{Binding IsVoteCenter, Converter={StaticResource InvertedBooleanToVisibilityConverter}}"
                            Style="{StaticResource PrimaryLargeButton}"
                            Margin="0 0 20 0"
                            Height="270"
                            Width="285"
                            userControls:ButtonHelper.DisableMultipleClicks="True"
                            Command="{Binding ShowPollingPlaceVoterListCommand}">
                        <StackPanel>
                            <Viewbox Height="54" Margin="0 0 0 30">
                                <Path>
                                    <Path.Style>
                                        <Style TargetType="Path" BasedOn="{StaticResource VoterListIcon}" />
                                    </Path.Style>
                                </Path>
                            </Viewbox>
                            <TextBlock Text="Voter List" Style="{StaticResource ReportsTextBlock}" Width="100" />
                        </StackPanel>
                    </Button>
                </StackPanel>
            </TabItem>
            <!--DEVICE TRANSACTIONS TAB-->
            <TabItem Header="Device Transactions" Visibility="{Binding IsHostEnabled, Converter={StaticResource OnVisibilityConverter}}">
                <Grid>
                    <Grid Margin="0 20 0 0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <!--Spacer-->
                            <ColumnDefinition Width="30"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <!--Download Transactions Status from Server-->
                        <Border Grid.Column="0" BorderThickness="2" BorderBrush="{StaticResource Gray5Brush}">
                            <Grid Margin="30 25 30 25">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="2*"/>
                                    <ColumnDefinition Width="3*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <Label Grid.Row="0" Grid.ColumnSpan="2" Content= "Download Transactions Status from Server" Style="{StaticResource LargeBlueLabel}" Margin="0 0 0 30"/>
                                <!--Download Sync Status-->
                                <Border Grid.Column="0" Grid.Row="1" Grid.ColumnSpan="2" CornerRadius="3" Width="467" Margin="0 0 0 40">
                                    <Border.Resources>
                                        <Path x:Key="IssueStandardRes">
                                            <Path.Style>
                                                <Style TargetType="Path" BasedOn="{StaticResource BallotStandardIcon}" />
                                            </Path.Style>
                                        </Path>
                                        <Path x:Key="IssueProvisionalRes">
                                            <Path.Style>
                                                <Style TargetType="Path" BasedOn="{StaticResource BallotProvisionalIcon}" />
                                            </Path.Style>
                                        </Path>
                                        <Path x:Key="BallotIssuedRes">
                                            <Path.Style>
                                                <Style TargetType="Path" BasedOn="{StaticResource BallotIssuedIcon}" />
                                            </Path.Style>
                                        </Path>
                                    </Border.Resources>
                                    <Border.Style>
                                        <Style TargetType="Border">
                                            <Setter Property="Background" Value="{StaticResource SecondaryLightGreenBrush}"/>
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding DownloadStatusColor}" Value="Green">
                                                    <Setter Property="Background" Value="{StaticResource SecondaryLightGreenBrush}" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding DownloadStatusColor}" Value="Yellow">
                                                    <Setter Property="Background" Value="{StaticResource SecondaryLightYellowBrush}" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding DownloadStatusColor}" Value="Red">
                                                    <Setter Property="Background" Value="{StaticResource SecondaryLightRedBrush2}" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Border.Style>
                                    <Grid Margin="0,0,0,20">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="425*"/>
                                            <ColumnDefinition Width="94*"/>
                                        </Grid.ColumnDefinitions>
                                        <Viewbox Width="27" Height="27" HorizontalAlignment="Left" VerticalAlignment="Top" Margin="27,51,0,0">
                                            <ContentControl>
                                                <ContentControl.Style>
                                                    <Style TargetType="{x:Type ContentControl}">
                                                        <Style.Triggers>
                                                            <DataTrigger Binding="{Binding DownloadStatusColor}" Value="Green">
                                                                <Setter Property="Content" Value="{StaticResource IssueStandardRes}"/>
                                                            </DataTrigger>
                                                            <DataTrigger Binding="{Binding DownloadStatusColor}" Value="Yellow">
                                                                <Setter Property="Content" Value="{StaticResource IssueProvisionalRes}"/>
                                                            </DataTrigger>
                                                            <DataTrigger Binding="{Binding DownloadStatusColor}" Value="Red">
                                                                <Setter Property="Content" Value="{StaticResource BallotIssuedRes}"/>
                                                            </DataTrigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </ContentControl.Style>
                                            </ContentControl>
                                        </Viewbox>
                                        <Label Margin="72,33,0,0" Grid.ColumnSpan="2" Grid.Column="0">
                                            <Label.Style>
                                                <Style TargetType="Label" BasedOn="{StaticResource LargeBoldLabel}">
                                                    <Setter Property="Content" Value="{Binding DownloadStatusMessageHeader}"/>
                                                </Style>
                                            </Label.Style>
                                        </Label>
                                        <TextBlock Text="{Binding DownloadStatusMessage}" TextWrapping="Wrap" Margin="78,80,0,0" Grid.ColumnSpan="2" Grid.Column="0">
                                            <TextBlock.Style>
                                                <Style TargetType="TextBlock" BasedOn="{StaticResource StandardTextBlock}"/>
                                            </TextBlock.Style>
                                        </TextBlock>
                                    </Grid>
                                </Border>
                                <!--Download Progress Bars-->
                                <StackPanel Grid.Column="0" Grid.Row="2" Grid.ColumnSpan="2">
                                    <StackPanel Margin="0 0 0 20">
                                        <TextBlock Text="Download from Server" Margin="0 0 0 10" Style="{StaticResource StandardTextBlock}"/>
                                        <ProgressBar x:Name="DownloadProgressBar" ValueChanged="DownloadProgressBar_OnValueChanged" Style="{StaticResource TransactionsProgressBar}" Value="{Binding DownloadSyncProgress}"/>
                                    </StackPanel>
                                    <StackPanel>
                                        <TextBlock Text="Applied from Download" Margin="0 0 0 10" Style="{StaticResource StandardTextBlock}"/>
                                        <ProgressBar x:Name="AppliedProgressBar" ValueChanged="AppliedProgressBar_OnValueChanged" Style="{StaticResource TransactionsProgressBar}" Value="{Binding AppliedSyncProgress}"/>
                                    </StackPanel>
                                </StackPanel>
                            </Grid>
                        </Border>
                        <Border Grid.Column="2" BorderThickness="2" BorderBrush="{StaticResource Gray5Brush}">
                            <Grid Margin="30 25 30 25">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="2*"/>
                                    <ColumnDefinition Width="3*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <Label Grid.Row="0" Grid.ColumnSpan="2" Content= "Upload Transactions Status to Server" Style="{StaticResource LargeBlueLabel}" Margin="0 0 0 30"/>

                                <!--Upload Sync Status-->
                                <Border Grid.Column="0" Grid.Row="1" Grid.ColumnSpan="2" CornerRadius="3" Width="467" Margin="0 0 0 40">
                                    <Border.Resources>
                                        <Path x:Key="IssueStandardRes">
                                            <Path.Style>
                                                <Style TargetType="Path" BasedOn="{StaticResource BallotStandardIcon}" />
                                            </Path.Style>
                                        </Path>
                                        <Path x:Key="IssueProvisionalRes">
                                            <Path.Style>
                                                <Style TargetType="Path" BasedOn="{StaticResource BallotProvisionalIcon}" />
                                            </Path.Style>
                                        </Path>
                                        <Path x:Key="BallotIssuedRes">
                                            <Path.Style>
                                                <Style TargetType="Path" BasedOn="{StaticResource BallotIssuedIcon}" />
                                            </Path.Style>
                                        </Path>
                                    </Border.Resources>
                                    <Border.Style>
                                        <Style TargetType="Border">
                                            <Setter Property="Background" Value="{StaticResource SecondaryLightGreenBrush}"/>
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding UploadStatusColor}" Value="Green">
                                                    <Setter Property="Background" Value="{StaticResource SecondaryLightGreenBrush}" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding UploadStatusColor}" Value="Yellow">
                                                    <Setter Property="Background" Value="{StaticResource SecondaryLightYellowBrush}" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding UploadStatusColor}" Value="Red">
                                                    <Setter Property="Background" Value="{StaticResource SecondaryLightRedBrush2}" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Border.Style>
                                    <Grid Margin="0,0,0,20">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="425*"/>
                                            <ColumnDefinition Width="94*"/>
                                        </Grid.ColumnDefinitions>
                                        <Viewbox Width="27" Height="27" HorizontalAlignment="Left" VerticalAlignment="Top" Margin="27,51,0,0">
                                            <ContentControl>
                                                <ContentControl.Style>
                                                    <Style TargetType="{x:Type ContentControl}">
                                                        <Style.Triggers>
                                                            <DataTrigger Binding="{Binding UploadStatusColor}" Value="Green">
                                                                <Setter Property="Content" Value="{StaticResource IssueStandardRes}"/>
                                                            </DataTrigger>
                                                            <DataTrigger Binding="{Binding UploadStatusColor}" Value="Yellow">
                                                                <Setter Property="Content" Value="{StaticResource IssueProvisionalRes}"/>
                                                            </DataTrigger>
                                                            <DataTrigger Binding="{Binding UploadStatusColor}" Value="Red">
                                                                <Setter Property="Content" Value="{StaticResource BallotIssuedRes}"/>
                                                            </DataTrigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </ContentControl.Style>
                                            </ContentControl>
                                        </Viewbox>
                                        <Label Margin="72,33,0,0" Grid.ColumnSpan="2" Grid.Column="0">
                                            <Label.Style>
                                                <Style TargetType="Label" BasedOn="{StaticResource LargeBoldLabel}">
                                                    <Setter Property="Content" Value="{Binding UploadStatusMessageHeader}"/>
                                                </Style>
                                            </Label.Style>
                                        </Label>
                                        <TextBlock Text="{Binding UploadStatusMessage}" TextWrapping="Wrap" Margin="78,80,0,10" Grid.ColumnSpan="2" Grid.Column="0">
                                            <TextBlock.Style>
                                                <Style TargetType="TextBlock" BasedOn="{StaticResource StandardTextBlock}"/>
                                            </TextBlock.Style>
                                        </TextBlock>
                                    </Grid>
                                </Border>
                                <!--Upload Progress Bars-->
                                <StackPanel Grid.Column="0" Grid.Row="2" Grid.ColumnSpan="2">
                                    <StackPanel Margin="0 0 0 20">
                                        <TextBlock Text="Sent to Server" Margin="0 0 0 0" Style="{StaticResource StandardTextBlock}"/>
                                        <ProgressBar x:Name="UploadProgressBar" ValueChanged="UploadProgressBar_OnValueChanged" Style="{StaticResource TransactionsProgressBar}" Value="{Binding UploadSyncProgress}" />
                                    </StackPanel>
                                </StackPanel>
                            </Grid>
                        </Border>
                    </Grid>
                </Grid>
            </TabItem>
        </TabControl>

        <Grid Grid.Row="2" Background="White">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="auto" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <Button Grid.Column="1"
                    Margin="40"
                    HorizontalAlignment="Right"
                    userControls:ButtonHelper.DisableMultipleClicks="True"
                    IsEnabled="{Binding OpenClosePollButtonEnabled}">
                <Button.Style>
                    <Style TargetType="Button" BasedOn="{StaticResource PrimaryLargeControl}">
                        <Setter Property="Foreground" Value="White" />
                        <Setter Property="Height" Value="152" />
                        <Setter Property="Width" Value="420" />
                        <Setter Property="Content" Value="Open Polls" />
                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="Button">
                                    <Grid x:Name="Grid">
                                        <Border x:Name="ButtonBorder" CornerRadius="3" Background="{TemplateBinding Background}">
                                        </Border>
                                        <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" Margin="{TemplateBinding Padding}" VerticalAlignment="{TemplateBinding VerticalContentAlignment}" RecognizesAccessKey="True"/>
                                    </Grid>
                                    <ControlTemplate.Triggers>
                                        <Trigger Property="IsEnabled" Value="False">
                                            <Setter TargetName="ButtonBorder" Property="Effect" Value="{x:Null}" />
                                            <Setter TargetName="ButtonBorder" Property="Background" Value="{StaticResource Gray6Brush}" />
                                            <Setter Property="Foreground" Value="{StaticResource Gray4Brush}" />
                                        </Trigger>
                                        <DataTrigger Binding="{Binding IsPollOpen}" Value="True">
                                            <Setter Property="Content" Value="Close Polls" />
                                            <Setter TargetName="ButtonBorder" Property="Effect" Value="{x:Null}" />
                                            <Setter Property="Command" Value="{Binding ClosePollsCommand}"/>
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding IsPollOpen}" Value="False">
                                            <Setter Property="Command" Value="{Binding OpenPollsCommand}"/>
                                        </DataTrigger>
                                    </ControlTemplate.Triggers>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                    </Style>
                </Button.Style>
            </Button>
        </Grid>
    </Grid>
</Page>
