<UserControl x:Class="Pollbook.Pages.PollPlaceSearch.ChangePollPlaceSearchView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:ui="clr-namespace:Pollbook.UserControls" 
             xmlns:uiCore="clr-namespace:Pollbook.UICore"
             DataContext="{Binding ChangePollPlaceSearch, Source={StaticResource Locator}}"
             mc:Ignorable="d" Focusable="True"
             Loaded="Page_Loaded">
    <UserControl.Resources>
        <LinearGradientBrush x:Key="BackgroundBrush" StartPoint="0,0" EndPoint="0,1">
            <GradientStop Color="{StaticResource SecondaryNavyGradientStartColor}" Offset="0" />
            <GradientStop Color="{StaticResource SecondaryNavyGradientEndColor}" Offset=".667"/>
        </LinearGradientBrush>

        <Style x:Key="highlight" TargetType="Run">
            <Setter Property="Foreground" Value="{StaticResource Gray1Brush}" />
            <Setter Property="FontFamily" Value="Noto Sans Display" />
            <Setter Property="FontSize" Value="30" />
        </Style>

        <Style x:Key="normal" TargetType="Run">
            <Setter Property="Foreground" Value="{StaticResource Gray2Brush}" />
            <Setter Property="FontFamily" Value="Noto Sans Display" />
            <Setter Property="FontSize" Value="30" />
        </Style>

        <uiCore:BoolInverterConverter x:Key="BoolInverterConverter" />
        <BooleanToVisibilityConverter x:Key="BooleanToVisibility" />
        <uiCore:StringToXamlConverter x:Key="StringToXamlConverter" />
    </UserControl.Resources>

    <Grid Background="{StaticResource BackgroundBrush}">
        <Grid x:Name="CancelSave" Margin="0,110,0,0" Background="White">

            <!-- Buttons -->
            <Grid Margin="40">
                <Grid.RowDefinitions>
                    <RowDefinition Height="201*"/>
                    <RowDefinition Height="326*"/>
                </Grid.RowDefinitions>
                <Label Grid.Row="0" Content="Select Poll Place" Style="{StaticResource Body1BoldFont}" Grid.RowSpan="2" />
                <StackPanel Grid.Row="0" Orientation="Horizontal" HorizontalAlignment="Right" VerticalAlignment="Top">
                    <Button Content="{Binding CancelLabel}"
                            Style="{StaticResource SecondarySmallButton}"
                            Width="180"
                            ui:ButtonHelper.DisableMultipleClicks="True"
                            Command="{Binding CancelCommand}"
                            Margin="0,0,20,0" />
                    <Button Content="{Binding SaveLabel}"
                            Style="{StaticResource PrimarySmallButton}"
                            Width="180"
                            ui:ButtonHelper.DisableMultipleClicks="True"
                            Command="{Binding SaveCommand}"
                            IsEnabled="{Binding SaveIsEnabled}"/>
                </StackPanel>
            </Grid>
        </Grid>

        <Grid Width="1662" Margin="0,60,0,0">
            <!-- 175 -->
            <Grid.RowDefinitions>
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>

            <Grid Margin="0,145,0,0" Visibility="{Binding IsBothEarlyVoteAndElectionDay, Converter={StaticResource BooleanToVisibility}}">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="233*"/>
                    <ColumnDefinition Width="1253*"/>
                    <ColumnDefinition Width="13*"/>
                    <ColumnDefinition Width="163*"/>
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="auto" />
                    <RowDefinition Height="auto" />
                </Grid.RowDefinitions>

                <StackPanel Grid.Row="0" Orientation="Horizontal" VerticalAlignment="Top" Margin="0,20,0,0" Grid.ColumnSpan="4">
                    <Label  Content="Display By" Style="{StaticResource Body1SemiBoldFont}" Margin="0,40,20,0" HorizontalAlignment="Stretch"/>
                    <ToggleButton Style="{StaticResource PrimaryMediumToggleButton}" Focusable="false" IsChecked="{Binding ShowEarlyVotePollPlacesToggle, Mode=TwoWay}" Margin="0,10,20,0" >
                        <ToggleButton.Content>
                            <TextBlock HorizontalAlignment="Center" Text="Early Vote" />
                        </ToggleButton.Content>
                    </ToggleButton>
                    <ToggleButton Style="{StaticResource PrimaryMediumToggleButton}" Focusable="false" IsChecked="{Binding ShowElectionDayPollPlacesToggle, Mode=TwoWay}" Margin="0,10,20,0">
                        <ToggleButton.Content>
                            <TextBlock HorizontalAlignment="Center" Text="Election Day" />
                        </ToggleButton.Content>
                    </ToggleButton>
                </StackPanel>
            </Grid>

            <!-- Header -->
            <Grid Margin="0,0,0,0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="305" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>

                <StackPanel Grid.Row="1">
                    <Label Content="{Binding SearchPollbookByLabel, TargetNullValue='No value', FallbackValue='Placeholder'}" d:Content="Selection Placeholder"  Margin="0,0,0,10"  
                               Style="{StaticResource Body1SemiBoldFont}"/>
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions >
                            <ColumnDefinition  Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <ui:PollPlaceSearchBox Grid.Column="0"
                                               Grid.Row="0"
                                               x:Name="searchBox" 
                                               Height="106" 
                                               Text="{Binding SearchBoxText}" 
                                               ClearCommand="{Binding ClearCommand}" 
                                               NoResults="{Binding NoResults}" 
                                               IsEnabled="{Binding SearchBoxEnabled}"
                                               Margin="0,0,15,0"/>

                        <StackPanel Grid.Column="1" Grid.Row="0" Orientation="Vertical" Margin="0,-8,0,0" Visibility="{Binding IsPollPlacePrecinctRadioButtonsVisible}" >
                            <RadioButton Content="{Binding PollingPlaceLabel}" 
                                         IsChecked="{Binding IsSearchPollPlaceByPrecinct, Mode=TwoWay, Converter={StaticResource BoolInverterConverter}}"  
                                         Style="{StaticResource StandardRadioButton}"
                                         Margin="0,0,0,2"/>
                            <RadioButton Content="{Binding PrecinctLabel}" 
                                         IsChecked="{Binding IsSearchByPrecinct, Mode=TwoWay,  Converter={StaticResource BoolInverterConverter}}"
                                         Style="{StaticResource StandardRadioButton}"/>
                        </StackPanel>
                        
                        <Label Grid.Column="0" Grid.Row="1">
                            <TextBlock TextTrimming="CharacterEllipsis" 
                                       Text="{Binding SearchBoxFooterInfo, TargetNullValue='', FallbackValue='Placeholder'}" 
                                       TextWrapping="Wrap"   />
                            <Label.Style>
                                <Style TargetType="Label" BasedOn="{StaticResource Body1SemiBoldFont}">
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding NoResults}" Value="True">
                                            <Setter Property="Foreground" Value="{StaticResource SecondaryDarkRedBrush}"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </Label.Style>

                        </Label>

                        <!-- Results -->
                        <Border Grid.Row="1" Grid.Column="0" Margin="0,0,15,0">
                            <Border.Style>
                                <Style TargetType="Border">
                                    <Setter Property="Visibility" Value="Hidden"/>
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding DisplayResults}" Value="True">
                                            <Setter Property="Visibility" Value="Visible"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </Border.Style>

                            <ListBox ItemsSource="{Binding SearchResults}" 
                                     SelectedItem="{Binding SelectedPollingPrecinct, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" 
                                     MaxHeight="330" x:Name="listbox" 
                                     Uid="txtPollPlaces" 
                                     AutomationProperties.AutomationId="txtPollPlaces" AutomationProperties.Name="txtPollPlacesDropDown"
                                     Style="{StaticResource ListBoxResultDropDown}">

                                <ListBox.ItemContainerStyle>
                                    <Style TargetType="ListBoxItem" BasedOn="{StaticResource ListBoxContainer}"/>
                                </ListBox.ItemContainerStyle>

                                <ListBox.ItemTemplate>
                                    <DataTemplate>
                                        <ContentControl>
                                            <ContentControl.Content>
                                                <MultiBinding Converter="{StaticResource StringToXamlConverter}">
                                                    <Binding Path="PollingPlaceDisplayName" />
                                                    <Binding Path="DataContext.SearchBoxText" RelativeSource="{RelativeSource Mode=FindAncestor, AncestorType=UserControl}" />
                                                </MultiBinding>
                                            </ContentControl.Content>
                                        </ContentControl>
                                    </DataTemplate>
                                </ListBox.ItemTemplate>

                            </ListBox>
                        </Border>

                        <StackPanel Grid.Row="1" Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                            <Viewbox Stretch="Uniform" 
                                         VerticalAlignment="Center" 
                                         HorizontalAlignment="Center" 
                                         Width="30" 
                                         Margin="0,02,12,0"
                                         Visibility="{Binding NoResults, Converter={StaticResource BooleanToVisibility}}">
                                <Path Style="{StaticResource WarningIcon}" Stretch="Fill"/>
                            </Viewbox>

                            <Label Content="Error:" >
                                <Label.Style>
                                    <Style TargetType="Label" BasedOn="{StaticResource Body1BoldFont}">
                                        <Setter Property="Visibility" Value="Collapsed"/>
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding NoResults}" Value="True">
                                                <Setter Property="Visibility" Value="Visible"/>
                                                <Setter Property="Foreground" Value="{StaticResource SecondaryDarkRedBrush}"/>
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </Label.Style>
                            </Label>
                            
                            <Label Content="No results found" Margin="0">
                                <Label.Style>
                                    <Style TargetType="Label" BasedOn="{StaticResource StandardLightLabel}">
                                        <Setter Property="Visibility" Value="Hidden"/>
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding NoResults}" Value="True">
                                                <Setter Property="Visibility" Value="Visible"/>
                                                <Setter Property="Foreground" Value="{StaticResource SecondaryDarkRedBrush}"/>
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </Label.Style>
                            </Label>

                        </StackPanel>
                    </Grid>
                </StackPanel>
            </Grid>
        </Grid>
    </Grid>
</UserControl>
