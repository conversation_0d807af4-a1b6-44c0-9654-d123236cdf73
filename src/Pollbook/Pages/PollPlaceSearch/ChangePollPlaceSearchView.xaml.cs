using CommonServiceLocator;
using ESS.Pollbook.ViewModel.Infrastructure;
using ESS.Pollbook.ViewModel.PollPlaceSearch;
using Pollbook.UserControls;
using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;

namespace Pollbook.Pages.PollPlaceSearch
{
	public partial class ChangePollPlaceSearchView : UserControl
	{
        private readonly IKeyboardService _keyboardService;

        public ChangePollPlaceSearchView()
        {
            InitializeComponent();

            _keyboardService = ServiceLocator.Current.GetInstance<IKeyboardService>();
            FocusManager.AddGotFocusHandler(this, UserControl_GotFocus);
        }

        private async void Page_Loaded(object sender, RoutedEventArgs e)
        {
            FocusHelper.Focus(searchBox);
            await ((ChangePollPlaceSearchViewModel)DataContext).PageLoadedAsync().ConfigureAwait(false);
        }

        private void UserControl_GotFocus(object sender, EventArgs e)
        {
	        if (Keyboard.FocusedElement is TextBox focusedElement)
		        _keyboardService.TextActivated(focusedElement, allowPageToMoveUp: false);
	        else
		        _keyboardService.TextDeactivated();
        }
	}
}
