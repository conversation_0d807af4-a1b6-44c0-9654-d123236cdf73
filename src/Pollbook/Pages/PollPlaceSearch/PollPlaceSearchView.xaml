<Page x:Class="Pollbook.Pages.PollPlaceSearch.PollPlaceSearchView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:uiCore="clr-namespace:Pollbook.UICore"
             xmlns:userControls="clr-namespace:Pollbook.UserControls"
             Loaded="Page_Loaded"
             DataContext="{Binding PollPlaceSearch, Source={StaticResource Locator}}"
             Background="{StaticResource BackgroundBrush}">
    <Page.Resources>
        <uiCore:HighlightedTextConverter x:Key="HighlightWord" />
        <uiCore:AddressTextConverter x:Key="AddressWord" />
        <BooleanToVisibilityConverter x:Key="BooleanToVisibility" />
    </Page.Resources>
    <Grid>
        <Grid Margin="200,0,200,0">
            <Grid.RowDefinitions>
                <RowDefinition Height="auto" />
                <RowDefinition Height="auto" />
                <RowDefinition Height="auto" />
            </Grid.RowDefinitions>
            <!-- Header -->
            <Grid Grid.Row="0" Margin="0,60,0,0">
                <StackPanel Grid.Column="0">
                    <Label Content="{Binding VoterAddressLabel}" Margin="0,0,0,10" Style="{StaticResource Body1SemiBoldFont}"/>
                    <userControls:PollPlaceSearchBox x:Name="searchBox" 
                                                     Height="106" 
                                                     Text="{Binding SearchTerm}"
                                                     ClearCommand="{Binding ClearCommand}"
                                                     NoResults="{Binding NoResults}" />
                </StackPanel>
            </Grid>
            <Grid Grid.Row="1">

                <TextBlock HorizontalAlignment="Center" Text="Start by entering house number and street name."
                           Margin="0,65,0,0">
                    <TextBlock.Style>
                        <Style TargetType="TextBlock" BasedOn="{StaticResource InstructionalLargeTextBlock}">
                            <Setter Property="Visibility" Value="Collapsed" />
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding DisplayVoterAddressInstructions}" Value="True">
                                    <Setter Property="Visibility" Value="Visible" />
                                </DataTrigger>
                                <DataTrigger Binding="{Binding NoResults}" Value="True">
                                    <Setter Property="Visibility" Value="Collapsed" />
                                </DataTrigger>
                                <DataTrigger Binding="{Binding TooManyResults}" Value="True">
                                    <Setter Property="Visibility" Value="Collapsed" />
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </TextBlock.Style>
                </TextBlock>
            </Grid>

            <!-- Results -->
            <Border Grid.Row="1">
                <Border.Style>
                    <Style TargetType="Border">
                        <Setter Property="Visibility" Value="Visible" />
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding Results.Count}" Value="0">
                                <Setter Property="Visibility" Value="Hidden" />
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </Border.Style>

                <ListBox x:Name="lbTodoList" ItemsSource="{Binding Results}"
                         SelectedItem="{Binding SelectedPollingItem, Mode=TwoWay}"
                         MaxHeight="436"
                         Style="{StaticResource ListBoxResultDropDown}">

                    <ListBox.ItemContainerStyle>
                        <Style TargetType="ListBoxItem" BasedOn="{StaticResource ListBoxContainer}" />
                    </ListBox.ItemContainerStyle>

                    <ListBox.ItemTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <TextBlock>
                                    <TextBlock.Style>
                                        <Style TargetType="TextBlock" BasedOn="{StaticResource BoldTextBlock}" />
                                    </TextBlock.Style>
                                    <TextBlock.Text>
                                        <MultiBinding Converter="{StaticResource HighlightWord}">
                                            <Binding
                                                RelativeSource="{RelativeSource FindAncestor, AncestorType={x:Type UserControl}}"
                                                Path="DataContext.SearchTerm" />
                                            <Binding Path="FullStreetAddress" />
                                        </MultiBinding>
                                    </TextBlock.Text>
                                </TextBlock>
                                <TextBlock>
                                    <TextBlock.Style>
                                        <Style TargetType="TextBlock" BasedOn="{StaticResource DataGridSmallTextBlock}" />
                                    </TextBlock.Style>
                                    <TextBlock.Text>
                                        <MultiBinding Converter="{StaticResource AddressWord}">
                                            <Binding
                                                RelativeSource="{RelativeSource FindAncestor, AncestorType={x:Type UserControl}}"
                                                Path="DataContext.SearchTerm" />
                                            <Binding Path="FullStreetAddress" />
                                        </MultiBinding>
                                    </TextBlock.Text>
                                </TextBlock>
                            </StackPanel>
                        </DataTemplate>
                    </ListBox.ItemTemplate>
                </ListBox>
            </Border>

            <Grid Grid.Row="1" VerticalAlignment="Top">
                <Label Content="Too many results. Keep typing." Margin="0,10,0,0">
                    <Label.Style>
                        <Style TargetType="Label" BasedOn="{StaticResource Body2BoldLightFont}">
                            <Setter Property="Visibility" Value="Collapsed" />
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding TooManyResults}" Value="True">
                                    <Setter Property="Visibility" Value="Visible" />
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </Label.Style>
                </Label>
                <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                    <Viewbox Stretch="Uniform" 
                             VerticalAlignment="Center" 
                             HorizontalAlignment="Center" 
                             Width="30" 
                             Margin="0,02,12,0"
                             Visibility="{Binding NoResults, Converter={StaticResource BooleanToVisibility}}">
                        <Path Style="{StaticResource WarningIcon}" Stretch="Fill"/>
                    </Viewbox>
                    <Label Content="Error: ">
                        <Label.Style>
                            <Style TargetType="Label" BasedOn="{StaticResource Body1BoldFont}">
                                <Setter Property="Visibility" Value="Collapsed" />
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding NoResults}" Value="True">
                                        <Setter Property="Visibility" Value="Visible" />
                                        <Setter Property="Foreground" Value="{StaticResource SecondaryDarkRedBrush}" />
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </Label.Style>
                    </Label>
                    <Label Content="Address not found">
                        <Label.Style>
                            <Style TargetType="Label" BasedOn="{StaticResource StandardLightLabel}">
                                <Setter Property="Visibility" Value="Hidden" />
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding NoResults}" Value="True">
                                        <Setter Property="Visibility" Value="Visible" />
                                        <Setter Property="Foreground" Value="{StaticResource SecondaryDarkRedBrush}" />
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </Label.Style>
                    </Label>
                </StackPanel>
            </Grid>

            <!-- Help -->
            <Grid Grid.Row="2" VerticalAlignment="Top" Margin="0,120,0,0">
                <userControls:PollPlaceSearchHelp 
                    Height="200" 
                    Visibility="{Binding NoResults, Converter={StaticResource BooleanToVisibility}}" 
                    />
            </Grid>
        </Grid>
    </Grid>
</Page>