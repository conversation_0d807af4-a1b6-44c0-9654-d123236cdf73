<Page x:Class="Pollbook.Pages.Reports.BallotReIssuedReport"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:uiCore="clr-namespace:Pollbook.UICore"
      mc:Ignorable="d" 
      xmlns:userControls="clr-namespace:Pollbook.UserControls"
      DataContext="{Binding BallotReIssuedReport, Source={StaticResource Locator}}"
      Loaded="BallotReIssuedReport_Loaded"
      Unloaded="BallotReIssuedReport_Unloaded"
      Background="White"
      d:DesignHeight="1200" d:DesignWidth="1920"
      Title="BallotReIssuedReport">
    <Page.Resources>
        <uiCore:NameTextConverter x:Key="NameTextConverter" />
        <uiCore:PartyTextConverter x:Key="PartyDisplayConverter" />
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
    </Page.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="auto" />
            <RowDefinition Height="auto" />
        </Grid.RowDefinitions>

        <!-- Title -->
        <StackPanel Grid.Row="0" Margin="120,42,80,0">
            <Label Content="Reissued Report" Style="{StaticResource Display2SemiBoldFont}" />
            <Label Content="{Binding ReportDate}" Style="{StaticResource Body2BoldLightFont}" />
        </StackPanel>

        <!-- Report Body -->
        <Grid Grid.Row="1" Margin="120,15,117,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="620" />
            </Grid.ColumnDefinitions>
            <!-- Report Data -->
            <Border Grid.Column="0" 
                    CornerRadius="0,0,10,10" 
                    Background="White" 
                    Margin="0,10,30,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="auto" />
                        <RowDefinition Height="*" />
                        <RowDefinition Height="85" />
                    </Grid.RowDefinitions>

                    <Grid Grid.Row="1">
                        <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                            <StackPanel.Style>
                                <Style TargetType="StackPanel">
                                    <Setter Property="Visibility" Value="Collapsed" />
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding LoadingResults}" Value="True">
                                            <Setter Property="Visibility" Value="Visible" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding LoadingResults}" Value="False">
                                            <Setter Property="Visibility" Value="Collapsed" />
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </StackPanel.Style>
                            <userControls:Spinner/>
                        </StackPanel>

                        <Grid Name="ResultsGrid" Margin="-30,10,0,0">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*" />
                            </Grid.RowDefinitions>
                            <Grid.Style>
                                <Style TargetType="Grid">
                                    <Setter Property="Visibility" Value="Collapsed" />
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding LoadingResults}" Value="True">
                                            <Setter Property="Visibility" Value="Collapsed" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding LoadingResults}" Value="False">
                                            <Setter Property="Visibility" Value="Visible" />
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </Grid.Style>
                            <!-- Rows -->
                            <ListView x:Name="LvwVoters" 
                                      ItemsSource="{Binding VirtualBallotReissuedList,IsAsync=True}" 
                                      BorderThickness="0" 
                                      ScrollViewer.HorizontalScrollBarVisibility="Disabled">
                                <ListView.Resources>
                                    <Style TargetType="ScrollBar" BasedOn="{StaticResource CustomVerticalScrollBarStyle}"/>
                                </ListView.Resources>
                                <ListView.View>
                                    <GridView ScrollViewer.HorizontalScrollBarVisibility="Disabled">
                                        <GridViewColumn Header="#" DisplayMemberBinding="{Binding LineNumber}" Width="100" />
                                        <GridViewColumn Header="Voter" DisplayMemberBinding="{Binding FullName, Converter= {StaticResource NameTextConverter}}" Width="500"/>
                                        <GridViewColumn Header="{Binding PartyLabel}" DisplayMemberBinding="{Binding PartyDisplayName,Converter={StaticResource PartyDisplayConverter}}" Width="125"/>
                                        <GridViewColumn Header="{Binding PrecinctLabel}" DisplayMemberBinding="{Binding PrecinctSplitName}" Width="840"/>   
                                        
                                        <GridView.ColumnHeaderContainerStyle>
                                            <Style TargetType="GridViewColumnHeader">
                                                <Setter Property="Foreground" Value="{StaticResource Gray3Brush}"></Setter>
                                                <Setter Property="FontFamily" Value="Noto Sans Display"></Setter>
                                                <Setter Property="FontSize" Value="30"/>
                                                <Setter Property="Height" Value="60"/>
                                                <Setter Property="Margin" Value="30,0,0,0"/>
                                                <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
                                                <Setter Property="VerticalContentAlignment" Value="Stretch" />
                                                <Setter Property="TextBlock.TextTrimming" Value="CharacterEllipsis"></Setter>
                                                <Setter Property="TextBlock.TextWrapping" Value="NoWrap"></Setter>
                                                <Setter Property="Template">
                                                    <Setter.Value>
                                                        <ControlTemplate TargetType="{x:Type GridViewColumnHeader}">
                                                            <TextBlock Text="{TemplateBinding Content}" Padding="5" Width="{TemplateBinding Width}" TextAlignment="Left"/>
                                                        </ControlTemplate>
                                                    </Setter.Value>
                                                </Setter>
                                            </Style>
                                        </GridView.ColumnHeaderContainerStyle>
                                    </GridView>
                                </ListView.View>

                                <ListView.ItemContainerStyle>
                                    <Style TargetType="ListViewItem" BasedOn="{StaticResource ReportListViewItemStyle}">
                                        <Setter Property="BorderBrush" Value="Transparent" />
                                        <Setter Property="BorderThickness" Value="0" />
                                        <Setter Property="Background" Value="White"></Setter>
                                        <Setter Property="Focusable" Value="false"/>
                                        <Setter Property="Foreground" Value="#152025"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="FontFamily" Value="Noto Sans Display"/>
                                        <Setter Property="FontSize" Value="35"/>
                                        <Setter Property="Height" Value ="85"/>
                                        <Setter Property="Margin" Value ="0,10,0,0"/>
                                    </Style>
                                </ListView.ItemContainerStyle>
                            </ListView>
                        </Grid>
                    </Grid>

                    <Border Grid.Row="2" CornerRadius="0,0,10,10" Background="{StaticResource Gray7Brush}">
                        <StackPanel Orientation="Horizontal">
                            <Label Margin="50,15,0,0" Content="{Binding VoterCount}" Style="{StaticResource Body1SemiBoldLight2Font}"/>
                            <Label Margin="50,15,29,0" Content="{Binding ReissuedBallotCount}" Style="{StaticResource Body1SemiBoldLight2Font}"/>
                        </StackPanel>
                    </Border>

                </Grid>
            </Border>

            <!-- Report Filters -->
            <StackPanel Grid.Column="1"  Margin="0,32,0,77" 
                        Height="700" 
                        HorizontalAlignment="Left">
                <!-- Group by Voter -->
                <CheckBox Style="{StaticResource StandardCheckbox}" Margin="0,0,0,30" Content="Group by Voter" 
                          IsChecked="{Binding Path=IsGroupByVoter, Mode=TwoWay}"/>

                <!-- Filter by Device  -->
                <Label Content="Filter by Device" Style="{StaticResource Body1SemiBoldFont}" Margin="0,10,0,0" Padding="0"/>
                <ComboBox x:Name="DeviceComboBox" Margin="0,10,0,30" HorizontalAlignment="Left"
                          ItemsSource="{Binding Devices, Mode=TwoWay}"
                          SelectedItem="{Binding Path=SelectedDevice, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                          Width="620" />

                <!-- Filter by date/time -->
                <Grid HorizontalAlignment="Stretch" VerticalAlignment="Center" Margin="0,0,0,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="auto"/>
                    </Grid.ColumnDefinitions>
                    <Label Grid.Column="0" Content="Filter by Date and Time" Style="{StaticResource Body1SemiBoldFont}" Padding="0"/>
                    <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Right">
                        <Button VerticalAlignment="Center"
                                userControls:ButtonHelper.DisableMultipleClicks="True"
                                Command="{Binding FilterByDateCommand}">
                            <Button.Template>
                                <ControlTemplate TargetType="Button">
                                    <ContentPresenter />
                                </ControlTemplate>
                            </Button.Template>
                            <Grid>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="{Binding SetLabel}" Style="{StaticResource BoldTextBlock}" Foreground="{StaticResource BluetifulBrush}" Margin="30,0,0,0" />
                                </StackPanel>
                            </Grid>
                        </Button>
                        <Button VerticalAlignment="Center"
                                userControls:ButtonHelper.DisableMultipleClicks="True"
                                Command="{Binding ClearAllCommand}">
                            <Button.Template>
                                <ControlTemplate TargetType="Button">
                                    <ContentPresenter />
                                </ControlTemplate>
                            </Button.Template>
                            <Grid>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="{Binding ClearLabel}" 
                                               Style="{StaticResource BoldTextBlock}" 
                                               Foreground="{StaticResource BluetifulBrush}" 
                                               Margin="20,0,0,0" />
                                </StackPanel>
                            </Grid>
                        </Button>
                    </StackPanel>
                </Grid>
                <StackPanel Orientation="Horizontal" Margin="0,0,0,30">
                    <ComboBox x:Name="MonthComboBox" Margin="0,10,10,0" Width="200"
                                ItemsSource="{Binding Months}"
                                              SelectedValue="UnitNumber"                                          
                                              DisplayMemberPath="UnitDescription"                                          
                                              SelectedItem="{Binding SelectedMonth}" />
                    <ComboBox x:Name="DayComboBox" Margin="0,10,10,0"  Width="200"
                             ItemsSource="{Binding Days}"
                                              SelectedValue="UnitNumber"   
                                              DisplayMemberPath="UnitDescription"  
                                              SelectedItem="{Binding SelectedDay}" />
                    <ComboBox x:Name="YearComboBox" Margin="0,10,0,0"  Width="200"
                             ItemsSource="{Binding Years}"
                                              SelectedValue="UnitNumber" 
                                              DisplayMemberPath="UnitDescription"                                            
                                              SelectedItem="{Binding SelectedYear}" />
                </StackPanel>
                <StackPanel Orientation="Horizontal" Margin="0,0,0,0">
                    <Label Content="Start:" Style="{StaticResource Body1SemiBoldFont}" Margin="0,20,2,20" Padding="0" />
                    <ComboBox x:Name="StartTimeComboBox" Margin="0,0,5,0"  Width="239"
                             ItemsSource="{Binding TimeStart}"
                                              SelectedValue="UnitNumber"                                          
                                              DisplayMemberPath="UnitDescription"                                          
                                              SelectedItem="{Binding SelectedTimeStart}" />
                    <Label Content="End:" Style="{StaticResource Body1SemiBoldFont}" Margin="-2,20,2,20" Padding="0"/>
                    <ComboBox x:Name="EndTimeComboBox" Margin="0,0,0,0"  Width="239"
                              ItemsSource="{Binding TimeEnd}"
                                              SelectedValue="UnitNumber"   
                                              DisplayMemberPath="UnitDescription"  
                                              SelectedItem="{Binding SelectedTimeEnd}" />
                </StackPanel>
                
            </StackPanel>
        </Grid>

        <!-- Buttons -->
        <Grid Grid.Row="2" Background="White">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="auto" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <Button Grid.Column="0"
                    Content="{Binding BackLabel}"
                    Style="{StaticResource SecondaryLargeButton}"
                    Margin="40"
                    userControls:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding BackCommand}"/>

            <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Right">
                <Button Content="{Binding ExportLabel}"
                        Style="{StaticResource PrimaryLargeButton}"
                        Margin="0,40,30,40"
                        userControls:ButtonHelper.DisableMultipleClicks="True"
                        Command="{Binding ExportCommand}"
                        IsEnabled="{Binding ExportEnabled}"/>
                <Button Content="{Binding PrintLabel}"
                        Style="{StaticResource PrimaryLargeButton}"
                        Margin="0,40,40,40"
                        userControls:ButtonHelper.DisableMultipleClicks="True"
                        Command="{Binding PrintCommand}"
                        Visibility="{Binding PrintVisible, Converter={StaticResource BooleanToVisibilityConverter}}"
                        IsEnabled="{Binding PrintEnabled}"/>
            </StackPanel>
        </Grid>

    </Grid>
</Page>
