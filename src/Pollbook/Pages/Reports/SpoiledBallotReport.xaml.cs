using ESS.Pollbook.ViewModel.Reports;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;


namespace Pollbook.Pages.Reports
{
    /// <summary>
    /// Interaction logic for SpoiledBallotReport.xaml
    /// </summary>
    public partial class SpoiledBallotReport : Page, IContextView
    {
        public bool IsModal => false;
        public SolidColorBrush PrimaryBackgroundBrush => (SolidColorBrush)Application.Current.FindResource("WhiteBrush");
        public SpoiledBallotReport()
        {
            InitializeComponent();
        }

        private async void SpoiledBallotReport_OnLoaded(object sender, RoutedEventArgs e)
        {
            await ((SpoiledBallotReportViewModel)DataContext).PageIsLoaded();
        }
    }
}
