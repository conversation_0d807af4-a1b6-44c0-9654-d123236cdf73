using ESS.Pollbook.ViewModel.Reports;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace Pollbook.Pages.Reports
{
    /// <summary>
    /// Interaction logic for PollingPlaceVoterList.xaml
    /// </summary>
    public partial class PollingPlaceVoterListReport : Page, IContextView
    {
        public bool IsModal => false;
        public SolidColorBrush PrimaryBackgroundBrush => (SolidColorBrush)Application.Current.FindResource("WhiteBrush");

        public PollingPlaceVoterListReport()
        {
            InitializeComponent();
        }

        void PollingPlaceVoterListReport_Loaded(object sender, RoutedEventArgs e)
        {
            ((PollingPlaceVoterListReportViewModel)DataContext).PageIsLoaded();
        }

        void PollingPlaceVoterListReport_Unloaded(object sender, RoutedEventArgs e)
        {
            ((PollingPlaceVoterListReportViewModel)DataContext).PageUnloaded();
        }
    }
}
