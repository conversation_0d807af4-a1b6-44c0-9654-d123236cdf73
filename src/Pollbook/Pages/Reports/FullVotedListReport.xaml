<Page x:Class="Pollbook.Pages.Reports.FullVotedListReport"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:uiCore="clr-namespace:Pollbook.UICore"
      mc:Ignorable="d"
      xmlns:userControls="clr-namespace:Pollbook.UserControls"
      xmlns:button="clr-namespace:Pollbook.UserControls.Button"
      DataContext="{Binding FullVotedListReport, Source={StaticResource Locator}}"
      Initialized="Page_Initialized"
      Loaded="FullVoterListReport_Loaded"
      Unloaded="FullVoterListReport_UnLoaded"
      Background="{StaticResource Gray8Brush}"
      d:DesignHeight="1200" d:DesignWidth="1920">
    
    <Page.Resources>
        <uiCore:NameTextConverter x:Key="NameTextConverter" />
        <uiCore:PartyTextConverter x:Key="PartyDisplayConverter" />
        <uiCore:UtcToLocalDateTimeConverter x:Key="UtcToLocalDateTimeConverter" />
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
    </Page.Resources>
    
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="383*"/>
            <ColumnDefinition Width="97*"/>
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="auto" />
            <RowDefinition Height="auto" />
        </Grid.RowDefinitions>

        <!-- Title -->
        <Grid Grid.Column="0" 
                    Grid.Row="0" 
                    Margin="120,30,120,0" 
                    HorizontalAlignment="Stretch" 
                    VerticalAlignment="Center" 
                    Grid.ColumnSpan="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            <StackPanel Grid.Column="0" 
                        HorizontalAlignment="Left" >
                <Label Content="Voted List" 
                       Style="{StaticResource Display2SemiBoldFont}" />
                <Label Content="{Binding ReportDate}" 
                       Style="{StaticResource Body2BoldLightFont}" />
            </StackPanel>

            <StackPanel Grid.Column="1"
                        VerticalAlignment="Center" 
                        HorizontalAlignment="Right">
                <Button HorizontalAlignment="Right"
                        userControls:ButtonHelper.DisableMultipleClicks="True"
                        Click="OpenFilterView" >
                    <Button.Template>
                        <ControlTemplate TargetType="Button">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="34"/>
                                </Grid.ColumnDefinitions>
                                <Viewbox Grid.Column="1" 
                                         Stretch="Uniform" 
                                         Width="24" 
                                         Height="24" 
                                         VerticalAlignment="Center">
                                    <Path Style="{StaticResource MenuIcon}" 
                                          Fill="{StaticResource SecondaryMediumBlueBrush}" />
                                </Viewbox>
                                <ContentPresenter Grid.Column="0" />
                            </Grid>
                        </ControlTemplate>
                    </Button.Template>
                    <TextBlock Text="{Binding FilterByButton}" 
                               Foreground="{StaticResource SecondaryMediumBlueBrush}"
                               VerticalAlignment="Center">
                        <TextBlock.Style>
                            <Style TargetType="TextBlock" BasedOn="{StaticResource BoldTextBlock}" />
                        </TextBlock.Style>
                    </TextBlock>
                </Button>
            </StackPanel>
        </Grid>

        <!-- Report Body -->
        <Grid Grid.Column="0" Grid.Row="1" Margin="120,20,120,20" Grid.ColumnSpan="2">
            <Grid.RowDefinitions>
                <RowDefinition Height="auto" />
                <RowDefinition Height="*" />
                <RowDefinition Height="85" />
            </Grid.RowDefinitions>
            
            <Grid Margin="0,10,0,0" Grid.Row="1">
                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                    <StackPanel.Style>
                        <Style TargetType="StackPanel">
                            <Setter Property="Visibility" Value="Collapsed" />
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding LoadingResults}" Value="True">
                                    <Setter Property="Visibility" Value="Visible" />
                                </DataTrigger>
                                <DataTrigger Binding="{Binding LoadingResults}" Value="False">
                                    <Setter Property="Visibility" Value="Collapsed" />
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </StackPanel.Style>
                    <userControls:Spinner/>
                </StackPanel>

                <Grid Name="ResultsGridTest">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="813*"/>
                        <ColumnDefinition Width="67*"/>
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>
                    <Grid.Style>
                        <Style TargetType="Grid">
                            <Setter Property="Visibility" Value="Collapsed" />
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding LoadingResults}" Value="True">
                                    <Setter Property="Visibility" Value="Collapsed" />
                                </DataTrigger>
                                <DataTrigger Binding="{Binding LoadingResults}" Value="False">
                                    <Setter Property="Visibility" Value="Visible" />
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </Grid.Style>

                    <!-- Rows -->
                    <ListView Grid.Column="0"
                              ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                              ScrollViewer.VerticalScrollBarVisibility="Auto"
                              ScrollViewer.CanContentScroll="False"
                              x:Name="LvwVotersTest"
                              ItemsSource="{Binding PagedFilteredVotedList,IsAsync=True}" 
                              BorderThickness="0" 
                              Grid.ColumnSpan="2"
                              ScrollViewer.ScrollChanged="LvwVotersTest_ScrollChanged">
                        <ListView.Resources>
                            <Style TargetType="ScrollBar" BasedOn="{StaticResource CustomVerticalScrollBarStyle}"/>
                        </ListView.Resources>
                        <ListView.View>
                            <GridView>
                                <GridViewColumn Header="#" DisplayMemberBinding="{Binding LineNumber}" Width="140"/>
                                <GridViewColumn DisplayMemberBinding="{Binding NotInRosterAsterisk }" Width="26"/>
                                <GridViewColumn Header="Voter" DisplayMemberBinding="{Binding FullName, Converter= {StaticResource NameTextConverter}}" Width="500"/>
                                <GridViewColumn Header="{Binding PartyLabel}" DisplayMemberBinding="{Binding PartyDisplayName,Converter={StaticResource PartyDisplayConverter}}" Width="225"/>
                                <GridViewColumn Header="{Binding PrecinctLabel}" DisplayMemberBinding="{Binding PrecinctSplitName}" Width="390"/>
                                <GridViewColumn Header="Timestamp" DisplayMemberBinding="{Binding TransactionDate, Converter={StaticResource UtcToLocalDateTimeConverter}, StringFormat=MM-dd-yyyy h:mmtt}" Width="380"/>
                                <GridView.ColumnHeaderContainerStyle>
                                    <Style TargetType="GridViewColumnHeader">
                                        <Setter Property="Foreground" Value="{StaticResource Gray3Brush}"></Setter>
                                        <Setter Property="FontFamily" Value="Noto Sans Display"></Setter>
                                        <Setter Property="FontSize" Value="30"/>
                                        <Setter Property="Height" Value="60"/>
                                        <Setter Property="Margin" Value ="30,20,0,0"/>
                                        <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
                                        <Setter Property="VerticalContentAlignment" Value="Stretch" />
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate TargetType="{x:Type GridViewColumnHeader}">
                                                    <TextBlock Text="{TemplateBinding Content}" Padding="5" Width="{TemplateBinding Width}" TextAlignment="Left" />
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Style>
                                </GridView.ColumnHeaderContainerStyle>
                            </GridView>
                        </ListView.View>
                        
                        <ListView.ItemContainerStyle>
                            <Style TargetType="ListViewItem" BasedOn="{StaticResource ReportListViewItemStyle}">
                                <Setter Property="BorderBrush" Value="Transparent" />
                                <Setter Property="BorderThickness" Value="0" />
                                <Setter Property="Background" Value="White"></Setter>
                                <Setter Property="Focusable" Value="false"/>
                                <Setter Property="Foreground" Value="#152025"/>
                                <Setter Property="FontWeight" Value="Bold"/>
                                <Setter Property="FontFamily" Value="Noto Sans Display"/>
                                <Setter Property="FontSize" Value="35"/>
                                <Setter Property="Height" Value ="85"/>
                            </Style>
                        </ListView.ItemContainerStyle>
                    </ListView>


                </Grid>
            </Grid>
     

            <Border Grid.Row="2" Margin="0,0,0,0" CornerRadius="0,0,10,10" Background="{StaticResource Gray7Brush}">
                <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                    <Label Margin="66,0,29,0" Content="{Binding RecordCount}" Style="{StaticResource Body1SemiBoldLight2Font}"/>
                    <Label Margin="0,0,29,0" Content="* Voter Not in Roster" Style="{StaticResource Body1SemiBoldLight2Font}" Visibility="{Binding IsNewVotersCountVisible,Converter={StaticResource BooleanToVisibilityConverter}}"/>
                    <Label Content ="{Binding SelectedFilterOptions}" Style="{StaticResource Body1SemiBoldLight2Font}"/>
                </StackPanel>
            </Border>
        </Grid>

        <!-- Buttons -->
        <Grid Grid.Column="0" Grid.Row="2" Background="White" Grid.ColumnSpan="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="auto" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <Button Grid.Column="0"
                    Content="{Binding BackLabel}"
                    Style="{StaticResource SecondaryLargeButton}"
                    Margin="40"
                    userControls:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding BackCommand}"/>

            <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Right">
                <Button Content="{Binding ExportLabel}"
                        Style="{StaticResource PrimaryLargeButton}"
                        Margin="0,40,30,40"
                        userControls:ButtonHelper.DisableMultipleClicks="True"
                        Command="{Binding ExportCommand}"
                        IsEnabled="{Binding ExportEnabled}"/>
                <Button Content="{Binding PrintLabel}"
                        Style="{StaticResource PrimaryLargeButton}"
                        Margin="0,40,40,40"
                        userControls:ButtonHelper.DisableMultipleClicks="True"
                        Command="{Binding PrintCommand}"
                        Visibility="{Binding PrintVisible, Converter={StaticResource BooleanToVisibilityConverter}}"
                        IsEnabled="{Binding PrintEnabled}"/>
            </StackPanel>
        </Grid>

        <StackPanel Grid.Column="0"  Name="DitherPane" Grid.Row="0"
                    Margin="0,-110,0,0"
                    Grid.RowSpan="3"
                    Background= "#********" Grid.ColumnSpan="2" />

        <StackPanel x:Name="VotedListFilterPanel" 
                    Grid.Row="0"
                    Margin="0,-110,0,0"  
                    Grid.RowSpan="3" 
                    Grid.Column="0" 
                    Width="606" 
                    ZIndex="5"
                    HorizontalAlignment="Right"
                    Background="White" Grid.ColumnSpan="2">
            <ScrollViewer x:Name="VotedListFilterScrollViewer" 
                          CanContentScroll="False"
                          PanningMode="VerticalFirst"
                          Width="{Binding ActualWidth, ElementName=VotedListFilterPanel}"
                          Height ="{Binding ActualHeight, ElementName=VotedListFilterPanel}"
                          Template="{StaticResource ScrollViewerTemplate}">
            <StackPanel Margin="30,0,10,50" >
                <!-- Clear All -->
                <Button Margin="105,30,0,0"
                        userControls:ButtonHelper.DisableMultipleClicks="True"
                        Command="{Binding ClearAllCommand}"
                        IsEnabled="True"
                        Width="400"
                        BorderBrush="{x:Null}"
                        Background="{x:Null}">
                    <Button.Template>
                        <ControlTemplate TargetType="Button">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="10"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <ContentPresenter Grid.Column="0" Grid.ColumnSpan="3" HorizontalAlignment="Right" Margin="0,0,0,0"/>
                            </Grid>
                        </ControlTemplate>
                    </Button.Template>
                        <TextBlock Text="{Binding FilterClearAll}" Foreground="{StaticResource SecondaryMediumBlueBrush}" Height="40">
                        <TextBlock.Style>
                            <Style TargetType="{x:Type TextBlock}" BasedOn="{StaticResource BoldTextBlockWithHeight}"  />
                        </TextBlock.Style>
                    </TextBlock>
                </Button>

                <!-- Group By Precinct -->
                <Grid  Margin="0,20,0,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="auto"/>
                    </Grid.ColumnDefinitions>
                    <Label Grid.Column="0" Content="{Binding GroupByPrecinctLabel, FallbackValue='Group by Precinct'}" Style="{StaticResource StandardBoldLabel}"/>

                    <ToggleButton Grid.Column="1" HorizontalAlignment="Right"
                                  IsChecked="{Binding IsGroupByPrecinct, Mode=TwoWay}" 
                                  Margin="0,0,0,0"/>
                    
                </Grid>
               
                <!-- Sort By -->
                <button:ButtonChevronFlyOut x:Name="SortByComboBox" Margin="5,25,0,0"
                                            RotateChevron="{Binding RotateChevronSortBy, Mode=TwoWay}"
                                            ChevronText="{Binding SortByLabel}"
                                            ChevronBrush="{StaticResource Gray1Brush}"
                                            ChevronState="{Binding ShowSortBy, Mode=TwoWay}">
                    <StackPanel>
                        <ComboBox Margin="0,15,0,0" Width="517" HorizontalAlignment="Left"
                                  Text="{Binding SelectedSortByText}" 
                                  ItemsSource="{Binding Path=SortByOptions}"
                                  SelectedItem="{Binding Path=SelectedSortByOption}" />
                    </StackPanel>
                </button:ButtonChevronFlyOut>

                <!-- Filter By Label -->
                <Label Content="{Binding FilterByLabel }" Margin="0,25,0,0" Style="{StaticResource Body1BoldFont}"/>

                <!-- Filter By PrecinctLabel -->
                <button:ButtonChevronFlyOut x:Name="PrecinctsComboBox" Margin="25,15,0,0"
                    RotateChevron="{Binding RotateChevronPrecinct, Mode=TwoWay}"
                    ChevronText="{Binding FilterByPrecinctLabel}"
                    ChevronBrush="{StaticResource Gray1Brush}"
                    ChevronState="{Binding ShowFilterByPrecinct, Mode=TwoWay}">
                    <StackPanel>
                        <ComboBox Margin="0,15,0,0" Width="497" HorizontalAlignment="Left"
                            ItemsSource="{Binding Path=Precincts, Mode=TwoWay}"
                            DisplayMemberPath="DisplayName"
                            SelectedValuePath="Id"
                            SelectedItem="{Binding Path=SelectedPrecinct}" />
                    </StackPanel>
                </button:ButtonChevronFlyOut>

                <!-- Filter By Date and Time -->
                <button:ButtonChevronFlyOut x:Name="DateTimeFilters" Margin="25,25,0,0" 
                RotateChevron="{Binding RotateChevronDateTime, Mode=TwoWay}"
                ChevronText="{Binding FilterByDateAndTimeLabel}"
                ChevronBrush="{StaticResource Gray1Brush}"
                ChevronState="{Binding ShowFilterDateTime, Mode=TwoWay}">
                    <StackPanel Margin="0,0,0,0">
                        <StackPanel Orientation="Horizontal">
                            <ComboBox  Margin="0,15,5,10" Width="179" 
                                       Text="{Binding SelectedMonthText}" 
                                       ItemsSource="{Binding Months}" 
                                       SelectedValue="UnitNumber" 
                                       DisplayMemberPath="UnitDescription"
                                       SelectedItem="{Binding SelectedMonth, Mode=TwoWay}" />

                            <ComboBox  Margin="0,15,5,10"  Width="140"
                                  Text="{Binding SelectedDayText}"
                                  ItemsSource="{Binding Days}"
                                  SelectedValue="UnitNumber"   
                                  DisplayMemberPath="UnitDescription"  
                                  SelectedItem="{Binding SelectedDay}" />

                            <ComboBox  Margin="0,15,0,10"  Width="168"
                                   Text="{Binding SelectedYearText}"
                                  ItemsSource="{Binding Years}"
                                  SelectedValue="UnitNumber" 
                                  DisplayMemberPath="UnitDescription"                                            
                                  SelectedItem="{Binding SelectedYear}" />
                        </StackPanel>
                        <StackPanel Orientation="Horizontal">
                            <Label Content="Start:" Style="{StaticResource Body1BoldFont}" Width="242" Margin="0,0,0,0" />
                            <Label Content="End:" Style="{StaticResource Body1BoldFont}" Width="243" Margin="5,0,0,0" />
                        </StackPanel>
                        <StackPanel Orientation="Horizontal">
                            <ComboBox  Margin="0,0,5,0"  Width="246" 
                                       Text="{Binding SelectedTimeStartText}"
                                  ItemsSource="{Binding TimeStart}"
                                  SelectedValue="UnitNumber"                                          
                                  DisplayMemberPath="UnitDescription"                                          
                                  SelectedItem="{Binding SelectedTimeStart}" />

                            <ComboBox Margin="0,0,0,0" Width="244"
                                       Text="{Binding SelectedTimeEndText}"
                                       ItemsSource="{Binding TimeEnd}"
                                       SelectedValue="UnitNumber"   
                                       DisplayMemberPath="UnitDescription"  
                                       SelectedItem="{Binding SelectedTimeEnd}" />
                        </StackPanel>
                    </StackPanel>
                </button:ButtonChevronFlyOut>

                <!-- Filter By Party -->
                <button:ButtonChevronFlyOut x:Name="PartiesComboBox" Margin="25,25,0,0" 
                RotateChevron="{Binding RotateChevronParty, Mode=TwoWay}"
                ChevronText="{Binding FilterByPartyLabel}"
                ChevronBrush="{StaticResource Gray1Brush}"
                ChevronState="{Binding ShowFilterByParty, Mode=TwoWay}">
                    <StackPanel>
                        <ComboBox Margin="0,15,0,0" Width="497" HorizontalAlignment="Left"
                              ItemsSource="{Binding Path=Parties, Mode=TwoWay}"
                              DisplayMemberPath="PartyDisplayName"
                              SelectedValuePath="PartyId"
                              SelectedItem="{Binding Path=SelectedParty}" />
                    </StackPanel>
                </button:ButtonChevronFlyOut>

                <!-- Filter By Voter Status -->
                <button:ButtonChevronFlyOut x:Name="VoterStatusComboBox" Margin="25,25,0,0" 
                RotateChevron="{Binding RotateChevronVoterStatus, Mode=TwoWay}"
                ChevronText="{Binding FilterByVoterStatusLabel}"
                ChevronBrush="{StaticResource Gray1Brush}"
                ChevronState="{Binding ShowFilterByVoterStatus, Mode=TwoWay}">
                    <StackPanel>
                        <ComboBox  Margin="0,15,0,0"  Width="497" HorizontalAlignment="Left"
                          ItemsSource="{Binding Path=VoterStatuses, Mode=TwoWay}"
                          DisplayMemberPath="Description"
                          SelectedValuePath="StatusId"
                          SelectedItem="{Binding Path=SelectedVoterStatus}" />
                    </StackPanel>
                </button:ButtonChevronFlyOut>

                <!-- Filter By Voter Verification -->
                <button:ButtonChevronFlyOut x:Name="VoterVerificationComboBox" Margin="25,25,0,0" 
                                            RotateChevron="{Binding RotateChevronVoterVerification, Mode=TwoWay}"
                                            ChevronText="{Binding FilterByVoterVerificationLabel}"
                                            ChevronBrush="{StaticResource Gray1Brush}"
                                            ChevronState="{Binding ShowFilterByVoterVerification, Mode=TwoWay}"
                                            Visibility="{Binding IsVoterVerificationVisible,Converter={StaticResource BooleanToVisibilityConverter}}">
                    <StackPanel>
                        <ComboBox  Margin="0,15,0,0"  Width="497" HorizontalAlignment="Left"
                                   ItemsSource="{Binding Path=VoterVerificationControls, Mode=TwoWay}"
                                   DisplayMemberPath="Value"
                                   SelectedValuePath="Key"
                                   SelectedItem="{Binding Path=SelectedVoterVerification}" />
                    </StackPanel>
                </button:ButtonChevronFlyOut>

                <!-- Filter By Device -->
                <button:ButtonChevronFlyOut x:Name="DeviceComboBox" Margin="25,25,0,0" 
                                            RotateChevron="{Binding RotateChevronDevice, Mode=TwoWay}"
                                            ChevronText="{Binding FilterByDeviceLabel}"
                                            ChevronBrush="{StaticResource Gray1Brush}"
                                            ChevronState="{Binding ShowFilterByDevice, Mode=TwoWay}">
                    <StackPanel>
                        <ComboBox  Margin="0,15,0,0"  Width="497" HorizontalAlignment="Left"
                                   ItemsSource="{Binding Path=Devices, Mode=TwoWay}"
                                   DisplayMemberPath="Value"
                                   SelectedValuePath="Key"
                                   SelectedItem="{Binding Path=SelectedDevice}" />
                    </StackPanel>
                </button:ButtonChevronFlyOut>

                <!-- Filter By Ballot Type -->
                <button:ButtonChevronFlyOut x:Name="BallotTypeComboBox" Margin="25,25,0,0" 
                                            RotateChevron="{Binding RotateChevronBallotType, Mode=TwoWay}"
                                            ChevronText="{Binding FilterByBallotTypeLabel}"
                                            ChevronBrush="{StaticResource Gray1Brush}"
                                            ChevronState="{Binding ShowFilterByBallotType, Mode=TwoWay}">
                    <StackPanel>
                        <ComboBox  Margin="0,15,0,0"  Width="497" HorizontalAlignment="Left"
                                   ItemsSource="{Binding Path=BallotTypes, Mode=TwoWay}"
                                   DisplayMemberPath="EnumerationValueDescription"
                                   SelectedItem="{Binding Path=SelectedBallotType}" />
                    </StackPanel>
                </button:ButtonChevronFlyOut>

                <!-- Filter By Ballot Method -->
                <button:ButtonChevronFlyOut x:Name="BallotMethodComboBox" Margin="25,25,0,0" 
                                            RotateChevron="{Binding RotateChevronBallotMethod, Mode=TwoWay}"
                                            ChevronText="{Binding FilterByBallotMethodLabel}"
                                            ChevronBrush="{StaticResource Gray1Brush}"
                                            ChevronState="{Binding ShowFilterByBallotMethod, Mode=TwoWay}">
                    <StackPanel>
                        <ComboBox  Margin="0,15,0,0"  Width="497" HorizontalAlignment="Left"
                                   ItemsSource="{Binding Path=BallotMethods}"
                                   SelectedItem="{Binding Path=SelectedBallotMethod}" />
                    </StackPanel>
                </button:ButtonChevronFlyOut>

                <!-- Filter By Roster Type -->
                <button:ButtonChevronFlyOut x:Name="RosterTypeComboBox" Margin="25,25,0,0" 
                                            RotateChevron="{Binding RotateChevronRosterType, Mode=TwoWay}"
                                            ChevronText="{Binding FilterByRosterTypeLabel}"
                                            ChevronBrush="{StaticResource Gray1Brush}"
                                            ChevronState="{Binding ShowFilterByRosterType, Mode=TwoWay}">
                    <StackPanel>
                        <ComboBox  Margin="0,15,0,0"  Width="497" HorizontalAlignment="Left"
                                   ItemsSource="{Binding Path=RosterTypes}"
                                   SelectedItem="{Binding Path=SelectedRosterType}" />
                    </StackPanel>
                </button:ButtonChevronFlyOut>
            </StackPanel>
            </ScrollViewer>
        </StackPanel>
    </Grid>
</Page>
