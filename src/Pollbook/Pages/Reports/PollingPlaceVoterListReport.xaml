<Page x:Class="Pollbook.Pages.Reports.PollingPlaceVoterListReport"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:uiCore="clr-namespace:Pollbook.UICore"
      mc:Ignorable="d"
      xmlns:userControls="clr-namespace:Pollbook.UserControls"
      DataContext="{Binding PollingPlaceVoterListReport, Source={StaticResource Locator}}"
      Loaded="PollingPlaceVoterListReport_Loaded"
      Unloaded="PollingPlaceVoterListReport_Unloaded"
      Background="White"
      d:DesignHeight="1200" d:DesignWidth="1920">
    <Page.Resources>
        <uiCore:NameTextConverter x:Key="NameTextConverter" />
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
    </Page.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="auto" />
            <RowDefinition Height="auto" />
        </Grid.RowDefinitions>

        <!-- Title -->
        <StackPanel Grid.Row="0" Margin="80,65,80,0">
            <Label Content="Voter List" Style="{StaticResource Display2SemiBoldFont}" />
            <Label Content="{Binding ReportDate}" Style="{StaticResource Body2BoldLightFont}" />
        </StackPanel>

        <!-- Report Body -->
        <Grid Grid.Row="1" Margin="80,15,80,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="500" />
            </Grid.ColumnDefinitions>
            <!-- Report Data -->
            <Border Grid.Column="0" CornerRadius="10,10,0,0" Background="White">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="auto" />
                        <RowDefinition Height="*" />
                        <RowDefinition Height="85" />
                    </Grid.RowDefinitions>

                    <Grid Grid.Row="1" Margin="0,10,0,0">
                        <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                            <StackPanel.Style>
                                <Style TargetType="StackPanel">
                                    <Setter Property="Visibility" Value="Collapsed" />
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding LoadingResults}" Value="True">
                                            <Setter Property="Visibility" Value="Visible" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding LoadingResults}" Value="False">
                                            <Setter Property="Visibility" Value="Collapsed" />
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </StackPanel.Style>
                            <userControls:Spinner/>
                        </StackPanel>

                        <Grid Name="ResultsGrid">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*" />
                            </Grid.RowDefinitions>
                            <Grid.Style>
                                <Style TargetType="Grid">
                                    <Setter Property="Visibility" Value="Collapsed" />
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding LoadingResults}" Value="True">
                                            <Setter Property="Visibility" Value="Collapsed" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding LoadingResults}" Value="False">
                                            <Setter Property="Visibility" Value="Visible" />
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </Grid.Style>
                            <!-- Rows -->
                            <ListView x:Name="LvwVoters" ItemsSource="{Binding VirtualVoterList,IsAsync=True}" BorderThickness="0" ScrollViewer.HorizontalScrollBarVisibility="Disabled">
                                <ListView.View>
                                    <GridView ScrollViewer.HorizontalScrollBarVisibility="Disabled">
                                        <GridViewColumn Header="#" DisplayMemberBinding="{Binding LineNumber}" Width="100" />
                                        <GridViewColumn Header="Voter" DisplayMemberBinding="{Binding FullName, Converter= {StaticResource NameTextConverter}}" Width="500"/>
                                        <GridViewColumn Header="{Binding PrecinctLabel}" DisplayMemberBinding="{Binding PrecinctSplitName}" Width="200"/>
                                        <GridViewColumn Header="Absentee" DisplayMemberBinding="{Binding AbsenteeStatus}" Width="200"/>
                                        <GridViewColumn Header="Voted" DisplayMemberBinding="{Binding ElectionDay}" Width="200"/>

                                        <GridView.ColumnHeaderContainerStyle>
                                            <Style TargetType="GridViewColumnHeader">
                                                <Setter Property="Foreground" Value="#7b878f"></Setter>
                                                <Setter Property="FontFamily" Value="Noto Sans Display"></Setter>
                                                <Setter Property="FontSize" Value="30"/>
                                                <Setter Property="Height" Value="60"/>
                                                <Setter Property="Margin" Value ="0,20,0,0"/>
                                                <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
                                                <Setter Property="VerticalContentAlignment" Value="Stretch" />
                                                <Setter Property="Template">
                                                    <Setter.Value>
                                                        <ControlTemplate TargetType="{x:Type GridViewColumnHeader}">
                                                            <TextBlock Text="{TemplateBinding Content}" Padding="5" Width="{TemplateBinding Width}" TextAlignment="Left" />
                                                        </ControlTemplate>
                                                    </Setter.Value>
                                                </Setter>
                                            </Style>
                                        </GridView.ColumnHeaderContainerStyle>

                                    </GridView>
                                </ListView.View>

                                <ListView.ItemContainerStyle>
                                    <Style TargetType="ListViewItem">
                                        <Setter Property="BorderBrush" Value="Transparent" />
                                        <Setter Property="BorderThickness" Value="0" />
                                        <Setter Property="Padding" Value="0,5,0,0" />
                                        <Setter Property="Focusable" Value="false"/>
                                        <Setter Property="Margin" Value ="0,10,0,0"/>
                                        <Setter Property="Foreground" Value="#152025"/>
                                        <Setter Property="FontFamily" Value="Noto Sans Display"/>
                                        <Setter Property="FontSize" Value="35"/>
                                        <Setter Property ="Height" Value ="100"/>
                                    </Style>
                                </ListView.ItemContainerStyle>
                                <ListView.Resources>
                                    <Style TargetType="ScrollBar" BasedOn="{StaticResource CustomVerticalScrollBarStyle}"/>
                                </ListView.Resources>
                            </ListView>
                        </Grid>
                    </Grid>

                    <Border Grid.Row="2" Margin="0,0,0,0" CornerRadius="0,0,10,10" Background="{StaticResource Gray6Brush}">
                        <StackPanel Orientation="Horizontal">
                            <Label Margin="66,15,29,0" Content="{Binding RecordCount}" Style="{StaticResource Body1SemiBoldLight2Font}"/>
                        </StackPanel>
                    </Border>

                </Grid>
            </Border>

            <!-- Report Filters -->
            <StackPanel Grid.Column="1"  Margin="20,-45,-40,0" Height="700">
                <!-- Group by Precinct -->
                <CheckBox Style="{StaticResource StandardCheckbox}" Margin="0,0,0,30" Content="{Binding GroupByPrecinctLabel}" IsChecked="{Binding Path=IsGroupByPrecinct, Mode=TwoWay}" CommandParameter="{Binding IsChecked, RelativeSource={RelativeSource Self}, Mode=OneWay}" Command="{Binding IsGroupByPrecinctCommand}" />

                <!-- Filter by Precinct -->
                <Label Content="{Binding FilterByPrecinctLabel}" Style="{StaticResource Body1SemiBoldFont}" Margin="0,10,0,10" />
                <ComboBox x:Name="PrecinctComboBox" Margin="0,10,0,10"
                          ItemsSource="{Binding Path=Precincts, Mode=TwoWay}"
                          DisplayMemberPath="PrecinctSplitName"
                          SelectedValuePath="PrecinctSplitId"
                          SelectedItem="{Binding Path=SelectedPrecinct}" />

                <StackPanel Orientation="Horizontal"/>
                <StackPanel Orientation="Horizontal" Margin="0,0,0,30"/>
            </StackPanel>
        </Grid>

        <!-- Buttons -->
        <Grid Grid.Row="2" Background="White">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="auto" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <Button Grid.Column="0"
                    Content="{Binding BackLabel}"
                    Style="{StaticResource SecondaryLargeButton}"
                    Margin="40"
                    userControls:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding BackCommand}"/>

            <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Right">
                <Button Content="{Binding ExportLabel}"
                        Style="{StaticResource PrimaryLargeButton}"
                        Margin="0,40,30,40"
                        userControls:ButtonHelper.DisableMultipleClicks="True"
                        Command="{Binding ExportCommand}"
                        IsEnabled="{Binding ExportEnabled}"/>
                <Button Content="{Binding PrintLabel}"
                        Style="{StaticResource PrimaryLargeButton}"
                        Margin="0,40,40,40"
                        userControls:ButtonHelper.DisableMultipleClicks="True"
                        Command="{Binding PrintCommand}"
                        Visibility="{Binding PrintVisible, Converter={StaticResource BooleanToVisibilityConverter}}"
                        IsEnabled="{Binding PrintEnabled}"/>
            </StackPanel>
        </Grid>
    </Grid>
</Page>
