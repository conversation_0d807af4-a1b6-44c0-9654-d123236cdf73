using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Ink;
using System.Windows.Media;
using ESS.Pollbook.Core.DTO;
using Pollbook.UserControls;
using static ESS.Pollbook.Core.DynamicControls.DynamicControlsLib;

namespace ESS.Pollbook.DynamicControls
{
    public static class DynamicControlsLib
    {
        public static List<UIElement> Parse(DynamicControlsDto dto)
        {
            List<UIElement> controlList = new List<UIElement>();

            try
            {
                if (dto != null)
                {
                    var controlType = Enum.Parse(typeof(DynamicControlType), dto.Control_Type_Code);
                    var containerType = Enum.Parse(typeof(DynamicContainerType), dto.Container_Type_Code);

                    // check the container type - static means a readonly label representation rather than actual control
                    if (containerType.Equals(DynamicContainerType.STATIC))
                    {
                        controlType = DynamicControlType.DISPLAYONLY;
                    }

                    var value = string.IsNullOrEmpty(dto.Value) ? dto.Default_Value : dto.Value;
                    dataType valueDataType = ParseString(value);

                    switch (controlType)
                    {
                        case DynamicControlType.CHECKBOX:
                            CheckBox checkbox = new CheckBox();
                            checkbox.Name = dto.Control_Name;
                            if (!string.IsNullOrEmpty(dto.Control_Binding_Property))
                            {
                                checkbox.SetBinding(CheckBox.ContentProperty, dto.Control_Binding_Property);
                            }
                            else
                            {
                                checkbox.Content = dto.Display_Name;
                            }
                            checkbox.IsChecked = string.IsNullOrEmpty(value) ? false : bool.Parse(value);

                            if (string.IsNullOrEmpty(dto.Control_Style)) dto.Control_Style = "StandardCheckBox";
                            checkbox.Style = Application.Current.FindResource(dto.Control_Style) as Style;

                            checkbox.Visibility = GetVisibility(dto.Visible);
                            checkbox.Margin = new Thickness(0, 25, 10, 0);
                            controlList.Add(checkbox);
                            break;

                        case DynamicControlType.LABEL:
                            Label label = new Label();
                            if (!string.IsNullOrEmpty(dto.Control_Binding_Property))
                            {
                                label.SetBinding(Label.ContentProperty, dto.Control_Binding_Property);
                            }
                            else
                            {
                                label.Content = value;
                            }

                            if (string.IsNullOrEmpty(dto.Control_Style)) dto.Control_Style = "StandardLabel";
                            label.Style = Application.Current.FindResource(dto.Control_Style) as Style;
                            label.Visibility = GetVisibility(dto.Visible);
                            controlList.Add(label);
                            break;

                        case DynamicControlType.TEXTBOX:
                            TextBox textbox = new TextBox();
                            if (!string.IsNullOrEmpty(dto.Control_Binding_Property))
                            {
                                textbox.SetBinding(TextBox.TextProperty, dto.Control_Binding_Property);
                            }
                            else
                            {
                                textbox.Text = value;
                            }
                            if (string.IsNullOrEmpty(dto.Control_Style)) dto.Control_Style = "StandardTextBox";
                            textbox.Style = Application.Current.FindResource(dto.Control_Style) as Style;
                            textbox.Visibility = GetVisibility(dto.Visible);
                            textbox.IsReadOnly = dto.Read_Only;
                            controlList.Add(textbox);
                            break;

                        case DynamicControlType.TEXTBLOCK:
                            TextBlock textblock = new TextBlock();
                            if (!string.IsNullOrEmpty(dto.Control_Binding_Property))
                            {
                                textblock.SetBinding(TextBlock.TextProperty, dto.Control_Binding_Property);
                            }
                            else
                            {
                                textblock.Text = value;
                            }

                            if (string.IsNullOrEmpty(dto.Control_Style)) dto.Control_Style = "StandardTextBlock";
                            textblock.Style = Application.Current.FindResource(dto.Control_Style) as Style;
                            textblock.Visibility = GetVisibility(dto.Visible);
                            controlList.Add(textblock);
                            break;

                        case DynamicControlType.DISPLAYONLY:
                            Label title = new Label();
                            title.Content = dto.Display_Name;
                            title.Style = Application.Current.FindResource("StandardLightLabel") as Style;
                            title.Visibility = GetVisibility(dto.Visible);
                            controlList.Add(title);

                            TextBlock display = new TextBlock();
                            display.Style = Application.Current.FindResource("Display3BoldTextBlock") as Style;
                            display.Visibility = GetVisibility(dto.Visible);

                            if (!string.IsNullOrEmpty(dto.Control_Binding_Property))
                            {
                                display.SetBinding(TextBlock.TextProperty, dto.Control_Binding_Property);
                            }
                            else
                            {
                                if (valueDataType == dataType.Boolean)
                                {
                                    display.Text = bool.Parse(value) ? "Yes" : "No";
                                }
                                else
                                    display.Text = value;
                            }

                            controlList.Add(display);
                            break;

                        case DynamicControlType.SIGNATURECANVAS:
                            SignatureCanvas signatureCanvas = new SignatureCanvas();
                            signatureCanvas.Name = dto.Control_Name;
                            signatureCanvas.Visibility = GetVisibility(dto.Visible);
                            controlList.Add(signatureCanvas);
                            break;

                        default:
                            break;
                    }
                }
                return controlList;
            }
            catch
            {
                return controlList;
            }
        }

        private static Visibility GetVisibility(int value)
        {
            switch (value)
            {
                // legacy boolean visible of true = visible
                case 1: 
                    return Visibility.Visible;

                // legacy boolean visible of false = hidden
                case 0: 
                    return Visibility.Hidden;

                // new functionality per presentation 
                case 2:
                    return Visibility.Collapsed;

                // if you don't assign a value - give it visible
                default:
                    return Visibility.Visible;
            }
        }

        /// <summary>
        /// Finds a Child of a given item in the visual tree. 
        /// </summary>
        /// <param name="parent">A direct parent of the queried item.</param>
        /// <typeparam name="T">The type of the queried item.</typeparam>
        /// <param name="childName">x:Name or Name of child. </param>
        /// <returns>The first parent item that matches the submitted type parameter. 
        /// If not matching item can be found, 
        /// a null parent is being returned.</returns>
        public static T FindChild<T>(DependencyObject parent, string childName)
            where T : DependencyObject
        {
            try
            {
                // Confirm parent and childName are valid. 
                if (parent == null) return null;

                T foundChild = null;

                int childrenCount = VisualTreeHelper.GetChildrenCount(parent);
                for (int i = 0; i < childrenCount; i++)
                {
                    var child = VisualTreeHelper.GetChild(parent, i);

                    // If the child is not of the request child type child
                    T childType = child as T;
                    if (childType == null)
                    {
                        // recursively drill down the tree
                        foundChild = FindChild<T>(child, childName);

                        // If the child is found, break so we do not overwrite the found child. 
                        if (foundChild != null) break;
                    }
                    else
                    {
                        if (!string.IsNullOrEmpty(childName))
                        {
                            var frameworkElement = child as FrameworkElement;
                            // If the child's name is set for search
                            if (frameworkElement != null && frameworkElement.Name.Equals(childName))
                            {
                                // if the child's name is of the request name
                                foundChild = (T)child;
                                break;
                            }
                            else
                            {
                                foundChild = FindChild<T>(child, childName);
                                if (foundChild != null) break;
                            }
                        }
                        else
                        {
                            foundChild = FindChild<T>(child, childName);
                            if (foundChild != null) break;
                        }
                    }
                }

                return foundChild;
            }
            catch
            {
                return null;
            }
        }
    }
}
