<UserControl x:Class="Pollbook.UserControls.SearchHelp"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:Pollbook.UserControls"
             xmlns:uiCore="clr-namespace:Pollbook.UICore"
             mc:Ignorable="d" 
             MinHeight="200"
             d:DesignHeight="200" d:DesignWidth="1650">
    <UserControl.Resources>
        <uiCore:BooleanToVisibilityConverter x:Key="OnVisibilityConverter" True="Visible" False="Collapsed" />
    </UserControl.Resources>
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="450" />
            <ColumnDefinition Width="49" />
            <ColumnDefinition Width="*" />
        </Grid.ColumnDefinitions>

        <!-- Message -->
        <Border Grid.Column="0" Background="{StaticResource SecondaryLightYellowBrush}" CornerRadius="12,0,0,12">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <Viewbox Grid.Column="0" Stretch="Uniform" VerticalAlignment="Center" HorizontalAlignment="Center" Width="Auto" Margin="45,75,40,75">
                    <Path Fill="{StaticResource SecondaryDarkYellowBrush}">
                        <Path.Style>
                            <Style TargetType="{x:Type Path}" BasedOn="{StaticResource HelpIcon}" />
                        </Path.Style>
                    </Path>
                </Viewbox>

                <TextBlock Grid.Column="1" VerticalAlignment="Center" Text="{Binding RelativeSource={RelativeSource AncestorType={x:Type local:SearchHelp}}, Path=HelpText}" TextWrapping="Wrap" Visibility="Visible">
                    <TextBlock.Style>
                        <Style TargetType="TextBlock" BasedOn="{StaticResource StandardTextBlock}" />
                    </TextBlock.Style>
                </TextBlock>
            </Grid>
        </Border>

        <!-- Divider -->
        <Canvas Grid.Column="1" Background="White">
            <Polygon Fill="{StaticResource SecondaryLightYellowBrush}">
                <Polygon.Points>
                    <Point X="0" Y="0"></Point>
                    <Point X="25" Y="100"></Point>
                    <Point X="0" Y="200"></Point>
                </Polygon.Points>
            </Polygon>
            <Polygon Fill="White">
                <Polygon.Points>
                    <Point X="0" Y="0"></Point>
                    <Point X="25" Y="0"></Point>
                    <Point X="50" Y="100"></Point>
                    <Point X="25" Y="200"></Point>
                    <Point X="0" Y="200"></Point>
                    <Point X="25" Y="100"></Point>
                </Polygon.Points>
            </Polygon>
            <Polygon Fill="{StaticResource Gray8Brush}">
                <Polygon.Points>
                    <Point X="25" Y="0"></Point>
                    <Point X="50" Y="0"></Point>
                    <Point X="50" Y="100"></Point>
                    <Point X="50" Y="200"></Point>
                    <Point X="25" Y="200"></Point>
                    <Point X="50" Y="100"></Point>
                </Polygon.Points>
            </Polygon>
        </Canvas>

        
        <Border Grid.Column="3" Background="{StaticResource Gray8Brush}" CornerRadius="0,12,12,0" Padding="0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <StackPanel x:Name="optionOne" Grid.Column="0" HorizontalAlignment="Center">
                    <StackPanel.Style>
                        <Style TargetType="StackPanel">
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding Visibility, ElementName=Btn}" Value="Collapsed">
                                    <Setter Property="Margin" Value="180,30,40,0" />
                                </DataTrigger>
                                <DataTrigger Binding="{Binding Visibility, ElementName=Btn}" Value="Visible">
                                    <Setter Property="Margin" Value="40,30,40,0" />
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </StackPanel.Style>
                    <TextBlock Text="1" HorizontalAlignment="Center" Margin="0,0,0,10">
                        <TextBlock.Style>
                            <Style TargetType="TextBlock" BasedOn="{StaticResource BoldTextBlock}">
                                <Setter Property="Foreground" Value="{StaticResource PrimaryNavyBrush}" />
                            </Style>
                        </TextBlock.Style>
                    </TextBlock>
                    <TextBlock Text="Check search criteria for accuracy" TextAlignment="Center" HorizontalAlignment="Center" Width="339" TextWrapping="Wrap">
                        <TextBlock.Style>
                            <Style TargetType="TextBlock" BasedOn="{StaticResource BoldTextBlock}">
                                <Setter Property="Foreground" Value="{StaticResource PrimaryNavyBrush}" />
                            </Style>
                        </TextBlock.Style>
                    </TextBlock>
                </StackPanel>

                <StackPanel x:Name="optionTwo" Grid.Column="1" HorizontalAlignment="Center" Margin="0,30,40,0">
                    <TextBlock Text="2" HorizontalAlignment="Center" Margin="0,0,0,10" Visibility="{Binding Visibility, ElementName=Btn}">
                        <TextBlock.Style>
                            <Style TargetType="TextBlock" BasedOn="{StaticResource BoldTextBlock}">
                                <Setter Property="Foreground" Value="{StaticResource PrimaryNavyBrush}" />
                            </Style>
                        </TextBlock.Style>
                    </TextBlock>
                    <Button x:Name="Btn"
                            Content="Expand Search"
                            Style="{DynamicResource PrimarySmallButton}" 
                            IsEnabled="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type local:SearchHelp}}, Path=EnableExpandSearch}" 
                            Visibility="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type local:SearchHelp}}, Path=VisibilityExpandSearch, Converter={StaticResource OnVisibilityConverter}}"
                            local:ButtonHelper.DisableMultipleClicks="True"
                            Command="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type local:SearchHelp}}, 
                     Path=ExpandSearchCommand, Mode=OneWay}">
                    </Button>
                </StackPanel>

                <StackPanel Grid.Column="1" HorizontalAlignment="Center" Margin="150,30,40,20">
                    <StackPanel.Style>
                        <Style TargetType="StackPanel">
                        <Setter Property="Visibility" Value="Collapsed" />
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding Visibility, ElementName=Btn}" Value="Collapsed">
                                <Setter Property="Visibility" Value="Visible" />
                            </DataTrigger>
                                <DataTrigger Binding="{Binding Visibility, ElementName=Btn}" Value="Visible">
                                <Setter Property="Visibility" Value="Collapsed" />
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                    </StackPanel.Style>
                    <TextBlock Text="2" HorizontalAlignment="Center" Margin="0,0,0,10" Visibility="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type local:SearchHelp}}, Path=RegisterNewVoterVisible, Converter={StaticResource OnVisibilityConverter}}">
                        <TextBlock.Style>
                            <Style TargetType="TextBlock" BasedOn="{StaticResource BoldTextBlock}">
                                <Setter Property="Foreground" Value="{StaticResource PrimaryNavyBrush}" />
                            </Style>
                        </TextBlock.Style>
                    </TextBlock>
                    <Button Content="Voter Not Found" 
                            Style="{DynamicResource PrimarySmallButton}" 
                            IsEnabled="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type local:SearchHelp}}, Path=EnableRegisterNewVoter}" 
                            Visibility="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type local:SearchHelp}}, Path=RegisterNewVoterVisible,Converter={StaticResource OnVisibilityConverter}}" 
                            local:ButtonHelper.DisableMultipleClicks="True"
                            Command="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type local:SearchHelp}}, Path=RegisterNewVoterCommand, 
                           Mode=OneWay}" />
                </StackPanel>

                <StackPanel Grid.Column="2" HorizontalAlignment="Center" Margin="0,30,40,0">
                    <StackPanel.Style>
                        <Style TargetType="StackPanel">
                            <Setter Property="Visibility" Value="Collapsed" />
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding Visibility, ElementName=Btn}" Value="Collapsed">
                                    <Setter Property="Visibility" Value="Collapsed" />
                                </DataTrigger>
                                <DataTrigger Binding="{Binding Visibility, ElementName=Btn}" Value="Visible">
                                    <Setter Property="Visibility" Value="Visible" />
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </StackPanel.Style>
                    <TextBlock  Text="3" HorizontalAlignment="Center" Margin="0,0,0,10" Visibility="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type local:SearchHelp}}, Path=RegisterNewVoterVisible, Converter={StaticResource OnVisibilityConverter}}" >
                        <TextBlock.Style>
                            <Style TargetType="TextBlock" BasedOn="{StaticResource BoldTextBlock}">
                                <Setter Property="Foreground" Value="{StaticResource PrimaryNavyBrush}" />
                            </Style>
                        </TextBlock.Style>
                    </TextBlock>
                    <Button Content="Voter Not Found"
                            Style="{DynamicResource PrimarySmallButton}" 
                            IsEnabled="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type local:SearchHelp}}, Path=EnableRegisterNewVoter}" 
                            Visibility="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type local:SearchHelp}}, Path=RegisterNewVoterVisible, Converter={StaticResource OnVisibilityConverter}}" 
                            local:ButtonHelper.DisableMultipleClicks="True"
                            Command="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type local:SearchHelp}}, Path=RegisterNewVoterCommand, 
                            Mode=OneWay}" />
                </StackPanel>

            </Grid>
        </Border>
    </Grid>
</UserControl>
