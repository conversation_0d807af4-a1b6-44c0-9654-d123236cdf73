using System.Windows;
using System.Windows.Controls;

namespace Pollbook.UserControls
{
    /// <summary>
    /// Interaction logic for EditListItem.xaml
    /// </summary>
    public partial class EditListItem : UserControl
    {
        public DataTemplate ContentOneTemplate
        {
            get { return (DataTemplate)GetValue(ContentOneTemplateProperty); }
            set { SetValue(ContentOneTemplateProperty, value); }
        }

        public static readonly DependencyProperty ContentOneTemplateProperty =
            DependencyProperty.Register("ContentOneTemplate", typeof(DataTemplate), typeof(EditListItem));

        public DataTemplateSelector ContentOneTemplateSelector
        {
            get { return (DataTemplateSelector)GetValue(ContentOneTemplateSelectorProperty); }
            set { SetValue(ContentOneTemplateSelectorProperty, value); }
        }

        public static readonly DependencyProperty ContentOneTemplateSelectorProperty =
            DependencyProperty.Register("ContentOneTemplateSelector", typeof(DataTemplateSelector), typeof(EditListItem));

        public EditListItem()
        {
            InitializeComponent();
        }
    }
}
