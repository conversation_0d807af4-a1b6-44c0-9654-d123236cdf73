<UserControl x:Class="Pollbook.UserControls.EditListItem"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:core="clr-namespace:Pollbook.UICore"
             xmlns:local="clr-namespace:Pollbook.UserControls"
             xmlns:sys="clr-namespace:System;assembly=mscorlib"
             Focusable="True"
             mc:Ignorable="d" 
             d:DesignHeight="300" d:DesignWidth="900">
    <UserControl.Resources>
        <Style x:Key="FocusableTextBoxStyle" TargetType="{x:Type TextBox}">
            <Style.Triggers>
                <DataTrigger Binding="{Binding IsFocused}" Value="True">
                    <Setter Property="FocusManager.FocusedElement" Value="{Binding RelativeSource={RelativeSource Self}}" />
                </DataTrigger>
            </Style.Triggers>
        </Style>
        <core:BooleanToVisibilityConverter x:Key="IsOptionalVisibilityConverter" True="Visible" False="Hidden" />
    </UserControl.Resources>
    
    <Grid Grid.Column="1">
        <Grid.RowDefinitions>
            <RowDefinition Height="*" />
            <RowDefinition Height="auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <Grid Grid.Row="0" VerticalAlignment="Bottom" Margin="0,0,0,23">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <TextBlock Grid.Column="0" x:Name="ItemLabel" Text="{Binding Label}" VerticalAlignment="Bottom" Style="{StaticResource StandardTextBlock}"/>

            <TextBlock Grid.Column="1" x:Name="OptionalLabel" Text="This is optional" Visibility="{Binding IsOptional, Converter={StaticResource IsOptionalVisibilityConverter}}" HorizontalAlignment="Right" VerticalAlignment="Bottom">
                <TextBlock.Style>
                    <Style TargetType="TextBlock" BasedOn="{StaticResource StandardTextBlock}">
                        <Setter Property="Foreground" Value="{StaticResource Gray4Brush}" />
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding IsInvalid}" Value="True">
                                <Setter Property="TextBlock.Foreground" Value="{StaticResource SecondaryDarkRedBrush}"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </TextBlock.Style>
            </TextBlock>
        </Grid>

        <Grid Grid.Row="1" VerticalAlignment="Center" Margin="0">
            <ContentControl
                    x:Name="ContentHolder"
                    Content="{Binding}"
                    ContentTemplate="{Binding ContentOneTemplate, RelativeSource={RelativeSource AncestorType={x:Type ContentControl}}}"
                    ContentTemplateSelector="{Binding ContentOneTemplateSelector, RelativeSource={RelativeSource AncestorType={x:Type ContentControl}}}" />
        </Grid>

        <TextBlock Grid.Row="2" x:Name="ErrorTextPrefix" Text="Error:" VerticalAlignment="Top" Margin="0,25,0,0">
            <TextBlock.Style>
                <Style TargetType="TextBlock" BasedOn="{StaticResource BoldTextBlock}">
                    <Setter Property="Foreground" Value="{StaticResource SecondaryDarkRedBrush}"/>
                    <Setter Property="Visibility" Value="Hidden"/>
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding IsInvalid}" Value="True">
                            <Setter Property="Visibility" Value="Visible"/>
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </TextBlock.Style>
        </TextBlock>
        <TextBlock Grid.Row="2" x:Name="ErrorText" Text="{Binding ErrorText}" VerticalAlignment="Top" Margin="88,25,0,0">
            <TextBlock.Style>
                <Style TargetType="TextBlock" BasedOn="{StaticResource StandardTextBlock}">
                    <Setter Property="Foreground" Value="{StaticResource SecondaryDarkRedBrush}"/>
                    <Setter Property="Visibility" Value="Hidden"/>
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding IsInvalid}" Value="True">
                            <Setter Property="Visibility" Value="Visible"/>
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </TextBlock.Style>
        </TextBlock>
    </Grid>
</UserControl>
