<UserControl x:Class="Pollbook.UserControls.Spinner"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:i="clr-namespace:System.Windows.Interactivity;assembly=System.Windows.Interactivity" 
             xmlns:local="clr-namespace:Pollbook.UserControls"
             mc:Ignorable="d" 
             d:DesignHeight="240" d:DesignWidth="240">
    <UserControl.Resources>
        <Color x:Key="FilledColor" R="0" G="145" B="192" A="255" />
        <Color x:Key="UnfilledColor" R="0" G="145" B="192" A="0" />

        <Color x:Key="ModalColor" R="255" G="255" B="255" A="100" />
        <SolidColorBrush x:Key="ModalBrush" Color="{StaticResource ModalColor}" />

        <Style x:Key="BusyAnimationStyle" TargetType="Control">
            <Setter Property="Background" Value="Transparent" />

            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Control">
                        <Border BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" Background="{TemplateBinding Background}">
                            <Grid Margin="50" VerticalAlignment="Center" HorizontalAlignment="Center" Height="120" Width="120">
                                <Canvas>
                                    <Canvas.Resources>
                                        <Style TargetType="Ellipse">
                                            <Setter Property="Width" Value="13.7"/>
                                            <Setter Property="Height" Value="13.7" />
                                            <Setter Property="Fill" Value="{StaticResource SecondaryLightBlueBrush}" />
                                        </Style>
                                    </Canvas.Resources>
                                    <Canvas.Style>
                                        <Style TargetType="Canvas">
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type local:Spinner}}, Path=IsBusy}" Value="True">
                                                    <DataTrigger.EnterActions>
                                                        <BeginStoryboard x:Name="SpinStoryboard">
                                                            <Storyboard>
                                                                <DoubleAnimation From="0" To="360" Duration="0:0:01.2" RepeatBehavior="Forever" Storyboard.TargetProperty="(RenderTransform).(RotateTransform.Angle)"/>
                                                            </Storyboard>
                                                        </BeginStoryboard>
                                                    </DataTrigger.EnterActions>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Canvas.Style>
                                    <Canvas.RenderTransform>
                                        <RotateTransform x:Name="SpinnerRotate" CenterX="60" CenterY="60" Angle="0" />
                                    </Canvas.RenderTransform>

                                    <Ellipse x:Name="ellipse0" Canvas.Top="53.15" Canvas.Left="0" />
                                    <Ellipse x:Name="ellipse1" Canvas.Top="22.65" Canvas.Left="11.6" />
                                    <Ellipse x:Name="ellipse2" Canvas.Top="3.65" Canvas.Left="39" Fill="{StaticResource SecondaryMediumBlueBrush}" />
                                    <Ellipse x:Name="ellipse3" Canvas.Top="4.75" Canvas.Left="71.6" Fill="{StaticResource PrimaryBlueBrush}" />
                                    <Ellipse x:Name="ellipse4" Canvas.Top="20.45" Canvas.Left="95.8" Width="21.1" Height="21.1" Fill="{StaticResource SecondaryDarkBlueBrush}" />

                                    <Ellipse x:Name="ellipse5" Canvas.Top="56.35" Canvas.Left="106.3" Fill="{StaticResource PrimaryBlueBrush}" />
                                    <Ellipse x:Name="ellipse6" Canvas.Top="86.85" Canvas.Left="94.8" Fill="{StaticResource SecondaryMediumBlueBrush}" />
                                    <Ellipse x:Name="ellipse7" Canvas.Top="105.75" Canvas.Left="67.4" />
                                    <Ellipse x:Name="ellipse8" Canvas.Top="104.75" Canvas.Left="34.8" />
                                    <Ellipse x:Name="ellipse9" Canvas.Top="84.75" Canvas.Left="8.4" />
                                </Canvas>
                                <Label Content="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type local:Spinner}}, Path=SpinnerText}" Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Grid>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>
    <Control Style="{StaticResource BusyAnimationStyle}" />
</UserControl>
