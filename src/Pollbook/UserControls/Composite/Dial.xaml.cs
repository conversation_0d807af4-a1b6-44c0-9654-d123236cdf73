using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;

namespace Pollbook.UserControls
{
    /// <summary>
    /// Interaction logic for Dial.xaml
    /// </summary>
    public partial class Dial : UserControl
    {
        private Dictionary<int, TextBlock> _dialItems = new Dictionary<int, TextBlock>();

        public string[] Items
        {
            get { return (string[])GetValue(ItemsProperty); }
            set { SetValue(ItemsProperty, value); }
        }

        public static readonly DependencyProperty ItemsProperty =
            DependencyProperty.Register("Items", typeof(string[]), typeof(Dial), new FrameworkPropertyMetadata(new string[0], ItemsChangedCallback));

        private static void ItemsChangedCallback(DependencyObject dependencyObject, DependencyPropertyChangedEventArgs args)
        {
            if (dependencyObject is Dial)
            {
                Dial control = (Dial)dependencyObject;

                control.CreateDialItems();
            }
        }

        public int SelectedIndex
        {
            get { return (int)GetValue(SelectedIndexProperty); }
            set { SetValue(SelectedIndexProperty, value); }
        }

        public static readonly DependencyProperty SelectedIndexProperty =
            DependencyProperty.Register("SelectedIndex", typeof(int), typeof(Dial), new FrameworkPropertyMetadata(0, SelectedIndexChangedCallback));

        private static void SelectedIndexChangedCallback(DependencyObject dependencyObject, DependencyPropertyChangedEventArgs args)
        {
            if (dependencyObject is Dial)
            {
                Dial control = (Dial)dependencyObject;
                int index = (int)args.NewValue;

                control.SetSelected(index);
            }
        }

        public Dial()
        {
            InitializeComponent();
        }

        private void CreateDialItems()
        {
            int i = 0;
            var dialItems = (StackPanel)FindName("DialItems");
            var selectedItemStyle = (Style)Application.Current.Resources["SelectedDialItem"];
            var itemStyle = (Style)Application.Current.Resources["DialItem"];

            if (_dialItems.Count > 0)
            {
                _dialItems.Clear();
                dialItems.Children.Clear();
            }

            foreach (var item in Items)
            {
                var textBlock = new TextBlock() { Text = $"{i + 1}. {item}", Style = itemStyle };

                if (i == 0)
                {
                    textBlock.Style = selectedItemStyle;
                }

                _dialItems.Add(i, textBlock);

                dialItems.Children.Add(textBlock);

                i++;
            }
        }

        private void SetSelected(int index)
        {
            if (_dialItems.Count <= index)
            {
                return;
            }

            var selectedItemStyle = (Style)Application.Current.Resources["SelectedDialItem"];
            var itemStyle = (Style)Application.Current.Resources["DialItem"];

            foreach (var dialItem in _dialItems)
            {
                dialItem.Value.Style = itemStyle;
            }

            _dialItems[index].Style = selectedItemStyle;

            DialView.ScrollToVerticalOffset(_dialItems[index].Height * index);
        }
    }
}
