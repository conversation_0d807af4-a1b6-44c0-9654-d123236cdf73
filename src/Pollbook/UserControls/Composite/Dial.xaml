<UserControl x:Class="Pollbook.UserControls.Dial"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:Pollbook.UserControls"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="300">
    <Grid Background="White">
        <ScrollViewer x:Name="DialView" VerticalScrollBarVisibility="Hidden">
            <StackPanel>
                <ContentControl x:Name="TopPadding" Height="195" />
                <StackPanel x:Name="DialItems" />
                <ContentControl x:Name="BottomPadding" Height="450" />
            </StackPanel>
        </ScrollViewer>

        <!-- Gradients -->
        <Rectangle Fill="{StaticResource DialTopBrush}" Height="90" VerticalAlignment="Top"></Rectangle>

        <Rectangle Fill="{StaticResource DialBottomBrush}" Height="90" VerticalAlignment="Bottom"></Rectangle>
    </Grid>
</UserControl>
