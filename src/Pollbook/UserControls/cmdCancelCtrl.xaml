<UserControl x:Class="DesignGeneralControls.cmdCancelCtrl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:DesignGeneralControls"
             xmlns:userControls="clr-namespace:Pollbook.UserControls"
             mc:Ignorable="d" 
             d:DesignHeight="300" d:DesignWidth="300">
    <UserControl.Resources>
        <Style TargetType="Button">
            <Setter Property="FontFamily" Value="Segoe UI Bold" />
            <Setter Property="FontSize" Value="30px" />
        </Style>
    </UserControl.Resources>
    <Grid>
        <Button Width="93px"
                Height="40px"
                userControls:ButtonHelper.DisableMultipleClicks="True"
                Background="White"
                BorderBrush="Transparent"
                Foreground="#0091c0">Cancel</Button>
    </Grid>
</UserControl>
