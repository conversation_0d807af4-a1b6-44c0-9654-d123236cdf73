using System;
using System.Windows.Controls;
using System.Windows.Input;


namespace DesignGeneralControls
{
    /// <summary>
    /// Interaction logic for cmdSearchResultCntrl.xaml
    /// </summary>
    public partial class cmdSearchResultCntrl : UserControl
    {
        public cmdSearchResultCntrl(String CaptionString)
        {
            InitializeComponent();
            btn_srchTxt.Text = CaptionString;
        }

        public String CaptionString
        {
            get { return btn_srchTxt.Text; }
            set { btn_srchTxt.Text = value; }
        }

        public double ControlSize
        {
             get { return (btn_srchTxt.Text.Length * (double)FindResource("Pixelspace")) + 24; }
        }

        public void DockPanel_MouseDown(object sender, MouseButtonEventArgs e)
        {

        }
    }

}
