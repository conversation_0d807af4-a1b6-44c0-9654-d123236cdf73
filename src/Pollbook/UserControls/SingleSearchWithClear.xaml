<UserControl x:Class="DesignGeneralControls.SingleSearchWithClear"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:DesignGeneralControls"
              xmlns:System="clr-namespace:System;assembly=mscorlib"
             mc:Ignorable="d" 
             d:DesignHeight="116" d:DesignWidth="920">
    <UserControl.Resources>
        <System:Double x:Key="PageFontSize">20</System:Double>
        <System:Double x:Key="PageButtonFontSize">18</System:Double>
        <FontFamily x:Key="PageFont" >Segoe UI Semibold</FontFamily>
        <SolidColorBrush x:Key="PageControlBackgroundColor">AliceBlue</SolidColorBrush>
        <Style TargetType="Button">
            <Setter Property="FontSize" Value="{StaticResource PageButtonFontSize}" />
            <Setter Property="FontFamily" Value="{StaticResource PageFont}" />
        </Style>
    </UserControl.Resources>
    <Grid Background="AliceBlue">
        <Border Name="_border" CornerRadius="10,10,10,10" BorderThickness="4" Width="910px" Height="106px" BorderBrush="#0098ca" Background="#f4f8fa" >
            <Border.BitmapEffect>
                <DropShadowBitmapEffect Color="Black" Direction="320" Softness="1" ShadowDepth="10" Opacity="0.5" />
            </Border.BitmapEffect>
            <Canvas Name="mainContainer">
                <Image Source="..\SharedObjects\Images\search.png" MouseDown="Image_MouseDown"  Stretch="Fill" Canvas.Left="35" Canvas.Top="33"  Width="36" Height="36" Panel.ZIndex="10" />
                <TextBox Name="UserEditCntrl" FontSize="30px" FontFamily="Segoe UI Semibold" Foreground="#152025" Canvas.Left="78px" Canvas.Top="33PX" Height="44px" 
                         Padding="0,0,0,0" BorderBrush="Transparent" BorderThickness="0"  
                         Background="Transparent"   KeyDown="UserEditCntrl_KeyDown" TextChanged="UserEditCntrl_TextChanged" />
                <Button Name="cmdClear" Width="86" Height="45" Canvas.Left="778px" FontSize="30px" Canvas.Top="29" BorderBrush="Transparent" Background="Transparent" BorderThickness="0" Foreground="#0091c0" Click="cmdClear_Click"  >Clear</Button>
            </Canvas>
        </Border>
    </Grid>
</UserControl>
