<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{3D13914E-B573-4407-8CDF-5A08F68C85DD}</ProjectGuid>
    <OutputType>library</OutputType>
    <RootNamespace>DesignGeneralControls</RootNamespace>
    <AssemblyName>DesignGeneralControls</AssemblyName>
    <TargetFrameworkVersion>v4.5.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <ProjectTypeGuids>{60dc8134-eba5-43b8-bcc9-bb4bc16c2548};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xaml">
      <RequiredTargetFramework>4.0</RequiredTargetFramework>
    </Reference>
    <Reference Include="WindowsBase" />
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="btnNavigateDown.xaml.cs">
      <DependentUpon>btnNavigateDown.xaml</DependentUpon>
    </Compile>
    <Compile Include="cmdCancelCtrl.xaml.cs">
      <DependentUpon>cmdCancelCtrl.xaml</DependentUpon>
    </Compile>
    <Compile Include="cmdDownArrow.xaml.cs">
      <DependentUpon>cmdDownArrow.xaml</DependentUpon>
    </Compile>
    <Compile Include="cmdHighLightText.xaml.cs">
      <DependentUpon>cmdHighLightText.xaml</DependentUpon>
    </Compile>
    <Compile Include="cmdReturnMultipleText_Shadow.xaml.cs">
      <DependentUpon>cmdReturnMultipleText_Shadow.xaml</DependentUpon>
    </Compile>
    <Compile Include="cmdSaveButton.xaml.cs">
      <DependentUpon>cmdSaveButton.xaml</DependentUpon>
    </Compile>
    <Compile Include="cmdSave_Shadow.xaml.cs">
      <DependentUpon>cmdSave_Shadow.xaml</DependentUpon>
    </Compile>
    <Compile Include="cmdSearchResultCntrl.xaml.cs">
      <DependentUpon>cmdSearchResultCntrl.xaml</DependentUpon>
    </Compile>
    <Compile Include="cmdUpArrow.xaml.cs">
      <DependentUpon>cmdUpArrow.xaml</DependentUpon>
    </Compile>
    <Compile Include="ComplexTextSearch_Shadow.xaml.cs">
      <DependentUpon>ComplexTextSearch_Shadow.xaml</DependentUpon>
    </Compile>
    <Compile Include="DefinedOptionList.xaml.cs">
      <DependentUpon>DefinedOptionList.xaml</DependentUpon>
    </Compile>
    <Compile Include="DisableControlWithText.xaml.cs">
      <DependentUpon>DisableControlWithText.xaml</DependentUpon>
    </Compile>
    <Compile Include="DisplayAddressOption.xaml.cs">
      <DependentUpon>DisplayAddressOption.xaml</DependentUpon>
    </Compile>
    <Compile Include="MultipleResultContainer.xaml.cs">
      <DependentUpon>MultipleResultContainer.xaml</DependentUpon>
    </Compile>
    <Compile Include="PrecinctCountyStateIndicator.xaml.cs">
      <DependentUpon>PrecinctCountyStateIndicator.xaml</DependentUpon>
    </Compile>
    <Compile Include="SearchResultPanel.xaml.cs">
      <DependentUpon>SearchResultPanel.xaml</DependentUpon>
    </Compile>
    <Compile Include="SimpleTextboxControlWithClear.xaml.cs">
      <DependentUpon>SimpleTextboxControlWithClear.xaml</DependentUpon>
    </Compile>
    <Compile Include="ComplexTextWithSearch.xaml.cs">
      <DependentUpon>ComplexTextWithSearch.xaml</DependentUpon>
    </Compile>
    <Compile Include="SimpleTxtBxCtrl.xaml.cs">
      <DependentUpon>SimpleTxtBxCtrl.xaml</DependentUpon>
    </Compile>
    <Compile Include="SingleSearchWithClear.xaml.cs">
      <DependentUpon>SingleSearchWithClear.xaml</DependentUpon>
    </Compile>
    <Page Include="btnNavigateDown.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="cmdCancelCtrl.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="cmdDownArrow.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="cmdHighLightText.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="cmdReturnMultipleText_Shadow.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="cmdSaveButton.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="cmdSave_Shadow.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="cmdSearchResultCntrl.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="cmdUpArrow.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="ComplexTextSearch_Shadow.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="DefinedOptionList.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="DisableControlWithText.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="DisplayAddressOption.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="MultipleResultContainer.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="PrecinctCountyStateIndicator.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="SearchResultPanel.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="SimpleTextboxControl.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="SimpleTextboxControlWithClear.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Compile Include="SimpleTextboxControl.xaml.cs">
      <DependentUpon>SimpleTextboxControl.xaml</DependentUpon>
    </Compile>
    <Page Include="ComplexTextWithSearch.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="SimpleTxtBxCtrl.xaml">
      <SubType>Designer</SubType>
      <Generator>XamlIntelliSenseFileGenerator</Generator>
    </Page>
    <Page Include="SingleSearchWithClear.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>