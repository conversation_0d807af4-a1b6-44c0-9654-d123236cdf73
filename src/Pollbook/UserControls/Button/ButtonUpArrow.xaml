<UserControl x:Class="Pollbook.UserControls.ButtonUpArrow"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:Pollbook.UserControls"
             mc:Ignorable="d" 
             d:DesignHeight="110" d:DesignWidth="110">
    <Grid>

            <Button Background="Transparent"
                    BorderBrush="Transparent"
                    local:ButtonHelper.DisableMultipleClicks="True"
                    Height="106px"
                    Width="106px">
            <Button.Template>
                <ControlTemplate>
                    <Image Source="..\..\Pollbook.Shared\Images\arrow-up.png" />
                </ControlTemplate>
            </Button.Template>
        </Button>

            
    </Grid>
</UserControl>
