using System.Windows;
using System.Windows.Data;

namespace Pollbook.UserControls
{
    public static class ButtonHelper
    {
        public static readonly DependencyProperty DisableMultipleClicksProperty =
            DependencyProperty.RegisterAttached(
                "DisableMultipleClicks",
                typeof(bool),
                typeof(ButtonHelper),
                new PropertyMetadata(false, OnDisableMultipleClicksChanged));

        public static bool GetDisableMultipleClicks(System.Windows.Controls.Button button)
        {
            return (bool)button.GetValue(DisableMultipleClicksProperty);
        }

        public static void SetDisableMultipleClicks(System.Windows.Controls.Button button, bool value)
        {
            button.SetValue(DisableMultipleClicksProperty, value);
        }

        private static void OnDisableMultipleClicksChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is System.Windows.Controls.Button button)
            {
                if ((bool)e.NewValue)
                {
                    button.Click += Button_Click;
                }
                else
                {
                    button.Click -= Button_Click;
                }
            }
        }

        private static void Button_Click(object sender, RoutedEventArgs e)
        {
            if (sender is System.Windows.Controls.Button button)
            {
                // Get and store the original binding and multi-binding values
                var binding = BindingOperations.GetBinding(button, UIElement.IsEnabledProperty);
                var multiBinding =
                    BindingOperations.GetMultiBinding(button, UIElement.IsEnabledProperty);

                // Temporarily disable the button upon button click and before operation
                // Note: Manually set IsEnabled will take precedence override style-based triggers or bindings and becomes new local value
                button.IsEnabled = false;

                // Re-enable the button after the operation is complete
                button.Dispatcher.InvokeAsync(() => Click_Complete(button, binding, multiBinding), System.Windows.Threading.DispatcherPriority.Background);
            }
        }

        private static void Click_Complete(System.Windows.Controls.Button button, Binding binding, MultiBinding multiBinding)
        {
            // Restore the original binding or multi-binding (if exist) to allow triggers and others to regain control
            if (binding != null)
            {
                BindingOperations.SetBinding(button, UIElement.IsEnabledProperty,
                    binding);
            }
            else if (multiBinding != null)
            {
                BindingOperations.SetBinding(button, UIElement.IsEnabledProperty,
                    multiBinding);
            }
            else
            {
                // If there was no binding, just restore to true for enable again
                button.IsEnabled = true;
            }
        }
    }
}