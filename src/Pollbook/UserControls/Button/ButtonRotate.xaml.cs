using System.Windows;
using System.Windows.Controls;
using System.IO;
using System.Drawing;
using System.Drawing.Drawing2D;
using System;
using ESS.Pollbook.Core.Logging;

namespace Pollbook.UserControls
{
    /// <summary>
    /// Interaction logic for ButtonRotate.xaml
    /// </summary>
    public partial class ButtonRotate : UserControl
    {

        #region PROPERTIES

        public static readonly DependencyProperty SourceImageBytesProperty = DependencyProperty.Register(nameof(SourceImageBytes),
            typeof(byte[]), typeof(ButtonRotate), new PropertyMetadata(null));

        public byte[] SourceImageBytes
        {
            get { return (byte[])GetValue(SourceImageBytesProperty); }
            set { SetValue(SourceImageBytesProperty, value); }
        }

        private readonly EssLogger _logger = new EssLogger();

        #endregion

        #region CONSTRUCTOR

        public ButtonRotate()
        {
            InitializeComponent();
        }

        #endregion

        #region EVENTS

        private void RotateSignature_Click(object sender, RoutedEventArgs e)
        {
            System.Drawing.Image copiedSignature = null;

            try
            {
                copiedSignature = ByteArrayToImage(SourceImageBytes);
                copiedSignature = RotateImage(copiedSignature);
                SourceImageBytes = ImageToByteArray(copiedSignature);
            }
            catch (Exception ex) 
            {
                _logger.LogError(ex);
            }
            finally
            {
                copiedSignature?.Dispose();
            }
        }

        #endregion

        #region METHODS

        private System.Drawing.Image ByteArrayToImage(byte[] byteArrayIn)
        {
            if (byteArrayIn == null)
                throw new ArgumentException(@"Can not convert null byte array to image", nameof(byteArrayIn));

            System.Drawing.Image returnImage = null;

            using (var ms = new MemoryStream(byteArrayIn))
            {
                returnImage = System.Drawing.Image.FromStream(ms);
            }

            return returnImage;
        }

        private byte[] ImageToByteArray(System.Drawing.Image imageIn)
        {
            if (imageIn == null)
                throw new ArgumentException(@"Can not convert null image to byte array", nameof(imageIn));

            var _imageConverter = new ImageConverter();
            return (byte[])_imageConverter.ConvertTo(imageIn, typeof(byte[])); 
        }

        private System.Drawing.Image RotateImage(System.Drawing.Image img)
        {
            if (img == null)
                throw new ArgumentException(@"Can not rotate null image", nameof(img));

            //create an empty Bitmap image
            var bmp = new Bitmap(img.Width, img.Height);

            //turn the Bitmap into a Graphics object
            using (Graphics gfx = Graphics.FromImage(bmp))
            {
                //now we set the rotation point to the center of our image
                gfx.TranslateTransform((float)bmp.Width / 2, (float)bmp.Height / 2);

                //now rotate the image
                gfx.RotateTransform(180);

                gfx.TranslateTransform(-(float)bmp.Width / 2, -(float)bmp.Height / 2);

                //set the InterpolationMode to HighQualityBicubic so to ensure a high
                //quality image once it is transformed to the specified size
                gfx.InterpolationMode = InterpolationMode.HighQualityBicubic;

                //now draw our new image onto the graphics object
                gfx.DrawImage(img, 0, 0, img.Width, img.Height);
            }

            //return the image
            return bmp;
        }

        #endregion
    }
}
