<UserControl x:Class="Pollbook.UserControls.ButtonSaveWithShadow"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:Pollbook.UserControls"
             mc:Ignorable="d" 
              d:DesignHeight="100" d:DesignWidth="200">
    <UserControl.Resources>
        <Style x:Key="RoundButtonTemplate" TargetType="Button">
            <Setter Property="Background" Value="#0091c0"/>
            <Setter Property="Foreground" Value="White" />
            <Setter Property="HorizontalContentAlignment" Value="Center"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="Padding" Value="1"/>
            <Setter Property="FontFamily" Value="Noto Sans Display" />
            <Setter Property="FontSize" Value="30px" />
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border CornerRadius="2" Background="{TemplateBinding Background}"
                                BorderThickness="1">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center">

                            </ContentPresenter>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <Grid>

        <Button Focusable="False"
                local:ButtonHelper.DisableMultipleClicks="True"
                Style="{StaticResource RoundButtonTemplate}"
                Width="179px"
                Height="80px"
                Margin="0,0,0,0"
                HorizontalAlignment="Center"
                BorderBrush="#FFF"
                Name="PasswordButton"
                Content="Save"
       >
            <Button.BitmapEffect>
                <DropShadowBitmapEffect Color="Black" Direction="320" Softness="1" ShadowDepth="10" Opacity="0.5" />
            </Button.BitmapEffect>
        </Button>

    </Grid>
</UserControl>
