<UserControl x:Class="Pollbook.UserControls.Button.ButtonWifiSetup"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:Pollbook.UserControls.Button"
             xmlns:core="clr-namespace:Pollbook.UICore"
             xmlns:userControls="clr-namespace:Pollbook.UserControls"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">

    <UserControl.Resources>
        <SolidColorBrush x:Key="Signal.Foreground" Color="White" Opacity="1.0"/>
        <SolidColorBrush x:Key="Signal.Background" Color="LightGray" Opacity="0.25"/>

        <!-- SignalProgressBar -->
        <Style x:Key="ArcStyle" TargetType="{x:Type core:SignalProgressBar}">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type core:SignalProgressBar}">
                        <Grid Width="36" Height="36">
                            <Path x:Name="PART_LevelOne"
							  Stroke="DarkGray" StrokeThickness="5">
                                <Path.Data>
                                    <PathGeometry>
                                        <PathFigure StartPoint="32,36">
                                            <ArcSegment Point="36,32"
													Size="8,8"
													IsLargeArc="False" SweepDirection="Clockwise"/>
                                        </PathFigure>
                                    </PathGeometry>
                                </Path.Data>
                            </Path>

                            <Path x:Name="PART_LevelTwo"
							  Stroke="DarkGray" StrokeThickness="2.5">
                                <Path.Data>
                                    <PathGeometry>
                                        <PathFigure StartPoint="24,36">
                                            <ArcSegment Point="36,24"
													Size="12,12"
													IsLargeArc="False" SweepDirection="Clockwise"/>
                                        </PathFigure>
                                    </PathGeometry>
                                </Path.Data>
                            </Path>

                            <Path x:Name="PART_LevelThree"
							  Stroke="DarkGray" StrokeThickness="2.5">
                                <Path.Data>
                                    <PathGeometry>
                                        <PathFigure StartPoint="16,36">
                                            <ArcSegment Point="36,16"
													Size="20,20"
													IsLargeArc="False" SweepDirection="Clockwise"/>
                                        </PathFigure>
                                    </PathGeometry>
                                </Path.Data>
                            </Path>

                            <Path x:Name="PART_LevelFour"
							  Stroke="DarkGray" StrokeThickness="2.5">
                                <Path.Data>
                                    <PathGeometry>
                                        <PathFigure StartPoint="8,36">
                                            <ArcSegment Point="36,8"
													Size="28,28"
													IsLargeArc="False" SweepDirection="Clockwise"/>
                                        </PathFigure>
                                    </PathGeometry>
                                </Path.Data>
                            </Path>

                            <Path x:Name="PART_LevelFive"
							  Stroke="DarkGray" StrokeThickness="2.5">
                                <Path.Data>
                                    <PathGeometry>
                                        <PathFigure StartPoint="0,36">
                                            <ArcSegment Point="36,0"
													Size="36,36"
                                                        RotationAngle="45"
													IsLargeArc="False" SweepDirection="Clockwise"/>
                                        </PathFigure>
                                    </PathGeometry>
                                </Path.Data>
                            </Path>
                        </Grid>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Border Background="Transparent" CornerRadius="5">
            <Button Height="101px"
                    Width="438px"
                    Margin="20,0,0,0"
                    userControls:ButtonHelper.DisableMultipleClicks="True"
                    Command="{Binding RelativeSource={RelativeSource AncestorType={x:Type local:ButtonWifiSetup}}, Path=Command}">
                <Button.Template>
                    <ControlTemplate>
                        <StackPanel Orientation="Horizontal">
                            <!-- Signal Graphic -->
                            <core:SignalProgressBar
                                                Margin="0,0,0,0" VerticalAlignment="Center" 
                                                Background="{StaticResource Signal.Background}" 
                                                Foreground="{StaticResource SecondaryMediumBlueBrush}" 
                                                Value="{Binding RelativeSource={RelativeSource AncestorType={x:Type local:ButtonWifiSetup}}, Path=SignalQuality}"
                                                Style="{StaticResource ArcStyle}" />
                            <StackPanel Orientation="Vertical" VerticalAlignment="Center">
                                <TextBlock x:Name="tTitle" Margin="20,0,0,0">
                                    <TextBlock.Style>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="FontFamily" Value="Noto Sans Display"/>
                                            <Setter Property="FontSize" Value="30"/>
                                            <Setter Property="Foreground" Value="{StaticResource SecondaryMediumBlueBrush}"/>
                                            <Setter Property="FontWeight" Value="Bold" />
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding RelativeSource={RelativeSource AncestorType={x:Type local:ButtonWifiSetup}}, Path=Connected}" Value="false">
                                                    <Setter Property="Text" Value="{Binding RelativeSource={RelativeSource AncestorType={x:Type local:ButtonWifiSetup}}, Path=DefaultText}" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding RelativeSource={RelativeSource AncestorType={x:Type local:ButtonWifiSetup}}, Path=Connected}" Value="true">
                                                    <Setter Property="Text" Value="{Binding RelativeSource={RelativeSource AncestorType={x:Type local:ButtonWifiSetup}}, Path=ProfileName}" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </TextBlock.Style>
                                </TextBlock>
                                <TextBlock x:Name="tDescription" Margin="20,0,0,0">
                                    <TextBlock.Style>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="FontFamily" Value="Noto Sans Display"/>
                                            <Setter Property="FontSize" Value="24"/>
                                            <Setter Property="Foreground" Value="{StaticResource SecondaryMediumBlueBrush}"/>
                                            <Setter Property="FontWeight" Value="Bold" />
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding RelativeSource={RelativeSource AncestorType={x:Type local:ButtonWifiSetup}}, Path=Connected}" Value="false">
                                                    <Setter Property="Visibility" Value="Collapsed" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding RelativeSource={RelativeSource AncestorType={x:Type local:ButtonWifiSetup}}, Path=Connected}" Value="true">
                                                    <Setter Property="Visibility" Value="Visible" />
                                                    <Setter Property="Text" Value="{Binding RelativeSource={RelativeSource AncestorType={x:Type local:ButtonWifiSetup}}, Path=Description}" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </TextBlock.Style>
                                </TextBlock>
                            </StackPanel>
                        </StackPanel>
                    </ControlTemplate>
                </Button.Template>
            </Button>
        </Border>
    </Grid>
</UserControl>
