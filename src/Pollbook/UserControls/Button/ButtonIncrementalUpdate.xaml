<UserControl x:Class="Pollbook.UserControls.ButtonIncrementalUpdate"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:Pollbook.UICore"
             xmlns:userControls="clr-namespace:Pollbook.UserControls"
             mc:Ignorable="d" 
             d:DesignHeight="110" Width="Auto">
    <UserControl.Resources>
        <ResourceDictionary>
            <local:BooleanToVisibilityConverter x:Key="CalloutVisibilityConverter" True="Visible" False="Collapsed" />
        </ResourceDictionary>
    </UserControl.Resources>
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="Auto" />
        </Grid.ColumnDefinitions>

        <!-- Smart updates -->
        <Button HorizontalAlignment="Left"
                VerticalAlignment="Top"
                userControls:ButtonHelper.DisableMultipleClicks="True"
                Command="{Binding IncrementalUpdateCommand}"
                IsEnabled="{Binding IsHostDisconnected}"
                Width="300"
                BorderBrush="{x:Null}"
                Background="{x:Null}">
            <Button.Template>
                <ControlTemplate TargetType="Button">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="10"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <ContentPresenter Grid.Column="2" />
                    </Grid>
                </ControlTemplate>
            </Button.Template>
            <TextBlock Text="{Binding IncrementalUpdatesLabel}" Foreground="#152025" FontSize="30px" Height="Auto" Margin="-154,0,-41,0" HorizontalAlignment="Left" Width="Auto">
                <TextBlock.Style>
                    <Style TargetType="TextBlock" BasedOn="{StaticResource BoldTextBlockWithHeight}" />
                </TextBlock.Style>
            </TextBlock>
        </Button>
        <Label  Foreground="#55636b" Content="{Binding LastUpdatedStatus}"
                   VerticalAlignment="Top" Width="Auto" HorizontalContentAlignment="Left" FontSize="24px" HorizontalAlignment="Left" Height="59" Margin="0,40,0,0" />
    </Grid>
</UserControl>
