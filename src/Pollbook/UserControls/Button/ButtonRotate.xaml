<UserControl x:Class="Pollbook.UserControls.ButtonRotate"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:Pollbook.UserControls"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">

    <UserControl.Resources>
        <Style x:Key="RotateButton" TargetType="{x:Type Button}">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type Button}">
                        <Border Name="Border" CornerRadius="2" BorderThickness="1" Background="Transparent" BorderBrush="Transparent">
                            <ContentPresenter Margin="2" 
                  HorizontalAlignment="Center"
                  VerticalAlignment="Center" />
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsKeyboardFocused" Value="true">
                                <Setter TargetName="Border" Property="BorderBrush" Value="Transparent" />
                            </Trigger>
                            <Trigger Property="IsDefaulted" Value="true">
                                <Setter TargetName="Border" Property="BorderBrush" Value="Transparent" />
                            </Trigger>
                            <Trigger Property="IsMouseOver" Value="true">
                                <Setter TargetName="Border" Property="Background" Value="Transparent" />
                            </Trigger>
                            <Trigger Property="IsPressed" Value="true">
                                <Setter TargetName="Border" Property="Background" Value="Transparent" />
                                <Setter TargetName="Border" Property="BorderBrush" Value="Transparent" />
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="false">
                                <Setter TargetName="Border" Property="Background" Value="Transparent" />
                                <Setter TargetName="Border" Property="BorderBrush" Value="Transparent" />
                                <Setter Property="Foreground" Value="Transparent"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

    </UserControl.Resources>

    <Grid>
        <Button Click="RotateSignature_Click"
                local:ButtonHelper.DisableMultipleClicks="True"
                Width="150" Height="150"
                BorderBrush="Transparent"
                Background="Transparent"
                VerticalAlignment="Top"
                HorizontalAlignment="Right"
                Style="{StaticResource RotateButton}" >
            <StackPanel Orientation="Vertical" HorizontalAlignment="Center" VerticalAlignment="Center" Width="150">
                <Viewbox Stretch="Uniform" VerticalAlignment="Center" HorizontalAlignment="Center" Width="50" Margin="0">
                    <Path>
                        <Path.Style>
                            <Style TargetType="{x:Type Path}" BasedOn="{StaticResource FlipScreenIcon}" />
                        </Path.Style>
                    </Path>
                </Viewbox>
                <Label Content="Rotate Signature" HorizontalAlignment="Center" Margin="0" Style="{StaticResource RotateButtonFont}" />
            </StackPanel>
        </Button>
    </Grid>
</UserControl>
