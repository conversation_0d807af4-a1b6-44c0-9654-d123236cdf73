using ESS.Pollbook.ViewModel.Utils;
using System.Windows;
using System.Windows.Controls;

namespace Pollbook.UserControls
{
    /// <summary>
    /// Interaction logic for LanguageSelector.xaml
    /// </summary>
    public partial class LanguageSelector : UserControl
    {
        public LanguageSelector()
        {
            InitializeComponent();
        }

        private async void LanguageSelector_OnLoaded(object sender, RoutedEventArgs e)
        {
            await ((LanguagesViewModel)DataContext).ControlOnLoadedAsync();
        }
    }


}
