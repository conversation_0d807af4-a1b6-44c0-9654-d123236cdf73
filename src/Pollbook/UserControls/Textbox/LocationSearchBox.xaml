<UserControl x:Class="Pollbook.UserControls.LocationSearchBox"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:i="clr-namespace:System.Windows.Interactivity;assembly=System.Windows.Interactivity" 
             xmlns:local="clr-namespace:Pollbook.UserControls"
             mc:Ignorable="d" 
             x:Name="Control"
             d:DesignHeight="106" d:DesignWidth="510"
             MinHeight="106" MinWidth="510">
    <Border CornerRadius="8" BorderThickness="4" ClipToBounds="False">
        <Grid>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="auto" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="auto" />
                </Grid.ColumnDefinitions>

                <Viewbox Grid.Column="0" Stretch="Uniform" VerticalAlignment="Center" HorizontalAlignment="Center" Width="36" Margin="31,0,23,0">
                    <Path>
                        <Path.Style>
                            <Style TargetType="{x:Type Path}" BasedOn="{StaticResource LocationIcon}">
                                <Style.Setters>
                                    <Setter Property="Fill" Value="{StaticResource PrimaryBlueBrush}" />
                                </Style.Setters>
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding Text.Length, ElementName=txtSearchBox}" Value="0">
                                        <Setter Property="Fill" Value="{StaticResource Gray2Brush}" />
                                    </DataTrigger>
                                    <DataTrigger Binding="{Binding IsFocused, ElementName=txtSearchBox}" Value="True">
                                        <Setter Property="Fill" Value="{StaticResource PrimaryBlueBrush}" />
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </Path.Style>
                    </Path>
                </Viewbox>
                <Grid Grid.Column="1">
                    <!-- Input -->
                    <TextBlock x:Name="lblInstructions" Text="Tap here to type...">
                        <TextBlock.Style>
                            <Style TargetType="{x:Type TextBlock}" BasedOn="{StaticResource InstructionalTextBlock}">
                                <Style.Setters>
                                    <Setter Property="Background" Value="Transparent" />
                                    <Setter Property="Margin" Value="0,10,10,10" />
                                    <Setter Property="VerticalAlignment" Value="Center" />
                                    <Setter Property="Visibility" Value="Hidden" />
                                </Style.Setters>
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding Text.Length, ElementName=txtSearchBox}" Value="0">
                                        <Setter Property="Visibility" Value="Visible"/>
                                    </DataTrigger>
                                    <DataTrigger Binding="{Binding IsFocused, ElementName=txtSearchBox}" Value="True">
                                        <Setter Property="Visibility" Value="Hidden"/>
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </TextBlock.Style>
                    </TextBlock>
                    <TextBox x:Name="txtSearchBox" Text="{Binding RelativeSource={RelativeSource AncestorType={x:Type local:LocationSearchBox}}, Path=Text, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}">
                        <TextBox.Style>
                            <Style TargetType="{x:Type TextBox}">
                                <Style.Setters>
                                    <Setter Property="FontFamily" Value="Noto Sans Display" />
                                    <Setter Property="FontSize" Value="30" />
                                    <Setter Property="Foreground" Value="{StaticResource Gray1Brush}" />
                                    <Setter Property="Background" Value="Transparent" />
                                    <Setter Property="BorderBrush" Value="Transparent" />
                                    <Setter Property="BorderThickness" Value="0" />
                                    <Setter Property="Margin" Value="0,10,10,10" />
                                    <Setter Property="VerticalAlignment" Value="Center" />
                                    <Setter Property="HorizontalAlignment" Value="Stretch" />
                                    <Setter Property="Cursor" Value="Hand" />
                                </Style.Setters>
                                <Style.Triggers>
                                    <Trigger Property="IsFocused" Value="True">
                                        <Setter Property="Cursor" Value="Arrow" />
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </TextBox.Style>
                    </TextBox>
                </Grid>
                <Button Grid.Column="2" Content="Clear" Click="Clear_Click" Margin="20,30,46,30">
                    <Button.Style>
                        <Style TargetType="{x:Type Button}">
                            <Style.Setters>
                                <Setter Property="FontFamily" Value="Noto Sans Display" />
                                <Setter Property="FontSize" Value="30" />
                                <Setter Property="Foreground" Value="{StaticResource SecondaryDarkBlueBrush}" />
                                <Setter Property="Background" Value="Transparent" />
                                <Setter Property="BorderBrush" Value="Transparent" />
                                <Setter Property="BorderThickness" Value="0" />
                            </Style.Setters>
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding Text.Length, ElementName=txtSearchBox}" Value="0">
                                    <Setter Property="Visibility" Value="Hidden"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </Button.Style>
                </Button>
            </Grid>
            
            <Popup Name="Popup" Placement="Bottom" IsOpen="True" Panel.ZIndex="-1" AllowsTransparency="True" Focusable="False" PopupAnimation="Slide">
                <Grid Name="DropDown" Background="Transparent" MinWidth="{Binding RelativeSource={RelativeSource AncestorType={x:Type local:LocationSearchBox}}, Path=ActualWidth}">
                    <Border x:Name="DropDownBorder" Background="White" CornerRadius="0,0,10,10" BorderThickness="0" BorderBrush="Transparent" Margin="0,0,8,0">
                        <Border.Effect>
                            <DropShadowEffect BlurRadius="30" Opacity="0.25"></DropShadowEffect>
                        </Border.Effect>
                    </Border>
                    <ScrollViewer Margin="4,6,4,6" SnapsToDevicePixels="True" CanContentScroll="False" VerticalScrollBarVisibility="Hidden">
                        
                        <ListView x:Name="lvwLocations" Grid.Row="1" ItemsSource="{Binding Locations}" SelectionMode="Single" BorderBrush="Transparent" Padding="0" Margin="0" BorderThickness="0" HorizontalContentAlignment="Stretch" Grid.IsSharedSizeScope="True">
                            <!-- Override to remove padding -->
                            <ListView.ItemContainerStyle>
                                <Style TargetType="ListViewItem">
                                    <Setter Property="BorderBrush" Value="Transparent" />
                                    <Setter Property="BorderThickness" Value="0" />
                                    <Setter Property="Padding" Value="0,5,0,0" />
                                </Style>
                            </ListView.ItemContainerStyle>
                            <!-- Override to remove padding -->
                            <ListView.Template>
                                <ControlTemplate TargetType="ListView">
                                    <Border CornerRadius="2">
                                        <StackPanel Margin="0" IsItemsHost="True" />
                                    </Border>
                                </ControlTemplate>
                            </ListView.Template>
                            <!-- Item Template -->
                            <ListView.ItemTemplate>
                                <DataTemplate>
                                    <DataTemplate.Triggers>
                                        <Trigger Property="Content" Value="{x:Null}">
                                            <Setter Property="Visibility" Value="Collapsed" />
                                        </Trigger>
                                    </DataTemplate.Triggers>
                                    <Border Height="132">
                                        <Grid Margin="10">
                                            <TextBlock Text="{Binding PollingPlaceName}" />
                                        </Grid>
                                    </Border>
                                </DataTemplate>
                            </ListView.ItemTemplate>
                            
                            <i:Interaction.Triggers>
                                <i:EventTrigger EventName="SelectionChanged">
                                    <i:InvokeCommandAction Command="{Binding SelectLocationCommand}" CommandParameter="{Binding ElementName=lvwLocations, Path=SelectedItem}"></i:InvokeCommandAction>
                                </i:EventTrigger>
                            </i:Interaction.Triggers>
                        </ListView>
                        
                    </ScrollViewer>
                </Grid>
            </Popup>
        </Grid>

        <Border.Style>
            <Style TargetType="{x:Type Border}">
                <Style.Setters>
                    <Setter Property="Background" Value="White" />
                    <Setter Property="BorderBrush" Value="{StaticResource PrimaryBlueBrush}" />
                </Style.Setters>
                <Style.Triggers>
                    <DataTrigger Binding="{Binding Text.Length, ElementName=txtSearchBox}" Value="0">
                        <Setter Property="Background" Value="{StaticResource Gray6Brush}"/>
                        <Setter Property="BorderBrush" Value="{StaticResource Gray6Brush}" />
                    </DataTrigger>
                    <DataTrigger Binding="{Binding IsFocused, ElementName=txtSearchBox}" Value="True">
                        <Setter Property="Background" Value="White"/>
                        <Setter Property="BorderBrush" Value="{StaticResource PrimaryBlueBrush}" />
                    </DataTrigger>
                </Style.Triggers>
            </Style>
        </Border.Style>
    </Border>
</UserControl>
