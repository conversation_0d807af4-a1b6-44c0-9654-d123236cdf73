using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;

namespace Pollbook.UserControls
{
    /// <summary>
    /// Interaction logic for PollPlaceSearchBox.xaml
    /// </summary>
    public partial class PollPlaceSearchBox : UserControl
    {
        public ICommand ClearCommand
        {
            get { return (ICommand)GetValue(ClearCommandProperty); }
            set { SetValue(ClearCommandProperty, value); }
        }

        public static readonly DependencyProperty ClearCommandProperty =
            DependencyProperty.Register("ClearCommand", typeof(ICommand), typeof(PollPlaceSearchBox));

        public string Text
        {
            get { return (string)GetValue(TextProperty); }
            set { SetValue(TextProperty, value); }
        }

        public static readonly DependencyProperty TextProperty =
            DependencyProperty.Register("Text", typeof(string), typeof(PollPlaceSearchBox), new FrameworkPropertyMetadata(string.Empty, FrameworkPropertyMetadataOptions.AffectsRender) { BindsTwoWayByDefault = true });


        public bool NoResults
        {
            get { return (bool)GetValue(NoResultsProperty); }
            set { SetValue(NoResultsProperty, value); }
        }

        public static readonly DependencyProperty NoResultsProperty =
            DependencyProperty.Register("NoResults", typeof(bool), typeof(PollPlaceSearchBox), new FrameworkPropertyMetadata(false, FrameworkPropertyMetadataOptions.AffectsRender));

        public PollPlaceSearchBox()
        {
            InitializeComponent();

            GotFocus += PollPlaceSearchBox_GotFocus;

            txtSearchBox.SelectionChanged += (sender, e) => MoveCustomCaret(txtSearchBox, Caret);
            txtSearchBox.LostFocus += (sender, e) => Caret.Visibility = Visibility.Collapsed;
            txtSearchBox.GotFocus += (sender, e) => Caret.Visibility = Visibility.Visible;
        }

        private void PollPlaceSearchBox_GotFocus(object sender, RoutedEventArgs e)
        {
            FocusHelper.Focus(txtSearchBox);
        }

        private void MoveCustomCaret(TextBox textBox, Border caret)
        {
            var caretLocation = textBox.GetRectFromCharacterIndex(textBox.CaretIndex).Location;
            var caretHeight = textBox.GetRectFromCharacterIndex(textBox.CaretIndex).Height;

            // Offset needed to position the caret in the text box. Without this, the cursor is rendered too high, don't know why.
            const double caretYOffset = 29.0;

            if (!double.IsInfinity(caretLocation.X))
            {
                Canvas.SetLeft(caret, caretLocation.X + textBox.Margin.Left);
            }
            if (!double.IsInfinity(caretLocation.Y))
            {
                Canvas.SetTop(caret, caretLocation.Y + caretYOffset);
                //Canvas.SetTop(Caret, caretLocation.Y + textBox.Margin.Top);
                if (!double.IsInfinity(caretHeight))
                {
                    caret.Height = caretHeight;
                }
            }
        }
    }
}
