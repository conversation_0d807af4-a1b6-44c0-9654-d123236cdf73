<UserControl x:Class="Pollbook.UserControls.AddressSearchBox"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:Pollbook.UserControls"
             xmlns:uicore="clr-namespace:Pollbook.UICore"
             mc:Ignorable="d"
             d:DesignHeight="106" d:DesignWidth="510"
             MinHeight="106" MinWidth="510">
    <Border CornerRadius="3" BorderThickness="3">
        <Border.Style>
            <Style TargetType="{x:Type Border}">
                <Style.Setters>
                    <Setter Property="Background" Value="White" />
                    <Setter Property="BorderBrush" Value="{StaticResource Gray4Brush}" />
                </Style.Setters>
                <Style.Triggers>
                    <DataTrigger Binding="{Binding Text.Length, ElementName=txtSearchBox}" Value="0">
                        <Setter Property="BorderBrush" Value="{StaticResource Gray4Brush}" />
                    </DataTrigger>
                    <DataTrigger Binding="{Binding IsFocused, ElementName=txtSearchBox}" Value="True">
                        <Setter Property="BorderBrush" Value="{StaticResource BluetifulBrush}" />
                    </DataTrigger>
                    <DataTrigger Binding="{Binding IsEnabled, ElementName=txtSearchBox}" Value="False">
                        <Setter Property="Background" Value="{StaticResource Gray7Brush}"/>
                    </DataTrigger>
                </Style.Triggers>
            </Style>
        </Border.Style>
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="auto" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="auto" />
            </Grid.ColumnDefinitions>
            <TextBlock x:Name="lblInstructions" Grid.Column="1" Text="">
                <TextBlock.Style>
                    <Style TargetType="{x:Type TextBlock}" BasedOn="{StaticResource InstructionalBoldTextBlock}">
                        <Style.Setters>
                            <Setter Property="Background" Value="Transparent" />
                            <Setter Property="Margin" Value="38,7,10,10" />
                            <Setter Property="VerticalAlignment" Value="Center" />
                            <Setter Property="Visibility" Value="Hidden" />
                        </Style.Setters>
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding Text.Length, ElementName=txtSearchBox}" Value="0">
                                <Setter Property="Visibility" Value="Visible"/>
                            </DataTrigger>
                            <DataTrigger Binding="{Binding IsFocused, ElementName=txtSearchBox}" Value="True">
                                <Setter Property="Visibility" Value="Hidden"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </TextBlock.Style>
            </TextBlock>
            <Grid Grid.Column="1">
                <uicore:KBTextBox x:Name="txtSearchBox" CaretBrush="Transparent" Text="{Binding RelativeSource={RelativeSource AncestorType={x:Type local:AddressSearchBox}}, Path=Text, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}">
                    <uicore:KBTextBox.Style>
                        <Style TargetType="{x:Type uicore:KBTextBox}" BasedOn="{StaticResource Body1BoldFont}">
                            <Style.Setters>
                                <Setter Property="Background" Value="Transparent" />
                                <Setter Property="BorderBrush" Value="Transparent" />
                                <Setter Property="BorderThickness" Value="0" />
                                <Setter Property="Margin" Value="38,0,10,0" />
                                <Setter Property="VerticalAlignment" Value="Center" />
                                <Setter Property="HorizontalAlignment" Value="Stretch" />
                                <Setter Property="Cursor" Value="Hand" />
                            </Style.Setters>
                            <Style.Triggers>
                                <Trigger Property="IsFocused" Value="True">
                                    <Setter Property="Cursor" Value="Arrow" />
                                </Trigger>
                                <Trigger Property="IsEnabled" Value="False">
                                    <Setter Property="Foreground" Value="{StaticResource Gray5Brush}"/>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </uicore:KBTextBox.Style>
                </uicore:KBTextBox>
                <Canvas>
                    <Border x:Name="Caret" Visibility="Collapsed" Canvas.Left="0" Canvas.Top="0" Width="2" Background="Black" >
                        <Border.Triggers>
                            <EventTrigger RoutedEvent="Border.Loaded">
                                <BeginStoryboard>
                                    <Storyboard x:Name="CaretStoryBoard" RepeatBehavior="Forever">
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetProperty="Background.Color" Duration="0:0:0:1" FillBehavior="HoldEnd">
                                            <ColorAnimationUsingKeyFrames.KeyFrames>
                                                <DiscreteColorKeyFrame KeyTime="0:0:0.500" Value="Transparent" />
                                                <DiscreteColorKeyFrame KeyTime="0:0:0.000" Value="Black" />
                                            </ColorAnimationUsingKeyFrames.KeyFrames>
                                        </ColorAnimationUsingKeyFrames>
                                    </Storyboard>
                                </BeginStoryboard>
                            </EventTrigger>
                        </Border.Triggers>
                    </Border>
                </Canvas>
            </Grid>
            <Button Grid.Column="2" Content="Clear" Command="{Binding RelativeSource={RelativeSource AncestorType={x:Type local:AddressSearchBox}}, Path=ClearCommand}" Focusable="False" Margin="20,22,46,20">
                <Button.Style>
                    <Style TargetType="{x:Type Button}" BasedOn="{StaticResource LinkLabel}">
                        <Style.Setters>
                            <Setter Property="Background" Value="Transparent" />
                            <Setter Property="BorderBrush" Value="Transparent" />
                            <Setter Property="BorderThickness" Value="0" />
                            <Setter Property="Height" Value="56" />
                        </Style.Setters>
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding Text.Length, ElementName=txtSearchBox}" Value="0">
                                <Setter Property="Visibility" Value="Collapsed"/>
                            </DataTrigger>
                            <DataTrigger Binding="{Binding IsEnabled, ElementName=txtSearchBox}" Value="False">
                                <Setter Property="Visibility" Value="Collapsed"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </Button.Style>

                <Button.Template>
                    <ControlTemplate TargetType="{x:Type Button}">
                        <Label Content="{TemplateBinding Content}" Background="{TemplateBinding Background}" Foreground="{TemplateBinding Foreground}" FontSize="{TemplateBinding FontSize}"
                               FontFamily="{TemplateBinding FontFamily}"/>
                    </ControlTemplate>
                </Button.Template>
            </Button>
        </Grid>
    </Border>
</UserControl>