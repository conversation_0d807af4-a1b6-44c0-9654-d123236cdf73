using CommonServiceLocator;
using ESS.Pollbook.ViewModel.Infrastructure;
using Pollbook.UserControls.Textbox;
using System;
using System.Linq;
using System.Text.RegularExpressions;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;

namespace Pollbook.UserControls
{
    /// <summary>
    /// Interaction logic for SearchBox.xaml
    /// </summary>
    public partial class SearchBox : UserControl
    {
        private readonly IKeyboardService _keyboardService;
        public string Text
        {
            get => (string)GetValue(TextProperty); 
            set =>  SetValue(TextProperty, value); 
        }

        public static readonly DependencyProperty TextProperty =
            DependencyProperty.Register(nameof(Text), typeof(string), typeof(SearchBox), new FrameworkPropertyMetadata(String.Empty, FrameworkPropertyMetadataOptions.AffectsRender) { BindsTwoWayByDefault = true });

        public string Value
        {
            get => (string)GetValue(ValueProperty); 
            set => SetValue(ValueProperty, value); 
        }

        public static readonly DependencyProperty ValueProperty =
            DependencyProperty.Register(nameof(Value), typeof(string), typeof(SearchBox), new FrameworkPropertyMetadata(String.Empty, FrameworkPropertyMetadataOptions.AffectsRender) { BindsTwoWayByDefault = true });

        public string Watermark
        {
            get => (string)GetValue(WatermarkProperty); 
            set => SetValue(WatermarkProperty, value); 
        }

        public static readonly DependencyProperty WatermarkProperty =
            DependencyProperty.Register(nameof(Watermark), typeof(string), typeof(SearchBox), new FrameworkPropertyMetadata(String.Empty));

        public string Mask
        {
            get => (string)GetValue(MaskProperty); 
            set => SetValue(MaskProperty, value); 
        }

        public static readonly DependencyProperty MaskProperty =
            DependencyProperty.Register(nameof(Mask), typeof(string), typeof(SearchBox), new FrameworkPropertyMetadata(String.Empty));

        public string PromptChar
        {
            get => (string)GetValue(PromptCharProperty); 
            set => SetValue(PromptCharProperty, value);
        }

        public static readonly DependencyProperty PromptCharProperty =
            DependencyProperty.Register(nameof(PromptChar), typeof(string), typeof(SearchBox), new FrameworkPropertyMetadata(String.Empty));
        
        public bool PlaceCursorToTheLeftOnFocus
        {
            get => (bool)GetValue(PlaceCursorToTheLeftOnFocusProperty); 
            set => SetValue(PlaceCursorToTheLeftOnFocusProperty, value); 
        }

        public static readonly DependencyProperty PlaceCursorToTheLeftOnFocusProperty =
            DependencyProperty.Register(nameof(PlaceCursorToTheLeftOnFocus), typeof(bool), typeof(SearchBox), new FrameworkPropertyMetadata(false));

        public bool TakeKeyboardFocusOnLoad
        {
            get => (bool)GetValue(TakeKeyboardFocusOnLoadProperty); 
            set => SetValue(TakeKeyboardFocusOnLoadProperty, value); 
        }

        public static readonly DependencyProperty TakeKeyboardFocusOnLoadProperty =
            DependencyProperty.Register(nameof(TakeKeyboardFocusOnLoad), typeof(bool), typeof(SearchBox), new FrameworkPropertyMetadata(false));

        public bool DefaultToNumeric
        {
            get => (bool)GetValue(DefaultToNumericProperty); 
            set => SetValue(DefaultToNumericProperty, value); 
        }

        public static readonly DependencyProperty DefaultToNumericProperty =
            DependencyProperty.Register(nameof(DefaultToNumeric), typeof(bool), typeof(SearchBox), new FrameworkPropertyMetadata(false));

        public string AdditionalCharacters
        {
            get => (string)GetValue(AdditionalCharactersToAllowProperty);
            set => SetValue(AdditionalCharactersToAllowProperty, value);
        }

        public static readonly DependencyProperty AdditionalCharactersToAllowProperty =
            DependencyProperty.Register(nameof(AdditionalCharacters), typeof(string), typeof(SearchBox), new FrameworkPropertyMetadata(string.Empty));

        public SearchBox()
        {
            InitializeComponent();
            _keyboardService = ServiceLocator.Current.GetInstance<IKeyboardService>();
        }

        private bool _clickedToFocus;

        private void MaskedItem_PreviewMouseDown(object sender, MouseButtonEventArgs e)
        {
            TextBox tb = (TextBox)sender;

            if (!tb.IsFocused)
            {
                _clickedToFocus = true;
            }
            else
            {
                _clickedToFocus = false;
            }
        }

        private void MaskedItem_PreviewMouseUp(object sender, MouseButtonEventArgs e)
        {
            if (PlaceCursorToTheLeftOnFocus && ContainsOnlyMaskCharacters(MaskedItem.Text))
            {
                MaskedItem.CaretIndex = 0;
                return;
            }

            TextBox tb = (TextBox)sender;
                        
            if (_clickedToFocus
                && tb.SelectedText.Length == 0)
            {
                MaskedItem.CaretIndex = FindPosition();
            }
        }

        private bool ContainsOnlyMaskCharacters(string text)
        {
            return text.ToCharArray().All(c => (c == ' ' || c == '-'));
        }

        private int FindPosition()
        {
            if (String.IsNullOrEmpty(MaskedItem.Text))
            {
                return 0;
            }

            if (String.IsNullOrEmpty(Mask))
            {
                return MaskedItem.Text.Length;
            }

            char[] maskArray = Regex.Replace(Mask, @"[\d]", "#").ToCharArray();
            char[] cleanedMaskArray = Regex.Replace(Mask, @"[\d ]", "#").Distinct().ToArray();
            string character;
            int testCast = -1;

            for (int index = 0; index < MaskedItem.Text.Length; index++)
            {
                character = MaskedItem.Text[index].ToString();

                if (maskArray[index] == character[0]
                    || cleanedMaskArray.Contains(character[0]))
                {
                    continue;
                }

                if (!Int32.TryParse(character, out testCast))
                {
                    return index;
                }
            }

            return 0;
        }

        // We have to have this flag because the Loaded event is called again when the page is un-loaded, for reasons known only to Microsoft.
        private bool _loaded = false;
        private void SearchBox_OnLoaded(object sender, RoutedEventArgs e)
        {
            if (!_loaded)
            {
                MaskedItem.SelectionChanged += SelectionChangedHandler;
                MaskedItem.LostFocus += (lostFocusSender, lostFocuse) => Caret.Visibility = Visibility.Collapsed;
                MaskedItem.GotFocus += (gotFocusSender, gotFocuse) => Caret.Visibility = Visibility.Visible;
                MaskedItem.DefaultToNumeric = DefaultToNumeric;

                if (TakeKeyboardFocusOnLoad)
                {
                    MaskedItem.Focusable = true;
                    MaskedItem.Focus();
                    _keyboardService.TextActivated((FrameworkElement)sender, allowPageToMoveUp: false);
                }
            }
            _loaded = true;
        }

        private void Control_Unloaded(object sender, RoutedEventArgs e)
        {
            MaskedItem.SelectionChanged -= SelectionChangedHandler;
        }

        void SelectionChangedHandler(object sender, RoutedEventArgs e)
        {
            TextboxUserControlHelper.MoveCustomCaret(MaskedItem, Caret);
        }
    }
}
