<UserControl x:Class="Pollbook.UserControls.Textbox.PasswordBoxWithCaret"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:Pollbook.UserControls.Textbox"             
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">

    <UserControl.Resources>
        <!-- Define the FocusVisualStyle as a Style for Control -->
        <Style x:Key="TransparentFocusVisualStyle" TargetType="Control">
            <Setter Property="Control.Template">
                <Setter.Value>
                    <ControlTemplate>
                        <Border Background="Transparent" BorderBrush="Transparent" BorderThickness="1">
                            <Rectangle Stroke="{DynamicResource {x:Static SystemColors.HighlightBrushKey}}" StrokeThickness="1"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        <!-- Custom Button Style to Remove Highlight -->
        <Style x:Key="NoHighlightButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderBrush" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="FocusVisualStyle" Value="{StaticResource TransparentFocusVisualStyle}"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- PasswordBox -->
        <Grid x:Name="PasswordBoxContainer" Grid.Row="0">
            <PasswordBox x:Name="txtPassword"
                 CaretBrush="Transparent"
                 Padding="0,0,90,0"
                 PasswordChanged="Password_Changed" 
                 Validation.ErrorTemplate="{StaticResource ValidationErrorTemplate}" 
                 PreviewMouseDown="PasswordBox_PreviewTouch" 
                 PreviewTouchDown="PasswordBox_PreviewTouch" 
                 Stylus.IsPressAndHoldEnabled="False" 
                 Loaded="txtPassword_Loaded"
                 Foreground="{StaticResource Gray1Brush}">
                <PasswordBox.Style>
                    <Style TargetType="PasswordBox" BasedOn="{StaticResource StandardPasswordBox}">
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding IsAuthenticationValid}" Value="False">
                                <Setter Property="BorderBrush" Value="{StaticResource SecondaryDarkRedBrush}" />
                            </DataTrigger>
                            <DataTrigger Binding="{Binding IsAuthenticationValid}" Value="Incorrect">
                                <Setter Property="BorderBrush" Value="{StaticResource SecondaryDarkRedBrush}" />
                            </DataTrigger>
                            <DataTrigger Binding="{Binding IsAuthenticationValid}" Value="Mismatched">
                                <Setter Property="BorderBrush" Value="{StaticResource SecondaryDarkRedBrush}" />
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </PasswordBox.Style>
            </PasswordBox>

            <!-- Show/Hide Password Textbox -->
            <TextBox x:Name="txtPlainText"
                 Visibility="Collapsed"
                 IsReadOnly="False"
                 Loaded="txtPlainText_Loaded"
                 CaretBrush="Transparent"
                 Padding="0,0,90,0"
                 PreviewMouseDown="PasswordBox_PreviewTouch"
                 PreviewTouchDown="PasswordBox_PreviewTouch" 
                 TextChanged="TxtPlainText_TextChanged">
                <TextBox.Style>
                    <Style TargetType="TextBox" BasedOn="{StaticResource StandardPassTextBox}">
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding IsAuthenticationValid}" Value="False">
                                <Setter Property="BorderBrush" Value="{StaticResource SecondaryDarkRedBrush}" />
                            </DataTrigger>
                            <DataTrigger Binding="{Binding IsAuthenticationValid}" Value="Incorrect">
                                <Setter Property="BorderBrush" Value="{StaticResource SecondaryDarkRedBrush}" />
                            </DataTrigger>
                            <DataTrigger Binding="{Binding IsAuthenticationValid}" Value="Mismatched">
                                <Setter Property="BorderBrush" Value="{StaticResource SecondaryDarkRedBrush}" />
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </TextBox.Style>
            </TextBox>

            <!-- Show/Hide Password Button -->
            <Button x:Name="ShowPasswordButton"
                Click="TogglePasswordButton_Click"
                VerticalAlignment="Center"
                HorizontalAlignment="Right"
                Margin="0,0,20,0"
                Background="Transparent" 
                Height="50"
                Width="50"
                BorderBrush="Transparent"
                Visibility="Visible"
                BorderThickness="0"
                FocusVisualStyle="{StaticResource TransparentFocusVisualStyle}"
                Style="{StaticResource NoHighlightButtonStyle}">
                <Button.Content>
                    <Viewbox Stretch="Uniform" VerticalAlignment="Center" HorizontalAlignment="Center" Width="50" Height="50">
                        <Path Stretch="Uniform" Fill="Black" Style="{StaticResource EyeSlashIcon}" />
                    </Viewbox>
                </Button.Content>
            </Button>

            <Button x:Name="HidePasswordButton"
                Click="TogglePasswordButton_Click"
                VerticalAlignment="Center"
                HorizontalAlignment="Right"
                Margin="0,0,20,0"
                Background="Transparent" 
                Height="50"
                Width="50"
                BorderBrush="Transparent"
                Visibility="Hidden"
                BorderThickness="0"
                FocusVisualStyle="{StaticResource TransparentFocusVisualStyle}"
                Style="{StaticResource NoHighlightButtonStyle}">
                <Button.Content>
                    <Viewbox Stretch="Uniform" VerticalAlignment="Center" HorizontalAlignment="Center" Width="45" Height="45">
                        <Path Stretch="Uniform" Fill="Black" Style="{StaticResource EyeIcon}" />
                    </Viewbox>
                </Button.Content>
            </Button>
        </Grid>

        <!-- Custom caret -->
        <Canvas>
            <Border x:Name="Caret" Visibility="Visible" Canvas.Left="0" Canvas.Top="0" Width="2" Background="Red" >
                <Border.Triggers>
                    <EventTrigger RoutedEvent="Border.Loaded">
                        <BeginStoryboard>
                            <Storyboard x:Name="CaretStoryBoard" RepeatBehavior="Forever">
                                <ColorAnimationUsingKeyFrames Storyboard.TargetProperty="Background.Color" Duration="0:0:0:1" FillBehavior="HoldEnd">
                                    <ColorAnimationUsingKeyFrames.KeyFrames>
                                        <DiscreteColorKeyFrame KeyTime="0:0:0.500" Value="Transparent" />
                                        <DiscreteColorKeyFrame KeyTime="0:0:0.000" Value="Black" />
                                    </ColorAnimationUsingKeyFrames.KeyFrames>
                                </ColorAnimationUsingKeyFrames>
                            </Storyboard>
                        </BeginStoryboard>
                    </EventTrigger>
                </Border.Triggers>
            </Border>
        </Canvas>
    </Grid>
</UserControl>
