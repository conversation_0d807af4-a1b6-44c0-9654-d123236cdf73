<UserControl x:Class="DesignGeneralControls.cmdSearchResultCntrl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:System="clr-namespace:System;assembly=mscorlib"
             xmlns:local="clr-namespace:DesignGeneralControls"
             mc:Ignorable="d" 
              d:DesignHeight="80" d:DesignWidth="200">
    <UserControl.Resources>
        <SolidColorBrush x:Key="TagBackground">#f4f8fa</SolidColorBrush>
        <SolidColorBrush x:Key="TagForeground">#152025</SolidColorBrush>
        <System:Double x:Key="Pixelspace">19</System:Double>
        <System:Double x:Key="fontsz">30</System:Double>
    </UserControl.Resources>
 
    <Grid Name="grd_Container"> 
        <Border CornerRadius="10,10,10,10" BorderBrush="{StaticResource TagBackground}" BorderThickness="3" VerticalAlignment="Center" HorizontalAlignment="Center">
            <DockPanel MouseDown="DockPanel_MouseDown">
                <TextBlock Name="btn_srchTxt" Foreground="{StaticResource TagForeground}"  Background="{StaticResource TagBackground}" HorizontalAlignment="Left" 
                   VerticalAlignment="Center" Margin="0,0,10,0" Height="36px" FontSize="{StaticResource fontsz}"  FontWeight="Bold" Text="Click me"  >
                </TextBlock> 
                
                <Image Source="..\SharedObjects\Images\close.png" VerticalAlignment="Center"  HorizontalAlignment="Left"    Width="24" Height="24" Stretch="None"/>
            </DockPanel>
        </Border>
    </Grid>
</UserControl>
