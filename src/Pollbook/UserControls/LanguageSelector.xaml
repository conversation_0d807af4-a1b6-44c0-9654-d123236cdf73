<UserControl x:Class="Pollbook.UserControls.LanguageSelector"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:Pollbook.UserControls"
             xmlns:uiCore="clr-namespace:Pollbook.UICore"
             mc:Ignorable="d" 
             DataContext="{Binding Languages, Source={StaticResource Locator}}"
             Loaded="LanguageSelector_OnLoaded"
             d:DesignHeight="450" d:DesignWidth="800">
    <UserControl.Resources>
        <uiCore:ZeroCollapsedNonZeroVisible x:Key="HideListBox" />
        <Style x:Key="ListBoxStyle" TargetType="ListBoxItem">
            <Setter Property="FontFamily" Value="Noto Sans Display" />
            <Setter Property="FontSize" Value="30" />
            <Setter Property="FontWeight" Value="Bold" />
            <Setter Property="Margin" Value="0,0,0,0" />
            <Setter Property="BorderThickness" Value="5" />
            <Setter Property="Visibility" Value="{Binding ElementName=LanguageListBox, Path=Items.Count, Converter={StaticResource HideListBox}}" />
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ListBoxItem">
                        <Border Name="Border">
                            <ContentPresenter />
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="ListBoxItem.IsSelected" Value="True">
                                <Setter Property="Foreground" Value="#0091c0" />
                            </Trigger>
                            <Trigger Property="ListBoxItem.IsSelected" Value="False">
                                <Setter Property="Foreground" Value="#55636b" />
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <ListBox Name="LanguageListBox" 
             ItemsSource="{Binding Languages}" 
             SelectedItem="{Binding SelectedItem}"
             ScrollViewer.VerticalScrollBarVisibility="Disabled" 
             ItemContainerStyle="{StaticResource ListBoxStyle}" 
             BorderThickness="0" 
             Background="Transparent">
        <ListBox.ItemsPanel>
            <ItemsPanelTemplate>
                <StackPanel IsItemsHost="True" Orientation="Horizontal" />
            </ItemsPanelTemplate>
        </ListBox.ItemsPanel>
        <ListBox.ItemTemplate>
            <DataTemplate>
                <StackPanel Orientation="Horizontal" Background="Transparent">
                    <Grid Background="#aebbc4" Height="36" Width="2" VerticalAlignment="Top" Margin="12,0,12,0">
                        <Grid.Style>
                            <Style TargetType="Grid">
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding RelativeSource={RelativeSource PreviousData}}" Value="{x:Null}">
                                        <Setter Property="Visibility" Value="Collapsed"/>
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </Grid.Style>
                    </Grid>
                    <TextBlock Text="{Binding}" Margin="5,0,5,0" Background="Transparent" />
                </StackPanel>
            </DataTemplate>
        </ListBox.ItemTemplate>
        
    </ListBox>
</UserControl>
