using System;
using System.Collections.Generic;
using System.Linq;
using ESS.Pollbook.Core.Interface.Navigation;
using ESS.Pollbook.Core.UICore;

namespace Pollbook.WorkFlow
{
    public class WorkFlowEngine : IWorkFlowEngine
    {
        public string GetRedirectPageName(string currentPage, string btnName)
        {
            var pageName = string.Empty;

            if (WorkFlowConstants.PageNavigationMapperCollection != null && WorkFlowConstants.PageNavigationMapperCollection.Count > 0)
            {
                var goToPageName = GetMappedCollectionByKey($"{currentPage}/{btnName}");
                if (goToPageName != null)
                {
                    pageName = goToPageName.ToPage;
                }
            }

            return pageName;
        }

        private PageNavigationMapper GetMappedCollectionByKey(string key)
        {
            WorkFlowConstants.PageNavigationMapperCollection.TryGetValue(key, out PageNavigationMapper routeCollection);
            return routeCollection;
        }

        public Type GetTypeByName(string className)
        {
            var returnVal = new List<Type>();

            foreach (var a in AppDomain.CurrentDomain.GetAssemblies())
            {
                var assemblyTypes = a.GetTypes();
                returnVal.AddRange(assemblyTypes.Where(t => t.Name == className));
            }

            return returnVal.FirstOrDefault();
        }
    }
}
