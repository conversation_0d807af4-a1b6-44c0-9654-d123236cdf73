using System;
using Autofac;
using ESS.Pollbook.Core.Infrastructure;
using ESS.Pollbook.ViewModel;
using ESS.Pollbook.ViewModel.Affidavit;
using ESS.Pollbook.ViewModel.Checklist;
using ESS.Pollbook.ViewModel.HelpCenter;
using ESS.Pollbook.ViewModel.IncrementalUpdates;
using ESS.Pollbook.ViewModel.InstantMessage;
using ESS.Pollbook.ViewModel.Maintenance;
using ESS.Pollbook.ViewModel.Password;
using ESS.Pollbook.ViewModel.PollPlaceDetails;
using ESS.Pollbook.ViewModel.PollPlaceSearch;
using ESS.Pollbook.ViewModel.Pollworker;
using ESS.Pollbook.ViewModel.Pollworker.Search;
using ESS.Pollbook.ViewModel.Pollworker.TimeCard;
using ESS.Pollbook.ViewModel.RegionalResults;
using ESS.Pollbook.ViewModel.Reports;
using ESS.Pollbook.ViewModel.SelectBallotType;
using ESS.Pollbook.ViewModel.SelectParty;
using ESS.Pollbook.ViewModel.SelectProvisionalReasonAndId;
using ESS.Pollbook.ViewModel.SelectReissueBallotReason;
using ESS.Pollbook.ViewModel.Supervisor;
using ESS.Pollbook.ViewModel.SurrenderMailInBallot;
using ESS.Pollbook.ViewModel.SystemControl;
using ESS.Pollbook.ViewModel.VerifyVoterId;
using ESS.Pollbook.ViewModel.Voter;
using ESS.Pollbook.ViewModel.VoterBallot;
using ESS.Pollbook.ViewModel.VoterDisabilityOath;
using ESS.Pollbook.ViewModel.VoterExport;
using ESS.Pollbook.ViewModel.VoterSearch;
using ESS.Pollbook.ViewModel.VoterSignature;
using ESS.Pollbook.ViewModel.VoterVerification;
using ESS.Pollbook.ViewModel.WaitTime;

namespace Pollbook
{
    public class NavigationModule : Module
    {
        protected override void Load(ContainerBuilder builder)
        {
            var nav = new FrameNavigationService();

            nav.Configure(typeof(AdvancedVoterSearchViewModel).FullName,
                new Uri("/Pages/VoterSearch/AdvancedVoterSearch.xaml", UriKind.Relative));
            nav.Configure(typeof(PollPlaceSearchViewModel).FullName,
                new Uri("/Pages/PollPlaceSearch/PollPlaceSearchView.xaml", UriKind.Relative));
            nav.Configure(typeof(ChangePollPlaceSearchViewModel).FullName,
                new Uri("/Pages/PollPlaceSearch/ChangePollPlaceSearchView.xaml", UriKind.Relative));
            nav.Configure(typeof(BatteryWarningViewModel).FullName,
                new Uri("/Pages/BatteryWarningView.xaml", UriKind.Relative));
            nav.Configure(typeof(VoterViewModel).FullName,
	            new Uri("/Pages/Voter/VoterView.xaml", UriKind.Relative));
            nav.Configure(typeof(VoterIssueBallotViewModel).FullName,
                new Uri("/Pages/VoterBallot/VoterIssueBallotView.xaml", UriKind.Relative));
            nav.Configure(typeof(LoginViewModel).FullName,
	            new Uri("/Pages/Login.xaml", UriKind.Relative));
            nav.Configure(typeof(DashboardViewModel).FullName,
	            new Uri("/Pages/Dashboard.xaml", UriKind.Relative));
            nav.Configure(typeof(VoterCheckInCompleteViewModel).FullName,
                new Uri("/Pages/VoterCheckInCompleteView.xaml", UriKind.Relative));
            nav.Configure(typeof(PrintingReceiptViewModel).FullName,
                new Uri("/Pages/PrintingReceiptView.xaml", UriKind.Relative));
            nav.Configure(typeof(PrintAuthDocFailedViewModel).FullName,
                new Uri("/Pages/PrintAuthDocFailedView.xaml", UriKind.Relative));
            nav.Configure(typeof(PrintBallotTotalsFailedViewModel).FullName,
                new Uri("/Pages/PrintBallotTotalsFailedView.xaml", UriKind.Relative));
            nav.Configure(typeof(PrintSpoiledBallotFailedViewModel).FullName,
                new Uri("/Pages/PrintSpoiledBallotFailedView.xaml", UriKind.Relative));
            nav.Configure(typeof(PrintVotedListFailedViewModel).FullName,
                new Uri("/Pages/PrintVotedListFailedView.xaml", UriKind.Relative));
            nav.Configure(typeof(PrintPollPlaceDetailsFailedViewModel).FullName,
                new Uri("/Pages/PrintPollPlaceDetailsFailedView.xaml", UriKind.Relative));
            nav.Configure(typeof(PrintBallotReissuedListFailedViewModel).FullName,
                new Uri("/Pages/PrintBallotReissuedListFailedView.xaml", UriKind.Relative));
            nav.Configure(typeof(PollPlaceDetailsViewModel).FullName,
                new Uri("/Pages/PollPlaceDetails/PollPlaceDetailsView.xaml", UriKind.Relative));
            nav.Configure(typeof(VoterSignatureViewModel).FullName,
                new Uri("/Pages/VoterSignature/VoterSignatureView.xaml", UriKind.Relative));
            nav.Configure(typeof(SelectSkipSignatureReasonViewModel).FullName,
                new Uri("/Pages/VoterSignature/SelectSkipSignatureReason.xaml", UriKind.Relative));
            nav.Configure(typeof(ConversationViewModel).FullName,
                new Uri("/Pages/InstantMessage/ConversationView.xaml", UriKind.Relative));
            nav.Configure(typeof(AlertsAndConversationsViewModel).FullName,
                new Uri("/Pages/InstantMessage/AlertsAndConversationsView.xaml", UriKind.Relative));
            nav.Configure(typeof(ComposeViewModel).FullName,
                new Uri("/Pages/InstantMessage/ComposeView.xaml", UriKind.Relative));
            nav.Configure(typeof(AlertViewModel).FullName,
                new Uri("/Pages/InstantMessage/AlertView.xaml", UriKind.Relative));
            nav.Configure(typeof(GenericAlertViewModel).FullName,
                new Uri("/Pages/GenericAlert.xaml", UriKind.Relative));
            nav.Configure(typeof(AlertDetailViewModel).FullName,
                new Uri("/Pages/InstantMessage/AlertDetailView.xaml", UriKind.Relative));
            nav.Configure(typeof(MonitorPollViewModel).FullName,
                new Uri("/Pages/MonitorPollView.xaml", UriKind.Relative));
            nav.Configure(typeof(FullVotedListReportViewModel).FullName,
                new Uri("/Pages/Reports/FullVotedListReport.xaml", UriKind.Relative));
            nav.Configure(typeof(BallotReissuedReportViewModel).FullName,
                new Uri("/Pages/Reports/BallotReissuedReport.xaml", UriKind.Relative));
            nav.Configure(typeof(BallotTotalsReportViewModel).FullName,
                new Uri("/Pages/Reports/BallotTotalsReport.xaml", UriKind.Relative));
            nav.Configure(typeof(SpoiledBallotReportViewModel).FullName,
                new Uri("/Pages/Reports/SpoiledBallotReport.xaml", UriKind.Relative));
            nav.Configure(typeof(SignatureComparisonViewModel).FullName,
	            new Uri("/Pages/VoterSignature/SignatureComparisonView.xaml", UriKind.Relative));
            nav.Configure(typeof(SelectPartyViewModel).FullName,
                new Uri("/Pages/SelectParty/SelectPartyView.xaml", UriKind.Relative));
            nav.Configure(typeof(CancelBallotViewModel).FullName,
                new Uri("/Pages/Voter/CancelBallotView.xaml", UriKind.Relative));
            nav.Configure(typeof(SignOutViewModel).FullName,
	            new Uri("/Pages/SignOutView.xaml", UriKind.Relative));
            nav.Configure(typeof(DualLogoutViewModel).FullName,
	            new Uri("/Pages/DualLogout.xaml", UriKind.Relative));
            nav.Configure(typeof(AddressChangedViewModel).FullName,
                new Uri("/Pages/AddressChangedView.xaml", UriKind.Relative));
            nav.Configure(typeof(VoterCannotBeEditedViewModel).FullName,
                new Uri("/Pages/VoterCannotBeEditedView.xaml", UriKind.Relative));
            nav.Configure(typeof(LoadElectionConfirmationViewModel).FullName,
                new Uri("/Pages/Maintenance/LoadElectionConfirmationView.xaml", UriKind.Relative));
            nav.Configure(typeof(WrongPollLocationViewModel).FullName,
                new Uri("/Pages/Voter/WrongPollLocationView.xaml", UriKind.Relative));
            nav.Configure(typeof(SendTextViewModel).FullName,
                new Uri("/Pages/PollPlaceDetails/SendTextView.xaml", UriKind.Relative));
            nav.Configure(typeof(SendTextErrorViewModel).FullName,
                new Uri("/Pages/PollPlaceDetails/SendTextErrorView.xaml", UriKind.Relative));
            nav.Configure(typeof(MaintenanceViewModel).FullName,
                new Uri("/Pages/MaintenanceView.xaml", UriKind.Relative));
            nav.Configure(typeof(LaunchViewModel).FullName,
	            new Uri("/Pages/Launch.xaml", UriKind.Relative));
            nav.Configure(typeof(ElectionLoadedViewModel).FullName,
                new Uri("/Pages/Maintenance/ElectionLoadedView.xaml", UriKind.Relative));
            nav.Configure(typeof(ElectionLoadingViewModel).FullName,
                new Uri("/Pages/Maintenance/ElectionLoadingView.xaml", UriKind.Relative));
            nav.Configure(typeof(ElectionLoadingFailedViewModel).FullName,
                new Uri("/Pages/Maintenance/ElectionLoadingFailedView.xaml", UriKind.Relative));
            nav.Configure(typeof(BackupTransactionsLoadedViewModel).FullName,
                new Uri("/Pages/Maintenance/BackupTransactionsLoadedView.xaml", UriKind.Relative));
            nav.Configure(typeof(BackupTransactionsLoadingViewModel).FullName,
                new Uri("/Pages/Maintenance/BackupTransactionsLoadingView.xaml", UriKind.Relative));
            nav.Configure(typeof(FailedViewModel).FullName,
                new Uri("/Pages/Maintenance/FailedView.xaml", UriKind.Relative));
            nav.Configure(typeof(TransactionsClearedViewModel).FullName,
                new Uri("/Pages/Maintenance/TransactionsClearedView.xaml", UriKind.Relative));
            nav.Configure(typeof(TransactionsClearingViewModel).FullName,
                new Uri("/Pages/Maintenance/TransactionsClearingView.xaml", UriKind.Relative));
            nav.Configure(typeof(TransactionsClearedFailedViewModel).FullName,
                new Uri("/Pages/Maintenance/TransactionsClearedFailedView.xaml", UriKind.Relative));
            nav.Configure(typeof(TransactionsClearConfirmationViewModel).FullName,
                new Uri("/Pages/Maintenance/TransactionsClearConfirmationView.xaml", UriKind.Relative));
            nav.Configure(typeof(UploadingTransactionsViewModel).FullName,
	            new Uri("/Pages/Maintenance/UploadingTransactionsView.xaml", UriKind.Relative));
            nav.Configure(typeof(UploadedTransactionsViewModel).FullName,
                new Uri("/Pages/Maintenance/UploadedTransactionsView.xaml", UriKind.Relative));
            nav.Configure(typeof(SelectTaskTypeViewModel).FullName,
	            new Uri("/Pages/Maintenance/SelectTaskTypeView.xaml", UriKind.Relative));
            nav.Configure(typeof(ManageElectionViewModel).FullName,
                new Uri("/Pages/Maintenance/ManageElectionView.xaml", UriKind.Relative));
            nav.Configure(typeof(VerifySoftwareViewModel).FullName,
                new Uri("/Pages/Maintenance/VerifySoftwareView.xaml", UriKind.Relative));
            nav.Configure(typeof(WifiMaintenanceViewModel).FullName,
                new Uri("/Pages/Maintenance/WifiMaintenanceView.xaml", UriKind.Relative));
            nav.Configure(typeof(WifiNetworkConfigurationViewModel).FullName,
                new Uri("/Pages/Maintenance/WifiNetworkConfigurationView.xaml", UriKind.Relative));
            nav.Configure(typeof(WifiKeyEntryViewModel).FullName,
                new Uri("/Pages/Maintenance/WifiKeyEntryView.xaml", UriKind.Relative));
            nav.Configure(typeof(WifiNetworkPropertiesViewModel).FullName,
                new Uri("/Pages/Maintenance/WifiNetworkPropertiesView.xaml", UriKind.Relative));
            nav.Configure(typeof(WifiConnectionErrorViewModel).FullName,
                new Uri("/Pages/Maintenance/WifiConnectionErrorView.xaml", UriKind.Relative));
            nav.Configure(typeof(SelectBallotTypeViewModel).FullName,
                new Uri("/Pages/SelectBallotType/SelectBallotTypeView.xaml", UriKind.Relative));
            nav.Configure(typeof(ExpressVoteActivationCardViewModel).FullName,
                new Uri("/Pages/SelectBallotType/ExpressVoteActivationCardView.xaml", UriKind.Relative));
            nav.Configure(typeof(BalotarViewModel).FullName,
                new Uri("/Pages/SelectBallotType/BalotarView.xaml", UriKind.Relative));
            nav.Configure(typeof(ConfigureElectionLandingViewModel).FullName,
                new Uri("/Pages/Maintenance/ConfigureElectionLanding.xaml", UriKind.Relative));
            nav.Configure(typeof(ConfigureElectionViewModel).FullName,
                new Uri("/Pages/Maintenance/ConfigureElection.xaml", UriKind.Relative));
            nav.Configure(typeof(ConfigurePrintingViewModel).FullName,
                new Uri("/Pages/Maintenance/ConfigurePrinting.xaml", UriKind.Relative));
            nav.Configure(typeof(VoterBallotDetailsViewModel).FullName,
                new Uri("/Pages/Voter/VoterBallotDetailsView.xaml", UriKind.Relative));
            nav.Configure(typeof(LoadingViewModel).FullName,
                new Uri("/Pages/SelectBallotType/LoadingView.xaml", UriKind.Relative));
            nav.Configure(typeof(SelectProvisionalReasonAndIdViewModel).FullName,
                new Uri("/Pages/SelectProvisionalReasonAndId/SelectProvisionalReasonAndIdView.xaml", UriKind.Relative));
            nav.Configure(typeof(SelectReissueBallotReasonViewModel).FullName,
                new Uri("/Pages/SelectReissueBallotReason/SelectReissueBallotReasonView.xaml", UriKind.Relative));
            nav.Configure(typeof(ManageVoterViewModel).FullName,
                new Uri("/Pages/Voter/ManageVoterView.xaml", UriKind.Relative));
            nav.Configure(typeof(SupervisorPwEntryViewModel).FullName,
                new Uri("/Pages/Supervisor/SupervisorPWEntryView.xaml", UriKind.Relative));
            nav.Configure(typeof(BallotCancelDetailsViewModel).FullName,
                new Uri("/Pages/Voter/BallotCancelDetailsView.xaml", UriKind.Relative));
            nav.Configure(typeof(VoterEditDetailsViewModel).FullName,
                new Uri("/Pages/Voter/VoterEditDetailsView.xaml", UriKind.Relative));
            nav.Configure(typeof(VoterAddDetailsViewModel).FullName,
                new Uri("/Pages/Voter/VoterAddDetailsView.xaml", UriKind.Relative));
            nav.Configure(typeof(PasswordViewModel).FullName,
                new Uri("/Pages/Password/PasswordEntryView.xaml", UriKind.Relative));
            nav.Configure(typeof(IncrementalUpdatesLoadingViewModel).FullName,
                new Uri("/Pages/IncrementalUpdates/IncrementalUpdatesLoadingView.xaml", UriKind.Relative));
            nav.Configure(typeof(UpgradeDeviceViewModel).FullName,
                new Uri("/Pages/Maintenance/UpgradeDeviceView.xaml", UriKind.Relative));
            nav.Configure(typeof(IncrementalUpdatesLoadedViewModel).FullName,
                new Uri("/Pages/IncrementalUpdates/IncrementalUpdatesLoadedView.xaml", UriKind.Relative));
            nav.Configure(typeof(IncrementalUpdatesFailedViewModel).FullName,
                new Uri("/Pages/IncrementalUpdates/IncrementalUpdatesFailedView.xaml", UriKind.Relative));
            nav.Configure(typeof(OptionsViewModel).FullName,
	            new Uri("/Pages/OptionsView.xaml", UriKind.Relative));
            nav.Configure(typeof(WaitTimeViewModel).FullName,
                new Uri("/Pages/WaitTime/WaitTimeView.xaml", UriKind.Relative));
            nav.Configure(typeof(CurrentWaitTimeTokenViewModel).FullName,
                new Uri("/Pages/WaitTime/CurrentWaitTimeTokenView.xaml", UriKind.Relative));
            nav.Configure(typeof(CancelWaitTimeTokenViewModel).FullName,
                new Uri("/Pages/WaitTime/CancelWaitTimeTokenView.xaml", UriKind.Relative));
            nav.Configure(typeof(CancelWaitTimeSucceededViewModel).FullName,
                new Uri("/Pages/WaitTime/CancelWaitTimeTokenSucceededView.xaml", UriKind.Relative));
            nav.Configure(typeof(DisplayWaitTimeTokenViewModel).FullName,
                new Uri("/Pages/WaitTime/DisplayWaitTimeTokenView.xaml", UriKind.Relative));
            nav.Configure(typeof(PrintWaitTimeTokenSucceededViewModel).FullName,
                new Uri("/Pages/WaitTime/PrintWaitTimeTokenSucceededView.xaml", UriKind.Relative));
            nav.Configure(typeof(PrintWaitTimeTokenFailedViewModel).FullName,
                new Uri("/Pages/WaitTime/PrintWaitTimeTokenFailedView.xaml", UriKind.Relative));
            nav.Configure(typeof(EnterWaitTimeTokenViewModel).FullName,
                new Uri("/Pages/WaitTime/EnterWaitTimeTokenView.xaml", UriKind.Relative));
            nav.Configure(typeof(DisplayWaitTimeViewModel).FullName,
                new Uri("/Pages/WaitTime/DisplayWaitTimeView.xaml", UriKind.Relative));
            nav.Configure(typeof(ShutDownViewModel).FullName,
	            new Uri("/Pages/ShutDownView.xaml", UriKind.Relative));
            nav.Configure(typeof(ElectionNotFoundViewModel).FullName,
                new Uri("/Pages/Maintenance/ElectionNotFound.xaml", UriKind.Relative));
            nav.Configure(typeof(VoterExportLoadingViewModel).FullName,
                new Uri("/Pages/VoterExport/VoterExportLoadingView.xaml", UriKind.Relative));
            nav.Configure(typeof(VoterExportLoadedViewModel).FullName,
                new Uri("/Pages/VoterExport/VoterExportLoadedView.xaml", UriKind.Relative));
            nav.Configure(typeof(VoterExportFailedViewModel).FullName,
                new Uri("/Pages/VoterExport/VoterExportFailedView.xaml", UriKind.Relative));
            nav.Configure(typeof(PQCViewModel).FullName,
	            new Uri("/Pages/PQCView.xaml", UriKind.Relative));
            nav.Configure(typeof(RestartSystemViewModel).FullName,
                new Uri("/Pages/SystemControl/RestartSystemView.xaml", UriKind.Relative));
            nav.Configure(typeof(SdCardCopyingViewModel).FullName,
                new Uri("/Pages/SystemControl/SdCardCopyingView.xaml", UriKind.Relative));
            nav.Configure(typeof(VoterEditViewModel).FullName,
                new Uri("/Pages/Voter/VoterEditView.xaml", UriKind.Relative));
            nav.Configure(typeof(VoterAddViewModel).FullName,
                new Uri("/Pages/Voter/VoterAddView.xaml", UriKind.Relative));
            nav.Configure(typeof(VoterVerificationViewModel).FullName,
                new Uri("/Pages/VoterVerification/VoterVerificationView.xaml", UriKind.Relative));
            nav.Configure(typeof(PollingPlaceVoterListReportViewModel).FullName,
                new Uri("/Pages/Reports/PollingPlaceVoterListReport.xaml", UriKind.Relative));
            nav.Configure(typeof(PrintPollingPlaceVoterListFailedViewModel).FullName,
                new Uri("/Pages/PrintPollingPlaceVoterListFailedView.xaml", UriKind.Relative));
            nav.Configure(typeof(VerifyVoterIdViewModel).FullName,
                new Uri("/Pages/VerifyVoterId/VerifyVoterIdView.xaml", UriKind.Relative));
            nav.Configure(typeof(VerifyVoterIdListAViewModel).FullName,
                new Uri("/Pages/VerifyVoterId/VerifyVoterIdListAView.xaml", UriKind.Relative));
            nav.Configure(typeof(VerifyVoterIdListBViewModel).FullName,
                new Uri("/Pages/VerifyVoterId/VerifyVoterIdListBView.xaml", UriKind.Relative));
            nav.Configure(typeof(VerifyVoterIdReasonableImpedimentDeclarationViewModel).FullName,
                new Uri("/Pages/VerifyVoterId/VerifyVoterIdReasonableImpedimentDeclarationView.xaml",
                    UriKind.Relative));
            nav.Configure(typeof(VoterVerificationsConfirmationViewModel).FullName,
                new Uri("/Pages/VerifyVoterId/VoterVerificationsConfirmationView.xaml", UriKind.Relative));
            nav.Configure(typeof(VerifyVoterIdSignatureViewModel).FullName,
                new Uri("/Pages/VerifyVoterId/VerifyVoterIdSignatureView.xaml", UriKind.Relative));
            nav.Configure(typeof(VoterReasonableImpedimentViewModel).FullName,
                new Uri("/Pages/Affidavit/VoterReasonableImpedimentView.xaml", UriKind.Relative));
            nav.Configure(typeof(VoterReasonableImpedimentReasonsViewModel).FullName,
                new Uri("/Pages/Affidavit/VoterReasonableImpedimentReasonsView.xaml", UriKind.Relative));
            nav.Configure(typeof(AffidavitTemplateViewModel).FullName,
                new Uri("/Pages/Affidavit/AffidavitTemplateView.xaml", UriKind.Relative));
            nav.Configure(typeof(AffidavitCaptureNameAddressViewModel).FullName,
                new Uri("/Pages/Affidavit/AffidavitCaptureNameAddressView.xaml", UriKind.Relative));
            nav.Configure(typeof(AffidavitConfirmationViewModel).FullName,
                new Uri("/Pages/Affidavit/AffidavitConfirmationView.xaml", UriKind.Relative));
            nav.Configure(typeof(PrintingAffidavitReportViewModel).FullName,
                new Uri("/Pages/Affidavit/PrintingAffidavitReportView.xaml", UriKind.Relative));
            nav.Configure(typeof(PrintingAffidavitReportFailedViewModel).FullName,
                new Uri("/Pages/Affidavit/PrintingAffidavitReportFailedView.xaml", UriKind.Relative));
            nav.Configure(typeof(DeviceLockedViewModel).FullName,
                new Uri("/Pages/DeviceLocked.xaml", UriKind.Relative));
            nav.Configure(typeof(PartyAffiliationAffidavitViewModel).FullName,
                new Uri("/Pages/Affidavit/PartyAffiliationAffidavitView.xaml", UriKind.Relative));
            nav.Configure(typeof(AffirmationOfResidenceAffidavitViewModel).FullName,
                new Uri("/Pages/Affidavit/AffirmationOfResidenceAffidavitView.xaml", UriKind.Relative));
            nav.Configure(typeof(VoterNameVerificationViewModel).FullName,
                new Uri("/Pages/Affidavit/VoterNameVerificationView.xaml", UriKind.Relative));

            nav.Configure(typeof(PollworkerManagementViewModel).FullName,
	            new Uri("/Pages/Pollworker/PollworkerManagementView.xaml", UriKind.Relative));
            nav.Configure(typeof(PollworkerActionConfirmationViewModel).FullName,
                new Uri("/Pages/Pollworker/PollworkerActionConfirmationView.xaml", UriKind.Relative));
            nav.Configure(typeof(PollworkerAddEditTimeViewModel).FullName,
	            new Uri("/Pages/Pollworker/TimeCard/PollworkerAddEditTimeView.xaml", UriKind.Relative));
            nav.Configure(typeof(PollworkerApprovalViewModel).FullName,
                new Uri("/Pages/Pollworker/TimeCard/PollworkerApprovalView.xaml", UriKind.Relative));
            nav.Configure(typeof(PollworkerManageTimeViewModel).FullName,
                new Uri("/Pages/Pollworker/TimeCard/PollworkerManageTimeView.xaml", UriKind.Relative));
            nav.Configure(typeof(PollworkerViewModel).FullName,
                new Uri("/Pages/Pollworker/TimeCard/PollworkerView.xaml", UriKind.Relative));
            nav.Configure(typeof(PollworkerAddEditViewModel).FullName,
	            new Uri("/Pages/Pollworker/Search/PollworkerAddEditView.xaml", UriKind.Relative));
            nav.Configure(typeof(PollworkerClockOutReasonViewModel).FullName,
                new Uri("/Pages/Pollworker/Search/PollworkerClockOutReasonView.xaml", UriKind.Relative));
            nav.Configure(typeof(PollworkerDetailViewModel).FullName,
                new Uri("/Pages/Pollworker/Search/PollworkerDetailView.xaml", UriKind.Relative));
            nav.Configure(typeof(PollworkerJobConfirmationViewModel).FullName,
                new Uri("/Pages/Pollworker/Search/PollworkerJobConfirmationView.xaml", UriKind.Relative));
            nav.Configure(typeof(PollworkerOathViewModel).FullName,
                new Uri("/Pages/Pollworker/Search/PollworkerOathView.xaml", UriKind.Relative));
            nav.Configure(typeof(PollworkerSearchViewModel).FullName,
                new Uri("/Pages/Pollworker/Search/PollworkerSearchView.xaml", UriKind.Relative));
            nav.Configure(typeof(PollworkerTimeHistoryDetailsViewModel).FullName,
                new Uri("/Pages/Pollworker/Search/PollworkerTimeHistoryDetailsView.xaml", UriKind.Relative));
            nav.Configure(typeof(PrintingBallotViewModel).FullName,
	            new Uri("/Pages/PrintingBallotView.xaml", UriKind.Relative));
            nav.Configure(typeof(PrintBallotFailedViewModel).FullName,
                new Uri("/Pages/PrintBallotFailedView.xaml", UriKind.Relative));
            nav.Configure(typeof(SurrenderMailInBallotViewModel).FullName,
	            new Uri("/Pages/SurrenderMailInBallot/SurrenderMailInBallotView.xaml", UriKind.Relative));
            nav.Configure(typeof(SurrenderMailInBallotResponseViewModel).FullName,
                new Uri("/Pages/SurrenderMailInBallot/SurrenderMailInBallotResponseView.xaml", UriKind.Relative));
            nav.Configure(typeof(HelpCenterViewModel).FullName,
	            new Uri("/Pages/HelpCenter/HelpCenterView.xaml", UriKind.Relative));
            nav.Configure(typeof(HelpCenterContentViewModel).FullName,
                new Uri("/Pages/HelpCenter/HelpCenterContentView.xaml", UriKind.Relative));
            nav.Configure(typeof(VerifyVoterIdExemptViewModel).FullName,
	            new Uri("/Pages/VerifyVoterId/VerifyVoterIdExemptView.xaml", UriKind.Relative));
            nav.Configure(typeof(VerifyVoterRegistrationForNotInRosterViewModel).FullName,
	            new Uri("/Pages/VerifyVoterId/VerifyVoterRegistrationForNotInRosterView.xaml", UriKind.Relative));
            nav.Configure(typeof(ApplyingConfigurationUpdatesViewModel).FullName,
	            new Uri("/Pages/ApplyingConfigurationUpdatesView.xaml", UriKind.Relative));
            nav.Configure(typeof(ApplyingConfigurationUpdatesFailedViewModel).FullName,
                new Uri("/Pages/ApplyingConfigurationUpdatesFailedView.xaml", UriKind.Relative));
            nav.Configure(typeof(ApplyingConfigurationUpdatesDoneViewModel).FullName,
                new Uri("/Pages/ApplyingConfigurationUpdatesDoneView.xaml", UriKind.Relative));
            nav.Configure(typeof(ConfigurationUpdateAlertViewModal).FullName,
                new Uri("/Pages/ConfigurationUpdateAlertView.xaml", UriKind.Relative));
            nav.Configure(typeof(ActivationCardViewModel).FullName,
	            new Uri("/Pages/SelectBallotType/ActivationCardView.xaml", UriKind.Relative));
            nav.Configure(typeof(NotInRosterReasonsViewModel).FullName,
	            new Uri("/Pages/Voter/NotInRosterReasonsView.xaml", UriKind.Relative));
            nav.Configure(typeof(RegionalResultsPollPlaceSearchViewModel).FullName,
                new Uri("/Pages/RegionalResults/RegionalResultsPollPlaceSearchView.xaml", UriKind.Relative));
            nav.Configure(typeof(RegionalResultsPasswordViewModel).FullName,
	            new Uri("/Pages/RegionalResults/RegionalResultsPasswordView.xaml", UriKind.Relative));
            nav.Configure(typeof(RegionalResultsErrorViewModel).FullName,
                new Uri("/Pages/RegionalResults/RegionalResultsErrorView.xaml", UriKind.Relative));
            nav.Configure(typeof(RegionalResultsTransmitResultsViewModel).FullName,
                new Uri("/Pages/RegionalResults/RegionalResultsTransmitResultsView.xaml", UriKind.Relative));
            nav.Configure(typeof(RegionalResultsLoadingViewModel).FullName,
                new Uri("/Pages/RegionalResults/RegionalResultsLoadingView.xaml", UriKind.Relative));
            nav.Configure(typeof(VoterDisabilityOathSignatureViewModel).FullName,
	            new Uri("/Pages/VoterDisabilityOath/VoterDisabilityOathSignatureView.xaml", UriKind.Relative));
            nav.Configure(typeof(SignatureCanvasViewModel).FullName,
	            new Uri("/UserControls/Signature/SignatureCanvas.xaml", UriKind.Relative));
            nav.Configure(typeof(ChecklistViewModel).FullName,
	            new Uri("/Pages/Checklist/ChecklistView.xaml", UriKind.Relative));
            nav.Configure(typeof(ManageDeviceViewModel).FullName,
	            new Uri("/Pages/ManageDeviceView.xaml", UriKind.Relative));
            nav.Configure(typeof(ManageDeviceLoginViewModel).FullName,
	            new Uri("/Pages/ManageDeviceLoginView.xaml", UriKind.Relative));
            nav.Configure(typeof(CaptureIdVoterConfirmationViewModel).FullName,
	            new Uri("/Pages/Voter/CaptureIdVoterConfirmationView.xaml", UriKind.Relative));
            nav.Configure(typeof(CaptureIdVoterVerificationViewModel).FullName,
	            new Uri("/Pages/Voter/CaptureIdVoterVerificationView.xaml", UriKind.Relative));
            nav.Configure(typeof(CaptureIdVoterPresentFormViewModel).FullName,
	            new Uri("/Pages/Voter/CaptureIdVoterPresentFormView.xaml", UriKind.Relative));

            nav.Configure(typeof(PrintAddressChangedDocFailedViewModel).FullName,
                new Uri("/Pages/PrintAddressChangedDocFailedView.xaml", UriKind.Relative));

            nav.Configure(typeof(GenericConfirmationViewModel).FullName, 
               new Uri("/Pages/GenericConfirmationView.xaml", UriKind.Relative));

            builder.RegisterInstance<IFrameNavigationService>(nav);
        }
    }
}