using ESS.ELLEGO.Rest.Exceptions;
using ESS.ELLEGO.Rest.Stats;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.OAuth;
using Flurl;
using Flurl.Http;
using Flurl.Http.Configuration;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;

namespace ESS.ELLEGO.Rest
{
	public class EssRestExecutor : IEssRestExecutor
	{
		private const string ApiMediaType = "application/json";
		private const string HeaderApiVersion = "x-api-version";
		private const string HeaderDeviceName = "x-device-name";

		private static readonly JsonSerializer _jsonSerializer = JsonSerializer.CreateDefault();
		private static readonly StringContent _emptyJsonContent = new StringContent(string.Empty, null, ApiMediaType);

		private readonly IEssLogger _logger;
		private readonly IApiMonitor _apiMonitor;

		public EssRestExecutor(IApiMonitor stats, IEssLogger essLogger)
		{
			_logger = essLogger;
			_apiMonitor = stats;
		}

		public async Task<T> PostAsync<T>(EssRestRequest restRequest)
		{
			try
			{
				var httpResponse = await ExecuteAsync(HttpMethod.Post, restRequest)
					.ConfigureAwait(false);

				return await DeserializeResponseAsync<T>(httpResponse)
					.ConfigureAwait(false);
			}
			catch (EssRestClientSerializationException)
			{
				throw; // already logged in DeserializeResponseAsync
			}
			catch (Exception ex)
			{
				_logger.LogError(ex, new Dictionary<string, string>
				{
					{ "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" },
					{ "Message", ex.Message }
				});
				throw;
			}
		}


		public async Task<HttpResponseMessage> ExecuteAsync(HttpMethod method, EssRestRequest restRequest)
		{
			if (await OAuthTokenHelper.Instance.IsExpired().ConfigureAwait(false))
				throw new EssOAuthException("Invalid OAuth token.");

			try
			{
				var response = await GenerateRequest(restRequest).PostAsync(CreateBody(restRequest));
				return response.ResponseMessage;
			}
			catch (FlurlHttpTimeoutException ex)
			{
				_logger.LogError(ex, new Dictionary<string, string> { { "Action", $"{GetType().Name}.ExecuteAsync" } });
				throw new EssRestClientException(MakeUrl(restRequest), ex);
			}
			catch (FlurlHttpException ex)
			{
				_logger.LogError(ex, new Dictionary<string, string> { { "Action", $"{GetType().Name}.ExecuteAsync" } });
				await _apiMonitor.AddEntry(ex.Call).ConfigureAwait(false);
				throw new EssRestClientException(MakeUrl(restRequest), ex);
			}
			catch (Exception ex)
			{
				_logger.LogError(ex, new Dictionary<string, string> { { "Action", $"{GetType().Name}.ExecuteAsync" } });
				throw new EssRestClientException(MakeUrl(restRequest), ex);
			}
		}


		public async Task<T> DeserializeResponseAsync<T>(HttpResponseMessage resp)
		{
			if (resp?.Content == null || !resp.IsSuccessStatusCode)
				return default;

			try
			{
				using (var stream = await resp.Content.ReadAsStreamAsync().ConfigureAwait(false))
				using (var reader = new StreamReader(stream))
				using (var json = new JsonTextReader(reader))
				{
					return _jsonSerializer.Deserialize<T>(json);
				}
			}
			catch (Exception ex)
			{
				_logger.LogError(ex, new Dictionary<string, string>
				{
					{ "Action", $"{GetType().Name}.DeserializeResponseAsync" },
					{ "Message", ex.Message }
				});
				throw new EssRestClientSerializationException(typeof(T), ex);
			}
		}


		[MethodImpl(MethodImplOptions.AggressiveInlining)]
		private HttpContent CreateBody(EssRestRequest restRequest) =>
			restRequest?.Body == null
				? _emptyJsonContent
				: new StringContent(JsonConvert.SerializeObject(restRequest.Body), null, ApiMediaType);


		private IFlurlRequest GenerateRequest(EssRestRequest restRequest)
		{
			var request = new FlurlRequest(MakeUrl(restRequest));
			request.Client = FlurlHttp.Clients.GetOrAdd(
				FlurlHttp.BuildClientNameByHost(request),
				restRequest.BaseUrl,
				_ => ConnectionBuilder(restRequest.BaseUrl).Build());

			return request;
		}

		private void OnBeforeCall(FlurlCall call)
		{
			call.HttpRequestMessage.Headers.Authorization =
				new AuthenticationHeaderValue("Bearer", OAuthTokenHelper.Instance.OAuthToken.AccessToken);

			_logger.LogDebug($"API Request: url={call.Request.Url}");
		}

		private void OnAfterCall(FlurlCall call)
		{
			_apiMonitor.AddEntry(call).ConfigureAwait(false);

			if (call.Completed)
			{
				_logger.LogDebug(
					$"API Response: url={call.Request.Url} http={(int)call.HttpResponseMessage.StatusCode} duration={call.Duration}");
			}
			else
			{
				_logger.LogDebug($"API Response: url={call.Request.Url} failed: {call.Exception?.Message}");
				if (call.Exception != null) _logger.LogError(call.Exception);
			}
		}

		private Url MakeUrl(EssRestRequest restRequest)
		{
			var url = new Url(restRequest.BaseUrl);
			if (restRequest.UrlSegments != null)
				url.AppendPathSegments(restRequest.UrlSegments);

			return url;
		}

		private IFlurlClientBuilder ConnectionBuilder(string baseUrl)
		{
			var timeoutSeconds = SystemConfiguration.ElectionConfiguration.SlowConnectionTimeoutSeconds;
			if (timeoutSeconds < 2 || timeoutSeconds > 10) timeoutSeconds = 2;

			return FlurlHttp.ConfigureClientForUrl(baseUrl)
				.WithTimeout(TimeSpan.FromSeconds(timeoutSeconds))
				.BeforeCall(OnBeforeCall)
				.AfterCall(OnAfterCall)
				.WithHeaders(new Dictionary<string, string>(2)
				{
					{ HeaderApiVersion, SystemConfiguration.ElectionConfiguration.ApiVersion },
					{ HeaderDeviceName, SystemDetails.MachineName }
				});
		}
	}
}
