using ESS.Pollbook.Core.StaticValues;
using System.Collections.Generic;

namespace ESS.ELLEGO.Rest
{
	/// <summary>
	/// The ESSRestRequest is not a direct serialized model for transport to the remote APIs.
	/// Rather this is a model that contains all the information for the request to be built.
	/// </summary>
	public class EssRestRequest
	{
		public string BaseUrl { get; set; } = SystemConfiguration.ElectionConfiguration.SyncPointUrl;

		public List<string> UrlSegments { get; set; }

		public object Body { get; set; }

		public int RequestTimeoutInMilliseconds { get; set; } = SystemConfiguration.ElectionConfiguration.SlowConnectionTimeoutSeconds * 1000;

		public EssRestRequest(string baseUrl = "")
		{
			if (!string.IsNullOrEmpty(baseUrl))
				BaseUrl = baseUrl;
		}
	}
}
