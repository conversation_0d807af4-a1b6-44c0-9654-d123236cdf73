using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.StaticValues;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using ICSharpCode.SharpZipLib.Zip;
using System.Reflection;

namespace ESS.Pollbook.Core.SignatureHandler
{
    public static class ZipStreamHandler
    {
	    private static readonly Dictionary<string, ZipFile> ZipStreams = new Dictionary<string, ZipFile>();

	    public static ZipFile GetZipFile(string zipFileName)
	    {
            return ZipStreams?.FirstOrDefault(kvp =>
                kvp.Key.Equals(Path.GetFileName(zipFileName), StringComparison.CurrentCultureIgnoreCase)
                ).Value;
        }

	    public static void Close(IEssLogger logger)
	    {
            if (ZipStreams == null)
                return;

            foreach (var zs in ZipStreams)
            {
	            try
	            {
		            zs.Value?.Close();
	            }
	            catch (IOException ex)
	            {
		            logger.LogError(ex, new Dictionary<string, string>
		            {
			            { "Action", "ZipStreamHandler." + MethodBase.GetCurrentMethod()?.Name },
			            { "ZipFileName", zs.Value?.Name }
		            });
	            }
            }

            ZipStreams.Clear();
        }

        public static void LoadSignatureZip(string basePath, IEssLogger logger)
        {
	        lock (ZipStreams)
	        {
		        foreach (var signatureZipFile in Directory.GetFiles(basePath, "Polldata_Signatures*.zip").ToList())
		        {
			        var zipFile = GetZipFile(signatureZipFile);
			        if (zipFile != null)
				        continue;

			        var counter = 0;
			        while (IsFileInUse(signatureZipFile) && counter < 25)
			        {
				        Thread.Sleep(100);
				        counter++;
			        }

			        if (counter == 25)
				        return;

			        try
			        {
				        // Fortify Suppression required here.  The stream "fs" is set internally.  When
				        // close is done, that stream is also closed (validated in ZipFile Dll) and paraemters.
				        ZipFile signatureZipStream = new ZipFile(File.OpenRead(signatureZipFile))
				        {
					        Password = SystemDetails.PQCPassword
				        };
				        ZipStreams.Add(Path.GetFileName(signatureZipFile), signatureZipStream);
			        }
			        catch (Exception ex)
			        {
				        logger.LogError(ex, new Dictionary<string, string>
				        {
					        { "Action", "ZipStreamHandler." + MethodBase.GetCurrentMethod()?.Name },
					        { "LoadSignatureZip", $"Unable to use Zip file: {signatureZipFile}. Deleting file." }
				        });

				        if (!IsFileInUse(signatureZipFile))
					        File.Delete(signatureZipFile);
			        }
		        }
	        }
        }

        private static bool IsFileInUse(string path)
        {
            if (string.IsNullOrEmpty(path))
                throw new ArgumentException("'path' cannot be null or empty.", nameof(path));

            try
            {
               using (var stream = new FileStream(path, FileMode.Open, FileAccess.Read))
               {
                  stream.Close();
               }
            }
            catch (IOException)
            {
                return true;
            }

            return false;
        }
    }
}
