using System;
using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.DTO;

namespace ESS.Pollbook.Core.StaticValues
{
    public static class LoggedInPollplaceInfo
    {

        public static PollPlaceDto LoggedInPollPlace { get; set; }

        public static bool IsPollOpen { get; set; }

        public static TimeSpan? WaitTime { get; set; } = null;

        public static bool IsEarlyVotingPollPlace =>
           LoggedInPollPlace?.PollingPlacePollTypeNumber == (int) PollType.EarlyVote;

        public static string CountyName { get; set; }


        public static string WaitTimeDisplay
        {
            get
            {
                if (WaitTime == null)
                {
                    return string.Empty;
                }

                TimeSpan waitTime = (TimeSpan)WaitTime;
                if (waitTime.Hours == 0)
                {
                    return WaitTimeMinutes();
                }
                else if (waitTime.Hours == 1)
                {
                    return $"1 hour, {WaitTimeMinutes()}";
                }
                else
                {
                    return $"{waitTime.Hours} hours, {WaitTimeMinutes()}";
                }
            }
        }

        private static string WaitTimeMinutes()
        {
            TimeSpan waitTime = (TimeSpan)WaitTime;
            if (waitTime.Minutes == 1)
            {
                return "1 minute";
            }
            return $"{waitTime.Minutes} minutes";
        }
    }
}
