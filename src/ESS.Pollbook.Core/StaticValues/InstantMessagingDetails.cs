using System;

namespace ESS.Pollbook.Core.StaticValues
{
    public class InstantMessagingDetails
    {
        // This is unsafe and should be modified.  Both ComposeViewModel and ConversationViewModel have lines that
        // register InstantMessagingDetails_IMConnectedChanged.  EventHandler.GetInvocationList() should be called
        // to checked for the event being already registered 

        // A subscription to a static event will root your subscribing instance which prevents the garbage collector from ever collecting it.
        // Ensure your object always unsubscribes
        public static event EventHandler IMConnectedChanged;

        private static bool _isIMConnected;

        public static bool IsIMConnected
        {
            get { return _isIMConnected; }
            set
            {
                if (value != _isIMConnected)
                {
                    _isIMConnected = value;
                    IMConnectedChanged?.Invoke(null, null);
                }
            }
        }
    }
}
