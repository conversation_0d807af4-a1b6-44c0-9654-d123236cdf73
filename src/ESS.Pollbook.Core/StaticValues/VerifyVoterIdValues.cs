namespace ESS.Pollbook.Core.StaticValues
{
    public static class VerifyVoterIdValues
    {
        public const string PageTitle = "Verify Voter ID";
        public const string ListATitle = PageTitle;
        public const string ListBTitle = PageTitle;
        public const string RIDTitle = PageTitle;
        public const string ExemptTitle = PageTitle;
        public const string VRCertTitle = PageTitle;
        public const string ExemptContent = "Did Voter present a voter registration certificate showing an exempt status?";
        public const string ListAContent = "Did Voter present an identification from List A?";
        public const string ListBContent = "Did Voter present an identification from List B?";
        public const string RIDContent = "Voter was provided the Reasonable Impediment Declaration?";
        public const string VoterVerificationConfirmation = "Please present the voter with the following form(s):";
        public const string VerifyVoterIdInstruction = "Please select one of the following supporting forms of identification \nin which the voter presented a copy or original.";
        public const string VerifyVoterIdGovDoc = "a government document that shows the voter's name and an address including the voter's voter registration certificate";
        public const string VerifyVoterIdUtilBill = "current utility bill";
        public const string VerifyVoterIdBankStmt = "bank statement";
        public const string VerifyVoterIdGovChk = "government check";
        public const string VerifyVoterIdPaycheck = "paycheck";
        public const string VerifyVoterIdBirthCert = "(a) a certified domestic (from a U.S. state or territory) birth certificate or (b) a document confirming birth admissible \nin a court of law which establishes the voter's identity (which may include a foreign birth document)";
    }
    public enum TexasAttributes
    {
        TxListA,
        TxListB,
        FormsComplete,
        TxExempt,
        TxVRCertification
    }
}
