using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.Model.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace ESS.Pollbook.Core.StaticValues
{
    public static class ConfigurationExtensions
    {
        /// <summary>
        /// Converts from a client configuration into an update configuration using the attributes
        /// </summary>
        /// <typeparam name="TModel"></typeparam>
        /// <param name="configuration"></param>
        /// <returns></returns>
        public static List<ConfigurationDto> ModelToConfigurationList<TModel>(this TModel configuration) where TModel : class, new()
        {
           var allProperties = typeof(TModel).GetProperties(BindingFlags.Instance | BindingFlags.Public);

            var properties = (from p in allProperties
                              let attr = p.GetCustomAttribute<ConfigurationMappingAttribute>()
                              select new
                              {
                                  Property = p,
                                  Attribute = attr
                              }).ToList();

            return (from property in properties
               let rawValue = property.Attribute.ConfigurationType == ConfigurationType.Value
                  ? property.Property.GetValue(configuration).ToString()
                  : null
               let valueId = property.Attribute.ConfigurationType == ConfigurationType.Enumeration
                  ? (int?) int.Parse(property.Property.GetValue(configuration).ToString())
                  : null
               select new ConfigurationDto
               {
                  Category = property.Attribute.Category,
                  ConfigurationUpdate = property.Attribute.ConfigurationUpdate,
                  DataType = property.Property.PropertyType,
                  Name = property.Attribute.Name,
                  RawValue = rawValue,
                  CommonEnumerationValueId = valueId
               }).ToList();
        }

        /// <summary>
        /// Convert a list of response configurations into a model using either the name of the property or the name specified in a ConfigurationMapping attribute
        /// </summary>
        /// <typeparam name="TModel">The type to return</typeparam>
        /// <param name="values">The list of response configurations to map</param>
        /// <returns>Model with properties set where matches are found</returns>
        public static TModel ConvertFromConfigurationList<TModel>(this IList<ConfigurationDto> values) where TModel : class, new()
        {
            var model = Activator.CreateInstance<TModel>();

            var allProperties = typeof(TModel).GetProperties(BindingFlags.Instance | BindingFlags.Public);

            var properties = (from p in allProperties
                              let attr = p.GetCustomAttribute<ConfigurationMappingAttribute>()
                              select new
                              {
                                  Property = p,
                                  Attribute = attr
                              }).ToList();

            foreach (var prop in properties)
            {
                var propertyType = prop.Property.PropertyType;
                var matchedConfiguration = values.FirstOrDefault(v => IsMatch(v.Name, prop.Property, prop.Attribute));

                object value;

                if (string.IsNullOrEmpty(matchedConfiguration?.Name))
                {
                    value = TypeConversionHelper.GetDefaultValue(propertyType);
                }
                else
                {
                    if (Nullable.GetUnderlyingType(propertyType) != null && matchedConfiguration.RawValue == null)
                    {
                        value = null;
                    }
                    else
                    {
                        value = TypeConversionHelper.Convert(matchedConfiguration.RawValue, prop.Property.PropertyType);
                    }
                }

                prop.Property.SetValue(model, value);
            }

            return model;
        }

        private static bool IsMatch(string key, PropertyInfo property, ConfigurationMappingAttribute attribute)
        {
           return string.Equals(key, attribute != null ? attribute.Name : property.Name, StringComparison.OrdinalIgnoreCase);
        }
    }
}
