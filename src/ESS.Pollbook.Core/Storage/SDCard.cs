using ESS.Pollbook.Core.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Management;

namespace ESS.Pollbook.Core.Storage
{
    public class SDCard
    {
        private readonly IEssLogger _essLogger;

        private static string _attachedSdCardDrive;
        public event EventHandler<RemovableDriveEventArgs> SDCardEvent;

        private const string _deviceCreatedEvent = "__InstanceCreationEvent";
        private const string _deviceModifiedEvent = "__InstanceModificationEvent";
        private const string _deviceDeletedEvent = "__InstanceDeletionEvent";

        public SDCard(IEssLogger essLogger)
        {
            _essLogger = essLogger;
        }

        public void InitializeSDCard()
        {
            SetInitialSdCard();
            InitializeSdCardWatcher();
        }

        public static string AttachedSdCardDrive
        {
            get => _attachedSdCardDrive;
            private set
            {
                if (!string.IsNullOrWhiteSpace(value))
                {
                    _attachedSdCardDrive = value.EndsWith(@"\") ? value : value + @"\";
                }
                else
                {
                    _attachedSdCardDrive = string.Empty;
                }
            }
        }

        public static bool IsSdCardMounted => !string.IsNullOrWhiteSpace(AttachedSdCardDrive);

        private void SetInitialSdCard()
        {
            var sdDrives = GetSdCardPaths();

            if (sdDrives.Count > 0)
            {
                AttachedSdCardDrive = sdDrives[0];
                _essLogger.LogInformation($"SD card path: {AttachedSdCardDrive}");
            }
            else
            {
                AttachedSdCardDrive = "";
                _essLogger.LogInformation("No SD card available.");
            }
        }

        public List<string> GetSdCardPaths()
        {
            var moSearcher = new ManagementObjectSearcher("SELECT DeviceId, Model FROM Win32_DiskDrive where MediaType='Removable Media' AND (InterfaceType='SCSI' OR InterfaceType<>'USB')");

            var drives = new List<string>();
            try
            {
                var moc = moSearcher.Get();
                foreach (ManagementObject mo in moSearcher.Get().Cast<ManagementObject>())
                {
                    foreach (ManagementObject b in mo.GetRelated("Win32_DiskPartition").Cast<ManagementObject>())
                    {
                        foreach (ManagementBaseObject c in b.GetRelated("Win32_LogicalDisk"))
                        {
                            drives.Add(c["Name"].ToString());
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Dictionary<string, string> dict = new Dictionary<string, string> {
                    ["Class"] = "SDCard",
                    ["Action"] = "GetSdCardPaths()"
                };
                _essLogger.LogError(ex, dict);
            }
            return drives;
        }

        private void InitializeSdCardWatcher()
        {
            const string query = "TargetInstance ISA 'Win32_LogicalDisk'";
            var watcher = GetWatcher("__InstanceOperationEvent", query);

            watcher.EventArrived += CardOperationEventHandler;
            watcher.Start();
        }

        private ManagementEventWatcher GetWatcher(string watcherType, string query)
        {
            var eventQuery = new WqlEventQuery(
                watcherType,
                new TimeSpan(0, 0, 3),
                query);

            // Bind to local machine  
            var scope = new ManagementScope("root\\CIMV2");
            scope.Options.EnablePrivileges = true;

            var eventWatcher = new ManagementEventWatcher(scope, eventQuery);
            return eventWatcher;
        }

        private bool CheckIsDriveSdCard(string driveToCheck)
        {
            var sdDrives = GetSdCardPaths();

            foreach (var drive in sdDrives)
            {
                if (drive.Substring(0, 1)
                    .Equals(driveToCheck.Substring(0, 1), StringComparison.CurrentCultureIgnoreCase))
                {
                    return true;
                }
            }
            return false;
        }

        private void CardOperationEventHandler(object sender, EventArrivedEventArgs e)
        {
            try
            {
                var className = e.NewEvent.ClassPath.ClassName;
                if (className.Equals(_deviceModifiedEvent, StringComparison.CurrentCultureIgnoreCase))
                    return;

                var detectedDrive = ((ManagementBaseObject)e.NewEvent["TargetInstance"])["Name"].ToString();
                if (detectedDrive.Equals("C:", StringComparison.CurrentCultureIgnoreCase))
                    return;

                if (!detectedDrive.EndsWith(@"\"))
                {
                    detectedDrive += @"\";
                }

                RemovableDriveEventArgs.CardEventType eventType;

                if (className.Equals(_deviceCreatedEvent, StringComparison.CurrentCultureIgnoreCase))
                {
                    eventType = RemovableDriveEventArgs.CardEventType.Inserted;
                }
                else if (className.Equals(_deviceDeletedEvent, StringComparison.CurrentCultureIgnoreCase))
                {
                    eventType = RemovableDriveEventArgs.CardEventType.Removed;
                }
                else
                {
                    return;
                }

                bool shouldRaiseEvent = false;
                if (eventType == RemovableDriveEventArgs.CardEventType.Removed && IsSdCardMounted &&
                    AttachedSdCardDrive.Substring(0, 1).Equals(detectedDrive.Substring(0, 1), StringComparison.CurrentCultureIgnoreCase))
                {
                    //SD card was removed
                    AttachedSdCardDrive = "";
                    shouldRaiseEvent = true;
                }
                else if (CheckIsDriveSdCard(detectedDrive))
                {
                    AttachedSdCardDrive = detectedDrive;
                    shouldRaiseEvent = true;
                }

                //Raise the event letting observers know there has been a change
                if (shouldRaiseEvent)
                    OnSdCardEvent(new RemovableDriveEventArgs(detectedDrive, eventType));
            }
            catch (Exception ex)
            {
                _essLogger.LogError(ex);
            }
        }

        private void OnSdCardEvent(RemovableDriveEventArgs e)
        {
            SDCardEvent?.Invoke(this, e);
        }
    }
}
