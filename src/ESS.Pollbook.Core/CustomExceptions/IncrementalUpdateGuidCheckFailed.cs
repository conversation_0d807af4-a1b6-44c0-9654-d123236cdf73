using System;
using System.Runtime.Serialization;

namespace ESS.Pollbook.Core.CustomExceptions
{
    [Serializable]
    public sealed class IncrementalUpdateGuidCheckException : Exception
    {
        public IncrementalUpdateGuidCheckException()
        {
        }

        public IncrementalUpdateGuidCheckException(string message) : base(message)
        {
        }

        public IncrementalUpdateGuidCheckException(string message, Exception innerException) : base(message,
            innerException)
        {
        }

        public IncrementalUpdateGuidCheckException(SerializationInfo info, StreamingContext context) : base(info,
            context)
        {
        }
    }
}
