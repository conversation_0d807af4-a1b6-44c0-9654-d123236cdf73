using System;
using System.Runtime.Serialization;

namespace ESS.Pollbook.Core.CustomExceptions
{
	[Serializable]
	public class ActivationCardException : Exception
	{
      public ActivationCardException() { }
      public ActivationCardException(string message) : base(message) { }

      public ActivationCardException(string message, Exception inner) : base(message, inner) { }

      private ActivationCardException(SerializationInfo info, StreamingContext context) : base(info, context) { }
	}
}
