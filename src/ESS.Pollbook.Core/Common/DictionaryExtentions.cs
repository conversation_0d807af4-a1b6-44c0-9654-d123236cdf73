using Newtonsoft.Json;
using System;
using System.Collections.Generic;

namespace ESS.Pollbook.Core.Common
{
    public static class DictionaryExtensions
    {
        /// <summary>
        /// Tries to get the value associated with the specified key and cast it to the desired type.
        /// </summary>
        /// <typeparam name="T">The type to cast the value to.</typeparam>
        /// <param name="dictionary">The dictionary to search in.</param>
        /// <param name="key">The key of the value to get.</param>
        /// <param name="defaultValue">The default value to return if the key is not found or the cast fails.</param>
        /// <returns>The value cast to the specified type, or the default value if the cast fails.</returns>
        public static T GetValueOrDefault<T>(this Dictionary<string, object> dictionary, string key, T defaultValue = default)
        {
            if (dictionary.TryGetValue(key, out object value) && value is T castValue)
            {
                return castValue;
            }

            return defaultValue;
        }

        /// <summary>
        /// Tries to get the json string value associated with the specified key and cast it to the desired type.
        /// </summary>
        /// <typeparam name="T">The type to cast the value to.</typeparam>
        /// <param name="dictionary">The dictionary to search in.</param>
        /// <param name="key">The key of the value to get.</param>
        /// <param name="defaultValue">The default value to return if the key is not found or the cast fails.</param>
        /// <returns>The value cast to the specified type, or the default value if the cast fails.</returns>
        public static T GetJsonObjectAsType<T>(this Dictionary<string, object> dictionary, string key, T defaultValue = default)
        {
            if (dictionary.TryGetValue(key, out object value))
            {
                try
                {
                    return JsonConvert.DeserializeObject<T>(value.ToString());
                }
                catch (Exception ex)
                {
                    return defaultValue;
                };
            }

            return defaultValue;
        }
    }
}
