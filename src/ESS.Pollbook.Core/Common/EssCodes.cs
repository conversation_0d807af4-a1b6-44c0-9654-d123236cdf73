using System.ComponentModel;

namespace ESS.Pollbook.Core.Common
{
    /// <summary>
    /// The list of available ESS Enumerations
    /// </summary>
    public enum EssEnumeration
    {
        /// <summary>
        /// Absentee Statuses
        /// </summary>
        AbsenteeStatus = 3,
        /// <summary>
        /// Address Type
        /// </summary>
        AddressType = 5,
        /// <summary>
        /// Address Unit Type
        /// </summary>
        AddressUnitType = 11,
        /// <summary>
        /// Alert Media Type
        /// </summary>
        AlertMediaType = 18,
        /// <summary>
        /// Alert Type
        /// </summary>
        AlertType = 19,
        /// <summary>
        /// Ballot Cancelation Reasons based on Enable_Cancel_Reasons
        /// </summary>
        BallotCancelReason = 45,
        /// <summary>
        /// Ballot Reissue Reason
        /// </summary>
        BallotReissueReason = 29,
        /// <summary>
        /// Response codes for batches
        /// </summary>
        BatchStatus = 26,
        /// <summary>
        /// Card Type
        /// </summary>
        CardType = 13,
        /// <summary>
        /// Bridgepoint SQLite DB Create Mode
        /// </summary>
        DBCreateMode = 6,
        /// <summary>
        /// Device Types
        /// </summary>
        DeviceType = 7,
        /// <summary>
        /// District Types
        /// </summary>
        DistrictType = 9,
        /// <summary>
        /// Election Type
        /// </summary>
        ElectionType = 36,
        /// <summary>
        /// ExportData
        /// </summary>
        ExportData = 15,
        /// <summary>
        /// File Type
        /// </summary>
        FileType = 14,
        /// <summary>
        /// Gender Codes
        /// </summary>
        Gender = 4,
        /// <summary>
        /// Identification requirements
        /// </summary>
        IdentificationRequirementStatus = 27,
        /// <summary>
        /// ISO Language Types
        /// </summary>
        ISOLanguage = 10,
        /// <summary>
        /// Job Title
        /// </summary>
        JobTitle = 20,
        /// <summary>
        /// Ballot Media Type
        /// </summary>
        MediaType = 12,
        /// <summary>
        /// SMS Message Service Status
        /// </summary>
        MessageStatus = 21,
        /// <summary>
        /// Message Types
        /// </summary>
        MessageType = 8,
        /// <summary>
        /// Poll Type
        /// </summary>
        PollType = 34,
        /// <summary>
        /// PollwareLive website user roles
        /// </summary>
        PollwareLiveUserRole = 37,
        /// <summary>
        /// Progress Indicator
        /// </summary>
        ProgressIndicator = 31,
        /// <summary>
        /// Provisional Ballot Reason
        /// </summary>
        ProvisionalBallotReason = 30,
        /// <summary>
        /// QueueItemStatus
        /// </summary>
        QueueItemStatus = 16,
        /// <summary>
        /// Queue Item Type
        /// </summary>
        QueueItemType = 17,
        /// <summary>
        /// Messaging Service used for sending SmsMessages
        /// </summary>
        SmsMessageService = 22,
        /// <summary>
        /// Response codes for transactions
        /// </summary>
        TransactionProcessingStatus = 25,
        /// <summary>
        /// Transactions and Device Event Types
        /// </summary>
        TransactionType = 1,
        /// <summary>
        /// Voter Statuses
        /// </summary>
        VoterStatus = 2,
        /// <summary>
        /// Skip signature reasons
        /// </summary>
        SkipSignatureReason = 46,
        /// <summary>
        /// Not In Roster Reasons
        /// </summary>
        NotInRosterReasons = 47,
        /// <summary>
        /// Unable to sign reasons
        /// </summary>
        UnableToSignReason = 50
    }

    /// <summary>
    /// Absentee Statuses
    /// </summary>
    public enum AbsenteeStatus
    {
        /// <summary>
        /// Unknown
        /// Code: Unknown
        /// </summary>
        Unknown = 0,

        /// <summary>
        /// Absentee
        /// Code: Absentee
        /// </summary>
        Absentee = 1,

        /// <summary>
        /// Active
        /// Code: Active
        /// </summary>
        Active = 2,

        /// <summary>
        /// Early Vote Issued
        /// Code: EARLY_VOTE_ISSUED
        /// </summary>
        EarlyVoteIssued = 10,

        /// <summary>
        /// Absentee Pending Status
        /// Code: PENDING
        /// </summary>
        Pending = 3000

    }

    /// <summary>
    /// Address Type
    /// </summary>
    public enum AddressType
    {
        /// <summary>
        /// Unknown
        /// Code: Unknown
        /// </summary>
        Unknown = 0,

        /// <summary>
        /// Residential
        /// Code: Residential
        /// </summary>
        Residential = 1

    }

    /// <summary>
    /// Address Unit Type
    /// </summary>
    public enum AddressUnitType
    {
        /// <summary>
        /// Unknown
        /// Code: Unknown
        /// </summary>
        Unknown = 0,

        /// <summary>
        /// The address contains a unit
        /// Code: Unit
        /// </summary>
        Unit = 1

    }

    /// <summary>
    /// Alert Media Type
    /// </summary>
    public enum AlertMediaType
    {
        /// <summary>
        /// Email Message
        /// Code: Email
        /// </summary>
        Email = 1000

    }

    /// <summary>
    /// Alert Type
    /// </summary>
    public enum AlertType
    {
        /// <summary>
        /// Device Not Syncing
        /// Code: DeviceNotSyncing
        /// </summary>
        DeviceNotSyncing = 1003,

        /// <summary>
        /// Polling Place Not Syncing
        /// Code: PollingPlaceNotSyncing
        /// </summary>
        PollingPlaceNotSyncing = 1000,

        /// <summary>
        /// Polls Not Open On Time
        /// Code: PollsNotOpenOnTime
        /// </summary>
        PollsNotOpenOnTime = 1001,

        /// <summary>
        /// Provisional Ballot Count
        /// Code: ProvisionalBallotCount
        /// </summary>
        ProvisionalBallotCount = 2000,

        /// <summary>
        /// Provisional Ballot Ratio
        /// Code: ProvisionalBallotRatio
        /// </summary>
        ProvisionalBallotRatio = 2001,

        /// <summary>
        /// Reissued Ballot Count
        /// Code: ReissuedBallotCount
        /// </summary>
        ReissuedBallotCount = 2002,

        /// <summary>
        /// Reissued Ballot Ratio
        /// Code: ReissuedBallotRatio
        /// </summary>
        ReissuedBallotRatio = 2003

    }

    /// <summary>
    /// Ballot Reissue Reason
    /// </summary>
    public enum BallotReissueReason
    {
        /// <summary>
        /// Other
        /// Code: OTHER
        /// </summary>
        Other = 0

    }

    /// <summary>
    /// Response codes for batches
    /// </summary>
    public enum BatchStatus
    {
        /// <summary>
        /// Batch was not processed
        /// Code: NOTPROCESSED
        /// </summary>
        NotProcessed = 0,

        /// <summary>
        /// All transactions failed to process
        /// Code: FAILURE
        /// </summary>
        Failure = 3000,

        /// <summary>
        /// Batch failed due to batch model validation failure
        /// Code: MODELVALIDATIONFAILURE
        /// </summary>
        ModelValidationFailure = 2000,

        /// <summary>
        /// Some of the transactions in the batch failed to process
        /// Code: PARTIALFAILURE
        /// </summary>
        PartialFailure = 3010,

        /// <summary>
        /// All transactions were processed successfully
        /// Code: SUCCESSFULLYPROCESSED
        /// </summary>
        SuccessfullyProcessed = 1000,

        /// <summary>
        /// All transactions were processed successfully, however, there are warnings for some transactions
        /// Code: SUCCESSFULLYPROCESSEDWITHWARNINGS
        /// </summary>
        SuccessfullyProcessedWithWarnings = 1010

    }

    /// <summary>
    /// Card Type
    /// </summary>
    public enum CardType
    {
        /// <summary>
        /// Unknown
        /// Code: Unknown
        /// </summary>
        Unknown = 0

    }

    /// <summary>
    /// Bridgepoint SQLite DB Create Mode
    /// </summary>
    public enum DBCreateMode
    {
        /// <summary>
        /// All (Default)
        /// Code: All
        /// </summary>
        All = 1,

        /// <summary>
        /// Empty Databases
        /// Code: Empty
        /// </summary>
        EmptyDatabases = 3,

        /// <summary>
        /// No Voter Signatures
        /// Code: NoSig
        /// </summary>
        NoVoterSignatures = 2

    }

    /// <summary>
    /// Device Types
    /// </summary>
    public enum DeviceType
    {
        /// <summary>
        /// Unknown
        /// Code: Unknown
        /// </summary>
        Unknown = 0,

        /// <summary>
        /// Tablet
        /// Code: Tablet
        /// </summary>
        Tablet = 1

    }

    /// <summary>
    /// District Types
    /// </summary>
    public enum DistrictType
    {
        /// <summary>
        /// Unknown
        /// Code: Unknown
        /// </summary>
        Unknown = 0,

        /// <summary>
        /// PrecinctGroup
        /// Code: PrecinctGroup
        /// </summary>
        PrecinctGroup = 2,

        /// <summary>
        /// WardGroup
        /// Code: WardGroup
        /// </summary>
        WardGroup = 1

    }

    /// <summary>
    /// Election Type
    /// </summary>
    public enum ElectionType
    {
        /// <summary>
        /// Closed Primary
        /// Code: CLOSED_PRIMARY
        /// </summary>
        ClosedPrimary = 300,

        /// <summary>
        /// General Election
        /// Code: GENERAL
        /// </summary>
        General = 100,

        /// <summary>
        /// Open Primary
        /// Code: OPEN_PRIMARY
        /// </summary>
        OpenPrimary = 200

    }

    /// <summary>
    /// ExportData
    /// </summary>
    public enum ExportData
    {
        /// <summary>
        /// Unknown
        /// Code: Unknown
        /// </summary>
        Unknown = 0,

        /// <summary>
        /// AllUsers
        /// Code: AllUsers
        /// </summary>
        AllUsers = 1000,

        /// <summary>
        /// BallotsByDistrict
        /// Code: BallotsByDistrict
        /// </summary>
        BallotsByDistrict = 2102,

        /// <summary>
        /// BallotsByPollingLocation
        /// Code: BallotsByPollingLocation
        /// </summary>
        BallotsByPollingLocation = 2100,

        /// <summary>
        /// BallotsByPrecinct
        /// Code: BallotsByPrecinct
        /// </summary>
        BallotsByPrecinct = 2101,

        /// <summary>
        /// BallotsByPrecinctPortion
        /// Code: BallotsByPrecinctPortion
        /// </summary>
        BallotsByPrecinctPortion = 2103,

        /// <summary>
        /// BallotsByType
        /// Code: BallotsByType
        /// </summary>
        BallotsByType = 2104,

        /// <summary>
        /// TurnoutByBallotParty
        /// Code: TurnoutByBallotParty
        /// </summary>
        TurnoutByBallotParty = 2205,

        /// <summary>
        /// TurnoutByDistrict
        /// Code: TurnoutByDistrict
        /// </summary>
        TurnoutByDistrict = 2202,

        /// <summary>
        /// TurnoutByHour
        /// Code: TurnoutByHour
        /// </summary>
        TurnoutByHour = 2204,

        /// <summary>
        /// TurnoutByPollingLocation
        /// Code: TurnoutByPollingLocation
        /// </summary>
        TurnoutByPollingLocation = 2200,

        /// <summary>
        /// TurnoutByPrecinct
        /// Code: TurnoutByPrecinct
        /// </summary>
        TurnoutByPrecinct = 2201,

        /// <summary>
        /// TurnoutByRegisteredParty
        /// Code: TurnoutByRegisteredParty
        /// </summary>
        TurnoutByRegisteredParty = 2203,

        /// <summary>
        /// VotedByBallotParty
        /// Code: VotedByBallotParty
        /// </summary>
        VotedByBallotParty = 2006,

        /// <summary>
        /// VotedByDistrict
        /// Code: VotedByDistrict
        /// </summary>
        VotedByDistrict = 2003,

        /// <summary>
        /// VotedByPollingLocation
        /// Code: VotedByPollingLocation
        /// </summary>
        VotedByPollingLocation = 2001,

        /// <summary>
        /// VotedByPrecinct
        /// Code: VotedByPrecinct
        /// </summary>
        VotedByPrecinct = 2002,

        /// <summary>
        /// VotedByRegisteredParty
        /// Code: VotedByRegisteredParty
        /// </summary>
        VotedByRegisteredParty = 2004,

        /// <summary>
        /// VotedNotInRoster
        /// Code: VotedNotInRoster
        /// </summary>
        VotedNotInRoster = 2005,

        /// <summary>
        /// VotedOverview
        /// Code: VotedOverview
        /// </summary>
        VotedOverview = 2000

    }

    /// <summary>
    /// File Type
    /// </summary>
    public enum FileType
    {
        /// <summary>
        /// Unknown
        /// Code: Unknown
        /// </summary>
        Unknown = 0,

        /// <summary>
        /// Comma Separated Values
        /// Code: CSV
        /// </summary>
        Csv = 1,

        /// <summary>
        /// Portable Document Format
        /// Code: PDF
        /// </summary>
        Pdf = 2

    }

    /// <summary>
    /// Gender Codes
    /// </summary>
    public enum Gender
    {
        /// <summary>
        /// Unknown
        /// Code: Unknown
        /// </summary>
        Unknown = 0,

        /// <summary>
        /// Female
        /// Code: Female
        /// </summary>
        Female = 2,

        /// <summary>
        /// Male
        /// Code: Male
        /// </summary>
        Male = 1,

        /// <summary>
        /// Other
        /// Code: Other
        /// </summary>
        Other = 3

    }

    /// <summary>
    /// Identification requirements
    /// </summary>
    public enum IdentificationRequirementStatus
    {
        /// <summary>
        /// Unknown
        /// Code: Unknown
        /// </summary>
        Unknown = 0,

        /// <summary>
        /// Voter identification not required
        /// Code: IDENTIFICATION_NOTREQUIRED
        /// </summary>
        IdentificationNotRequired = 1000,

        /// <summary>
        /// Voter identification required
        /// Code: IDENTIFICATION_REQUIRED
        /// </summary>
        IdentificationRequired = 2000

    }

    /// <summary>
    /// ISO Language Types
    /// </summary>
    public enum ISOLanguage
    {
        /// <summary>
        /// Unknown
        /// Code: Unknown
        /// </summary>
        Unknown = 0,

        /// <summary>
        /// English Language code
        /// Code: eng
        /// </summary>
        English = 1,

        /// <summary>
        /// Spanish
        /// Code: spa
        /// </summary>
        Spanish = 2

    }

    /// <summary>
    /// Job Title
    /// </summary>
    public enum JobTitle
    {
        /// <summary>
        /// All Job Titles
        /// Code: All
        /// </summary>
        AllJobTitles = 100,

        /// <summary>
        /// Specific Job Title
        /// Code: Specific
        /// </summary>
        SpecificJobTitle = 200

    }

    /// <summary>
    /// Ballot Media Type
    /// </summary>
    public enum MediaType
    {
        /// <summary>
        /// Unknown
        /// Code: Unknown
        /// </summary>
        Unknown = 0,

        /// <summary>
        /// Digitial Recording (DRE)
        /// Code: DRE
        /// </summary>
        DigitialRecording = 2,

        /// <summary>
        /// ExpressVote
        /// Code: ExpressVote
        /// </summary>
        ExpressVote = 3,

        /// <summary>
        /// Paper
        /// Code: Paper
        /// </summary>
        Paper = 1,

        /// <summary>
        /// Dominion Activation Card
        /// Code: DAC
        /// </summary>
        Dominion = 4

    }

    /// <summary>
    /// SMS Message Service Status
    /// </summary>
    public enum MessageStatus
    {
        /// <summary>
        /// Unknown
        /// Code: Unknown
        /// </summary>
        Unknown = 0,

        /// <summary>
        /// SMS Service has accepted the request
        /// Code: Accepted
        /// </summary>
        AcceptedByService = 1000,

        /// <summary>
        /// The message failed to send to the recipient due to an error
        /// Code: Failed
        /// </summary>
        FailedToSend = 3010,

        /// <summary>
        /// SMS Service has confirmed the recipient recieved the message
        /// Code: Confirmed
        /// </summary>
        ReceiptConfirmed = 2020,

        /// <summary>
        /// SMS Service is sending the message to the recipient
        /// Code: Sending
        /// </summary>
        SendingToUser = 2000,

        /// <summary>
        /// SMS Service has sent the message to the recipient (unconfirmed receipt)
        /// Code: Sent
        /// </summary>
        SentToUser = 2010,

        /// <summary>
        /// The message was not delivered
        /// Code: Undelivered
        /// </summary>
        Undelivered = 3000,

        /// <summary>
        /// The SMS Service has recieved a response from the recipient
        /// Code: Responded
        /// </summary>
        UserResponded = 4010,

        /// <summary>
        /// The SMS Service is recieving a response from the recipient
        /// Code: Responding
        /// </summary>
        UserResponding = 4000

    }

    /// <summary>
    /// Message Types
    /// </summary>
    public enum MessageType
    {
        /// <summary>
        /// Unknown
        /// Code: Unknown
        /// </summary>
        Unknown = 0,

        /// <summary>
        /// Alternate Email
        /// Code: Alternate_Email
        /// </summary>
        AlternateEmail = 2,

        /// <summary>
        /// Email
        /// Code: Email
        /// </summary>
        Email = 1,

        /// <summary>
        /// SMS
        /// Code: SMS
        /// </summary>
        Sms = 3

    }

    /// <summary>
    /// Poll Type
    /// </summary>
    public enum PollType
    {
        /// <summary>
        /// Both
        /// Code: BOTH
        /// </summary>
        Both = 100,

        /// <summary>
        ///    Early Vote
        ///    Code: EARLY_VOTE
        ///    CEV ID: 196
        /// </summary>
        EarlyVote = 300,
        /// <summary>
        ///    Early Vote
        ///    Code: EARLY_VOTE
        ///    Code: EV
        /// </summary>
        EV = 300,
        /// <summary>
        ///    Election Day
        ///    Code: ELECTION_DAY
        ///    CEV ID: 195
        /// </summary>
        ElectionDay = 200,
        /// <summary>
        ///    Election Day
        ///    Code: ELECTION_DAY
        ///    Code: ED
        /// </summary>
        ED = 200,


    }

    /// <summary>
    /// PollwareLive website user roles
    /// </summary>
    public enum PollwareLiveUserRole
    {
        /// <summary>
        /// Pollware Live website Administrator
        /// Code: ADMIN
        /// </summary>
        Admin = 3,

        /// <summary>
        /// Pollware Live website ESS Account Manager
        /// Code: ESS_ACCT_MANAGER
        /// </summary>
        EssAcctManager = 2,

        /// <summary>
        /// Pollware Live website ESS Administrator
        /// Code: ESS_ADMIN
        /// </summary>
        EssAdmin = 1,

        /// <summary>
        /// Pollware Live website report creator
        /// Code: REPORT_CREATOR
        /// </summary>
        ReportCreator = 5,

        /// <summary>
        /// Pollware Live website user
        /// Code: USER
        /// </summary>
        User = 4

    }

    /// <summary>
    /// Progress Indicator
    /// </summary>
    public enum ProgressIndicator
    {
        /// <summary>
        /// Unknown
        /// Code: UNKNOWN
        /// </summary>
        Unknown = 0,

        /// <summary>
        /// Process Completed
        /// Code: COMPLETED
        /// </summary>
        Completed = 2000,

        /// <summary>
        /// Process Failure
        /// Code: FAILED
        /// </summary>
        Failed = 3000,

        /// <summary>
        /// Process Started
        /// Code: STARTED
        /// </summary>
        Started = 1000

    }

    /// <summary>
    /// Provisional Ballot Reason
    /// </summary>
    public enum ProvisionalBallotReason
    {
        /// <summary>
        /// Other
        /// Code: OTHER
        /// </summary>
        Other = 0

    }

    /// <summary>
    /// QueueItemStatus
    /// </summary>
    public enum QueueItemStatus
    {
        /// <summary>
        /// Unprocessed
        /// Code: Unprocessed
        /// </summary>
        Unprocessed = 0,

        /// <summary>
        /// Completed
        /// Code: Completed
        /// </summary>
        Completed = 1,

        /// <summary>
        /// Failed
        /// Code: Failed
        /// </summary>
        Failed = 3,

        /// <summary>
        /// InProgress
        /// Code: InProgress
        /// </summary>
        InProgress = 2

    }

    /// <summary>
    /// Queue Item Type
    /// </summary>
    public enum QueueItemType
    {
        /// <summary>
        /// Report
        /// Code: Report
        /// </summary>
        Report = 1,

        /// <summary>
        /// Bridgepoint Sqlite Database
        /// Code: SqliteDatabase
        /// </summary>
        SqliteDatabase = 2

    }

    /// <summary>
    /// Response codes for transactions
    /// </summary>
    public enum TransactionProcessingStatus
    {
	    /// <summary>
	    /// Transaction was not processed
	    /// Code: NOTPROCESSED
	    /// </summary>
	    NotProcessed = 0,

	    /// <summary>
	    /// An error ocurred while processing this transaction
	    /// Code: ERRORWHILEPROCESSING
	    /// </summary>
	    ErrorWhileProcessing = 3000,

	    /// <summary>
	    /// Model Validation Failed
	    /// Code: MODELVALIDATIONFAILURE
	    /// </summary>
	    ModelValidationFailure = 2000,

	    /// <summary>
	    /// The transaction was successful, but was processed as unknown
	    /// Code: PROCESSEDASUNKNOWN
	    /// </summary>
	    ProcessedAsUnknownType = 4000,

	    /// <summary>
	    /// Transaction was submitted successfully
	    /// Code: SUCCESSFULLYPROCESSED
	    /// </summary>
	    SuccessfullyProcessed = 1000
    }

    /// <summary>
    /// Transactions and Device Event Types
    /// </summary>
    public enum TransactionType
    {
        /// <summary>
        /// Unknown
        /// Code: UNKOWN
        /// </summary>
        Unknown = 0,

        /// <summary>
        /// Cancel a ballot
        /// Code: BALLOT_CANCL
        /// </summary>
        BallotCancel = 3010,

        /// <summary>
        /// Issue a ballot
        /// Code: BALLOT_ISSUE
        /// </summary>
        BallotIssue = 3000,

        /// <summary>
        /// A Keep alive used to periodically update the status of a device
        /// Code: HEARTBEAT
        /// </summary>
        Heartbeat = 100,

        /// <summary>
        /// Incremental data update from syncpoint
        /// Code: INCREMENTALUPDATE
        /// </summary>
        IncrementalUpdate = 5000,

        /// <summary>
        /// Close the polls for a polling place
        /// Code: POLLS_CLOSE
        /// </summary>
        PollsClose = 2010,

        /// <summary>
        /// Open the polls for a polling place
        /// Code: POLLS_OPEN
        /// </summary>
        PollsOpen = 2000,

        /// <summary>
        /// Report an error that ocurred within the software
        /// Code: SOFTWARE_ERROR
        /// </summary>
        SoftwareError = 1030,

        /// <summary>
        /// Software has exited
        /// Code: SOFTWARE_EXIT
        /// </summary>
        SoftwareExit = 1020,

        /// <summary>
        /// Software initializing
        /// Code: SOFTWARE_INITIALIZATION
        /// </summary>
        SoftwareInitialization = 1040,

        /// <summary>
        /// A user has signed in to the software
        /// Code: SOFTWARE_SIGNIN
        /// </summary>
        SoftwareSignIn = 1000,

        /// <summary>
        /// A user has signed out of the software
        /// Code: SOFTWARE_SIGNOUT
        /// </summary>
        SoftwareSignOut = 1010,

        /// <summary>
        /// Add a new voter
        /// Code: VOTER_ADD
        /// </summary>
        VoterAdd = 4000,

        /// <summary>
        /// Edit a voter
        /// Code: VOTER_EDIT
        /// </summary>
        VoterEdit = 4010,

        /// <summary>
        /// Change the status of a voter
        /// Code: VOTER_STATUSCHANGE
        /// </summary>
        VoterStatusChange = 4020,

        /// <summary>
        /// Custom transaction type that will define its subtype in the JSON
        /// Code: CUSTOM
        /// </summary>
        Custom = 6000,

        /// <summary>
        /// Test mode type
        /// CODE: TEST_MODE
        /// </summary>
        TestMode = 200

    }

    /// <summary>
    /// Voter Statuses
    /// </summary>
    public enum VoterStatus
    {
        /// <summary>
        /// Unknown
        /// Code: Unknown
        /// </summary>
        Unknown = 0,

        /// <summary>
        /// Active
        /// Code: Active
        /// </summary>
        Active = 1,

        /// <summary>
        /// Inactive
        /// Code: Inactive
        /// </summary>
        Inactive = 2,

        /// <summary>
        /// Pending
        /// Code: Pending
        /// </summary>
        Pending = 3
    }

    public enum DeviceStatuses
    {
        [Description("Unknown - specifically can happen if the election is invalid.")]
        Unknown = 0,
        [Description("Initial value, has not been in contact with the host to retrieve an appropriate value.")]
        Initial = 221,
        [Description("Pending device authorization in Connect.")]
        Pending = 222,
        [Description("Device is approved for use.")]
        Approved = 223,
        [Description("Access has been explicitly denied for this device.")]
        AccessDenied = 224
    }

    public enum LoadElectionCode
    {
        [Description("Unknown problem")]
        Unknown,

        [Description("Database Not Found")]
        DBFNF,

        [Description("Database Invalid")]
        DBINV,

        [Description("Zipfile Failed To Extract")]
        ZPFFTE,

        [Description("ZipFile Invalid")]
        ZPFINV,

        [Description("Database connection failed to close")]
        CNFC
    }

    public enum BallotStyleType
    {
        Provisional,
        Standard
    }

    public enum NotInRosterReasonCodes
    {
       OTHER = 1,
       DL,
       SSN4
    }

    #region pollworker

    public enum PollworkerScreenManagementOptions
    {
        Required = 1,
        Optional = 2,
        Hidden = 3
    }

    public enum TransactionTypes
    {
        InsertPollworker = 1,
        EditPollworker,
        TimeChange,
        PollworkerOath,
        JudgeApproval
    }

    #endregion pollworker
    public enum ChecklistType
    {
        [Description("Poll place open.")]
        PPO = 0,
        [Description("Poll place closed.")]
        PPC = 1
    }

    public enum ChecklistPollType
    {
        [Description("Election Day")]
        ED = 1,
        [Description("Early Vote")]
        EV = 2,
        [Description("Both")]
        Both = 3
    }

}