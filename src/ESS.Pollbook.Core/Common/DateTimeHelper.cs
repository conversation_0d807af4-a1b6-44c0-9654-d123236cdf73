using ESS.Pollbook.Core.StaticValues;
using System;
using System.Globalization;

namespace ESS.Pollbook.Core.Common
{
    public static class DateTimeHelper
    {
        public const string PollbookDateTimeFormat = "MM-dd-yyyy h:mm tt";
        public const string PollbookDateTimeFormatMSec = "MM-dd-yyyy hh:mm:ss.fffffff";
        public const string PollbookDisplayFormatSeconds = "MM-dd-yyyy h:mm:ss tt";
        public static string GetDisplayDateTime(DateTime dateTime, string pollBookDateTimeFormat = PollbookDisplayFormatSeconds)
        {
            return SystemDetails.IsPQCVerified ? GetDateTimeByTimeZone(dateTime, pollBookDateTimeFormat) : GetDateTimeByLocal(dateTime, pollBookDateTimeFormat);
        }

        public static string GetDateTimeByTimeZone(DateTime dateTime, string pollBookDateTimeFormat)
        {
            if (dateTime == DateTime.MinValue) return string.Empty;
            var timeZone = SystemConfiguration.ElectionConfiguration.TimeZoneName;
            var timeZoneInfo = TimeZoneInfo.FindSystemTimeZoneById(timeZone);
            return TimeZoneInfo.ConvertTimeFromUtc(dateTime, timeZoneInfo).ToString(pollBookDateTimeFormat);
        }

        public static string GetDateTimeByLocal(DateTime dateTime, string pollBookDateTimeFormat)
        {
            return dateTime == DateTime.MinValue ? string.Empty : Convert.ToDateTime(dateTime).ToLocalTime().ToString(pollBookDateTimeFormat);
        }

        public static DateTime? GetDate(this string dateString)
        {
            return DateTime.TryParseExact(dateString, "MMddyyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out var date)
                ? date : (DateTime?)null;
        }
    }
}
