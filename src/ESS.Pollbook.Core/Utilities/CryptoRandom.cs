using System;
using System.Security.Cryptography;

namespace ESS.Pollbook.Core.Utilities {
	public sealed class CryptoRandom
	{
		private static CryptoRandom _instance;
		private const int Msb = 1 << 31;

		public static CryptoRandom Instance
		{
			get
			{
				if (_instance != null)
					return _instance;
				_instance = new CryptoRandom();
				return _instance;
			}
		}

		private readonly RandomNumberGenerator _rng;

		private CryptoRandom()
		{
			_rng = RandomNumberGenerator.Create();
		}

		public int NextPositiveOrNegativeInt()
		{
			byte[] buffer = new byte[sizeof(int)];
			NextBytes(buffer);
			return BitConverter.ToInt32(buffer, 0);
		}

		public void NextBytes(byte[] buffer)
		{
			_rng.GetBytes(buffer);
		}

		public double NextDouble()
		{
			return Next() * 1.0 / int.MaxValue;
		}

		public int Next()
		{
			return NextPositiveOrNegativeInt() & ~Msb;
		}

		/// <summary>
		/// Next Random number from 0 to exclusiveMax-1
		/// </summary>
		/// <param name="exclusiveMax">exclusive as in NOT part of the range</param>
		/// <returns></returns>
		/// <exception cref="ArgumentOutOfRangeException">If max is less then zero</exception>
		public int Next(int exclusiveMax)
		{
			if (exclusiveMax < 0)
				throw new ArgumentOutOfRangeException(nameof(exclusiveMax), $"Must be >= 0, was {exclusiveMax}");
			return (int)(NextDouble() * exclusiveMax);
		}

		/// <summary>
		/// Next Random number from inclusiveMin to exclusiveMax
		/// </summary>
		/// <param name="inclusiveMin">inclusive, as in part of the range</param>
		/// <param name="exclusiveMax">exclusive as in NOT part of the range</param>
		/// <returns></returns>
		/// <exception cref="ArgumentOutOfRangeException">If max is greater then min</exception>
		public int Next(int inclusiveMin, int exclusiveMax)
		{
			if (inclusiveMin > exclusiveMax)
				throw new ArgumentOutOfRangeException(nameof(inclusiveMin), $"Must be <= {nameof(exclusiveMax)}, was {inclusiveMin} ({nameof(exclusiveMax)} = {exclusiveMax})");
			return inclusiveMin + (int)(NextDouble() * (exclusiveMax - inclusiveMin));
		}
	}
}