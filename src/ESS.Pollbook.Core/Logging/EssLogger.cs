using log4net;
using log4net.Core;
using System;
using System.Collections.Generic;
using System.IO;
using System.Runtime.CompilerServices;
using System.Text;
using System.Collections.Concurrent;

namespace ESS.Pollbook.Core.Logging
{
    public class EssLogger : IEssLogger
    {
        private readonly ILog _log;

        private readonly ConcurrentDictionary<string, int> _logEntryCache = new ConcurrentDictionary<string, int>();

        private DateTime _logDate = DateTime.Today;

        private const int MaxMessageCount = 100;

        public const string ActionKey = "Action";

        public EssLogger()
        {
            _log = LogManager.GetLogger(typeof(EssLogger));
        }

        public EssLogger(string pathToLog4NetConfig)
        {
	        log4net.Config.XmlConfigurator.Configure(new FileInfo(pathToLog4NetConfig));
	        _log = LogManager.GetLogger(typeof(EssLogger));
        }

        public bool IsDebugEnabled()
        {
	        return _log.IsDebugEnabled;
        }

        public void ChangeLoggingLevel(Level level)
        {
	        var logger = (log4net.Repository.Hierarchy.Logger)_log.Logger;
	        logger.Level = level;
        }

        public void LogDebug(string message, Dictionary<string, string> properties = null)
        {
	        _log.Debug(FormatRawMessage(message, properties));
        }

        public void LogInformation(string message, Dictionary<string, string> properties = null)
        {
	        _log.Info(FormatRawMessage(message, properties));
        }

        public void LogWarning(string warning, Dictionary<string, string> properties = null)
        {
	        _log.Warn(FormatRawMessage(warning, properties));
        }

        public void LogError(Exception exception, Dictionary<string, string> properties = null,
	        [CallerFilePath]
	        string callerFilePath = null,
	        [CallerMemberName]
	        string callerMemberName = null)
        {
	        if (exception == null)
		        return;

	        if (properties == null)
		        properties = new Dictionary<string, string>();

	        if (!string.IsNullOrEmpty(callerFilePath))
	        {
		        var p = Path.GetFileNameWithoutExtension(callerFilePath);
		        if (!string.IsNullOrEmpty(p))
			        properties[ActionKey] = $"{p}.{callerMemberName}";
	        }

	        LogExceptionOrError(exception.Message, properties, exception);
        }

        public void LogError(string error, Dictionary<string, string> properties = null,
	        [CallerFilePath]
	        string callerFilePath = null,
	        [CallerMemberName]
	        string callerMemberName = null)
        {
	        if (string.IsNullOrWhiteSpace(error))
		        return;

	        if (properties == null)
		        properties = new Dictionary<string, string>();

	        if (!string.IsNullOrEmpty(callerFilePath))
	        {
		        var p = Path.GetFileNameWithoutExtension(callerFilePath);
		        if (!string.IsNullOrEmpty(p))
			        properties[ActionKey] = $"{p}.{callerMemberName}";
	        }

	        LogExceptionOrError(error, properties);
        }

        private void LogExceptionOrError(string error, Dictionary<string, string> properties = null, Exception exception = null)
        {
	        string cacheKey = null;

	        if (properties?.TryGetValue(ActionKey, out cacheKey) == false)
		        cacheKey = error;

	        if (string.IsNullOrWhiteSpace(cacheKey))
		        return;

	        LogAndCache(FormatRawMessage(error, properties), cacheKey, exception);
        }

        private void LogAndCache(string formattedMessage, string cacheKey, Exception exception = null)
        {
	        // On each new day, the log file will roll over. We want to start fresh with each new log file, so clear the cache on each new day.
	        if (DateTime.Today > _logDate)
	        {
		        _logEntryCache.Clear();
		        _logDate = DateTime.Today;
	        }

	        _logEntryCache.AddOrUpdate(cacheKey, 0, (_, cnt) => ++cnt);
	        if (_logEntryCache[cacheKey] > MaxMessageCount)
		        return;

	        _log.Error(formattedMessage, exception);
	        if (_logEntryCache[cacheKey] == MaxMessageCount)
		        _log.Error($"The preceding error message has been logged {MaxMessageCount} times and will not be logged again.");
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        private static string FormatRawMessage(string message, Dictionary<string, string> properties)
        {
	        if (properties == null || properties.Count == 0)
		        return message;

	        var formattedMessage = new StringBuilder();
	        formattedMessage.Append(message);
	        foreach (var property in properties)
		        formattedMessage.Append(Environment.NewLine).Append($" {property.Key}:{property.Value}");

	        return formattedMessage.ToString();
        }
    }
}
