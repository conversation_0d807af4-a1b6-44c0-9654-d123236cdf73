namespace ESS.Pollbook.Core.Constants
{
    public static class PeerToPeerConstants
    {
        public const int maxVoterTransactionCount = 4750;
        public const int maxEditVoterTransactionCount = 250;
        public const int maxTotalTransactionCount = maxVoterTransactionCount + maxEditVoterTransactionCount;
        public const int syncResponseTimeoutSeconds = 30;
        public const int largeSyncResponseTimeoutSeconds = 600;
        public const int maxTransactionsPerRequest = 250000;
        public const int maxBallotTransactionsPerRequest = 125000;
        public const int maxEditTransactionsPerRequest = 100000;
        public const int maxAddTransactionsPerRequest = 25000;
    }
}
