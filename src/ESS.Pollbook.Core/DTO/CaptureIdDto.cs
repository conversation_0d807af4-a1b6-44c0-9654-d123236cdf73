namespace ESS.Pollbook.Core.DTO
{
	public class CaptureIdDto
	{
		/// <summary>
		/// Specifically populated from CaptureIdConfigurationViewModel.
		/// </summary>
		public bool IdWasPresentedByVoter { get; set; }

		/// <summary>
		/// When the screen is presented for which ID type they select, take that
		/// record and create a clone here of it.
		/// </summary>
		public ElectionCaptureIdVoterTypeDbo CaptureIdType { get; set; }
	}
}
