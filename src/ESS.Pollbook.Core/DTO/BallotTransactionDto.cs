namespace ESS.Pollbook.Core.DTO
{
    public class BallotTransactionDto : CustomTypeDescriptor
    {
        public string Voter { get; set; }
        public string SourceKey { get; set; }
        public string BallotStyleId { get; set; }
        public string MediaType { get; set; }
        public string CardType { get; set; }
        public bool Provisional { get; set; }
        public int? ProvisionalId { get; set; }
        public bool IsReissue { get; set; }
        public bool IsSignature { get; set; }
        public bool IsVibsAudio { get; set; }
        public bool IsVbsHide { get; set; }
        public bool IsVibsMagnify { get; set; }
        public bool IsPrinted { get; set; }
        public bool IsTest { get; set; }
        public string PrinterName { get; set; }
        public int? LanguageEnumerationId { get; set; }
        public string ReasonCode { get; set; }
        public string BallotNumber { get; set; }
        public bool IsAlternateStyle { get; set; }
        public string UserInitials { get; set; }
        public string UserName { get; set; }
        public string TransactionType { get; private set; }
    }
}
