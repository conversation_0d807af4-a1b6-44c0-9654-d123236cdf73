namespace ESS.Pollbook.Core.DTO
{
    public class PrinterFormatFieldDto
    {
        public int EarlyVote { get; set; }
        public int LineNumber { get; set; }
        public string FieldName { get; set; }
        public string CustomLabel { get; set; }
        public int DisplayLabel { get; set; }
        public int DisplayContent { get; set; }
        public string Justified { get; set; }
        public int HorizontalLine { get; set; }
        public string LabelFontFamily { get; set; }
        public string ContentFontFamily { get; set; }
        public int LabelFontSize { get; set; }
        public int ContentFontSize { get; set; }
        public int LabelBold { get; set; }
        public int ContentBold { get; set; }
        public int LabelUnderline { get; set; }
        public int ContentUnderline { get; set; }
        public int LabelReversed { get; set; }
        public int ContentReversed { get; set; }
    }
}
