using System;

namespace ESS.Pollbook.Core.DTO
{
    public class DynamicControlsStoredDataDto : CustomTypeDescriptor
    {
        #region Properties
        public int Control_Stored_Data_Control_ID { get; set; }
        public int Control_Stored_Data_Voter_ID { get; set; }
        public string Control_Stored_Data_Value { get; set; }
        public DateTime Control_Stored_Data_UTC_Datetime { get; set; }
        public string Control_Stored_Data_User_Name { get; set; }
        public string Control_Stored_Data_Application_Name { get; set; }
        public string Control_Stored_Data_Application_User_Name { get; set; }
        public DateTime Control_Stored_Data_Application_Datetime { get; set; }
        #endregion

        #region Constructors
        public DynamicControlsStoredDataDto()
        {
            // constructor
        }
        #endregion
    }
}
