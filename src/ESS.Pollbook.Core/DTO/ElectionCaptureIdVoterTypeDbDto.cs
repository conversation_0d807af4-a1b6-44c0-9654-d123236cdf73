namespace ESS.Pollbook.Core.DTO
{
	/// <summary>
	/// This classes specific function is transfer too and from the database table Election_Capture_Voter_ID_Type.
	/// This is a DATABASE only DTO and should only be utilized in a read only fashion.  It can get passed around
	/// but never modified.
	/// </summary>
	public class ElectionCaptureIdVoterTypeDbo : CustomTypeDescriptor
	{
		public int Capture_Voter_ID_Type_ID { get; set; }

		public string Capture_Voter_ID_Type_Name { get; set; }

		public string Capture_Voter_ID_Type_Form_Name { get; set; }

		/// <summary>
		/// Database has this field as nullable.
		/// </summary>
		public int? Capture_Voter_ID_Type_Sort_Order { get; set; }

		public int Provisional_Only { get; set; }
	}
}
