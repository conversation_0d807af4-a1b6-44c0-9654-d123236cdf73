using System;

namespace ESS.Pollbook.Core.DTO
{
    public class CommonEnumerationValueDto : CustomTypeDescriptor
    {
        public string EnumerationName { get; set; }
        public int ParentEnumerationId { get; set; }
        public int EnumerationValueId { get; set; }
        public int EnumerationId { get; set; }
        public int EnumerationValueNumber { get; set; }
        public string EnumerationValueCode { get; set; }
        public string EnumerationValueName { get; set; }
        public string EnumerationValueDescription { get; set; }
        public DateTime RecordUpdateUTCDateTime { get; set; }
        public string RecordUpdateUserName { get; set; }
        public string RecordUpdateApplicationName { get; set; }
        public string RecordUpdateApplicationUserName { get; set; }
        public DateTime? RecordUpdateApplicationDateTime { get; set; }
    }
}
