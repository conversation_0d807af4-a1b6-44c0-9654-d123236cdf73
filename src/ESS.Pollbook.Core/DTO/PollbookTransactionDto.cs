using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.Model;
using System;
using System.Collections.Generic;

namespace ESS.Pollbook.Core.DTO
{
    public class PollbookTransactionDto : CustomTypeDescriptor
    {
        HostTransactionResponse _JSONObject;

        public string EmailAddress { get; set; }
        public string FirstName { get; set; }
        public string MiddleName { get; set; }
        public string MiddleInitial { get; set; }
        public string LastName { get; set; }
        public string NameSuffix { get; set; }
        public string HouseNumber { get; set; }
        public string StreetName { get; set; }
        public string UnitValue { get; set; }
        public string City { get; set; }
        public string Zip { get; set; }
        public int? PrecinctSplitId { get; set; }
        public VoterAddressDto Address { get; set; }

        public int TransactionId { get; set; }

        public string DeviceName { get; set; }

        public string SerialNumber { get; set; }

        public string SessionGuid { get; set; }

        public string TransactionGuid { get; set; }

        public string SystemIdentifier { get; set; }

        public string SourceKey { get; set; }

        public int? PollingPlaceId { get; set; }

        public string TransactionType { get; set; }

        public string JSON { get; set; }

        public byte[] Signature { get; set; }

        public byte[] AdditionalSignature1 { get; set; }

        public byte[] AdditionalSignature2 { get; set; }

        public DateTime TransactionDate { get; set; }

        public bool IsPrinted { get; set; }

        public bool IsReissue { get; set; }

        public long HostTransactionId { get; set; }

        public int UploadQueueRetryAttempts { get; set; }

        public Dictionary<string, string> Attributes { get; set; }

        public VoterDto Voter { get; set; }

        public string BallotPartyName { get; set; }

        public string PrecinctName { get; set; }

        public int BallotPartyId { get; set; }

        public int MediaTypeEnumId { get; set; }

        public int? AbsenteeStatusEnumId { get; set; }
        public int? IdentificationRequirementStatusEnumId { get; set; }
        public int? VoterStatusEnumId { get; set; }

        public bool HasAddressChanged { get; set; }

        public bool IsProvisional { get; set; }

        public bool IsEarlyVote { get; set; }

        public int PrecinctSplitBallotStyleId { get; set; }

        public string ProvisionalBallotIdentifier { get; set; }
        public EssEnumeration? StatusType { get; set; }

        public int? BallotStyleId { get; set; }

        public string CreatedBy { get; set; }


        public int PrecinctId { get; set; }

        public bool IsThisBallotCancelled { get; set; }

        public List<AffidavitTemplateDto> Affidavits { get; set; }

        public string ProcessingStatus { get; set; }

        public string RecordSource { get; set; }

        public int Rowno { get; set; }

        public string ParentTransactionGuid { get; set; }

      private HostTransactionResponse JSONObject
        {
            get
            {
                if (_JSONObject == null)
                {
                    if (JSON != null)
                    {
                        _JSONObject = HostTransactionResponse.Create(JSON);
                    }
                    else
                    {
                        throw new Exception("Could not create a JSON object because the JSON property is empty");
                    }
                }
                return _JSONObject;
            }
        }

        public static PollbookTransactionDto Create()
        {
            var transaction = new PollbookTransactionDto();

            transaction.TransactionGuid = Guid.NewGuid().ToString("N");
            transaction.TransactionDate = DateTime.UtcNow;

            return transaction;
        }

        public List<object> ConvertToObjectList()
        {
            var emptyString = string.Empty;
            return new List<object>
            {
                emptyString,
                TransactionGuid,
                ParentTransactionGuid,
                emptyString,
                SystemIdentifier,
                DeviceName,
                SerialNumber,
                SourceKey,
                PollingPlaceId,
                TransactionType,
                JSON,
                Signature,
                AdditionalSignature1,
                AdditionalSignature2,
                TransactionDate,
                HostTransactionId,
                UploadQueueRetryAttempts,
                ProcessingStatus,
                RecordSource
            };
        }
   }
}
