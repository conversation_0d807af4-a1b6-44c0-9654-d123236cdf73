using ESS.Pollbook.Core.Attributes;
using NClone.MetadataProviders;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Text.Json.Serialization;

namespace ESS.Pollbook.Core.DTO
{
	public class VoterDto : CustomTypeDescriptor
	{
		[JsonIgnore] [Newtonsoft.Json.JsonIgnore]
		private static readonly string[] BirthdayFormats =
			{ "MM-dd-yyyy hh:mm", "M-dd-yyyy hh:mm", "MM-dd-yyyy", "MM-dd-yyyy hh:mm:ss", "MM/dd/yyyy" };

		[VoterAddEvent("AbsenteeStatusEnumId")]
		[VoterEditEvent("AbsenteeStatusEnumId")]
		public int AbsenteeStatusEnumId { get; set; }

		[JsonIgnore]
		[Newtonsoft.Json.JsonIgnore]
		public string DobDisplay { get; set; }

        [VoterAddEvent("AffidavitNumber")]
        [VoterEditEvent("AffidavitNumber")]
        public string AffidavitNumber { get; set; }

        public DateTime? BallotRecordUpdateApplicationDatetime { get; set; }

        public int BallotEventPollPlaceId { get; set; }

        [VoterAddEvent("BallotStyleId")]
        [VoterEditEvent("BallotStyleId")]
        public int? BallotStyleId { get; set; }

        [VoterAddress("City")]
        public string City { get; set; }

        [VoterAddress("AddressFullText")]
        public string CompleteAddress { get; set; }

        [VoterAddEvent("CountyId")]
        [VoterEditEvent("CountyId")]
        public int CountyId { get; set; }

        [VoterAddEvent("DateOfBirth", "StringToDateTime")]
        [VoterEditEvent("DateOfBirth", "StringToDateTime")]
        public string DateOfBirth { get; set; } = string.Empty;

        [VoterAddEvent("DriversLicenseNumber")]
        [VoterEditEvent("DriversLicenseNumber")]
        public string DriversLicenseNumber { get; set; }

        [VoterAddEvent("FirstName")]
        [VoterEditEvent("FirstName")]
        public string FirstName { get; set; }

        [VoterAddress("HouseNumber")]
        public string HouseNumber { get; set; }

        [VoterAddEvent("IdentificationRequiredStatusEnumId")]
        [VoterEditEvent("IdentificationRequiredStatusEnumId")]
        public int IdentificationStatusEnumId { get; set; }

        public bool IsPreviouslyVoted { get; set; }

        [VoterAddEvent("LastName")]
        [VoterEditEvent("LastName")]
        public string LastName { get; set; }

        [VoterAddEvent("MiddleName")]
        [VoterEditEvent("MiddleName")]
        public string MiddleName { get; set; }

        [VoterAddEvent("NameSuffix")]
        [VoterEditEvent("NameSuffix")]
        public string NameSuffix { get; set; }

        [VoterAddEvent("PartyId")]
        [VoterEditEvent("PartyId")]
        public int PartyId { get; set; }

        public string PartyName { get; set; }

        [VoterAddEvent("PrecinctSplitId")]
        [VoterEditEvent("PrecinctSplitId")]
        public int PrecinctSplitId { get; set; }

        [VoterAddEvent("SsnLast4")]
        [VoterEditEvent("SsnLast4")]
        public string SsnLast4Numbers { get; set; }

        [VoterAddress("IsoStateProvinceCode")]
        public string State { get; set; }

        [VoterAddress("StreetName")]
        public string StreetName { get; set; }

        [VoterAddress("UnitTypeName")]
        public string UnitTypeName { get; set; }

        [VoterAddress("UnitTypeCode")]
        public string UnitTypeCode { get; set; }

        [VoterAddress("UnitTypeEnumId")]
        public int? UnitTypeCodeId { get; set; }

        [VoterAddress("UnitValue")]
        public string UnitValue { get; set; }

        public bool? VoterBallotStatusProvisionalBallotIndicator { get; set; }

        public bool? VoterBallotStatusEarlyVoteIndicator { get; set; }

        [VoterAddEvent("VoterId")]
        [VoterEditEvent("VoterId")]
        public long? VoterId { get; set; }

        [VoterAddress("VoterAddressId")]
        public long? VoterAddressId { get; set; }

        public string VoterKey { get; set; }

        [VoterAddEvent("VoterStatusEnumId")]
        [VoterEditEvent("VoterStatusEnumId")]
        public int VoterStatusEnumId { get; set; }

        [VoterAddEvent("VoterBallotStyleTypeJurisdictionEnumValueId")]
        [VoterEditEvent("VoterBallotStyleTypeJurisdictionEnumValueId")]
        public int VoterBallotStyleTypeJurisdictionEnumValueId { get; set; }

        [VoterAddEvent("RecordInitialLoadIndicator")]
        [VoterEditEvent("RecordInitialLoadIndicator")]
        public bool RecordInitialLoadIndicator { get; set; }

        [VoterAddress("PostalCode")]
        public string Zip { get; set; }

        public bool HasHostResults { get; set; }

        public string CountyName { get; set; }

        public string BallotStyleDescription { get; set; }

        public string PrecinctSplitName { get; set; }

        public bool HasMultipleBallots { get; set; }

        public string IdRequired { get; set; }

        [VoterAddEvent("Comments")]
        [VoterEditEvent("Comments")]
        public string CommentText { get; set; }

        [VoterAddEvent("EmailAddress")]
        [VoterEditEvent("EmailAddress")]
        public string EmailAddress { get; set; }

        [VoterAddEvent("LanguageEnumId")]
        [VoterEditEvent("LanguageEnumId")]
        public int? ISOLanguageEnumerationValueId { get; set; }

        [VoterAddEvent("NamePrefix")]
        [VoterEditEvent("NamePrefix")]
        public string NamePrefix { get; set; }

        public string AdditionalPostalCode { get; set; }

        [VoterAddress("HouseNumberFraction")]
        public string HouseNumberFractionValue { get; set; }

        [VoterAddress("IsoCountryCode")]
        public string ISOCountryCode { get; set; }

        public PollPlaceDto VoterPollPlaceInfo { get; set; }
        public List<DynamicControlsDto> GetDynamicFormControls { get; set; }

        public int MediaTypeEnumId { get; set; }

        public int PrecinctId { get; set; }

        public bool HasAddressChanged { get; set; }

        public bool NeedExpressVoteCard { get; set; }

        public DynamicDto Dynamic { get; set; }

        public PrecinctPartyDto PrecinctParty { get; set; }

        public VerifyVoterIdDto TexasVerifyVoterId { get; set; }

        /// <summary>
        /// Utilized as another means to capture ID outside of the Texas Verify.
        /// This is general usage.  If Null, ignore.
        /// </summary>
        public CaptureIdDto CaptureId { get; set; }

        public VoterBallotDto VoterBallotDto { get; set; }

        public byte[] AcknowledgementNotOnRosterInitials { get; set; }

        public byte[] AcknowledgementSimilarNameInitials { get; set; }

        public int? SignatureSkipReasonEnumId { get; set; }

        public string SignatureSkipReasonOtherText { get; set; }

        public byte[] VoterTransactionSignature { get; set; }

        public byte[] AdditionalSignature1 { get; set; }

        public byte[] AdditionalSignature2 { get; set; }

        public byte[] VoterVRSignatureImage { get; set; }

        [JsonIgnore]
        [Newtonsoft.Json.JsonIgnore]
        public string FullName => GetFullName(false);

        [JsonIgnore]
        [Newtonsoft.Json.JsonIgnore]
        public string Surname => GetFullName();

        public DateTime? BirthDate { get; set; }

        [VoterAddress("AdditionalPostalCode")]
        public string Zip5 => Zip?.Length > 5 ? Zip.Substring(0, 5) : Zip;

        /// <summary>
        /// Dictionaries and NClone do not work well together.  The issue is not
        /// the value in it, but the internal array called "entries". This is visible
        /// in Visual Studio Debugger looking at the raw view of the variable.  The issue
        /// is that this might still be populated even though the values are cleared. The
        /// deep clone then will blow up with a CircularReferenceFoundException reporting a
        /// cyclic reference.  Adding this attribute fixes that by only doing a copy of the
        /// internal values and leaving the rest alone.
        /// </summary>
        [CustomReplicationBehavior(ReplicationBehavior.Copy)]
        public Dictionary<string, AffidavitTemplateDto> Affidavits { get; set; }

        public List<DynamicControlsDto> SelectedAffidavits { get; set; }

        public int CurrentIndex { get; set; }

        [VoterAddEvent("GenderId")]
        [VoterEditEventAttribute("GenderId")]

        public int? GenderId { get; set; }

        [VoterAddEvent("SignatureImageFileName")]
        [VoterEditEventAttribute("SignatureImageFileName")]
        public string VoterVRSignaturePath { get; set; }

        [VoterAddEvent("VoterDeleted", "IntToBool")]
        [VoterEditEventAttribute("VoterDeleted", "IntToBool")]
        public int Voter_Deleted { get; set; }

        public bool ValidPoll { get; set; }

        [VoterAddEvent("FirstNameSearch")]
        [VoterEditEventAttribute("FirstNameSearch")]
        public string FirstNameSearch { get; set; }

        [VoterAddEvent("LastNameSearch")]
        [VoterEditEventAttribute("LastNameSearch")]
        public string LastNameSearch { get; set; }

        public int? NotInRosterReasonsEnumId { get; set; }

        [VoterAddress("SessionId", "CreateGuid")]
        [JsonIgnore]
        [Newtonsoft.Json.JsonIgnore]
        public string SessionIdentifier { get; set; }

        /// <summary>
        /// Date the voter originally registered to vote, if available.
        /// </summary>
        public DateTime? RegistrationDate { get; set; }

        public long? RegistrationDateTicks { get; set; }

        /// <summary>
        /// Utilize for all your name display needs
        /// </summary>
        /// <returns></returns>
        public string GetFullName(bool surnameFirst = true)
        {
	        if (surnameFirst)
		        return string.Join(" ", new List<string>(4) { LastName + (!string.IsNullOrWhiteSpace(LastName) ? "," : string.Empty), FirstName, MiddleName, NameSuffix }.Where(x => !string.IsNullOrWhiteSpace(x)).Select(y => y.Trim()));

	        return string.Join(" ", new List<string>(4) { FirstName, MiddleName, LastName, NameSuffix }.Where(x => !string.IsNullOrWhiteSpace(x)).Select(y => y.Trim()));
        }

        /// <summary>
        /// Avoid having to reengineer the voter search page, going with this.
        /// This is what was done with FullName/SurName
        /// </summary>
        [JsonIgnore]
        [Newtonsoft.Json.JsonIgnore]
        public string AddressLineText => AddressLine();

        /// <summary>
        /// Avoid having to reengineer the voter search page, going with this.
        /// This is what was done with FullName/Surname
        /// </summary>
        [JsonIgnore]
        [Newtonsoft.Json.JsonIgnore]
        public string CityStateZipLineText => CityStateZipLine();

        public string GetAddress(bool oneLine = true)
        {
	        StringBuilder sb = new StringBuilder(AddressLine());

	        if (sb.Length > 0) // only if we have street address data do we want a comma or newline
	        {
		        if (oneLine)
			        sb.Append(", ");
		        else
			        sb.AppendLine();
	        }

	        sb.Append(CityStateZipLine());
	        return sb.ToString();
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        private string AddressLine()
        {
	        var houseNumber = HouseNumber?.Trim();
	        var streetName = StreetName?.TrimEnd();
	        var unit = !string.IsNullOrWhiteSpace(UnitValue) ? UnitValue.Trim() : null;
	        var unitTypeName = !string.IsNullOrWhiteSpace(UnitTypeName) ? UnitTypeName.Trim() : null;
            var houseNumberFraction = !string.IsNullOrWhiteSpace(HouseNumberFractionValue)
                ? HouseNumberFractionValue.Trim()
                : null;

            if (!string.IsNullOrWhiteSpace(houseNumberFraction) && !string.IsNullOrWhiteSpace(houseNumber))
            {
                houseNumber += $" {houseNumberFraction}";
            }

	        if (!string.IsNullOrWhiteSpace(unit) && !string.IsNullOrWhiteSpace(streetName))
		        streetName += ",";
 
	        return $"{houseNumber} {streetName} {unitTypeName} {unit}".Trim();
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        private string CityStateZipLine()
        {
            var city = City?.TrimEnd();
            var state = State?.Trim();
            var zip = Zip5?.Trim();
            var additionalPostalCode = AdditionalPostalCode?.Trim();

            if (!string.IsNullOrWhiteSpace(city))
                city += ",";

            if (!string.IsNullOrWhiteSpace(additionalPostalCode) && !string.IsNullOrWhiteSpace(zip))
            {
                zip += $"-{additionalPostalCode}";
            }

            return $"{city} {state} {zip}".Trim();
        }

        public string GetAge()
        {
            if (!DateTime.TryParseExact(DateOfBirth, BirthdayFormats, new CultureInfo("en-US"), DateTimeStyles.None, out var birthday))
                return string.Empty;

            var today = DateTime.Today;
            var age = today.Year - birthday.Year;

            if (birthday > today.AddYears(-age))
                age--;

            return age.ToString();
        }
	}
}