using System;

namespace ESS.Pollbook.Core.DTO
{
    public class VoterBallotDto : CustomTypeDescriptor
    {
        public string BallotId { get; set; }

        public int BallotStyleId { get; set; }

        public string BallotStyleCode { get; set; }

        public int VoterId { get; set; }

        public string BallotStyleDescription { get; set; }

        public string BallotStyleKey { get; set; }

        public string BallotType { get; set; }

        public int BallotStyleTypeJurisdictionEnumerationValueId { get; set; }
        public string JurisdictionEnumerationValueName { get; set; }

        // Party Properties
        public int PartyId { get; set; }
        public int? PartySourcePartyKey { get; set; }
        public string PartyName { get; set; }
        public string PartyDisplayName { get; set; }
        public int? PartyDisplayOrderNumber { get; set; }


        public int VoterPartyId { get; set; }

        public int PrecinctId { get; set; }

        public string PrecinctName { get; set; }
        public int PrecinctSplitId { get; set; }
        public int PrecinctSplitPrecinctId { get; set; }
        public int PrecinctSplitBallotStyleId { get; set; }
        public string PrecinctSplitName { get; set; }
        public string PrecinctSplitDisplayName { get; set; }

        public bool IsProvisional { get; set; }

        public string ProvisionalId { get; set; }

        public int? ReissueReasonEnumId { get; set; }

        public int? ProvisionalReasonEnumId { get; set; }

        public string ProvisionalReasonOtherText { get; set; }

        public string BallotStylePDFFileName { get; set; }

        public int? CancelReasonEnumId { get; set; }

        public DateTime? TimeStamp { get; set; }

        public bool? SurrenderAbsentee { get; set; }
    }
}