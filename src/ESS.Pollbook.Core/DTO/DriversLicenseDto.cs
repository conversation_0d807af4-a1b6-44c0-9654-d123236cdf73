namespace ESS.Pollbook.Core.DTO
{
    public class DriversLicenseDto
    {
        // TODO: ADD ATTRIBUTES TO THESE PROPERTIES
        public string FullName { get; set; }
        public string FamilyName { get; set; }
        public string GivenName { get; set; }
        public string MiddleName { get; set; }
        public string NameSuffix { get; set; }
        public string NamePrefix { get; set; }
        public string MailingStreetAddress1 { get; set; }
        public string MailingStreetAddress2 { get; set; }
        public string MailingCity { get; set; }
        public string MailingJurisdictionCode { get; set; }
        public string MailingPostalCode { get; set; }
        public string ResidenceStreetAddress1 { get; set; }
        public string ResidenceStreetAddress2 { get; set; }
        public string ResidenceCity { get; set; }
        public string ResidenceJurisdictionCode { get; set; }
        public string ResidencePostalCode { get; set; }
        public string LicenseOrIdNumber { get; set; }
        public string LicenseClassificationCode { get; set; }
        public string LicenseRestrictionCode { get; set; }
        public string LicenseEndorsementsCode { get; set; }
        public string HeightInFT_IN { get; set; }
        public string HeightInCM { get; set; }
        public string WeightInLBS { get; set; }
        public string WeightInKG { get; set; }
        public string EyeColor { get; set; }
        public string HairColor { get; set; }
        public string LicenseExpirationDate { get; set; }
        public string DateOfBirth { get; set; }
        public string Sex { get; set; }
        public string LicenseOrIdDocumentIssueDate { get; set; }
        public string IssueTimestamp { get; set; }
        public string NumberOfDuplicates { get; set; }
        public string MedicalIndicatorCodes { get; set; }
        public string OrganDonor { get; set; }
        public string NonResidentIndicator { get; set; }
        public string UniqueCustomerIdentifier { get; set; }
        public string SocialSecurityNumber { get; set; }
        public string MiddleNameOrInitial { get; set; }
        public string Suffix { get; set; }
        public string Prefix { get; set; }
        public string PhysicalDescriptionWeightRange { get; set; }
        public string DocumentDiscriminator { get; set; }
        public string CountryTerritoryOfIssuance { get; set; }
        public string FederalCommercialVehicleCodes { get; set; }
        public string PlaceOfBirth { get; set; }
        public string AuditInformation { get; set; }
        public string InventoryControlNumber { get; set; }
        public string RaceEthnicity { get; set; }
        public string StandardVehicleClassification { get; set; }
        public string StandardEndorsementCode { get; set; }
        public string StandardRestrictionCode { get; set; }
        public string JurisdictionSpecificVehicleClassificationDescription { get; set; }
        public string JurisdictionSpecific { get; set; }
        public string JurisdictionSpecificRestrictionCodeDescription { get; set; }
        public string LastName { get; set; }
        public string FirstName { get; set; }
        public string ComplianceType { get; set; }
        public string CardRevisionDate { get; set; }
        public string HazMatEndorsementExpiryDate { get; set; }
        public string LimitedDurationDocumentIndicator { get; set; }
        public string FamilyNameTruncation { get; set; }
        public string FirstNamesTruncation { get; set; }
        public string MiddleNamesTruncation { get; set; }
        public string Under18Until { get; set; }
        public string Under19Until { get; set; }
        public string Under21Until { get; set; }
        public string OrganDonorIndicator { get; set; }
        public string VeteranIndicator { get; set; }
        public string PermitClassificationCode { get; set; }
        public string PermitExpirationDate { get; set; }
        public string PermitIdentifier { get; set; }
        public string PermitIssueDate { get; set; }
        public string PermitRestrictionCode { get; set; }
        public string PermitEndorsementCode { get; set; }
    }
}
