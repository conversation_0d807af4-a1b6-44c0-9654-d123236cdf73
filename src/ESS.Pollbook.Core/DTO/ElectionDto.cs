namespace ESS.Pollbook.Core.DTO
{
    /// <summary>
    /// Election object to be populated when the pollworker logs in.
    /// </summary>
    public class ElectionDto : CustomTypeDescriptor
    {

        #region Constructor(s)
        /// <summary>
        /// Default Constructor
        /// </summary>
        public ElectionDto()
        {
            //TODO:  Items to be set upon instantiation of the Election object.
        }

        #endregion


        #region Public Properties

        public string ElectionDatabaseGuid { get; set; }

        public string Name { get; set; }

        public string JurisdictionName { get; set; }

        public PollPlaceDto PollPlace { get; set; }

        public int PollingLocationId { get; set; }

        public string PollingLocationCode { get; set; }

        public string PrecinctNumber { get; set; }


        public string State { get; set; }

        public int CountyId { get; set; }

        public string CountyNumber { get; set; }

        #endregion

        ///// <summary>The assigned Consolidation</summary>
        //private static Consolidation _consolidation = new Consolidation();

        /// <summary>The list of county names for the election</summary>
        //private static CountyList countyList = null;

        /// <summary>The lsit of parties for the election</summary>
        //private static PartyList partyList = null;


        //private static string _cardWriterProgram = String.Empty;
        ///// <summary>Category field exists.</summary>
        //private static ItemExists signaturesSourceexists = ItemExists.Unknown;


        ///// <summary>Get the county of the election.</summary>
        //public static County County
        //{
        //    get { return Consolidation.County; }
        //}

        /// <summary>Gets the consolidation for the election.</summary>
        //public static Consolidation Consolidation
        //{
        //    get { return _consolidation; }
        //}

        /// <summary>Get a value indicating if a seperate SignaturesSource exists in the Consolidations table.</summary>
        /// 
        /// 
        /// 
        //public static bool SignaturesSourceExists
        //{
        //    get
        //    {
        //        if (signaturesSourceexists == ItemExists.Unknown)
        //        {
        //            //signaturesSourceexists = Database.Db.FieldExists("SignaturesSource", "Precincts") ? ItemExists.Yes : ItemExists.No;
        //        }
        //        return signaturesSourceexists == ItemExists.Yes;
        //    }
        //}
    }
}
