using System;
using System.Text;
using System.Collections.Generic;
using System.Linq;

namespace ESS.Pollbook.Core.DTO
{
    public class PollPlaceDto : CustomTypeDescriptor
    {
        public int PollingPlacePollTypeEnumID { get; set; }

        public int PollingPlacePollTypeNumber { get; set; }

        public int PollingPlaceVoteCenterIndicator { get; set; }

        public string PollingPlaceCode { get; set; }

        public string PollingPlaceHouseNumber { get; set; }

        public string PollingPlaceStreetName { get; set; }

        public string PollingPlaceUnitName { get; set; }

        public string PollingPlaceCityName { get; set; }

        public int PollingPlaceCountyId { get; set; }

        public int PollingPlaceCountyNumber { get; set; }

        public int PollingPlacePrecinctNumber { get; set; }

        public string PollingPlaceStateProvinceCode { get; set; }

        public string PollingPlacePostalCode { get; set; }

        public string PollTypeName { get; set; }

        private byte[] pollingPlaceImage;

        public byte[] PollingPlaceImage
        {
	        get => pollingPlaceImage;
	        set => pollingPlaceImage = (value == null || value.Length == 0) ? null : value;
        }

        public int PollingPlaceId { get; set; }
        public string PollingPlaceNumber { get; set; }
        public string PollingPlaceName { get; set; }
        public string PollingPlaceDisplayName { get; set; }
        public string SearchedText { get; set; }

        public string PollingPlaceFullText => AddressLine1();

        public string PollingPlaceCityStateZip => CityStateZip();

        public string Formatted => GetFormatted();

        private string GetFormatted()
        {
	        StringBuilder sb = new StringBuilder();
	        sb.AppendLine(PollingPlaceDisplayName?.Trim());
	        sb.AppendLine(AddressLine1());
	        sb.Append(CityStateZip());
	        return sb.ToString();
        }

        private string AddressLine1()
        {
	        return string.Join(" ", new List<string>(3) { PollingPlaceHouseNumber, PollingPlaceStreetName?.TrimEnd() + (!string.IsNullOrWhiteSpace(PollingPlaceUnitName) ? "," : string.Empty), PollingPlaceUnitName }.Where(x => !string.IsNullOrWhiteSpace(x)).Select(y => y.Trim()));
        }

        private string CityStateZip()
        {
	        return string.Join(" ", new List<string>(3) { PollingPlaceCityName?.TrimEnd() + (!string.IsNullOrWhiteSpace(PollingPlaceCityName) ? "," : string.Empty), PollingPlaceStateProvinceCode, PollingPlacePostalCode }.Where(x => !string.IsNullOrWhiteSpace(x)).Select(y => y.Trim()));
        }
    }
}
