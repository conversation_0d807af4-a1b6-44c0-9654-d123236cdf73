namespace ESS.Pollbook.Core.DTO
{
    public class DynamicControlsDto : CustomTypeDescriptor
    {
        #region Properties

        public int Control_ID { get; set; }
        public string Control_Name { get; set; }
        public string Display_Name { get; set; }
        public int Sort_Order { get; set; }
        public int Visible { get; set; }
        public bool Required { get; set; }
        public bool Read_Only { get; set; }
        public string Control_Type_Code { get; set; }
        public string Form_Name { get; set; }
        public string Form_Display_Name { get; set; }
        public string Container_Name { get; set; }
        public string Container_Display_Name { get; set; }
        public string Container_Type_Code { get; set; }
        public string Value { get; set; }
        public string Default_Value { get; set; }
        public string Control_Style { get; set; }
        public string Control_Binding_Property { get; set; }
        public int? Control_Stored_Data_ID { get; set; }
        public bool? Control_Text_Wrap { get; set; }
        public int? Control_Max_Text_Length { get; set; }
        public string Control_Pollbook_Defined_Text_Name { get; set; }
        public string Control_JSON_Tag { get; set; }
        public string Affidavit_Type_Name { get; set; }
        public string Affidavit_Type_Code { get; set; }
        public string Affidavit_Template_Name { get; set; }
        public bool Affidavit_Type_Require_Signature { get; set; }
        public bool Affidavit_Type_Enable_Printing { get; set; }
        public bool Affidavit_Type_Enable_Name_Capture { get; set; }
        public bool Affidavit_Type_Enable_Address_Capture { get; set; }
        public bool Affidavit_Type_Enable_Confirmation { get; set; }
        public bool Affidavit_Type_Enable_Relationship_Capture { get; set; }
        public bool Affidavit_Type_Enable_Compensation_Capture { get; set; }

        #endregion

        #region Contructors
        public DynamicControlsDto()
        {
            // constructor
        }
        #endregion
    }
}
