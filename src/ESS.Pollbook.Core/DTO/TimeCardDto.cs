using ESS.Pollbook.Core.StaticValues;
using System;

namespace ESS.Pollbook.Core.DTO
{
    public class TimeCardDto
    {
        public Guid TimeCardGuid { get; set; }
        public Guid PollworkerGUID { get; set; }
        public string PollworkerFirstName { get; set; }
        public string PollworkerLastName { get; set; }
        public int PollworkerTitleId { get; set; }
        public string TitleName { get; set; }
        public int? StartTimeActivityId { get; set; }
        public int? EndTimeActivityId { get; set; }
        public int PollingPlaceId { get; set; }
        public string PollingPlaceDisplayName { get; set; }
        public Guid TransactionGUID { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public string Comments { get; set; }
        public string JudgeApprovedDateTime { get; set; }
        public Guid? JudgePollworkerGUID { get; set; }
        public DateTime RecordUpdateUtcDatetime { get; set; }
        public string RecordUpdateUserName { get; set; }
        public string RecordUpdateApplicationName { get; set; }
        public string RecordUpdateApplicationUserName { get; set; }
        public DateTime RecordUpdateApplicationDatetime { get; set; }
        public bool IsNewTimeCard { get; set; }

        #region Calculated Fields

        #region UTC datetimes
        private DateTime _dateTimeJudgeApproved;

        public DateTime? DateTimeJudgeApproved
        {
            get
            {
                DateTime.TryParse(JudgeApprovedDateTime, out _dateTimeJudgeApproved);
                return _dateTimeJudgeApproved;
            }
        }

        #endregion

        #region Local DateTimes

        public string StartTimeString => StartTime.ToLocalTime().ToString("hh:mm tt");

        public string EndTimeString
        {
            get
            {
                if (EndTime != null)
                {
                    return EndTime.Value.ToLocalTime().ToString("hh:mm tt");
                }
                else
                {
                    return string.Empty;
                }
            }
        }

        #endregion

        public TimeSpan TotalTimeValue
        {
            get
            {
                if (EndTime != null)
                {
                    var tend = new DateTime(EndTime.Value.Ticks - EndTime.Value.Ticks % TimeSpan.TicksPerMinute, EndTime.Value.Kind);
                    var tstar = new DateTime(StartTime.Ticks - StartTime.Ticks % TimeSpan.TicksPerMinute, StartTime.Kind);
                    return tend.Subtract(tstar);
                }
                else
                    return TimeSpan.Zero;
            }
        }

        public string TotalTimeDisplay => $"{TotalTimeValue.Hours}:{TotalTimeValue.Minutes.ToString("00")}";

        public bool JudgeApproved => !string.IsNullOrEmpty(JudgeApprovedDateTime);

        public bool IsRightPollingPlace =>
            LoggedInPollplaceInfo.LoggedInPollPlace.PollingPlaceId.Equals(PollingPlaceId);

        public string TimeCardGuidToVarchar => $"'{TimeCardGuid.ToString()}'";

        public string PollingPlaceDisplay
        {
            get
            {
                // MAXPOLLPLACENAMEDISPLAYLENGTH is 100
                if (PollingPlaceDisplayName?.Length > 100)
                    return PollingPlaceDisplayName.Substring(0, 100);
                return PollingPlaceDisplayName;
            }
        }
        #endregion
    }
}
