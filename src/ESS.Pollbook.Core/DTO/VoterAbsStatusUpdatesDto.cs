using ESS.Pollbook.Core.Attributes;
using Newtonsoft.Json;

namespace ESS.Pollbook.Core.DTO
{
    public class VoterAbsStatusUpdatesDto : CustomTypeDescriptor
    {
        [JsonIgnore]
        public long? VoterId { get; set; }

        [VoterAttribute("VoterStatusEnumId")]
        public int VoterStatusEnumId { get; set; }

        [VoterAttribute("AbsenteeStatusEnumId")]
        public int AbsenteeStatusEnumId { get; set; }
        
        [VoterAttribute("VoterDeleted")]
        public int VoterDeleted { get; set; }
    }
}
