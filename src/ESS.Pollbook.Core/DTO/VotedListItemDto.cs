using System;

namespace ESS.Pollbook.Core.DTO
{
    public class VotedListItemDto : CustomTypeDescriptor, ICloneable
    {
        public int VoterId { get; set; }
        public string VoterKey { get; set; }
        public string FullName { get; set; }
        public string PartyDisplayName { get; set; }
        public string PrecinctSplitName { get; set; }
        public DateTime TransactionDate { get; set; }
        public int LineNumber { get; set; }
        public bool RecordInitialLoadIndicator { get; set; }
        public string NotInRosterAsterisk { get; set; }
        public bool IsProvisional { get; set; }
        public int MediaTypeEnumId { get; set; }

        public object Clone()
        {
            return this.MemberwiseClone();
        }
    }

    public class NewVoterInfo
    {
        public string SourceKey { get; set; }
        public string FullName { get; set; }
    }
}
