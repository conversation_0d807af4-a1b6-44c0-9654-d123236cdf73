using System.Runtime.InteropServices;

namespace ESS.Pollbook.Core.UICore
{
    public static class ScreenDisplayFlip
    {
        public static void ToggleDisplay()
        {
            try
            {
                var dm = new DevMode();
                dm.dmSize = (short)Marshal.SizeOf(dm);

                if (0 != NativeMethods.EnumDisplaySettings(
                        null,
                        NativeMethods.ENUM_CURRENT_SETTINGS,
                        ref dm))
                {
                    dm.dmDisplayOrientation = (dm.dmDisplayOrientation == NativeMethods.DMDO_180)
                        ? NativeMethods.DMDO_DEFAULT
                        : NativeMethods.DMDO_180;
                }

                NativeMethods.ChangeDisplaySettings(ref dm, 0);
            }
            catch
            {
                // Not sure if this can throw exceptions, but it is called from codebehind so need to trap them here. 
                // Just swallow the exception.
            }
        }

        public static void FlipToDefault()
        {
            var dm = new DevMode();
            dm.dmSize = (short)Marshal.SizeOf(dm);
            int currentOrientation = 0;

            if (0 != NativeMethods.EnumDisplaySettings(
                    null,
                    NativeMethods.ENUM_CURRENT_SETTINGS,
                    ref dm))
            {
                currentOrientation = dm.dmDisplayOrientation;
            }

            if (currentOrientation != NativeMethods.DMDO_DEFAULT)
            {
                dm.dmDisplayOrientation = NativeMethods.DMDO_DEFAULT;
                NativeMethods.ChangeDisplaySettings(ref dm, 0);
            }
        }

        public static void FlipTo180()
        {
            var dm = new DevMode();
            dm.dmSize = (short)Marshal.SizeOf(dm);
            int currentOrientation = 0;

            if (0 != NativeMethods.EnumDisplaySettings(
                    null,
                    NativeMethods.ENUM_CURRENT_SETTINGS,
                    ref dm))
            {
                currentOrientation = dm.dmDisplayOrientation;
            }

            if (currentOrientation != NativeMethods.DMDO_180)
            {
                dm.dmDisplayOrientation = NativeMethods.DMDO_180;
                NativeMethods.ChangeDisplaySettings(ref dm, 0);
            }
        }
    }

    [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Ansi)]
    internal struct DevMode
    {
        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 32)] public string dmDeviceName;

        public short dmSpecVersion;
        public short dmDriverVersion;
        public short dmSize;
        public short dmDriverExtra;
        public int dmFields;
        public int dmPositionX;
        public int dmPositionY;
        public int dmDisplayOrientation;
        public int dmDisplayFixedOutput;
        public short dmColor;
        public short dmDuplex;
        public short dmYResolution;
        public short dmTTOption;
        public short dmCollate;

        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 32)] public string dmFormName;

        public short dmLogPixels;
        public short dmBitsPerPel;
        public int dmPelsWidth;
        public int dmPelsHeight;
        public int dmDisplayFlags;
        public int dmDisplayFrequency;
        public int dmICMMethod;
        public int dmICMIntent;
        public int dmMediaType;
        public int dmDitherType;
        public int dmReserved1;
        public int dmReserved2;
        public int dmPanningWidth;
        public int dmPanningHeight;
    };

    internal class NativeMethods
    {
        public const int ENUM_CURRENT_SETTINGS = -1;
        public const int DMDO_DEFAULT = 0;
        public const int DMDO_90 = 1;
        public const int DMDO_180 = 2;
        public const int DMDO_270 = 3;

        // PInvoke declaration for EnumDisplaySettings Win32 API
        [DllImport("user32.dll", CharSet = CharSet.Ansi)]
        public static extern int EnumDisplaySettings(
            string lpszDeviceName,
            int iModeNum,
            ref DevMode lpDevMode);

        // PInvoke declaration for ChangeDisplaySettings Win32 API
        [DllImport("user32.dll")]
        public static extern int ChangeDisplaySettings(
            ref DevMode lpDevMode, int dwFlags);
    }
}
