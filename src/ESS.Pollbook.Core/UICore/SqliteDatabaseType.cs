using ESS.Pollbook.Core.Attributes;

namespace ESS.Pollbook.Core.UICore
{
    public enum SqliteDatabaseType
    {
        [Database("Polldata.db3")]
        Polldata,

        [Database("Polldata_AuditLog.db3")]
        AuditLog,

        [Database("Polldata_PollworkerManagement.db3")]
        Pollworker,

        [Database("Polldata_RegionalResults.db3")]
        RegionalResults,

        [Database("Polldata_Signatures.db3")]
        Signature,

        [Database("Polldata_Transactions.db3")]
        Transaction
    }
}
