using ESS.Pollbook.Core.Common;
using System.Collections.Generic;
using System.Collections.Immutable;

namespace ESS.Pollbook.Core.Model.VoterEligibility
{
    public static class VoterVotingStatusWithEligibilityColors
    {
        public static readonly ImmutableDictionary<string, int> VoterStatusWithColor =
            ImmutableDictionary.CreateRange<string, int>(new[]
            {
                new KeyValuePair<string, int>(VoterStatus.Active.ToString(), (int) StatusColorsByPriorityEnum.Green),
                new KeyValuePair<string, int>(VoterStatus.Pending.ToString(), (int) StatusColorsByPriorityEnum.Yellow),
                new KeyValuePair<string, int>(VoterStatus.Inactive.ToString(), (int) StatusColorsByPriorityEnum.Red)
            });

        public static readonly ImmutableDictionary<string, int> AbsenteeStatusWithColor =
            ImmutableDictionary.CreateRange<string, int>(new[]
            {
                new KeyValuePair<string, int>(AbsenteeStatus.Active.ToString(), (int) StatusColorsByPriorityEnum.Green),
                new KeyValuePair<string, int>(AbsenteeStatus.Pending.ToString(), (int) StatusColorsByPriorityEnum.Yellow),
                new KeyValuePair<string, int>(AbsenteeStatus.Absentee.ToString(), (int) StatusColorsByPriorityEnum.Red),
                new KeyValuePair<string, int>(AbsenteeStatus.EarlyVoteIssued.ToString(), (int) StatusColorsByPriorityEnum.Red)
            });
    }
}
