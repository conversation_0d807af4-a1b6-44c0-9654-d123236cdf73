using System;

namespace ESS.Pollbook.Core.Model.VoterEligibility
{
    public enum CalculationTypeEnum
    {
        Unknown = 1,
        AbsenteeBallot = 2,
        PollingLocation = 4,
        RequiresId = 8,
        PreviouslyVoted = 16,
        VoterStatus = 32,
        PollStatus = 64
    }

    public enum CalculationStatusEnum
    {
        Other = 1,
        Success = 2,
        Warning = 4,
        Error = 8
    }

    public class CalculationResponse : CustomTypeDescriptor
    {
        public int SortPriority { get; set; }

        public string CalculationType { get; set; }

        public string CalculationStatus { get; set; }

        public CalculatedStatusResponse CalculatedStatusResponse { get; set; }

        public string Message { get; set; }

        public string DisplayMessage { get; set; }

        public string CommentText { get; set; }

        public string VoterStatusEnumerationValueName { get; set; }

        public string AbsenteeStatusEnumerationValueName { get; set; }

        public bool IsPreviouslyVoted { get; set; }

        public bool? IsProvisional { get; set; }

        public DateTime? BallotDateTime { get; set; }
    }
}
