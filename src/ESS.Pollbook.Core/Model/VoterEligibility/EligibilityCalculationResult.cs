using System;
using System.Collections.Generic;
using System.Runtime.CompilerServices;

namespace ESS.Pollbook.Core.Model.VoterEligibility
{
	public class EligibilityCalculationResult : CustomTypeDescriptor
	{
	    public string Type { get; set; }

	    public string Status { get; set; }

        public CalculatedStatusResponse VoterStatusResponse { get; set; }

        public CalculatedStatusResponse AbsenteeStatusResponse { get; set; }

        public CalculatedStatusResponse IdRequiredResponse { get; set; }

        public string ErrorMessage { get; set; }

        public string DisplayMessage { get; set; }

        public string StatusText { get; set; }

        public string CommentText { get; set; }

        /// <summary>
        /// Key,value pair to store the CalculationResultStatusesEnum as keys and the voter's corresponding 
        ///Enumeration_Value_Name values for color calculation for displaying in search results page
        /// </summary>
        public Dictionary<string, string> ResultStatusesForColorCalculation { get; set; }

        /// <summary>
        /// Stores lookup values for the different statuses, for retrieving the correct texts according to the mappings
        /// </summary>
        public Dictionary<string, object> ResultStatusesForStatusTextsMappings { get; set; }

        /// <summary>
        /// Key,value pair to store the CalculationResultStatusesEnum as keys and the voter's corresponding 
        /// Jurisdiction_Enum_Value_Description_Text values for displaying in search results page
        /// </summary>
        public Dictionary<string, string> ResultStatusesForDisplayMessage { get; set; }

        public bool StandardBallotDisabled { get; set; }

        /// <summary>
        /// Store possible status subtexts
        /// </summary>
        public Dictionary<string, string> StatusSubtexts { get; set; } = new Dictionary<string, string>();

        public string StatusColor { get; set; }

        public bool VoterHasPreviouslyVoted { get; set; }

        public bool VoterHasProvisionalBallot { get; set; }

        public DateTime? VoterBallotDateTime { get; set; }

        public bool? SurrenderMailBallot { get; set; }

        public bool WrongPollingLocation { get; set; }
	}

	public enum CalculationResultStatusesEnum
    {
        PollStatus,
        PollingLocation,
        VoterStatus,
        AbsenteeStatus,
        BallotIssued
    }

    public enum StatusColorsByPriorityEnum
    {
        Green = 0,
        Yellow = 1,
        Red = 2,
        Gray = 3
    }
}
