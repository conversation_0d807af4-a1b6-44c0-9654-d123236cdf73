using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Utilities;
using System;

namespace ESS.Pollbook.Core.Model
{
    public enum SearchFilterEnum
    {
        None = 0,
        PollingPlaceId = 1,
        CountyId = 2,
        State = 3
    }

    public class VoterSearchConfiguration : CustomTypeDescriptor
    {
        private const int DEFAULT_MAX_RESULT_THRESHOLD = 25;

        public int MaxResultThreshold { get; private set; }
        public int MaxMemoryListThreshold => MaxResultThreshold;
        public string SqlSortOrder { get; private set; }

        private VoterSearchConfiguration(int maxResultThreshold = DEFAULT_MAX_RESULT_THRESHOLD, string sqlSortOrder = null)
        {
            MaxResultThreshold = maxResultThreshold;
            SqlSortOrder = sqlSortOrder;
        }

        public static VoterSearchConfiguration Create(int maxResultThreshold = DEFAULT_MAX_RESULT_THRESHOLD, string sqlSortOrder = null)
        {
            return new VoterSearchConfiguration(maxResultThreshold, sqlSortOrder);
        }
    }

    public class VoterNameSearchRequest : CustomTypeDescriptor
    {
        public string FirstName { get; set; }

        public string LastName { get; set; }

        public DateTime? DateOfBirth { get; set; }

        public string DriversLicenseNumber { get; set; }

        public string VoterSourceKey { get; set; }

        public string StreetNumber { get; set; }
        
        public string StreetName { get; set; }
        
        public PartyDto Party { get; set; }

        public PollPlaceDto PollPlace { get; set; }

        public SystemStatsDto SystemStats { get; set; }

        public SearchFilterEnum SearchFilter { get; private set; }

        public int? SearchFilterValue { get; set; }

        public VoterSearchConfiguration Configuration { get; private set; }

        public bool HasHostResults { get; set; }

        public bool IsBarcodeSearch { get; set; }

        public VoterNameSearchRequest(string firstName, 
            string lastName, 
            DateTime? dateOfBirth, 
            string driversLicenseNumber,
            string voterSourceKey,
            string streetNumber,
            string streetName,
            PartyDto party,
            PollPlaceDto pollPlace = null, 
            SystemStatsDto systemStats = null, 
            SearchFilterEnum searchFilter = SearchFilterEnum.None,
            int? searchFilterValue = null, 
            VoterSearchConfiguration configuration = null, 
            bool isBarcodeSearch = false)
        {
            FirstName = Helpers.GetAlphaNumericString(firstName?.Trim());
            LastName = Helpers.GetAlphaNumericString(lastName?.Trim());
            DateOfBirth = dateOfBirth;
            DriversLicenseNumber = driversLicenseNumber;
            VoterSourceKey = voterSourceKey?.Trim();
            StreetNumber = streetNumber;
            StreetName = streetName;
            Party = party; 
            PollPlace = pollPlace;
            SystemStats = systemStats;
            SearchFilter = searchFilter;
            SearchFilterValue = searchFilterValue;
            IsBarcodeSearch = isBarcodeSearch;

            if (configuration == null)
            {
                Configuration = VoterSearchConfiguration.Create();

                return;
            }

            Configuration = configuration;
        }
    }
}
