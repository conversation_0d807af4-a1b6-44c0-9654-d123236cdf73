using ESS.Pollbook.Core.DTO;

namespace ESS.Pollbook.Core.Model
{
    public sealed class ListboxWithFreeformTextItem
    {
        public int EnumerationValueId { get; set; }
        public string EnumerationValueCode { get; set; }
        public string EnumerationValueDescription { get; set; }
        public int JurisdictionEnumerationValueId { get; set; }
        public string JurisdictionEnumerationValueDescription { get; set; }
        public int EnumerationValueNumber { get; set; }
        public string OtherText { get; set; } = null;

        public ListboxWithFreeformTextItem()
        {
        }

        public ListboxWithFreeformTextItem(ElectionJurisdictionEnumValueDto dto)
        {
            EnumerationValueId = dto.EnumerationValueId;
            EnumerationValueCode = dto.EnumerationValueCode;
            EnumerationValueDescription = dto.EnumerationValueDescription;
            JurisdictionEnumerationValueId = dto.JurisdictionEnumerationValueId;
            JurisdictionEnumerationValueDescription = dto.JurisdictionEnumerationValueDescription;
            EnumerationValueNumber = dto.EnumerationValueNumber;
        }
    }
}
