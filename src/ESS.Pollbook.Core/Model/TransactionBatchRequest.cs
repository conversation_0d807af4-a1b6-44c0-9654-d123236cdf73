using ESS.Pollbook.Core.Model.SyncPoint;
using ESS.Pollbook.Core.Model.Transactions;
using System;
using System.Collections.Generic;

namespace ESS.Pollbook.Core.Model
{
    public class TransactionBatchRequest : RequestBase
    {
        public IEnumerable<EssTransactionModel> Transactions { get; set; }
        public int? PollingPlaceId { get; set; }
        public string TransactionVoterSourceKey { get; set; }
        public long LastTransaction { get; set; }
        public string SystemIdentifier { get; set; }
        public DeviceDetails DeviceDetails { get; set; }
        public DateTime SystemCurrentDateTimeUtc { get; set; }
        public bool IsTestMode { get; set; }
        public long PollbookTransactionsCount { get; set; }
        public long HostTransactionsCount { get; set; }
        public long FailedTransactionsCount { get; set; }
        public string UserIp { get; set; }

    }
}
