using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Model.SyncPoint;
using ESS.Pollbook.Core.Model.Transactions;
using ESS.Pollbook.Core.StaticValues;
using System;

namespace ESS.Pollbook.Core.Model
{
    public class PollbookTransactionRequest
    {
        public static TransactionBatchRequest Create(TransactionBase transaction, TransactionType type, PollbookTransactionDto pollbookTransaction)
        {
            TransactionBatchRequest transactionRequest = new TransactionBatchRequest
            {
                PollingPlaceId = pollbookTransaction.PollingPlaceId,
                DeviceDetails = new DeviceDetails()
                {
                    ModelNumber = SystemDetails.DeviceModelNumber,
                    SerialNumber = SystemDetails.DeviceSerialNumber,
                    DeviceName = SystemDetails.MachineName,
                    DeviceTypeEnumId = null
                },
                IsTestMode = false,
                SystemCurrentDateTimeUtc = DateTime.UtcNow,
                SystemIdentifier = pollbookTransaction.SystemIdentifier,
                TransactionVoterSourceKey = pollbookTransaction.Voter?.VoterKey
            };

            EssTransactionModel transactionModel = new EssTransactionModel
            {
                TransactionType = type,
                SignatureImage = pollbookTransaction.Signature,
                AdditionalSignature1 = pollbookTransaction.AdditionalSignature1,
                AdditionalSignature2 = pollbookTransaction.AdditionalSignature2,
                TransactionJson = pollbookTransaction.JSON
            };

            transactionRequest.Transactions = new[] { transactionModel };
            return transactionRequest;
        }

        public static TransactionBatchRequest Create(PollbookTransactionDto transaction)
        {
            var transactionRequest = new TransactionBatchRequest
            {
                PollingPlaceId = transaction.PollingPlaceId,
                DeviceDetails = new DeviceDetails()
                {
                    DeviceName = transaction.DeviceName,
                    SerialNumber = transaction.SerialNumber,
                    ModelNumber = SystemDetails.DeviceModelNumber
                },
                IsTestMode = false,
                SystemCurrentDateTimeUtc = transaction.TransactionDate,
                SystemIdentifier = transaction.SystemIdentifier,
                TransactionVoterSourceKey = transaction.SourceKey
            };

            var transactionModel = new EssTransactionModel
            {
                TransactionType = (TransactionType)Enum.Parse(typeof(TransactionType), transaction.TransactionType, true),
                SignatureImage = transaction.Signature,
                AdditionalSignature1 = transaction.AdditionalSignature1,
                AdditionalSignature2 = transaction.AdditionalSignature2,
                TransactionJson = transaction.JSON
            };

            transactionRequest.Transactions = new[] { transactionModel };
            return transactionRequest;
        }
    }
}
