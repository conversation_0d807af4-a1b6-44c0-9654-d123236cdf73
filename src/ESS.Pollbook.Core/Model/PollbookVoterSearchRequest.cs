using System;

namespace ESS.Pollbook.Core.Model
{
    public class PollbookVoterSearchRequest : RequestBase
    {
        public string VoterLastName { get; set; }
        public string VoterFirstName { get; set; }
        public DateTime? VoterDateofBirth { get; set; }
        public string VoterSourceKey { get; set; }
        public string VoterDriversLicense { get; set; }
        public string SearchFilter { get; set; }
        public int? SearchFilterValue { get; set; }
        public int PollingPlaceId { get; set; }
        public string VoterAffidavitNumber { get; set; }
        public string  HouseNumber { get; set; }
        public string StreetName { get; set; }
        public int? PartyId { get; set; }
    }
}
