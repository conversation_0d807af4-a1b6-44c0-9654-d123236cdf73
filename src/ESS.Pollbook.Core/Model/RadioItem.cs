using ManagedNativeWifi;

namespace ESS.Pollbook.Core.Model
{
    public class RadioItem
    {
        #region Properties
        public virtual InterfaceInfo Interface { get; }

        /// <summary>
        /// SSID (maximum 32 bytes)
        /// </summary>
        public virtual NetworkIdentifier Ssid { get; }

        /// <summary>
        /// Name alias
        /// </summary>
        public string Name => string.IsNullOrWhiteSpace(this.Ssid.ToString()) ? "Hidden" : this.Ssid.ToString();

        /// <summary>
        /// BSS network type
        /// </summary>
        public BssType BssType { get; }

        /// <summary>
        /// Band
        /// </summary>
        public float Band { get; }

        /// <summary>
        /// Signal quality (0-100)
        /// </summary>
        public int SignalQuality { get; set; }

        /// <summary>
        /// Whether security is enabled on this network
        /// </summary>
        public bool IsSecurityEnabled { get; }

        /// <summary>
        /// Associated wireless profile name
        /// </summary>
        public string ProfileName { get; set; }

        /// <summary>
        /// Default authentication algorithm to be used to connect to this network for the first time
        /// </summary>
        public virtual AuthenticationAlgorithm AuthenticationAlgorithm { get; }

        /// <summary>
        /// Default cipher algorithm to be used to connect to this network
        /// </summary>
        public virtual CipherAlgorithm CipherAlgorithm { get; }

        public bool IsConnected { get; set; }

        public ProfileItem Profile { get; set; }
        #endregion

        #region Constructor

        public RadioItem(
            InterfaceInfo interfaceInfo,
            NetworkIdentifier ssid,
            BssType bssType,
            int signalQuality,
            bool isSecurityEnabled,
            string profileName,
            ProfileItem profile,
            AuthenticationAlgorithm authenticationAlgorithm,
            CipherAlgorithm cipherAlgorithm,
            bool isConnected,
            float band)
        {
            this.Interface = interfaceInfo;
            this.Ssid = ssid;
            this.BssType = bssType;
            this.SignalQuality = signalQuality;
            this.IsSecurityEnabled = isSecurityEnabled;
            this.ProfileName = profileName;
            this.AuthenticationAlgorithm = authenticationAlgorithm;
            this.CipherAlgorithm = cipherAlgorithm;
            this.IsConnected = isConnected;
            this.Band = band;
            this.Profile = profile;
        }
        #endregion
    }
}
