namespace ESS.Pollbook.Core.Model.IncrementalUpdates
{
    public class TableModel : CustomTypeDescriptor
    {
        public string SchemaName { get; private set; }
        public string TableName { get; private set; }

        public string ApiEndPointName { get; private set; }
        public string CompleteTableName { get; private set; }

        public TableModel(string schemaName, string tableName)
        {
            SchemaName = schemaName;
            TableName = tableName;
            CompleteTableName = SchemaName + "_" + TableName;
            ApiEndPointName = TableName.Replace("_", "-");
        }
    }
}
