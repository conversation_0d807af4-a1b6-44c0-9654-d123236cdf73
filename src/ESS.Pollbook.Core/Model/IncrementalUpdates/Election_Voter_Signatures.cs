using System;

namespace ESS.Pollbook.Core.Model.IncrementalUpdates
{
    public class Election_Voter_Signatures
    {
        public long Voter_Signature_ID { get; set; }
        public long Voter_Signature_Voter_ID { get; set; }
        public string Voter_Source_Voter_Key { get; set; }
        public string Voter_Signature_Server_FilePath { get; set; }
        public string Voter_Signature_Pollbook_FilePath { get; set; }
        public string Voter_Signature_Pollbook_FileName { get; set; }
        public DateTime Record_Update_UTC_Datetime { get; set; }
        public string Record_Update_User_Name { get; set; }
        public string Record_Update_Application_Name { get; set; }
        public string Record_Update_Application_User_Name { get; set; }
        public DateTime Record_Update_Application_Datetime { get; set; }
    }
}
