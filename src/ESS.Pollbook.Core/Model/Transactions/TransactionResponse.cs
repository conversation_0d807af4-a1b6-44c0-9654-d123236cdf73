using ESS.Pollbook.Core.Common;
using System.Collections.Generic;

namespace ESS.Pollbook.Core.Model.Transactions
{
    public class TransactionResponse
    {
        public TransactionProcessingStatus TransactionStatus { get; set; }
        public List<string> StatusMessages { get; set; }
        public TransactionType SubmittedAsType { get; set; }
        public TransactionType ProcessedAsType { get; set; }
        public string TransactionIdentifier { get; set; }
        public long ServerTransactionId { get; set; }
        public TransactionResponse()
        {
            StatusMessages = new List<string>();
        }
    }
}
