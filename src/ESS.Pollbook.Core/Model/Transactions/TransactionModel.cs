using ESS.Pollbook.Core.Common;
using System;

namespace ESS.Pollbook.Core.Model.Transactions
{
    public class TransactionModel
    {
        public long TransactionId { get; set; }

        public int? PollingPlaceId { get; set; }

        public string SessionIdentifier { get; set; }

        public long DeviceSessionId { get; set; }

        public string TransactionIdentifier { get; set; }

        public string VoterSourceKey { get; set; }

        public bool IsTestTransaction { get; set; }

        public int TransactionTypeEnumId { get; set; }

        public TransactionType TransactionType { get; set; }

        public string OtherTransactionType { get; set; }

        public DateTime ServerCreatedOnUtc { get; set; }

        public int? DeviceId { get; set; }

        public string DeviceSystemIdentifier { get; set; }

        public DateTime DeviceCreatedOn { get; set; }

        public DateTime AdjustedDeviceCreatedOn { get; set; }

        public long? ParentTransactionId { get; set; }

        public string TransactionParentDeviceTransactionGuid { get; set; }

        public string TransactionJson { get; set; }

        public string DeviceDeviceName { get; set; }

        public string DeviceSerialNumberValue { get; set; }
    }
}
