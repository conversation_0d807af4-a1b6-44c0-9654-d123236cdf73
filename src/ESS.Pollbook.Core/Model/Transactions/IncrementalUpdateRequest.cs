using System;
using System.ComponentModel.DataAnnotations;
using System.Globalization;

namespace ESS.Pollbook.Core.Model.Transactions
{
    public class IncrementalUpdateRequest
    {
        [RequiredDateTime(ErrorMessage = "{0} is required")]
        public DateTime? LastRecordUpdateUtc { get; set; }
    }

    [AttributeUsage(AttributeTargets.Property | AttributeTargets.Field, AllowMultiple = false, Inherited = true)]
    public class RequiredDateTimeAttribute : ValidationAttribute
    {
        public override bool IsValid(object value)
        {
            var dateValue = value as DateTime?;

            if (dateValue.HasValue)
            {
                return !(dateValue.Value.Equals(DateTime.MinValue) || dateValue.Value.Equals(DateTime.MaxValue));
            }

            return false;
        }

        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            return IsValid(value)
                ? ValidationResult.Success
                : new ValidationResult(FormatErrorMessage(validationContext.MemberName),
                    new[] { validationContext.MemberName });
        }

        public override string FormatErrorMessage(string name)
        {
            return string.Format(CultureInfo.CurrentCulture, ErrorMessageString, name);
        }
    }
}
