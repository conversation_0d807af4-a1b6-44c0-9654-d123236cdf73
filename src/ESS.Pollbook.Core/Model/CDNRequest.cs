using Newtonsoft.Json;
using System;
using System.Collections.Generic;

namespace ESS.Pollbook.Core.Model
{
    [Serializable]
    public class CDNRequest
    {
        public string ElectionDatabaseGuid { get; set; }
        public string UserIp { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public List<string> ImagePaths { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public List<string> FilePaths { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string StyleSheetName { get; set; }
    }
}
