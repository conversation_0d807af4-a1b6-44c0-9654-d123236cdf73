using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.EventsDto;
using ESS.Pollbook.Core.Model;
using System;

namespace ESS.Pollbook.Core.Events
{
    public partial class TransactionEventCreator
    {
        public HeartbeatEventDto CreatePowerStatusEvent(PollbookTransactionDto transaction, PowerStatusRequest powerStatusRequest, UserDto user)
        {
            HeartbeatEventDto powerStatusEvent = new HeartbeatEventDto
            {
                IsPluggedIn = powerStatusRequest.ACOnline,
                BatteryCharge = powerStatusRequest.BatteryCharge,
                TransactionIdentifier = transaction.TransactionGuid.Replace("-", ""),
                ParentTransactionIdentifier = null,
                SessionId = Guid.NewGuid().ToString().Replace("-", ""),
                SessionSequenceNumber = 0,
                CreatedOnUtc = transaction.TransactionDate,
                CreatedBy = user.Username,
                Attributes = null,
            };

            return powerStatusEvent;
        }

        public TestModeEventDto ConvertToTestModeEvent(HeartbeatEventDto heartBeatEvent)
        {
            TestModeEventDto testModeEvent = new TestModeEventDto()
            {
                IsPluggedIn = heartBeatEvent.IsPluggedIn,
                BatteryCharge = heartBeatEvent.BatteryCharge,
                TransactionIdentifier = heartBeatEvent.TransactionIdentifier,
                ParentTransactionIdentifier = null,
                SessionId = heartBeatEvent.SessionId,
                SessionSequenceNumber = 0,
                CreatedOnUtc = heartBeatEvent.CreatedOnUtc,
                CreatedBy = heartBeatEvent.CreatedBy,
                Attributes = null,
            };

            return testModeEvent;
        }
    }
}
