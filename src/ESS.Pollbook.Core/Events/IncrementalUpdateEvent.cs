using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.EventsDto;
using System;

namespace ESS.Pollbook.Core.Events
{
    public partial class TransactionEventCreator
    {
        public IncrementalUpdateEventDto CreatIncrementalUpdateEvent(PollbookTransactionDto transaction, string userName, int progressIndicatorEnumId)
        {
	        var incrementalUpdateEvent = new IncrementalUpdateEventDto
	        {
		        TransactionType = TransactionType.IncrementalUpdate,
		        TransactionIdentifier = transaction.TransactionGuid.Replace("-", ""),
		        ParentTransactionIdentifier = null,
		        SessionId = Guid.NewGuid().ToString().Replace("-", ""),
		        SessionSequenceNumber = 0,
		        CreatedOnUtc = transaction.TransactionDate,
		        CreatedBy = userName,
		        Attributes = null,
		        ProgressIndicatorEnumId = progressIndicatorEnumId
	        };
            return incrementalUpdateEvent;

        }
    }
}
