using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.EventsDto;
using System;

namespace ESS.Pollbook.Core.Events
{
    public partial class TransactionEventCreator
    {
        public ErrorEventDto CreateErrorEvent(PollbookTransactionDto transaction, UserDto user, string message, string parentTransId)
        {
	        var errorEvent = new ErrorEventDto
	        {
		        TransactionIdentifier = transaction.TransactionGuid.Replace("-", ""),
		        ParentTransactionIdentifier = parentTransId,
		        SessionId = Guid.NewGuid().ToString().Replace("-", ""),
		        SessionSequenceNumber = 0,
		        CreatedOnUtc = transaction.TransactionDate,
		        CreatedBy = user.Username,
		        Attributes = null,
		        ErrorMessage = message
	        };
            return errorEvent;

        }
    }
}
