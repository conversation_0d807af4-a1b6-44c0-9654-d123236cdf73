using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.DTO;
using System;

namespace ESS.Pollbook.Core.NavigationParameters
{
    public class BallotTotalsReportParameter
    {
        public SystemStatsDto SystemStatsDto { get; set; }
        public PollPlaceDto PollPlaceDto { get; set; }
        public string Timestamp { get; set; }

        public string PaperText { get; set; }

        public string ExpressVoteText { get; set; }
    }

    public class SpoiledBallotReportParameter
    {
        public SystemStatsDto SystemStatsDto { get; set; }
        public PollPlaceDto PollPlaceDto { get; set; }

        public int BallotsReIssuedReason1 { get; set; }
        public string BallotsReIssuedReason1Description { get; set; }
        public int BallotsReIssuedReason2 { get; set; }
        public string BallotsReIssuedReason2Description { get; set; }
        public int BallotsReIssuedReason3 { get; set; }
        public string BallotsReIssuedReason3Description { get; set; }
        public int BallotsReIssuedProvisional { get; set; }

        public string Timestamp { get; set; }
    }

    public class VoterAuthorityDocParameter
    {
        public VoterDto Voter { get; set; }
        public bool IsInBallotIssueWorkFlow { get; set; }

        public DateTime PrintDateTime { get; set; }
    }

    public class PrintAddressChangedDocParameter
    {
        public VoterDto EditedVoter { get; set; }
        public VoterDto PreviousVoter { get; set; }
        public DateTime PrintDateTime { get; set; }
    }

    public class SupervisorPwEntryParameter
    {
        public VoterDto Voter { get; set; }
        public PollworkerDto PollWorker { get; set; }
        public SupervisorPwEntryTargetType TargetType { get; set; }
        
    }
}
