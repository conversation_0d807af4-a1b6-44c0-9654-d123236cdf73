using System;
using System.Collections.Generic;

namespace ESS.Pollbook.Core.Messaging
{
    public class VerifyVoterIdMessage
    {
        public bool? NIR_VR_Verified { get; set; }
        public bool? ExemptSelection { get; set; }
        public bool? ListASelection { get; set; }

        public bool? ListBSelection { get; set; }

        [Obsolete("This property is being superseded by Reasonable Impediment workflow screens")]
        public bool? RIDSelection { get; set; }

        public bool? FormsComplete { get; set; }

        public Dictionary<int, bool> VerifyVoterIdOptions { get; set; }
    }
}
