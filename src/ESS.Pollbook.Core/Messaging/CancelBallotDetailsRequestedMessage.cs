using ESS.Pollbook.Core.DTO;

namespace ESS.Pollbook.Core.Messaging
{
    public class CancelBallotDetailsRequestedMessage
    {
        public VoterHistoryDto CancelBallotDetails { get; private set; }
        public VoterDto Voter { get; private set; }

        public CancelBallotDetailsRequestedMessage(VoterHistoryDto statusChangeDetails, VoterDto voter)
        {
            Voter = voter;
            CancelBallotDetails = statusChangeDetails;
        }
    }
}
