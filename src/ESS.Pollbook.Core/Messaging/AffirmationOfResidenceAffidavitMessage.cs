using ESS.Pollbook.Core.DTO;

namespace ESS.Pollbook.Core.Messaging
{
    public class AffirmationOfResidenceAffidavitMessage
    {
        public VoterDto EditedVoter { get; private set; }
        public VoterDto PreviousVoter { get; private set; }

        public AffirmationOfResidenceAffidavitMessage(VoterDto editedVoter, VoterDto previousVoter)
        {
            EditedVoter = editedVoter;
            PreviousVoter = previousVoter;
        }
    }
}
