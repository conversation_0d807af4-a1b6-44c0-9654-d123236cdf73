using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.Model.Transactions;
using System.ComponentModel.DataAnnotations;

namespace ESS.Pollbook.Core.EventsDto
{
    public class ErrorEventDto : TransactionBase
    {
        public new TransactionType TransactionType => TransactionType.SoftwareError;

        [Required(AllowEmptyStrings = false)]
        [MaxLength(2048)]
        public string ErrorMessage { get; set; }
    }
}
