using ESS.Pollbook.Core.Attributes;
using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Model.Transactions;
using System;
using System.Collections.Generic;

namespace ESS.Pollbook.Core.EventsDto
{
    public class VoterEditEventDto : TransactionBase
    {
      public new TransactionType TransactionType => TransactionType.VoterEdit;

      [VoterAttribute("AbsenteeStatusEnumId")]
      public int? AbsenteeStatusEnumId { get; set; }

      public VoterAddressDto Address { get; set; }

      [VoterAttribute("AffidavitNumber")]
      public string AffidavitNumber { get; set; }

      [VoterAttribute("BallotStyleId")]
      public int? BallotStyleId { get; set; }

      [VoterAttribute("CountyId")]
      public int? CountyId { get; set; }

      [VoterAttribute("CommentText")]
      public string Comments { get; set; }

      [VoterAttribute("DateOfBirth", "DateToString")]
      public DateTime? DateOfBirth { get; set; }

      [VoterAttribute("DriversLicenseNumber")]
      public string DriversLicenseNumber { get; set; }

      [VoterAttribute("EmailAddress")]
      public string EmailAddress { get; set; }

      [VoterAttribute("FirstName")]
      public string FirstName { get; set; }

      [VoterAttribute("IdentificationStatusEnumId")]
      public int? IdentificationRequirementStatusEnumId { get; set; }

      [VoterAttribute("ISOLanguageEnumerationValueId")]
      public int? LanguageEnumId { get; set; }

      [VoterAttribute("LastName")]
      public string LastName { get; set; }

      [VoterAttribute("MiddleName")]
      public string MiddleName { get; set; }

      [VoterAttribute("NamePrefix")]
      public string NamePrefix { get; set; }

      [VoterAttribute("NameSuffix")]
      public string NameSuffix { get; set; }

      [VoterAttribute("PartyId")]
      public int? PartyId { get; set; }

      [VoterAttribute("PrecinctSplitId")]
      public int? PrecinctSplitId { get; set; }

      [VoterAttribute("VoterVRSignaturePath")]
      public string SignatureImageFileName { get; set; }

      [VoterAttribute("SsnLast4Numbers")]
      public string SsnLast4 { get; set; }

      [VoterAttribute("Voter_Deleted","BoolToInt")]
      public bool? VoterDeleted { get; set; }

      [VoterAttribute("VoterId")]
      public long? VoterId { get; set; }

      [VoterAttribute("VoterStatusEnumId")]
      public int? VoterStatusEnumId { get; set; }

      [VoterAttribute("VoterBallotStyleTypeJurisdictionEnumValueId")]
      public int VoterBallotStyleTypeJurisdictionEnumValueId { get; set; }

      [Voter("NotInRosterReasonsEnumId")]
      public int? NotInRosterReasonsEnumId { get; set; }

      [Voter("RecordInitialLoadIndicator")]
      public bool RecordInitialLoadIndicator { get; set; }

      [VoterAttribute("FirstNameSearch")]
      public string FirstNameSearch { get; set; }

      [VoterAttribute("LastNameSearch")]
      public string LastNameSearch { get; set; }

      [Voter("GenderId")]
      public int? GenderId { get; set; }

      [VoterAttribute("RegistrationDate")]
      public DateTime? RegistrationDate { get; set; }

      [VoterAttribute("Affidavits")]
      public List<AffidavitTemplateDto> Affidavits { get; set; }
    }
}
