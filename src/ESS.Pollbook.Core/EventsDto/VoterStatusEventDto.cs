using ESS.Pollbook.Core.Attributes;
using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.Model.Transactions;

namespace ESS.Pollbook.Core.EventsDto
{
    public class VoterStatusEventDto : TransactionBase
    {
        public new TransactionType TransactionType => TransactionType.VoterStatusChange;

        public long? VoterId { get; set; }

        public int? VoterStatusEnumId { get; set; }

        public int? AbsenteeStatusEnumId { get; set; }
        
        [Voter("Voter_Deleted", "BoolToInt")]
        public bool? VoterDeleted { get; set; }
    }
}
