using Autofac.Features.Indexed;
using ESS.Pollbook.Components.Repository.PollbookDefinedText;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Core.UICore;
using ESS.Pollbook.DataAccess.Interfaces;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ESS.Pollbook.Core.Logging;


namespace ESS.Pollbook.Components.Repository.Test.IncrementalUpdates
{
    [TestClass]
	public class PollbookDefinedTextTests
    {
        private IPollbookDefinedTextRepository _pollbookDefinedTextRepository;
        private Mock<IIndex<SqliteDatabaseType, IEssSqliteReaderAdapter>> _readerAdaptersMock;
        private Mock<IEssSqliteReaderAdapter> _readerAdapterMock;
        private Mock<IEssLogger> _essLoggerMock;

        [ClassInitialize]
        public static void ClassInitialize(TestContext testContext)
        {
            // context comment
        }

        public PollbookDefinedTextTests() { }

        [TestInitialize]
        public void Initialize()
        {
            _readerAdapterMock = new Mock<IEssSqliteReaderAdapter>();
            _readerAdaptersMock = new Mock<IIndex<SqliteDatabaseType, IEssSqliteReaderAdapter>>();
            _essLoggerMock = new Mock<IEssLogger>();
            _readerAdaptersMock.Setup(m => m[SqliteDatabaseType.Polldata]).Returns(_readerAdapterMock.Object);

            _pollbookDefinedTextRepository = new PollbookDefinedTextRepository(_essLoggerMock.Object, _readerAdaptersMock.Object);
        }

        [TestMethod()]
        public async Task LoadPollbookDefinedTextFromDb_ReturnsCorrectText()
        {
            //assemble
            var language = "English";
            var expectedValue = "Defined Text";
            var expected = new ElectionPollbookDefinedTextDto()
            {
                PollbookDefinedTextDescription = "Test",
                PollbookDefinedTextName = "Test",
                PollbookDefinedTextLanguage = language,
                PollbookDefinedTextValue = expectedValue
            };
            IEnumerable<ElectionPollbookDefinedTextDto> expectedList = new List<ElectionPollbookDefinedTextDto> { expected };

            _readerAdapterMock.Setup(m => m
                            .QueryAsync<ElectionPollbookDefinedTextDto>(It.IsAny<string>(), It.IsAny<object>()))
                        .Returns(Task.FromResult(expectedList));

            //act
            var result = (await _pollbookDefinedTextRepository.LoadPollbookDefinedTextFromDb()).ToList();

            //assert
            Assert.AreEqual(1, result.Count);
            Assert.AreEqual(expectedValue, result[0].PollbookDefinedTextValue);
            _readerAdapterMock.Verify(m => m
                .QueryAsync<ElectionPollbookDefinedTextDto>(It.IsAny<string>(), It.IsAny<object>())
                , Times.Exactly(1));
        }

        [TestMethod()]
        public async Task GetPollbookDefinedTextCurrentLanguage_ReturnsCorrectText_WhenTextIsFound()
        {
            //assemble
            await LoadDefinedText();

            //act
            var resultText = _pollbookDefinedTextRepository
                .GetPollbookDefinedTextForLanguage("Test", "English");

            //assert
            Assert.AreEqual("Defined Text", resultText);
        }

        [TestMethod()]
        public async Task GetPollbookDefinedTextCurrentLanguage_ReturnsCorrectText_WhenTextIsNotFound()
        {
            //assemble
            await LoadDefinedText();

            //act
            var resultText = _pollbookDefinedTextRepository
                .GetPollbookDefinedTextForLanguage("Test1", "Japanese");

            //assert
            Assert.AreEqual("Text not found!", resultText);
        }

        [TestMethod()]
        public async Task GetPollbookDefinedTextForControlIdAndLanguage_ReturnsCorrectText()
        {
            //assemble
            var controlId = 39;
            var language = "English";
            var sqlParams = new { ControlId = controlId, Language = language };
            var expectedValue = "Oath Text";
            var expected = new ElectionPollbookDefinedTextDto() {
                PollbookDefinedTextDescription = "Test",
                PollbookDefinedTextName = "Test",
                PollbookDefinedTextLanguage = language,
                PollbookDefinedTextValue = expectedValue
            };

            _readerAdapterMock.Setup(m => m
                            .QueryFirstOrDefaultAsync<ElectionPollbookDefinedTextDto>(It.IsAny<string>(), It.IsAny<object>()))
                        .Returns(Task.FromResult(expected));

            //act
            var result = await _pollbookDefinedTextRepository.GetPollbookDefinedTextForControlIdAndLanguage(controlId, language);

            //assert
            Assert.AreEqual(expectedValue, result.PollbookDefinedTextValue);
            _readerAdapterMock.Verify(m => m
                .QueryFirstOrDefaultAsync<ElectionPollbookDefinedTextDto>(It.IsAny<string>()
                    , It.Is<object>(p => (int)p.GetType().GetProperty("ControlId").GetValue(p, null) == sqlParams.ControlId
                                   && (string)p.GetType().GetProperty("Language").GetValue(p, null) == sqlParams.Language))
                , Times.Exactly(1));
        }

        private async Task LoadDefinedText()
        {
            var language = "English";
            var expectedValue = "Defined Text";
            var expected = new ElectionPollbookDefinedTextDto()
            {
                PollbookDefinedTextDescription = "Test",
                PollbookDefinedTextName = "Test",
                PollbookDefinedTextLanguage = language,
                PollbookDefinedTextValue = expectedValue
            };
            var expected2 = new ElectionPollbookDefinedTextDto()
            {
                PollbookDefinedTextDescription = "Test2",
                PollbookDefinedTextName = "Test2",
                PollbookDefinedTextLanguage = "WrongLanguage",
                PollbookDefinedTextValue = expectedValue
            };
            IEnumerable<ElectionPollbookDefinedTextDto> expectedList = 
                new List<ElectionPollbookDefinedTextDto> { expected, expected2 };

            _readerAdapterMock.Setup(m => m
                            .QueryAsync<ElectionPollbookDefinedTextDto>(It.IsAny<string>(), It.IsAny<object>()))
                        .Returns(Task.FromResult(expectedList));

            DefinedText.PollbookDefinedTextDtos = (await _pollbookDefinedTextRepository.LoadPollbookDefinedTextFromDb()).ToList();
            _pollbookDefinedTextRepository.SetPollbookDefinedTextCurrentLanguage(language);
        }
    }
}
