using ESS.Pollbook.Components.Repository.PollbookTransaction;
using ESS.Pollbook.Components.Repository.VoterEditInfo;
using ESS.Pollbook.Components.Repository.VRSignatures;
using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.CustomExceptions;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Events;
using ESS.Pollbook.Core.EventsDto;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.Model;
using ESS.Pollbook.Core.Model.Transactions;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Core.Utilities;
using ESS.Pollbook.Hardware.Storage;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;

namespace ESS.Pollbook.Components.Business.PollbookTransaction
{
   public class PollbookTransactionFactory : IPollbookTransactionFactory
   {
      private readonly IPollbookTransactionRepository _pollbookTransactionRepository;
      private readonly IPollbookFailedTransactionRepository _pollbookFailedTransactionRepository;
      private readonly IVoterAddEditInfoRepository _voterAddEditInfoRepository;
      private readonly IVrSignaturesRepository _vrSignaturesRepository;
      private readonly IEssLogger _essLogger;

      public PollbookTransactionFactory(
          IPollbookTransactionRepository pollbookTransactionRepository,
          IPollbookFailedTransactionRepository pollbookFailedTransactionRepository,
          IVoterAddEditInfoRepository voterAddEditInfoRepository,
          IVrSignaturesRepository vrSignaturesRepository,
          IEssLogger essLogger)
      {
         _pollbookTransactionRepository = pollbookTransactionRepository;
         _pollbookFailedTransactionRepository = pollbookFailedTransactionRepository;
         _voterAddEditInfoRepository = voterAddEditInfoRepository;
         _vrSignaturesRepository = vrSignaturesRepository;
         _essLogger = essLogger;
      }

      public async Task<PollbookTransactionResponse> CreateBallotTransaction(PollbookTransactionDto transaction, TransactionBatchRequest transactionRequest)
      {
         var value = await DoCreateTransaction(transaction, transactionRequest);
         return value;
      }
      
      public async Task<TransactionBatchRequest> CreateTransactionRequest(PollbookTransactionDto transaction)
      {
         //create/Get BallotEvent
         var eventCreator = new TransactionEventCreator();
         var ballotEvent = eventCreator.CreateBallotEvent(transaction, CurrentUserInfo.LoggedInUser);
         ballotEvent.IsReissue = transaction.Voter.IsPreviouslyVoted;
         ballotEvent.IsEarlyVote = transaction.IsEarlyVote;

         ballotEvent.Attributes["address_changed"] = transaction.Voter.HasAddressChanged ? "1" : "0";

         if (transaction.Voter.TexasVerifyVoterId != null)
         {
            ballotEvent.Attributes[TexasAttributes.TxVRCertification.ToString()] =
                transaction.Voter.TexasVerifyVoterId.NIR_VR_Verified ? "1" : "0";
            ballotEvent.Attributes[TexasAttributes.TxExempt.ToString()] =
                transaction.Voter.TexasVerifyVoterId.Exempt ? "1" : "0";
            ballotEvent.Attributes[TexasAttributes.TxListA.ToString()] =
                transaction.Voter.TexasVerifyVoterId.ListA ? "1" : "0";
            ballotEvent.Attributes[TexasAttributes.TxListB.ToString()] =
                transaction.Voter.TexasVerifyVoterId.ListB ? "1" : "0";

            if (SystemConfiguration.ElectionConfiguration.EnableTexasFormConfirmation)
            {
               ballotEvent.Attributes[TexasAttributes.FormsComplete.ToString()] =
                   transaction.Voter.TexasVerifyVoterId.FormsComplete ? "1" : "0";
            }
         }

         if (transaction.Voter.Dynamic != null)
         {
            foreach (KeyValuePair<string, string> kvp in transaction.Voter.Dynamic.DynamicData)
            {
               ballotEvent.DynamicData.Add(kvp.Key, kvp.Value);
            }
         }

         if (transaction.Voter.AcknowledgementSimilarNameInitials != null)
            ballotEvent.AcknowledgementSimilarNameInitials = transaction.Voter.AcknowledgementSimilarNameInitials;

         if (transaction.Voter.AcknowledgementNotOnRosterInitials != null)
            ballotEvent.AcknowledgementNotOnRosterInitials = transaction.Voter.AcknowledgementNotOnRosterInitials;

         if (transaction.Voter.Affidavits != null)
            ballotEvent.Affidavits = transaction.Voter.Affidavits.Values.ToList();

         ballotEvent.SurrenderAbsentee = transaction.Voter.VoterBallotDto.SurrenderAbsentee;
         ballotEvent.SignatureSkipReasonEnumId = transaction.Voter.SignatureSkipReasonEnumId;
         ballotEvent.SignatureSkipReasonOtherText = transaction.Voter.SignatureSkipReasonOtherText;

         ballotEvent.CaptureVoterIDPresentedIndicator = transaction.Voter.CaptureId?.IdWasPresentedByVoter ?? false;
         ballotEvent.CaptureVoterIdTypeId = transaction.Voter.CaptureId?.IdWasPresentedByVoter == true
             ? (int?)transaction.Voter.CaptureId.CaptureIdType?.Capture_Voter_ID_Type_ID
             : null;

         transaction.JSON = JsonConvert.SerializeObject(ballotEvent);
         var transactionRequest =
             PollbookTransactionRequest.Create(ballotEvent, ballotEvent.TransactionType,
                 transaction); //Given an event, create the transaction request 
         
         return transactionRequest;
      }

      public async Task<PollbookTransactionResponse> CreateBallotCancelTransaction(PollbookTransactionDto transaction)
      {
         var eventCreator = new TransactionEventCreator();
         var ballotCancelEvent = eventCreator.CreateBallotCancelEvent(transaction, CurrentUserInfo.LoggedInUser);
         transaction.JSON = JsonConvert.SerializeObject(ballotCancelEvent);
         var transactionRequest =
             PollbookTransactionRequest.Create(ballotCancelEvent, ballotCancelEvent.TransactionType, transaction);
         var value = await DoCreateTransaction(transaction, transactionRequest);
         return value;
      }

      public async Task<PollbookTransactionResponse> CreatePollsOpenTransaction(PollbookTransactionDto transaction)
      {
         var sw = new Stopwatch();
         sw.Start();

         var eventCreator = new TransactionEventCreator();
         var pollsOpenEvent = eventCreator.CreatePollsOpenedEvent(transaction, CurrentUserInfo.LoggedInUser);
         transaction.JSON = JsonConvert.SerializeObject(pollsOpenEvent);
         var transactionRequest =
             PollbookTransactionRequest.Create(pollsOpenEvent, pollsOpenEvent.TransactionType, transaction);
         var value = await DoCreateTransaction(transaction, transactionRequest);
         _essLogger.LogDebug(
            $"CreatePollsOpenTransaction - completed saving to local [elapsed time:{sw.ElapsedMilliseconds}ms]");
         sw.Stop();
         return value;
      }

      public async Task<PollbookTransactionResponse> CreatePollsCloseTransaction(PollbookTransactionDto transaction,
          string username)
      {
         var sw = new Stopwatch();
         sw.Start();

         var eventCreator = new TransactionEventCreator();
         var pollsCloseEvent = eventCreator.CreatePollsClosedEvent(transaction, username);
         _essLogger.LogDebug($"CreatePollsCloseTransaction - create event [elapsed time:{sw.ElapsedMilliseconds}ms]");

         transaction.JSON = JsonConvert.SerializeObject(pollsCloseEvent);
         var transactionRequest =
             PollbookTransactionRequest.Create(pollsCloseEvent, pollsCloseEvent.TransactionType, transaction);

         sw.Restart();
         var value = await DoCreateTransaction(transaction, transactionRequest);
         _essLogger.LogDebug($"CreatePollsCloseTransaction - docreate [elapsed time:{sw.ElapsedMilliseconds}ms]");
         sw.Stop();

         return value;
      }

      public async Task<PollbookTransactionResponse> CreateUserLoginTransaction(PollbookTransactionDto transaction,
          UserDto user)
      {
         var eventCreator = new TransactionEventCreator();
         var loginEvent = eventCreator.CreateLoginEvent(transaction, user.Username);
         transaction.JSON = JsonConvert.SerializeObject(loginEvent);
         var transactionRequest =
             PollbookTransactionRequest.Create(loginEvent, loginEvent.TransactionType, transaction);
         var value = await DoCreateTransaction(transaction, transactionRequest);
         return value;
      }

      public async Task<PollbookTransactionResponse> CreateUserLogoutTransaction(PollbookTransactionDto transaction,
          UserDto user)
      {
         var eventCreator = new TransactionEventCreator();
         var logoutEvent = eventCreator.CreateLogoutEvent(transaction, user.Username);
         transaction.JSON = JsonConvert.SerializeObject(logoutEvent);
         var transactionRequest =
             PollbookTransactionRequest.Create(logoutEvent, logoutEvent.TransactionType, transaction);
         var response = await DoCreateTransaction(transaction, transactionRequest);
         await _pollbookTransactionRepository.ForceManualWalCommandAsync();
         return response;
      }

      public async Task<PollbookTransactionResponse> CreateUserShutdownTransaction(PollbookTransactionDto transaction)
      {
         var eventCreator = new TransactionEventCreator();
         var exitEvent = eventCreator.CreateShutdownEvent(transaction, CurrentUserInfo.LoggedInUser);
         transaction.JSON = JsonConvert.SerializeObject(exitEvent);
         var transactionRequest =
             PollbookTransactionRequest.Create(exitEvent, exitEvent.TransactionType, transaction);
         var value = await DoCreateTransaction(transaction, transactionRequest);
         return value;
      }

      public PollbookTransactionResponse CreatePowerStatusTransaction(PollbookTransactionDto transaction,
          PowerStatusRequest powerStatusRequest)
      {
         var eventCreator = new TransactionEventCreator();
         var powerStatusEvent =
             eventCreator.CreatePowerStatusEvent(transaction, powerStatusRequest, CurrentUserInfo.LoggedInUser);
         transaction.JSON = JsonConvert.SerializeObject(powerStatusEvent);
         var transactionRequest =
             PollbookTransactionRequest.Create(powerStatusEvent, powerStatusEvent.TransactionType, transaction);
         transactionRequest.PollingPlaceId = transaction.PollingPlaceId;
         return new PollbookTransactionResponse()
         {
            HostSyncTransactionRequest = transactionRequest,
            PollbookTransactionId = 0,
            TransactionInsertSuccess = true
         };
      }

      public async Task<PollbookTransactionResponse> CreateTestModeTransaction(PollbookTransactionDto transaction,
          PowerStatusRequest powerStatusRequest)
      {
         var eventCreator = new TransactionEventCreator();
         var testModeEvent = eventCreator.ConvertToTestModeEvent(eventCreator.CreatePowerStatusEvent(transaction,
             powerStatusRequest, new UserDto() { Username = "ExpressPoll" }));
         transaction.JSON = JsonConvert.SerializeObject(testModeEvent);
         var transactionRequest =
             PollbookTransactionRequest.Create(testModeEvent, testModeEvent.TransactionType, transaction);
         var response = await DoCreateTransaction(transaction, transactionRequest);
         return response;
      }


      public async Task<PollbookTransactionResponse> CreateVoterAddTransaction(PollbookTransactionDto transaction)
      {
         var eventCreator = new TransactionEventCreator();
         var voterAddEvent = eventCreator.CreateVoterAddEvent(transaction);
         transaction.JSON = JsonConvert.SerializeObject(voterAddEvent);
         var transactionRequest =
             PollbookTransactionRequest.Create(voterAddEvent, voterAddEvent.TransactionType, transaction);
         var value = await DoCreateTransaction(transaction, transactionRequest);
         return value;
      }

		public async Task<PollbookTransactionResponse> CreateVoterEditTransaction(PollbookTransactionDto transaction,
			VoterDto previousVoter, bool includeAffidavits = true)
		{
			var eventCreator = new TransactionEventCreator();
			var voterEditEvent = eventCreator.CreateVoterEditEvent(transaction, previousVoter, includeAffidavits);
			transaction.JSON = JsonConvert.SerializeObject(voterEditEvent);
			var transactionRequest =
				PollbookTransactionRequest.Create(voterEditEvent, voterEditEvent.TransactionType, transaction);
			var value = await DoCreateTransaction(transaction, transactionRequest);
			return value;
		}

      public async Task<PollbookTransactionResponse> CreateWaitTimeGeneratedTransaction(
          PollbookTransactionDto transaction, int token, DateTime timestamp)
      {
         var eventCreator = new TransactionEventCreator();
         var waitTimeGeneratedEvent =
             eventCreator.CreateWaitTimeGeneratedEvent(transaction, CurrentUserInfo.LoggedInUser, token, timestamp);
         transaction.JSON = JsonConvert.SerializeObject(waitTimeGeneratedEvent);
         var transactionRequest = PollbookTransactionRequest.Create(waitTimeGeneratedEvent,
             waitTimeGeneratedEvent.TransactionType, transaction);
         var response = await DoCreateTransaction(transaction, transactionRequest);
         return response;
      }

      public async Task<PollbookTransactionResponse> CreateWaitTimeEnteredTransaction(
          PollbookTransactionDto transaction, DateTime timestamp, int waitTimeMinutes)
      {
         var eventCreator = new TransactionEventCreator();
         var waitTimeEnteredEvent = eventCreator.CreateWaitTimeEnteredEvent(transaction,
             CurrentUserInfo.LoggedInUser, timestamp, waitTimeMinutes);
         transaction.JSON = JsonConvert.SerializeObject(waitTimeEnteredEvent);
         var transactionRequest = PollbookTransactionRequest.Create(waitTimeEnteredEvent,
             waitTimeEnteredEvent.TransactionType, transaction);
         var response = await DoCreateTransaction(transaction, transactionRequest);
         return response;
      }

      public async Task<PollbookTransactionResponse> CreateErrorMessageTransaction(PollbookTransactionDto transaction,
          string errorMsg, string parentTransId,
          bool isMaintenance = false)
      {
         var eventCreator = new TransactionEventCreator();

         UserDto currentLoginUser;
         if (isMaintenance)
         {
            currentLoginUser = new UserDto()
            {
               Username = ApplicationUserNameEnum.Maintenance.ToString()
            };
         }
         else
         {
            currentLoginUser = CurrentUserInfo.LoggedInUser;
         }

         var errorEvent = eventCreator.CreateErrorEvent(transaction, currentLoginUser, errorMsg, parentTransId);
         transaction.JSON = JsonConvert.SerializeObject(errorEvent);
         var transactionRequest =
             PollbookTransactionRequest.Create(errorEvent, errorEvent.TransactionType, transaction);
         var value = await DoCreateTransaction(transaction, transactionRequest);
         return value;
      }

      public async Task<PollbookTransactionResponse> CreateIncrementalUpdateTransaction(
          PollbookTransactionDto transaction, int progressIndicatorId,
          bool isMaintenance = false)
      {
         PollbookTransactionResponse response = new PollbookTransactionResponse
         {
            TransactionInsertSuccess = false
         };
         try
         {
            var eventCreator = new TransactionEventCreator();
            var currentLoginUser = CurrentUserInfo.LoggedInUser;
            var loginUserName = string.Empty;

            if (isMaintenance)
            {
               loginUserName = ApplicationUserNameEnum.Maintenance.ToString();
            }
            else if (currentLoginUser != null)
            {
               loginUserName = currentLoginUser.Username;
            }

            var incrementalUpdateEvent =
                eventCreator.CreatIncrementalUpdateEvent(transaction, loginUserName, progressIndicatorId);
            transaction.JSON = JsonConvert.SerializeObject(incrementalUpdateEvent);
            var transactionRequest = PollbookTransactionRequest.Create(incrementalUpdateEvent,
                incrementalUpdateEvent.TransactionType, transaction);
            response = await DoCreateTransaction(transaction, transactionRequest);
            return response;
         }
         catch (Exception ex)
         {
            Dictionary<string, string> logProps = new Dictionary<string, string>
                {
                    { "Action", "Creating incremental update transaction" }
                };
            _essLogger.LogError(ex, logProps);
            return response;
         }
      }

      private async Task<PollbookTransactionResponse> DoCreateTransaction(PollbookTransactionDto transaction,
          TransactionBatchRequest request)
      {
         var response = _pollbookTransactionRepository.InsertTransaction(transaction);

         switch (response.IsSuccess)
         {
            case true:
               {
                  if (SystemConfiguration.ElectionConfiguration.PeerSync &&
                      Helpers.IsPeerSharableTransaction(transaction))
                     _pollbookTransactionRepository.ShareWithPeers(transaction);
                  break;
               }
            case false:
               _ = _pollbookTransactionRepository.WriteInsertTransactionToQueue(response, transaction);
               break;
         }

         return await CreateTransactionResponse(request, transaction.TransactionGuid);
      }

      public async Task<PollbookTransactionResponse> CreateTransactionResponse(TransactionBatchRequest request,
          string guid)
      {
         var isSuccess = false;
         var id = await _pollbookTransactionRepository.GetLastTransactionIdAsync(guid);

         if (id > 0)
         {
            isSuccess = true;
         }

         return new PollbookTransactionResponse()
         {
            HostSyncTransactionRequest = request,
            PollbookTransactionId = id,
            TransactionInsertSuccess = isSuccess
         };
      }

      public string GetFormattedSequenceNumber(int number)
      {
         return (number + 1).ToString("000");
      }
      
      public async Task ProcessFailedTransactionAsync(bool isFromBackGroundProcess = false, CancellationToken token = default)
      {
         if (token.IsCancellationRequested)
         {
            return;
         }
         
         var failedTransactions = (await _pollbookFailedTransactionRepository.GetFailedTransactions(isFromBackGroundProcess)).ToList();
         if (!failedTransactions.Any())
         {
            _essLogger.LogDebug("No failed transactions found to process.");
            return;
         }

         var successfulTransactions = new List<FailedTransactionDto>();
         var failedTransactionGuids = new List<FailedTransactionDto>();

         if (token.IsCancellationRequested)
         {
            return;
         }

         foreach (var transaction in failedTransactions)
         {
            switch (transaction.TransactionType)
            {
               case nameof(TransactionType.VoterAdd):
                  ProcessVoterAddTransaction(transaction, successfulTransactions, failedTransactionGuids);
                  break;
               case nameof(TransactionType.VoterEdit):
                  ProcessVoterEditTransaction(transaction, successfulTransactions, failedTransactionGuids);
                  break;
               case nameof(TransactionType.VoterStatusChange):
                  ProcessVoterStatusTransaction(transaction, successfulTransactions, failedTransactionGuids);
                  break;
            }

            if (!token.IsCancellationRequested) continue;

            _essLogger.LogDebug($"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name} has been cancelled.");
            break;
         }

         _pollbookFailedTransactionRepository.BulkUpdateFailedTransactions(successfulTransactions);
         _pollbookFailedTransactionRepository.BulkUpdateFailedTransactions(failedTransactionGuids, true);
         
         _pollbookTransactionRepository.BulkUpdateStatusForTransactionsByGuid(successfulTransactions.Select(g => g.TransactionGuid), ProcessingStatus.Completed);
      }

      private void ProcessVoterEditTransaction(PollbookTransactionDto tx,
          List<FailedTransactionDto> successfulTransactions, List<FailedTransactionDto> failedTransactionGuids)
      {
         var localResponse = new LocalResponse();

         try
         {
            var jsonObj = JsonConvert.DeserializeObject<VoterEditEventDto>(tx.JSON);
            var voterDto = Mapper.MapToVoter(jsonObj);
            voterDto.VoterKey = tx.SourceKey;

            localResponse =
                _voterAddEditInfoRepository.UpdateVoter(voterDto, tx.DeviceName, tx.TransactionDate);

            if (localResponse.IsSuccess)
            {
               localResponse = _voterAddEditInfoRepository.UpdateVoterAddress(
                   Mapper.MapToEvent<VoterAddressDto>(voterDto), tx.DeviceName, tx.TransactionDate);
            }
         }
         catch (Exception ex)
         {
            var logProps = new Dictionary<string, string>
                    { { "Action", "PollbookTransactionFactory.ProcessVoterEditTransaction" } };
            _essLogger.LogError(ex, logProps);

            localResponse.IsSuccess = false;
            localResponse.LocalException = ex;
         }

         UpdateTxGuidList(tx, successfulTransactions, failedTransactionGuids, localResponse.LocalException?.Message);
      }

        private void ProcessVoterStatusTransaction(PollbookTransactionDto tx,
          List<FailedTransactionDto> successfulTransactions, List<FailedTransactionDto> failedTransactionGuids)
      {
         var localResponse = new LocalResponse();

         try
         {
            var jsonObj = JsonConvert.DeserializeObject<VoterStatusEventDto>(tx.JSON);
            var voterStatusDto = Mapper.MapToVoterAbsStatusUpdates(jsonObj);

            localResponse = _voterAddEditInfoRepository.UpdateVoterStatus(voterStatusDto, tx.TransactionDate);
         }
         catch (Exception ex)
         {
            var logProps = new Dictionary<string, string>
                    { { "Action", $"{GetType().Name}.{ MethodBase.GetCurrentMethod()?.Name}"
            } };
            _essLogger.LogError(ex, logProps);

            localResponse.IsSuccess = false;
            localResponse.LocalException = ex;
         }

         UpdateTxGuidList(tx, successfulTransactions, failedTransactionGuids, localResponse.LocalException?.Message);
      }

      private void ProcessVoterAddTransaction(PollbookTransactionDto tx,
          List<FailedTransactionDto> successfulTransactions, List<FailedTransactionDto> failedTransactionGuids)
      {
         var localResponse = new LocalResponse();

         try
         {
            var jsonObj = JsonConvert.DeserializeObject<VoterAddEventDto>(tx.JSON);
            if (jsonObj == null)
            {
               localResponse.LocalException = new Exception("JSON object is null.");
               UpdateTxGuidList(tx, successfulTransactions, failedTransactionGuids,
                   localResponse.LocalException.Message);

               return;
            }

            if (jsonObj.VoterId != null) jsonObj.Address.VoterId = jsonObj.VoterId.Value;
            var voterDto = Mapper.MapToVoter(jsonObj);
            voterDto.VoterKey = tx.SourceKey;

            if (string.IsNullOrEmpty(voterDto.FirstNameSearch))
               voterDto.FirstNameSearch = Helpers.GetAlphaNumericString(voterDto.FirstName);
            if (string.IsNullOrEmpty(voterDto.LastNameSearch))
               voterDto.LastNameSearch = Helpers.GetAlphaNumericString(voterDto.LastName);

            localResponse =
                _voterAddEditInfoRepository.InsertVoter(voterDto, tx.SourceKey, tx.DeviceName,
                    tx.TransactionDate);

            if (localResponse.IsSuccess)
            {
               if (jsonObj.Address == null)
               {
                  localResponse.IsSuccess = false;
                  localResponse.LocalException = new Exception("Address is null");
               }
               else
               {
                  var address = jsonObj.Address;
                  localResponse = _voterAddEditInfoRepository.InsertVoterAddress(address,
                      tx.DeviceName,
                      tx.TransactionDate);
               }
            }
         }
         catch (Exception ex)
         {
            var logProps = new Dictionary<string, string>
                    { { "Action", "PollbookTransactionFactory.ProcessVoterAddTransaction" } };
            _essLogger.LogError(ex, logProps);

            localResponse.IsSuccess = false;
            localResponse.LocalException = ex;
         }

         UpdateTxGuidList(tx, successfulTransactions, failedTransactionGuids, localResponse.LocalException?.Message);
      }

      private static void UpdateTxGuidList(PollbookTransactionDto tx,
          List<FailedTransactionDto> successfulTransactions, List<FailedTransactionDto> failedTransactionGuids,
          string error)
      {
         if (tx == null) return; 
         
         var targetList = string.IsNullOrEmpty(error)
            ? successfulTransactions
            : failedTransactionGuids;
         
         targetList.Add(new FailedTransactionDto
         {
            TransactionGuid = tx.TransactionGuid,
            ErrorMessage    = error ?? string.Empty
         });
      }

      private static IEnumerable<FailedTransactionDto> ConvertVoterTransactionsToFailedTransactions(
          List<string> transactions)
      {
         foreach (var transaction in transactions)
         {
            yield return new FailedTransactionDto
            { TransactionGuid = transaction, ErrorMessage = string.Empty };
         }
      }

      private VoterAddResponse ProcessVoterAddTransactions(List<PollbookTransactionDto> transactions)
      {
         bool status = false;
         var voterAddResponse = new VoterAddResponse
         {
            VoterAddGuids =
                 ConvertVoterTransactionsToFailedTransactions(transactions.Select(t => t.TransactionGuid).ToList()),
            AddVoterList = new List<List<object>>(),
            AddVoterAddressList = new List<List<object>>(),
            SignaturesList = new Dictionary<string, VoterSignatureDto>()
         };
         try
         {
            foreach (var transaction in transactions)
            {
               var voterAddEvent = BuildVoterAddEvent(transaction.JSON);

               if (voterAddEvent == null)
                  continue;

               voterAddResponse.AddVoterList.Add(_voterAddEditInfoRepository
                   .ConvertAddVoterList(
                       voterAddEvent,
                       transaction.SourceKey,
                       transaction.DeviceName,
                       transaction.TransactionDate));

               voterAddResponse.AddVoterAddressList.Add(_voterAddEditInfoRepository
                   .ConvertAddVoterAddressList(
                       voterAddEvent,
                       transaction.DeviceName,
                       transaction.TransactionDate));

               if (!string.IsNullOrEmpty(voterAddEvent.SignatureImageFileName))
               {
                  var signatureDto = BuildSignatureDto(voterAddEvent, transaction.SourceKey,
                      voterAddEvent.TransactionType);
                  if (signatureDto != null)
                     voterAddResponse.SignaturesList[transaction.SourceKey] = signatureDto;
               }
            }

            status = _voterAddEditInfoRepository.BulkInsertAddVoterTransactions(voterAddResponse.AddVoterList)
                     && _voterAddEditInfoRepository.BulkInsertAddVoterAddressTransactions(voterAddResponse
                         .AddVoterAddressList);

            if (status)
               _pollbookTransactionRepository.BulkUpdateStatusForTransactionsByGuid(
                  transactions.Select(t => t.TransactionGuid), ProcessingStatus.Completed);
         }
         catch (Exception ex)
         {
            var logProps = new Dictionary<string, string>
                {
                    {
                        "Action",
                        $"PollbookTransactionFactory.ProcessVoterAddTransactions Processing add voter Transactions failed: {ex.Message}"
                    }
                };
            _essLogger.LogError(ex, logProps);
         }


         if (!status)
         {
            var failedList = new List<FailedTransactionDto>();

            foreach (var transaction in transactions)
            {
               var voterEditEvent = BuildVoterEditEvent(transaction.JSON);
               var voter = Mapper.MapToVoter<VoterEditEventDto>(voterEditEvent);
               var voterAddress = voterEditEvent.Address;

               if ((_voterAddEditInfoRepository.InsertVoter(voter)).IsSuccess
                   || (_voterAddEditInfoRepository.InsertVoterAddress(voterAddress)).IsSuccess)
               {
                  failedList.Add(new FailedTransactionDto()
                  {
                     ErrorMessage = "Error",
                     TransactionGuid = transaction.TransactionGuid
                  });
               }

               _pollbookTransactionRepository.UpdateStatusForTransaction(transaction.TransactionGuid, ProcessingStatus.Completed);

               if (failedList.Count > 0)
               {
                  _pollbookFailedTransactionRepository.BulkInsertFailedTransactionGUIDs(failedList);
               }
            }

            voterAddResponse.Status = true;
         }

         return voterAddResponse;
      }

      private VoterAddEventDto BuildVoterAddEvent(string json)
      {
         var voterAddEvent = JsonConvert.DeserializeObject<VoterAddEventDto>(json);
         if (voterAddEvent != null)
         {
            voterAddEvent.FirstNameSearch = Helpers.GetAlphaNumericString(voterAddEvent.FirstName);
            voterAddEvent.LastNameSearch = Helpers.GetAlphaNumericString(voterAddEvent.LastName);

            return voterAddEvent;
         }

         return null;
      }

      private static VoterSignatureDto BuildSignatureDto(object transaction, string key,
          TransactionType transactionType)
      {
         dynamic voterEvent = null;
         switch (transactionType)
         {
            case TransactionType.VoterAdd:
               voterEvent = (VoterAddEventDto)transaction;
               break;
            case TransactionType.VoterEdit:
               voterEvent = (VoterEditEventDto)transaction;
               break;
            default:
               return null;
         }

         return new VoterSignatureDto()
         {
            Voter_Signature_Voter_ID = voterEvent.VoterId,
            Voter_Source_Voter_Key = key,
            Voter_Signature_Server_FilePath =
                 Path.Combine(StorageLocator.DefaultDbLocation, voterEvent.SignatureImageFileName),
            Voter_Signature_Pollbook_FilePath =
                 Path.Combine(StorageLocator.DefaultDbLocation, voterEvent.SignatureImageFileName),
            Voter_Signature_Pollbook_FileName = key
         };
      }

      private VoterEditEventDto BuildVoterEditEvent(string json)
      {
         var voterEditEvent = JsonConvert.DeserializeObject<VoterEditEventDto>(json);
         if (voterEditEvent != null)
         {
            voterEditEvent.FirstNameSearch = Helpers.GetAlphaNumericString(voterEditEvent.FirstName);
            voterEditEvent.LastNameSearch = Helpers.GetAlphaNumericString(voterEditEvent.LastName);

            return voterEditEvent;
         }

         return null;
      }

      private bool ProcessVoterSignatures(Dictionary<string, VoterSignatureDto> addSignatures,
          Dictionary<string, VoterSignatureDto> editSignatures)
      {
         try
         {
            var allSignatures = MergeVoterSignatureSets(editSignatures, addSignatures);

            if (allSignatures?.Count > 0)
            {
               _vrSignaturesRepository.BulkUpsertSignatures(allSignatures.Values.ToList());
            }

            return true;
         }
         catch (Exception ex)
         {
            var logProps = new Dictionary<string, string>
                {
                    { "Action", "PollbookTransactionFactory.ProcessVoterSignatures" }
                };
            _essLogger.LogError(ex, logProps);
         }

         return false;
      }
      
      private static Dictionary<string, VoterSignatureDto> MergeVoterSignatureSets(
          Dictionary<string, VoterSignatureDto> editSignatures, Dictionary<string, VoterSignatureDto> addSignatures)
      {
         Dictionary<string, VoterSignatureDto> allSignatures;
         try
         {
            allSignatures = editSignatures.Merge(addSignatures);
         }
         catch (DictionaryMergeException ex)
         {
            switch (ex.ErrorNumber)
            {
               case DictionaryMergeExceptionCode.D00:
                  return editSignatures;
               case DictionaryMergeExceptionCode.N01:
               case DictionaryMergeExceptionCode.C01:
                  return addSignatures;
               case DictionaryMergeExceptionCode.N02:
               case DictionaryMergeExceptionCode.C02:
                  return editSignatures;
               default:
                  throw;
            }
         }

         return allSignatures;
      }

      private sealed class VoterAddResponse
      {
         public IEnumerable<FailedTransactionDto> VoterAddGuids { get; set; }
         public List<List<object>> AddVoterList { get; set; }
         public List<List<object>> AddVoterAddressList { get; set; }
         public Dictionary<string, VoterSignatureDto> SignaturesList { get; set; }
         public bool Status { get; set; }
      }

      private sealed class VoterEditResponse
      {
         public IEnumerable<FailedTransactionDto> VoterEditGuids { get; set; }
         public List<List<object>> EditVoterList { get; set; }
         public List<List<object>> EditVoterAddressList { get; set; }
         public Dictionary<string, VoterSignatureDto> SignaturesList { get; set; }
         public bool Status { get; set; }
      }
   }
}