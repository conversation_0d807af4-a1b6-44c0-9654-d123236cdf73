using System.Collections.Generic;

namespace ESS.Pollbook.Components.Business.ResourceTranslation
{
    public enum TranslationContext
    {
        VoterView_History
    }

    public class ResourceTranslationFactory : IResourceTranslationFactory
    {
        public Dictionary<TranslationContext, Dictionary<string, string>> _resourcesDictionary;

        public ResourceTranslationFactory()
        {
            _resourcesDictionary = new Dictionary<TranslationContext, Dictionary<string, string>>();

            //As resources are currently hard coded, we define a separate method for creating translations for each different context as needed.

            PopulateDictionaryForVoterView();
        }

        private void PopulateDictionaryForVoterView()
        {
            //Make sure to use lower cases for the resource keys
            Dictionary<string, string> translationsForVoterView = new Dictionary<string, string>();
            translationsForVoterView.Add("ballotissue", "Ballot Issued");
            translationsForVoterView.Add("ballotcancel", "Ballot Canceled");
            translationsForVoterView.Add("reissuedballot", "Ballot Reissued");
            translationsForVoterView.Add("voteredit", "Voter Edited");
            translationsForVoterView.Add("voteradd", "Voter Added");
            translationsForVoterView.Add("earlyballotissue", "Early Ballot Issued");
            translationsForVoterView.Add("earlyballotreissue", "Early Ballot Reissued");
            _resourcesDictionary.Add(TranslationContext.VoterView_History, translationsForVoterView);
        }

        /// <summary>
        /// Returns the translation for a given keyword, in a given context.
        /// </summary>
        /// <param name="context">The context where this function is being called from (view, report, sms message, use case, etc), in which the translation retrieved makes sense</param>
        /// <param name="keyword">The keyword we want to translate, case insensitve</param>
        /// <exception cref="KeyNotFoundException">If either context or keyword are not found</exception>
        /// <returns></returns>
        public string GetTranslation(TranslationContext context, string keyword)
        {
            return _resourcesDictionary[context][keyword.ToLower()];
        }
    }
}
