using ESS.Pollbook.Components.Repository.Printer;
using ESS.Pollbook.Core;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.Model;
using ESS.Pollbook.Hardware.Printing;
using System;
using System.Collections.Generic;
using System.Text;

namespace ESS.Pollbook.Components.Business.Printing
{
	public class PrintWaitTimeFactory : PrintFactoryBase, IPrintWaitTimeFactory
	{
        private readonly IPrinterManager _printerManager;
        private readonly IEssLogger _essLogger;

        public PrintWaitTimeFactory(IPrinterManager printerManager, IEssLogger essLogger, IPrinterRepository printerRepository)
	        : base(essLogger, printerRepository)
        {
            _printerManager = printerManager;
            _essLogger = essLogger;
        }

        public PrintResponse PrintData(params object[] data)
        {
            var response = new PrintResponse();
            try
            {
                CreatePrintRequest();

                if (_printerRequest == null)
                {
                    response.PrinterErrorMessage = PrinterErrorMessages.PrinterNotSelected;
                    return response;
                }

                _printerRequest.ReportType = ReportTypes.WaitTime;

                _printerRequest.PrintData = CreatePrintData(data);
                response = _printerManager.PrintDocument(_printerRequest);
            }
            catch (Exception ex)
            {
                Dictionary<string, string> logProps = new Dictionary<string, string>();
                logProps.Add("Action", "PrintData");
                _essLogger.LogError(ex, logProps);
            }
            return response;
        }

        private string CreatePrintData(params object[] waitTimeData)
        {
            var stringBuilder = new StringBuilder();

            foreach (var obj in waitTimeData)
            {
                stringBuilder.Append((string)obj + Environment.NewLine);
            }
            return stringBuilder.ToString();
        }

        protected override void CreateConcretePrintRequest()
        {
        }
    }
}
