using ESS.Pollbook.Components.Repository.Printer;
using ESS.Pollbook.Core;
using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.Constants;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Interface;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.Model;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Hardware.Printing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ESS.Pollbook.Components.Business.Printing
{
	public class PrintBallotReissuedListFactory : PrintFactoryBase, IPrintBallotReissuedListFactory
	{
        #region Private Members

        private string _printData;
        private readonly IPrinterManager _printerManager;
        private readonly CommaFormatter _CommaFormatter;
        private readonly IEssLogger _essLogger;

        private string PartyLabel => DefinedText.PollbookDefinedTextDtos.FirstOrDefault(n => n.PollbookDefinedTextLanguage.Equals("English") && n.PollbookDefinedTextName.Equals("Party"))?.PollbookDefinedTextValue.ToUpper();
        private string PrecinctLabel => DefinedText.PollbookDefinedTextDtos.FirstOrDefault(n => n.PollbookDefinedTextLanguage.Equals("English") && n.PollbookDefinedTextName.Equals("Precinct"))?.PollbookDefinedTextValue.ToUpper();

        #endregion

        #region Constants

        // Voted list specific constants
        const string CONST_REPORT_TITLE = "REISSUED REPORT";

        const string CONST_REPORT_HEADER1 = "  #   VOTER";
        const string CONST_REPORT_HEADER2 = "      PARTY          PRECINCT";

        private const string CONST_REPORT_FOOTER1 = "VOTERS REISSUED BALLOTS ";
        private const string CONST_REPORT_FOOTER2 = " TOTAL:   ";
        private const string CONST_REPORT_FOOTER3 = " VOTERS TOTAL:   ";
        private const string CONST_REPORT_FOOTER4 = " DEVICE:  ";

        #endregion

        #region Constructor

        public PrintBallotReissuedListFactory(CommaFormatter commaFormatter, IAppConfig appConfig, IPrinterManager printerManager, IEssLogger essLogger,
	        IPrinterRepository printerRepository) :
	        base(essLogger, printerRepository)
        {
            _appConfig = appConfig;
            _printerManager = printerManager;
            _CommaFormatter = commaFormatter;
            _essLogger = essLogger;
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Create the data string that will be printed.
        /// </summary>
        private void CreatePrintData(params object[] ballotReissuedListCount)
        {
            StringBuilder ballotReissuedListInfo = new StringBuilder();

            var ballotReissuedCount = (BallotReissuedListCount)ballotReissuedListCount[0];

            var ballotReissuedList = ballotReissuedCount.BallotReissuedListItems;

            if (ballotReissuedList == null)
            {
                ballotReissuedList = new List<BallotReissuedListItemDto>();
            }

            foreach (var ballotReissuedListItem in ballotReissuedList)
            {
                ballotReissuedListInfo.Append(ballotReissuedListItem.LineNumber.ToString().PadLeft(5, ' '))
                    .Append(" ")
                    .AppendLine(ballotReissuedListItem.FullName.ToUpper())
                    .Append(string.Empty.PadLeft(8, ' '))
                    .Append(ballotReissuedListItem.PartyDisplayName.ToUpper().PadRight(17, ' '))
                    .AppendLine(ballotReissuedListItem.PrecinctSplitName.ToUpper())
                    .AppendLine();
            }

            StringBuilder sb = new StringBuilder();
            string standardHeader = sb.AppendLine(CONST_REPORT_TITLE)
                .AppendLine(string.Empty)
                .AppendLine(ReportingConstants.CONST_ELECTION_HEADER +
                            SystemConfiguration.ElectionConfiguration.ElectionName)
                .AppendLine(ReportingConstants.CONST_POLLLOC_HEADER)
                .AppendLine(ReportingConstants.CONST_INDENT +
                            LoggedInPollplaceInfo.LoggedInPollPlace.PollingPlaceDisplayName)
                .AppendLine(ReportingConstants.CONST_INDENT + ReportingConstants.CONST_INDENT +
                            LoggedInPollplaceInfo.LoggedInPollPlace.PollingPlaceStreetName.Trim())
                .AppendLine(ReportingConstants.CONST_INDENT + ReportingConstants.CONST_INDENT +
                            $"{LoggedInPollplaceInfo.LoggedInPollPlace.PollingPlaceCityName}, {LoggedInPollplaceInfo.LoggedInPollPlace.PollingPlaceStateProvinceCode} {LoggedInPollplaceInfo.LoggedInPollPlace.PollingPlacePostalCode}")
                .AppendLine(ReportingConstants.CONST_DEVICENAME_HEADER + SystemDetails.MachineName).ToString();

            using (PrintUtility pu = new PrintUtility(ballotReissuedListInfo, _printerRequest, _essLogger))
            {
                _printData = standardHeader +
                             ballotReissuedCount.Timestamp + Environment.NewLine +
                             Environment.NewLine +
                             $" {CONST_REPORT_HEADER1}" + Environment.NewLine +
                             string.Empty.PadRight(7, ' ') + PartyLabel.PadRight(15, ' ') + PrecinctLabel + Environment.NewLine +
                             Environment.NewLine +
                             pu.ToString() + Environment.NewLine +
                             CONST_REPORT_FOOTER1 + Environment.NewLine +
                             CONST_REPORT_FOOTER2 + _CommaFormatter.FormatString(ballotReissuedList.Count) +
                             Environment.NewLine +
                             CONST_REPORT_FOOTER3 + _CommaFormatter.FormatString(ballotReissuedCount.VoterCount) +
                             Environment.NewLine +
                             CONST_REPORT_FOOTER4 + SystemDetails.MachineName +
                             Environment.NewLine + Environment.NewLine;
            }
        }

        #endregion

        #region Public Methods

        public PrintResponse PrintData(params object[] data)
        {
            PrintResponse response = new PrintResponse();
            try
            {
                CreatePrintRequest();

                if (_printerRequest == null)
                {
                    response.PrinterErrorMessage = PrinterErrorMessages.PrinterNotSelected;
                    return response;
                }

                // set this according to the voted list needs
                _printerRequest.FontSize = "8";
                _printerRequest.ReportType = ReportTypes.BallotReissued;

                CreatePrintData(data);
                _printerRequest.PrintData = _printData;
                response = _printerManager.PrintDocument(_printerRequest);
            }
            catch (Exception ex)
            {
                Dictionary<string, string> logProps = new Dictionary<string, string>();
                logProps.Add("Action", "PrintData");
                _essLogger.LogError(ex, logProps);
            }

            return response;
        }

        #endregion

        protected override void CreateConcretePrintRequest()
        {
        }
    }
}
