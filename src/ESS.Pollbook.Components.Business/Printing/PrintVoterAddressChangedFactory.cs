using ESS.Pollbook.Components.Repository.Printer;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Interface;
using ESS.Pollbook.Core;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.Model;
using ESS.Pollbook.Hardware.Printing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ESS.Pollbook.Core.StaticValues;
using System.Windows.Forms;

namespace ESS.Pollbook.Components.Business.Printing
{
    public class PrintVoterAddressChangedFactory : PrintFactoryBase, IPrintVoterAddressChangedFactory
    {
        private readonly IEssLogger _essLogger;
        private readonly ISystemManagement _systemManagement;
        private readonly IPrinterRepository _printerRepository;
        private readonly IVoterAddressChangedDocumentEngine _voterAddressChangedDocumentEngine;

        #region Constructor

        public PrintVoterAddressChangedFactory(IAppConfig appConfig,
            IEssLogger essLogger,
            IPrinterRepository printerRepository,
            IVoterAddressChangedDocumentEngine voterAddressChangedDocumentEngine,
            ISystemManagement systemManagement) : base(essLogger, printerRepository)
        {
            _appConfig = appConfig;
            _essLogger = essLogger;
            _systemManagement = systemManagement;
            _printerRepository = printerRepository;
            _voterAddressChangedDocumentEngine = voterAddressChangedDocumentEngine;
        }

        #endregion

        #region Public Methods

        public async Task<PrintResponse> PrintData(VoterDto editedVoter, VoterDto previousVoter)
        {
            try
            {
                CreatePrintRequest();

                var response = _systemManagement.GetPrinterInformationByName(_currentPrinter.PrinterName, true) ??
                               new PrintResponse() { PrinterErrorMessage = PrinterErrorMessages.PrinterNotFound };

                if (_printerRequest == null)
                {
                    response.PrinterErrorMessage = PrinterErrorMessages.PrinterNotSelected;
                }
                else if (response.PrinterAvailable())
                {
                    PrinterManagement.SetPrinterAsDefault(response.PrinterName);

                    var webBrowserForPrinting = new WebBrowser();

                    var fields = await GetAddressChangedDocumentFieldsAsync(LoggedInPollplaceInfo.IsEarlyVotingPollPlace);
                    webBrowserForPrinting.DocumentText = _voterAddressChangedDocumentEngine.BuildHtmlDocument(fields, editedVoter, previousVoter);

                    webBrowserForPrinting.DocumentCompleted += PrintDocument;
                }
                else
                {
                    response.PrinterErrorMessage = PrinterErrorMessages.PrinterNotConnected;
                }

                return response;
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string> { { "Action", "PrinterFacade.PrintVoterAddressChanged" } };
                _essLogger.LogError(ex, logProps);
                return null;
            }
        }

        public PrintResponse PrintData(params object[] data)
        {
            return new PrintResponse() { PrinterErrorMessage = "Not Implemented" };
        }

        #endregion

        #region Private Methods

        private void PrintDocument(object sender, WebBrowserDocumentCompletedEventArgs e)
        {
            ((WebBrowser)sender).Print();
            ((WebBrowser)sender).Dispose();
        }

        private async Task<IEnumerable<PrinterFormatFieldDto>> GetAddressChangedDocumentFieldsAsync(bool isEarlyVote)
        {
            var ev = isEarlyVote ? 1 : 0;
            var fields = await _printerRepository.GetReportFieldsAsync();

            if (fields is null)
                return Enumerable.Empty<PrinterFormatFieldDto>();

            var addrFields = fields
                .Where(f => f.EarlyVote == ev && f.FieldName.StartsWith("addr_"))
                .OrderBy(f => f.LineNumber);

            return addrFields;
        }

        #endregion

        #region Protected Methods

        protected override void CreateConcretePrintRequest()
        {
        }

        #endregion
    }
}
