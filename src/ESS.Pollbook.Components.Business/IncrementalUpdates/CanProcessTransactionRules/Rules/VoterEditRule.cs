using ESS.Pollbook.Components.Repository.PollbookTransaction;
using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Model.Transactions;
using System;
using System.Collections.Generic;

namespace ESS.Pollbook.Components.Business.IncrementalUpdates.CanProcessTransactionRules.Rules
{
    /// <summary>
    /// This rule tests to see if a VoterEdit can be processed after a BallotIssue
    /// Type must be VoterEdit
    /// Must be IsEarlyVote == true
    /// Must have a IssueBallot before the VoterEdit "issuedBallots"
    /// Must be the same device as the IssuedBallot transaction
    /// </summary>
    public class VoterEditRule : ICanProcessTransactionRule
    {
        public TransactionType TxType  => TransactionType.VoterEdit;

        public LocalResponse CanProcessTransactionRule(PollbookTransactionDto tx, Dictionary<string, BallotDto> ballotsIssued)
        {
            try
            {
                // Attempt to get the issued ballot for the given SourceKey
                if (!ballotsIssued.TryGetValue(tx.SourceKey, out var issuedBallotTx))
                {
                    return new LocalResponse { IsSuccess = true }; // No matching ballot found, transaction is valid
                }

                // Validate device name and polling place against the issued ballot
                if (!string.Equals(tx.DeviceName, issuedBallotTx.DeviceName, StringComparison.OrdinalIgnoreCase) ||
                    tx.PollingPlaceId != issuedBallotTx.PollingPlaceId)
                {
                    return new LocalResponse
                    {
                        IsSuccess = false,
                        LocalException = new Exception("Vote Edit after ballot issue and Device Names OR Polling Places don't match.")
                    };
                }

                // Transaction is valid
                return new LocalResponse { IsSuccess = true };
            }
            catch (Exception e)
            {
                // Handle unexpected exceptions gracefully
                return new LocalResponse { IsSuccess = false, LocalException = e };
            }
        }
    }
}
