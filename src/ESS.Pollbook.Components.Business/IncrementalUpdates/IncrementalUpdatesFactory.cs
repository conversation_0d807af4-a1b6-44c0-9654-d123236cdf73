using ESS.Pollbook.Components.Business.IncrementalUpdates.CanProcessTransactionRules;
using ESS.Pollbook.Components.Business.IncrementalUpdates.CanProcessTransactionRules.Rules;
using ESS.Pollbook.Components.Repository.Device;
using ESS.Pollbook.Components.Repository.IncrementalUpdates;
using ESS.Pollbook.Components.Repository.PollbookTransaction;
using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.CustomExceptions;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.EventsDto;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.Model;
using ESS.Pollbook.Core.Model.SyncPoint;
using ESS.Pollbook.Core.Model.Transactions;
using ESS.Pollbook.Core.StaticValues;
using ESS.Pollbook.Core.Utilities;
using ESS.Pollbook.Hardware.Storage;
using Newtonsoft.Json;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using ESS.Pollbook.Components.Repository;
using ESS.Pollbook.Components.Repository.VRSignatures;
using ESS.Pollbook.MasterProcessor.Abstractions;
using ESS.Pollbook.MasterProcessor.Abstractions.Shared;
using static ESS.Pollbook.Components.Repository.IncrementalUpdates.AccumulativeUpdateRepository;

namespace ESS.Pollbook.Components.Business.IncrementalUpdates
{
    public class IncrementalUpdatesFactory : IIncrementalUpdatesFactory
    {
        private readonly IPollbookTransactionRepository _pollbookTransactionRepository;
        private readonly IAccumulativeUpdateRepository _accumulativeUpdateRepository;
        private readonly IIncrementalUpdatesRepository _incrementalUpdatesRepository;
        private readonly IEssLogger _essLogger;
        private readonly IPollbookFailedTransactionRepository _pollbookFailedTransactionRepository;
        private readonly IDeviceRepository _deviceRepository;
        private readonly IVrSignaturesRepository _vrSignaturesRepository;
        private readonly IStorageLocator _storageLocator;
        private readonly IPollbookQueue _pollbookQueue;

        private List<PollbookTransactionDto> _transactions;
        private readonly Type _ruleType = typeof(ICanProcessTransactionRule);

        private Dictionary<string, BallotDto> _ballotsIssued =
            new Dictionary<string, BallotDto>();

        public Dictionary<string, BallotDto> BallotsIssued
        {
            get => _ballotsIssued;
            set => _ballotsIssued = value;
        }

        private ITransactionCalculator _voterAddCalculator;
        private ITransactionCalculator _voterEditCalculator;
        private readonly object _voterEditEventsLock = new object(); 
        private List<Homunculus> _voterEditEvents;
        private Stopwatch sw;

        public IncrementalUpdatesFactory(
            IPollbookTransactionRepository pollbookTransactionRepository,
            IAccumulativeUpdateRepository updateRepository,
            IIncrementalUpdatesRepository incrementalUpdatesRepository,
            IPollbookFailedTransactionRepository pollbookFailedTransactionRepository,
            IDeviceRepository deviceRepository,
            IVrSignaturesRepository vrSignaturesRepository,
            IStorageLocator storageLocator,
            IEssLogger essLogger,
            IPollbookQueue pollbookQueue)
        {
            _pollbookTransactionRepository = pollbookTransactionRepository;
            _accumulativeUpdateRepository = updateRepository;
            _incrementalUpdatesRepository = incrementalUpdatesRepository;
            _pollbookFailedTransactionRepository = pollbookFailedTransactionRepository;
            _deviceRepository = deviceRepository;
            _vrSignaturesRepository = vrSignaturesRepository;
            _storageLocator = storageLocator;
            _essLogger = essLogger;
            _pollbookQueue = pollbookQueue;

            InitializeTransactionCalculators();
        }

        private void InitializeTransactionCalculators()
        {
            var allRules = GetType().Assembly.GetTypes()
                .Where(p => _ruleType.IsAssignableFrom(p) && !p.IsInterface)
                .Select(r => Activator.CreateInstance(r) as ICanProcessTransactionRule).ToList();

            _voterAddCalculator = new TransactionCalculator(allRules, TransactionType.VoterAdd, ref _ballotsIssued);
            _voterEditCalculator = new TransactionCalculator(allRules, TransactionType.VoterEdit, ref _ballotsIssued);
        }

        public async Task<LocalResponse> CheckForIncrementalUpdateZipFileAsync()
        {
            _voterEditEvents = new List<Homunculus>(); // clear are cache of VoterEditEventDtos

            var localResponse = new LocalResponse();

            var incrUpdZipFileName =
                _storageLocator.GetIncrementalUpdateZipFileName(StorageLocator.INCREMENTAL_UPDATE_ZIP_FILE_PATTERN);

            if (string.IsNullOrEmpty(incrUpdZipFileName))
            {
                localResponse.IsSuccess = false;
                localResponse.LocalException = new Exception("No IncUpdZip file found.");
                return localResponse;
            }

            localResponse.IsSuccess = true;

            try
            {
                await _deviceRepository.ExtractAllFilesFromZipToFolderAsync(incrUpdZipFileName,
                    StorageLocator.DefaultDbLocation,
                    SystemDetails.PQCPassword);
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string>
                    { { "Action", "ExtractAllFilesFromZipToFolderAsync FAILED}" } };
                _essLogger.LogError(ex, logProps);

                localResponse.IsSuccess = false;
                localResponse.LocalException = ex;

                DeleteIncUpdDb3FilePatternFiles();
            }

            return localResponse;
        }

        private void DeleteIncUpdDb3FilePatternFiles()
        {
            try
            {
                var files = Directory.EnumerateFiles(Path.Combine(StorageLocator.DefaultDbLocation),
                    StorageLocator.INCREMENTAL_UPDATE_DB3_NAME_PATTERN);

                foreach (var file in files)
                {
                    File.Delete(file);
                    _essLogger.LogInformation(file,
                        new Dictionary<string, string>
                        {
                            { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name },
                            { "Message", $"Deleted file: {file}" }
                        });
                }
            }
            catch (Exception ex)
            {
                _essLogger.LogError(ex,
                    new Dictionary<string, string>
                    {
                        { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name },
                        { "Message", ex.Message }
                    });
            }
        }

        public string GetIncrementalUpdateFileName(string fileNamePattern, string path)
        {
            try
            {
                return _storageLocator.GetIncrementalUpdateFileName(fileNamePattern, path);
            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string> { { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } };
                _essLogger.LogError(ex, logProps);
            }

            return string.Empty;
        }

        public async Task<LocalResponse> ApplyIncrementalUpdatesUsbAsync()
        {
            sw = Stopwatch.StartNew();

            var localResponse = await CheckForIncrementalUpdateZipFileAsync();

            if (!localResponse.IsSuccess)
            {
                _essLogger.LogInformation($"No file found. [Elapsed time:{sw.ElapsedMilliseconds}ms]");
                return localResponse;
            }

            if (SystemConfiguration.ElectionConfiguration.ElectionDatabaseGuid !=
                await _incrementalUpdatesRepository.GetIncUpdDbGuidAsync(
                    GetIncrementalUpdateFileName(StorageLocator.INCREMENTAL_UPDATE_DB3_NAME_PATTERN,
                        StorageLocator.DefaultDbLocation)))
            {
                localResponse.IsSuccess = false;
                localResponse.LocalException =
                    new IncrementalUpdateGuidCheckException(
                        "Invalid database update.");
                _essLogger.LogInformation(localResponse.LocalException.Message);
                DeleteIncUpdDb3FilePatternFiles();
                return localResponse;
            }

            //If the username is set to the IncUpdateUser then user has not signed in and queues are not running so run inserts without the queue
            var bypassQueues = CurrentUserInfo.LoggedInUser.Username == StorageLocator.INCREMENTAL_UPDATE_USB_USERNAME;

            if (bypassQueues)
            {
                localResponse = await ApplyIncrementalUpdatesAsync();

                if (SystemConfiguration.ElectionConfiguration.PeerSync && localResponse.IsSuccess)
                    await _pollbookTransactionRepository.InitializeTransactionGuids();
            }
            else
            {
                localResponse = await ExtractAndQueueTransactionsFromUsb();
            }

            sw.Stop();

            return localResponse;
        }

        public async Task<LocalResponse> ApplyAutoIncrementalUpdatesAsync(string filename)
        {
            _voterEditEvents = new List<Homunculus>(); // clear are cache of VoterEditEventDtos
            sw = Stopwatch.StartNew();
            LocalResponse localResponse = new LocalResponse();
            try
            {
                await _deviceRepository.ExtractAllFilesFromZipToFolderAsync(filename, StorageLocator.DefaultDbLocation,
                    SystemDetails.PQCPassword, true);

                localResponse = await ApplyIncrementalUpdatesAsync();
            }
            catch (Exception ex)
            {
                _essLogger.LogError(ex,
                    new Dictionary<string, string>
                    {
                        { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name },
                        { "Message", string.Format($"ExtractAllFilesFromZipToFolderAsync FAILED : {ex.Message}") }
                    });

                localResponse.IsSuccess = false;
                localResponse.LocalException = ex;

                DeleteIncUpdDb3FilePatternFiles();
            }

            sw.Stop();
            return localResponse;
        }

        public async Task PrimeBallotsIssuedDictionary()
        {
            if (_pollbookTransactionRepository.VotersWithBallots == null)
                await _pollbookTransactionRepository.BuildVoterBallotList();

            _ballotsIssued = _pollbookTransactionRepository.VotersWithBallots;
        }

        public async Task<LocalResponse> ExtractAndQueueTransactionsFromUsb()
        {
            var localResponse = new LocalResponse();

            var inputFilePath = GetIncrementalUpdateFileName(StorageLocator.INCREMENTAL_UPDATE_DB3_NAME_PATTERN,
                StorageLocator.DefaultDbLocation);

            try
            {
                var transactions = await ExtractTransactions(inputFilePath);

                if (!transactions.Any())
                {
                    localResponse.IsSuccess = true;
                    return localResponse;
                }

                // Enqueue transactions
                _pollbookQueue.Transactions.Enqueue(new PollbookQueueItem
                {
                    QueueParams = new Dictionary<string, object>(1) { ["incrementalUpdateTransactions"] = transactions },
                    ItemQueueProcess = QueueProcess.OfflineIncrementalUpdateTransaction,
                    ItemQueueType = QueueType.Transaction,
                    ItemQueuePriority = QueuePriority.Standard
                });

                _pollbookQueue.Polldata.Enqueue(new PollbookQueueItem
                {
                    QueueParams = new Dictionary<string, object>(1) { ["incrementalUpdatePolldataTransactions"] = transactions },
                    ItemQueueProcess = QueueProcess.OfflineIncrementalUpdatePolldata,
                    ItemQueueType = QueueType.Polldata,
                    ItemQueuePriority = QueuePriority.Standard
                });

                localResponse.IsSuccess = true;

                return localResponse;
            }
            catch (Exception ex)
            {
                localResponse.IsSuccess = false;
                localResponse.LocalException = ex;

                _essLogger.LogError(ex, new Dictionary<string, string>
                {
                    ["Action"] = $"IncrementalUpdatesFactory.{nameof(ExtractAndQueueTransactionsFromUsb)}"
                });

                return localResponse;
            }
        }

        private async Task<LocalResponse> ApplyIncrementalUpdatesAsync()
        {
            var localResponse = new LocalResponse();

            var successfulTransactionsGuids = new List<FailedTransactionDto>();
            var failedTransactionGuids = new List<FailedTransactionDto>();

            var inputFilePath = GetIncrementalUpdateFileName(StorageLocator.INCREMENTAL_UPDATE_DB3_NAME_PATTERN,
                StorageLocator.DefaultDbLocation);

            try
            {
                var transactions = await ExtractTransactions(inputFilePath);

                if (transactions.Any())
                {
                    var bulkUpsertResponse = await Task.Run(() => _pollbookTransactionRepository.BulkUpsertHostTransactions(transactions));

                    foreach (var tx in transactions)
                    {
                        if (!bulkUpsertResponse.IsSuccess)
                            localResponse = _pollbookTransactionRepository.InsertTransaction(tx);

                        if (SystemConfiguration.ElectionConfiguration.PeerSync && Helpers.IsPeerSharableTransaction(tx))
                        {
                            // Parse string to enum before adding to cache
                            if (Enum.TryParse<TransactionType>(tx.TransactionType, out var transactionType))
                            {
                                _pollbookTransactionRepository.AddTransactionGuidToSet(transactionType, tx.TransactionGuid);
                            }
                            else
                            {
                                _essLogger.LogWarning($"Unknown transaction type '{tx.TransactionType}' for GUID '{tx.TransactionGuid}' during incremental update");
                            }
                        }

                        switch (tx.TransactionType)
                        {
                            case nameof(TransactionType.VoterAdd):
                            case nameof(TransactionType.VoterEdit):
                            case nameof(TransactionType.VoterStatusChange):
                                ProcessVoterIncUpdTransaction(tx, successfulTransactionsGuids,
                                    failedTransactionGuids);
                                break;
                            case nameof(TransactionType.BallotIssue):
                                if (tx.PollingPlaceId != null)

                                    _ballotsIssued[tx.SourceKey] = new BallotDto()
                                    {
                                        DeviceName = tx.DeviceName,
                                        PollingPlaceId = tx.PollingPlaceId.Value,
                                        SourceKey = tx.SourceKey
                                    };
                                break;
                            case nameof(TransactionType.BallotCancel):
                                _ballotsIssued.Remove(tx.SourceKey);
                                break;
                        }
                    }

                    try
                    {
                        _accumulativeUpdateRepository.HousekeepingIndicesDelete();

                        await BulkSaveVoterData(true).ConfigureAwait(true); // Process status events
                        await BulkSaveVoterData(false).ConfigureAwait(true); // Process voter events

                        UndeleteVoters();
                    }
                    finally
                    {
                        _accumulativeUpdateRepository.HousekeepingIndicesApply();
                    }
                }
                _incrementalUpdatesRepository.UpsertIncUpdSignatures(inputFilePath);

                await UpdateElectionSettingsIniFile();

                _essLogger.LogDebug(
                    $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name} completed. [Elapsed time:{sw.ElapsedMilliseconds}ms  Total Records:{_voterEditEvents.Count}]");

                localResponse.IsSuccess = true;
            }
            catch (OperationCanceledException operationCanceledEx)
            {
                localResponse.IsSuccess = false;
                localResponse.LocalException = operationCanceledEx;
                _essLogger.LogDebug($"Exception encountered. [Elapsed time:{sw.ElapsedMilliseconds}ms]");
                var logProps = new Dictionary<string, string>
                    { { "Action", "ApplyIncrementalUpdatesUsb-OperationCanceledException" } };
                _essLogger.LogError(operationCanceledEx, logProps);
            }
            catch (EssInvalidPQCException invalidPqcException)
            {
                localResponse.IsSuccess = false;
                localResponse.LocalException = invalidPqcException;
                _essLogger.LogDebug($"Exception encountered. [Elapsed time:{sw.ElapsedMilliseconds}ms]");
                var logProps = new Dictionary<string, string>
                    { { "Action", "ApplyIncrementalUpdatesUsb-invalidPqcException" } };
                _essLogger.LogError(invalidPqcException, logProps);
            }
            catch (Exception ex)
            {
                localResponse.IsSuccess = false;
                localResponse.LocalException = ex;
                _essLogger.LogDebug($"Exception encountered. [Elapsed time:{sw.ElapsedMilliseconds}ms]");
                var logProps = new Dictionary<string, string> { { "Action", "ApplyIncrementalUpdatesUsb-Exception" } };
                _essLogger.LogError(ex, logProps);
            }

            if (File.Exists(inputFilePath))
                File.Delete(inputFilePath);

            return localResponse;
        }

        public async Task UpdateElectionSettingsIniFile()
        {
            var settingsPath = _storageLocator.GetElectionSettingsFilePath();
            var backupPath   = _storageLocator.GetElectionSettingsBackupFilePath();
            var tempFilePath = Path.Combine("C:\\StorageData", "ElectionSettings-Temp.ini");

            try
            {
                var lastUpdateUtc = await GetLastUpdatedDateTimeAsync().ConfigureAwait(false);
                var settings = TryReadSettings(settingsPath) ??
                               (RestoreFromBackup(settingsPath, backupPath) ? TryReadSettings(settingsPath) : null);
                
                if (settings == null)
                {
                    _essLogger.LogWarning("Election settings could not be loaded, and backup restoration failed.");
                    return;
                }
                
                var newLastSync = lastUpdateUtc.HasValue
                    ? DateTimeHelper.GetDisplayDateTime(lastUpdateUtc.Value)
                    : settings.LastSync;
                
                if (string.Equals(newLastSync, settings.LastSync, StringComparison.Ordinal))
                    return;
                
                if (!File.Exists(backupPath))
                    File.Copy(settingsPath, backupPath, overwrite: false);

                settings.LastSync = newLastSync;
                WriteSettingsToTempFileAndReplace(settings, tempFilePath, settingsPath, backupPath);
            }
            catch (Exception e)
            {
                var logProps = new Dictionary<string, string>
                {
                   { "Action", "Error while updating election settings." }
                };
                _essLogger.LogError(e, logProps);
            }
            finally
            {
                // Clean up temporary files
                CleanUpTempFile(tempFilePath);
            }
        }
        
        private ElectionSettingsModel TryReadSettings(string path)
        {
            if (!File.Exists(path))
                return null;

            try
            {
                var json = File.ReadAllText(path);         // settings file is tiny – sync read is fine
                return string.IsNullOrWhiteSpace(json)
                    ? null
                    : JsonConvert.DeserializeObject<ElectionSettingsModel>(json);
            }
            catch (Exception ex)
            {
                _essLogger.LogError(ex, new Dictionary<string, string>(1) { { "Action", "Read election settings" } });
                return null;
            }
        }

        private bool RestoreFromBackup(string original, string backup)
        {
            if (!File.Exists(backup))
                return false;

            try
            {
                File.Copy(backup, original, overwrite: true);
                _essLogger.LogInformation("Election settings restored from backup.");
                return true;
            }
            catch (Exception ex)
            {
                _essLogger.LogError(ex, new Dictionary<string, string>(1) { { "Action", "Restore election settings" } });
                return false;
            }
        }

        private void WriteSettingsToTempFileAndReplace(
           ElectionSettingsModel electionSettings,
           string tempFilePath,
           string originalFilePath,
           string backupFilePath)
        {
            if (electionSettings == null || string.IsNullOrWhiteSpace(tempFilePath))
                return;
            
            try
            {
                var json = JsonConvert.SerializeObject(electionSettings, Formatting.None);
                if (json.Length == 0)      // nothing to write
                {
                    _essLogger.LogError("Election settings serialization produced empty JSON.");
                    return;
                }
                
                // Write new settings to the temporary file
                File.WriteAllBytes(tempFilePath, System.Text.Encoding.UTF8.GetBytes(json));

                File.Replace(tempFilePath, originalFilePath, backupFilePath);
                _essLogger.LogInformation("Election settings file has been updated successfully.");
            }
            catch (Exception ex)
            {
                _essLogger.LogError(ex,
                   new Dictionary<string, string> { { "Action", "Failed to write and replace election settings file." } });
            }
        }

        private void CleanUpTempFile(string filePath)
        {
            if (string.IsNullOrWhiteSpace(filePath))
                return; 
            
            try
            {
                var fi = new FileInfo(filePath);
                if (!fi.Exists)                 // single I/O operation
                    return;

                fi.Delete();                    // File.Delete does not re-check existence
                _essLogger.LogInformation($"Temporary file deleted: {fi.FullName}");
            }
            catch (IOException ioEx)            // restrict to common I/O problems
            {
                _essLogger.LogError(ioEx, new Dictionary<string, string> { { "Action", "Failed to clean up temporary file." } });
            }
            catch (UnauthorizedAccessException unAuthEx)
            {
                _essLogger.LogError(unAuthEx, new Dictionary<string, string> { { "Action", "Failed to clean up temporary file." } });
            }
        }


        private void UndeleteVoters()
        {
            try
            {
                if (_ballotsIssued.Count == 0)
                    return;

                List<Homunculus> snapshot;
                lock (_voterEditEventsLock)
                {
                    if (_voterEditEvents.Count == 0)
                        return;

                    snapshot = _voterEditEvents.ToList();
                }
                
                var undeleteIds = new HashSet<long>();
                
                // Single-pass iteration with optimized filtering
                for (int i = 0; i < snapshot.Count; i++)
                {
                    var voter = snapshot[i];
                    
                    if (voter?.StatusEventDto == null)
                        continue;
                
                    var eventDto = voter.StatusEventDto;
            
                    // Check deleted flag and voter ID existence
                    if (eventDto?.VoterDeleted != true || !eventDto.VoterId.HasValue)
                        continue;
                
                    // Dictionary lookup last (most expensive operation)
                    if (_ballotsIssued.ContainsKey(voter.SourceKey))
                    {
                        undeleteIds.Add(eventDto.VoterId.Value);
                    }
                }
                
                // Only call repository if we have IDs to process
                if (undeleteIds.Count > 0)
                {
                    _incrementalUpdatesRepository.UnDeleteVotersWithBallots(undeleteIds);
                }

            }
            catch (Exception ex)
            {
                // Log errors with additional properties for debugging purposes
                var logProps = new Dictionary<string, string> { { "Action", nameof(UndeleteVoters) } };
                _essLogger.LogError(ex, logProps);
            }
        }

        private async Task<bool> BulkSaveVoterData(bool processStatusEvents = false)
        {
            List<Homunculus> events;
            lock (_voterEditEventsLock)                     
            {
                events = processStatusEvents
                    ? _voterEditEvents.Where(o => o.StatusEventDto != null).ToList()
                    : _voterEditEvents.Where(o => o.EventDto   != null).ToList();
            }
            
            if (events.Count == 0)
                return false;

            var sw = Stopwatch.StartNew();
            bool hasFailures = false;

            try
            {
                return await Task.Run(async () =>
                {
                    var failedTransactionGuids = new List<FailedTransactionDto>();

                    var remainingFailures =
                        _accumulativeUpdateRepository.BulkSaveVoterInfo(events, 10000, processStatusEvents);
                    int[] batchSizes = { 5000, 1000, 10, 1 };

                    foreach (var batchSize in batchSizes)
                    {
                        if (remainingFailures.Count == 0)
                            break;

                        remainingFailures =
                            _accumulativeUpdateRepository.BulkSaveVoterInfo(remainingFailures, batchSize,
                                processStatusEvents);
                    }

                    hasFailures = remainingFailures.Count > 0;
                    
                    // Extract transaction GUIDs once for both success and failure scenarios
                    var successGuids = new List<string>();
                    var signatureEvents = new List<Homunculus>();
                    
                    if (processStatusEvents)
                    {
                        // Pre-filter and extract GUIDs for status events
                        foreach (var evt in events)
                        {
                            if (!string.IsNullOrEmpty(evt.StatusEventDto?.TransactionIdentifier))
                            {
                                successGuids.Add(evt.StatusEventDto.TransactionIdentifier);
                            }
                        }
                    }
                    else
                    {
                        // Pre-filter and extract GUIDs for regular events, including signature filtering
                        foreach (var evt in events)
                        {
                            if (!string.IsNullOrEmpty(evt.EventDto?.TransactionIdentifier))
                            {
                                successGuids.Add(evt.EventDto.TransactionIdentifier);
                    
                                // Collect signature events during the same loop
                                if (!string.IsNullOrEmpty(evt.EventDto.SignatureImageFileName))
                                {
                                    signatureEvents.Add(evt);
                                }
                            }
                        }
                    }
                    
                    if (hasFailures)
                    {
                        var failedGuids = new List<string>(remainingFailures.Count);

                        foreach (var err in remainingFailures)
                        {
                            string transactionGuid = processStatusEvents
                                ? err.StatusEventDto?.TransactionIdentifier
                                : err.EventDto?.TransactionIdentifier;

                            if (!string.IsNullOrEmpty(transactionGuid))
                            {
                                failedTransactionGuids.Add(new FailedTransactionDto
                                {
                                    TransactionGuid = transactionGuid,
                                    ErrorMessage = "INC UPD: failed to upsert"
                                });
                                failedGuids.Add(transactionGuid);
                            }
                        }

                        if (failedTransactionGuids.Count > 0)
                        {
                            // Process failure operations in parallel
                            await Task.WhenAll(
                                Task.Run(() => _pollbookFailedTransactionRepository.BulkInsertFailedTransactionGUIDs(failedTransactionGuids)),
                                Task.Run(() => _pollbookTransactionRepository.BulkUpdateStatusForTransactionsByGuid(failedGuids, ProcessingStatus.Error))
                            ).ConfigureAwait(false);
                        }
                    }
                    
                    // Process signatures
                    if (!processStatusEvents && signatureEvents.Count > 0)
                    {
                        // Pre-allocate signatures list with known capacity
                        var signatures = new List<List<object>>(signatureEvents.Count);
                        for (var index = 0; index < signatureEvents.Count; index++)
                        {
                            var item = signatureEvents[index];
                            signatures.Add(Converters.ConvertVoterSignatureList(item.EventDto, item.SourceKey));
                        }

                        // Process signature operations
                        await Task.Run(() => _vrSignaturesRepository.BulkUpsertVRSignatures(signatures))
                            .ConfigureAwait(false);
                    }
                    
                    // Enqueue transaction status update
                    _pollbookQueue.Transactions.Enqueue(new PollbookQueueItem
                    {
                        QueueParams = new Dictionary<string, object>(2) { ["guids"] = successGuids, ["status"] = ProcessingStatus.Completed },
                        ItemQueueProcess = QueueProcess.UpdateTransactionStatus,
                        ItemQueueType = QueueType.Transaction,
                        ItemQueuePriority = QueuePriority.Low
                    });
                    
                    return hasFailures;
                }).ConfigureAwait(false);
            }
            finally
            {
                sw.Stop();
                _essLogger.LogDebug(
                    $"{GetType().Name}.{nameof(BulkSaveVoterData)} -- elapsed time: {sw.ElapsedMilliseconds}ms -- " +
                    $"# of records: {events.Count} -- Type: {(processStatusEvents ? "Status" : "Voter")}");
            }
        }

        private async Task<List<PollbookTransactionDto>> ExtractTransactions(string inputFilePath)
        {
            if (string.IsNullOrEmpty(inputFilePath))
                throw new Exception("Issue with getting Incremental Update File.");

            await PrimeBallotsIssuedDictionary();

            var transactions = (await _incrementalUpdatesRepository.GetIncrementalUpdateDataUsbAsync(inputFilePath))
                .ToList();

            if (!transactions.Any())
            {
                return new List<PollbookTransactionDto>();
            }

            transactions.Sort((a, b) => a.HostTransactionId.CompareTo(b.HostTransactionId));
            
            foreach (var hostTx in transactions)
            {
                var txModel = JsonConvert.DeserializeObject<EssTransactionModel>(hostTx.JSON);
                hostTx.JSON = txModel?.TransactionJson;
            }

            return transactions;
        }

        private void ProcessVoterIncUpdTransaction(PollbookTransactionDto tx,
            ICollection<FailedTransactionDto> successfulTransactions,
            ICollection<FailedTransactionDto> failedTransactionGuids)
        {
            var response = _voterAddCalculator.CanProcessTransaction(tx);
            if (!response.IsSuccess)
            {
                UpdateTxGuidList(tx, successfulTransactions, failedTransactionGuids, response);
                return;
            }
            
            if (!Enum.TryParse(tx.TransactionType, out TransactionType txType))
            {
                response = new LocalResponse
                {
                    IsSuccess      = false,
                    LocalException = new ArgumentException(
                        $"Unknown transaction type: {tx.TransactionType}")
                };
                UpdateTxGuidList(tx, successfulTransactions, failedTransactionGuids, response);
                return;
            }

            try
            {
                
                var homunculus = new Homunculus
                {
                    DeviceName         = tx.DeviceName,
                    SourceKey          = tx.SourceKey,
                    TransactionDateTime= tx.TransactionDate,
                    HostTransactionId  = tx.HostTransactionId
                };
                
                switch (txType)
                {
                    case TransactionType.VoterStatusChange:
                        homunculus.StatusEventDto =
                            JsonConvert.DeserializeObject<VoterStatusEventDto>(tx.JSON)
                            ?? throw new InvalidOperationException("Failed to deserialize VoterStatusEventDto.");

                        homunculus.StatusEventDto.TransactionIdentifier = tx.TransactionGuid;
                        break;

                    default:    // VoterAdd / VoterEdit
                        homunculus.EventDto =
                            JsonConvert.DeserializeObject<VoterEditEventDto>(tx.JSON)
                            ?? throw new InvalidOperationException("Failed to deserialize VoterEditEventDto.");

                        homunculus.EventDto.TransactionIdentifier = tx.TransactionGuid;
                        break;
                }
                
                
                lock (_voterEditEventsLock)
                {
                    _voterEditEvents.Add(homunculus);
                }

                response.IsSuccess = true;
            }
            catch (Exception ex)
            {
                _essLogger.LogError(ex, new Dictionary<string, string>(1)
                {
                    { "Action", $"{GetType().Name}.{nameof(ProcessVoterIncUpdTransaction)}" }
                });
                response = new LocalResponse { IsSuccess = false, LocalException = ex };
            }

            UpdateTxGuidList(tx, successfulTransactions, failedTransactionGuids, response);
        }

        private static void UpdateTxGuidList(
           PollbookTransactionDto tx,
           ICollection<FailedTransactionDto> successfulTransactions,
           ICollection<FailedTransactionDto> failedTransactionGuids,
           LocalResponse localResponse)
        {
            // Validate inputs to avoid potential null reference issues
            if (tx == null || localResponse == null)
            {
                return; // Gracefully exit if inputs are invalid
            }

            // Determine the correct collection and create the FailedTransactionDto object only once
            var targetCollection = localResponse.IsSuccess ? successfulTransactions : failedTransactionGuids;
            var errorMessage = localResponse.IsSuccess ? string.Empty : localResponse.LocalException?.Message ?? "Unknown error";

            targetCollection.Add(new FailedTransactionDto
            {
                TransactionGuid = tx.TransactionGuid,
                ErrorMessage = errorMessage
            });
        }

        public async Task<DateTime?> GetLastUpdatedDateTimeAsync()
        {
            return await _incrementalUpdatesRepository.GetLastUpdateDateTimeAsync();
        }

        public async Task GetVoterTransactionsForProcessing()
        {
            var transactions = (await _pollbookTransactionRepository.GetVoterTransactionsForProcessing()).ToList();
            await ApplyIncrementalUpdatesAsync(transactions);
        }

        public async Task<LocalResponse> ApplyIncrementalUpdatesAsync(List<PollbookTransactionDto> transactions = null, bool offline = false)
        {
            _voterEditEvents = new List<Homunculus>();

            if (transactions == null && _transactions != null)
                transactions = _transactions;

            if (transactions == null || transactions.Count == 0)
                return new LocalResponse { IsSuccess = true };
            
            var sw = Stopwatch.StartNew();
            var transactionCount = transactions.Count;
            var failedGuids = new List<FailedTransactionDto>(capacity: transactionCount);
            var successfulGuids = new List<FailedTransactionDto>(capacity: transactionCount);
            
            try
            {
                await PrimeBallotsIssuedDictionary().ConfigureAwait(false);
                
                var voterTransactions = new List<PollbookTransactionDto>();
                var ballotIssueTransactions = new List<PollbookTransactionDto>();
                var ballotCancelTransactions = new List<PollbookTransactionDto>();
                
                foreach (var tx in transactions)
                {
                    switch (tx.TransactionType)
                    {
                        case nameof(TransactionType.VoterAdd):
                        case nameof(TransactionType.VoterEdit):
                        case nameof(TransactionType.VoterStatusChange):
                            voterTransactions.Add(tx);
                            break;

                        case nameof(TransactionType.BallotIssue):
                            if (tx.PollingPlaceId.HasValue)
                                ballotIssueTransactions.Add(tx);
                            break;

                        case nameof(TransactionType.BallotCancel):
                            ballotCancelTransactions.Add(tx);
                            break;
                    }
                }
                
                // Process voter transactions
                foreach (var tx in voterTransactions)
                {
                    ProcessVoterIncUpdTransaction(tx, successfulGuids, failedGuids);
                }

                // Batch process ballot operations for better dictionary performance
                foreach (var tx in ballotIssueTransactions)
                {
                    _ballotsIssued[tx.SourceKey] = new BallotDto
                    {
                        DeviceName = tx.DeviceName,
                        PollingPlaceId = tx.PollingPlaceId.Value,
                        SourceKey = tx.SourceKey
                    };
                }

                // Batch remove ballot cancellations
                foreach (var tx in ballotCancelTransactions)
                {
                    _ballotsIssued.Remove(tx.SourceKey);
                }

                await BulkSaveVoterData(processStatusEvents: true).ConfigureAwait(false);
                await BulkSaveVoterData(processStatusEvents: false).ConfigureAwait(false);
                UndeleteVoters();

                if (failedGuids.Count > 0)
                {
                    var failedTransactionGuids = failedGuids.Select(f => f.TransactionGuid).ToList();
                    // Run both operations in parallel
                    var insertTask = Task.Run(() => 
                        _pollbookFailedTransactionRepository.BulkInsertFailedTransactionGUIDs(failedGuids, true));
    
                    var updateTask = Task.Run(() => 
                        _pollbookTransactionRepository.BulkUpdateStatusForTransactionsByGuid(
                            failedTransactionGuids,
                            ProcessingStatus.Error));
    
                    // Wait for both operations to complete
                    await Task.WhenAll(insertTask, updateTask);

                }

                if (offline)
                {
                    var inputFilePath = GetIncrementalUpdateFileName(StorageLocator.INCREMENTAL_UPDATE_DB3_NAME_PATTERN,
                                        StorageLocator.DefaultDbLocation);

                    try
                    {
                        _incrementalUpdatesRepository.UpsertIncUpdSignatures(inputFilePath);
                        await UpdateElectionSettingsIniFile();
                    }
                    catch (Exception ex)
                    {
                        _essLogger.LogError(ex, new Dictionary<string, string>
                        {
                            { "Action", $"{GetType().Name}.{nameof(ApplyIncrementalUpdatesAsync)}" }
                        });
                        
                        return new LocalResponse { IsSuccess = false, LocalException = ex };
                    }
                }
            }
            catch (Exception e)
            {
                _essLogger.LogError(e, new Dictionary<string, string>
                {
                    { "Action", $"{GetType().Name}.{nameof(ApplyIncrementalUpdatesAsync)}" },
                    { "TransactionCount", transactions.Count.ToString() },
                    { "ElapsedTime", sw.ElapsedMilliseconds.ToString() }
                });
        
                return new LocalResponse { IsSuccess = false, LocalException = e };
            }
            finally
            {
                // Log performance metrics
                _essLogger.LogDebug(
                    $"{GetType().Name}.{nameof(ApplyIncrementalUpdatesAsync)} completed. [Elapsed time:{sw.ElapsedMilliseconds}ms  Total Records:{_voterEditEvents.Count}]");
        
                // Clean up to avoid memory leaks
                _transactions = null;
            }
            
            return new LocalResponse { IsSuccess = true };
        }

        public List<PollbookTransactionDto> SetupTransactionsFromHostSync(List<TransactionModel> hostTransactionModels)
        {
            _essLogger.LogDebug("Entered BulkUpsertHostTransactions()");

            if (hostTransactionModels == null || hostTransactionModels.Count == 0)
                return null;

            _transactions = ConvertToHostTransactionDtos(hostTransactionModels);

            _transactions.Sort((a, b) => a.HostTransactionId.CompareTo(b.HostTransactionId));

            return _transactions;
        }

        private List<PollbookTransactionDto> ConvertToHostTransactionDtos(List<TransactionModel> hostTransactionModels)
        {
            const string RecordSourceIncremental = nameof(ProcessingRecordSource.Incremental);
            var hostTransactionDtos = new ConcurrentBag<PollbookTransactionDto>();
            var failedTransactions = new ConcurrentBag<FailedTransactionDto>();
            
            var parallelOptions = new ParallelOptions { 
                MaxDegreeOfParallelism = Math.Min(Environment.ProcessorCount, 8) 
            };

            Parallel.ForEach(Partitioner.Create(0, hostTransactionModels.Count), parallelOptions, range =>
            {
                for (int i = range.Item1; i < range.Item2; i++)
                {
                    var item = hostTransactionModels[i];
                    try
                    {
                        var transactionModel = JsonConvert.DeserializeObject<EssTransactionModel>(item.TransactionJson);
                        var processingStatus = CalculateStatusFromTransactionType(item.TransactionType).ToString();
                        var parentTransactionGuid = item.ParentTransactionId > 0
                            ? item.ParentTransactionId.ToString()
                            : string.Empty;

                        hostTransactionDtos.Add(new PollbookTransactionDto
                        {
                            SessionGuid = item.SessionIdentifier,
                            TransactionGuid = item.TransactionIdentifier,
                            ParentTransactionGuid = parentTransactionGuid,
                            SystemIdentifier = item.DeviceSystemIdentifier,
                            SourceKey = item.VoterSourceKey,
                            PollingPlaceId = item.PollingPlaceId,
                            TransactionType = item.TransactionType.ToString(),
                            JSON = transactionModel?.TransactionJson,
                            Signature = transactionModel?.SignatureImage,
                            TransactionDate = item.DeviceCreatedOn,
                            HostTransactionId = item.TransactionId,
                            UploadQueueRetryAttempts = 0,
                            DeviceName = item.DeviceDeviceName,
                            SerialNumber = item.DeviceSerialNumberValue,
                            ProcessingStatus = processingStatus,
                            RecordSource = RecordSourceIncremental
                        });
                    }
                    catch (Exception ex)
                    {
                        // Minimize dictionary creation in the hot path
                        _essLogger.LogError(ex, new Dictionary<string, string>(1)
                        {
                            { "Action", $"{GetType().Name}.ConvertToHostTransactionDtos" }
                        });

                        failedTransactions.Add(new FailedTransactionDto
                        {
                            TransactionGuid = item.TransactionIdentifier,
                            ErrorMessage = ex.Message
                        });
                    }
                }
            });

            // Insert all failed transactions in bulk at the end for efficiency
            if (!failedTransactions.IsEmpty)
            {
                _pollbookFailedTransactionRepository.BulkInsertFailedTransactionGUIDs(failedTransactions.ToList(), true);
            }

            return hostTransactionDtos.ToList();
        }

        private static ProcessingStatus CalculateStatusFromTransactionType(TransactionType tType)
        {
            switch (tType)
            {
                case TransactionType.VoterAdd:
                case TransactionType.VoterEdit:
                    return ProcessingStatus.Processing;

                case TransactionType.Unknown:
                case TransactionType.BallotCancel:
                case TransactionType.BallotIssue:
                case TransactionType.Heartbeat:
                case TransactionType.IncrementalUpdate:
                case TransactionType.PollsClose:
                case TransactionType.PollsOpen:
                case TransactionType.SoftwareError:
                case TransactionType.SoftwareExit:
                case TransactionType.SoftwareInitialization:
                case TransactionType.SoftwareSignIn:
                case TransactionType.SoftwareSignOut:
                case TransactionType.VoterStatusChange:
                case TransactionType.Custom:
                case TransactionType.TestMode:
                default:
                    return ProcessingStatus.Completed;
            }
        }

        public bool ProcessHostTransactions(List<PollbookTransactionDto> hostTransactions)
        {
            if (hostTransactions == null || hostTransactions.Count == 0)
                return true;

            var returnResult = new LocalResponse { IsSuccess = false };
            List<FailedTransactionDto> failedTransactions = null;

            try
            {
                // Try bulk upsert first - most optimal path
                returnResult = _pollbookTransactionRepository.BulkUpsertHostTransactions(hostTransactions);

                if (!returnResult.IsSuccess)
                {
                    failedTransactions = new List<FailedTransactionDto>();

                    // Process individual transactions with pre-allocated capacity
                    ProcessIndividualTransactions(hostTransactions, failedTransactions);
                    returnResult.IsSuccess = failedTransactions.Count < hostTransactions.Count;
                }

                // Handle peer sync if enabled - batch operation
                if (SystemConfiguration.ElectionConfiguration.PeerSync)
                {
                    ProcessPeerSyncBatch(hostTransactions);
                }

                // Handle failed transactions if any exist
                if (failedTransactions?.Count > 0)
                {
                    _ = ProcessFailedTransactionsBatch(failedTransactions);
                }

            }
            catch (Exception ex)
            {
                var logProps = new Dictionary<string, string>
               { { "Action", $"{GetType().Name}.{MethodBase.GetCurrentMethod()?.Name}" } };
                _essLogger.LogError(ex, logProps);
            }

            return returnResult.IsSuccess;
        }
        
        private void ProcessIndividualTransactions(List<PollbookTransactionDto> hostTransactions, List<FailedTransactionDto> failedTransactions)
        {
            var transactionCount = hostTransactions.Count;
    
            // Pre-allocate with estimated capacity
            if (failedTransactions.Capacity < transactionCount)
                failedTransactions.Capacity = transactionCount;

            for (int i = 0; i < transactionCount; i++)
            {
                var transaction = hostTransactions[i];
                var result = _pollbookTransactionRepository.InsertTransaction(transaction);
        
                if (!result.IsSuccess)
                {
                    failedTransactions.Add(new FailedTransactionDto
                    {
                        TransactionGuid = transaction.TransactionGuid,
                        ErrorMessage = result.LocalException?.Message ?? "Unknown error"
                    });
                }
            }
        }

        private void ProcessPeerSyncBatch(List<PollbookTransactionDto> hostTransactions)
        {
            // Batch peer sync operations to reduce individual calls
            var transactionsByType = GroupTransactionsByType(hostTransactions);
    
            foreach (var typeGroup in transactionsByType)
            {
                // Parse string to enum before adding to cache
                if (Enum.TryParse<TransactionType>(typeGroup.Key, out var transactionType))
                {
                    foreach (var transaction in typeGroup.Value)
                    {
                        _pollbookTransactionRepository.AddTransactionGuidToSet(
                            transactionType, 
                            transaction.TransactionGuid);
                    }
                }
                else
                {
                    // Log warning for unknown transaction type but continue processing
                    _essLogger.LogWarning($"Unknown transaction type '{typeGroup.Key}' encountered during peer sync batch processing");
                }
            }

        }

        private Dictionary<string, List<PollbookTransactionDto>> GroupTransactionsByType(List<PollbookTransactionDto> hostTransactions)
        {
            var groups = new Dictionary<string, List<PollbookTransactionDto>>();
    
            for (int i = 0; i < hostTransactions.Count; i++)
            {
                var transaction = hostTransactions[i];
                var transactionType = transaction.TransactionType;
        
                if (!groups.TryGetValue(transactionType, out var list))
                {
                    list = new List<PollbookTransactionDto>();
                    groups[transactionType] = list;
                }
                list.Add(transaction);
            }
    
            return groups;
        }
        
        private async Task ProcessFailedTransactionsBatch(List<FailedTransactionDto> failedTransactions)
        {
            // Extract GUIDs once for bulk operations
            var failedGuids = ExtractGuidsFromFailedTransactions(failedTransactions);

            var updateTask = Task.Run(() => 
                _pollbookTransactionRepository.BulkUpdateStatusForTransactionsByGuid(
                    failedGuids, 
                    ProcessingStatus.Error));

            var insertTask = Task.Run(() => 
                _pollbookFailedTransactionRepository.BulkInsertFailedTransactionGUIDs(
                    failedTransactions, 
                    true));

            // Wait for both operations to complete
            await Task.WhenAll(updateTask, insertTask);
        }


        private IEnumerable<string> ExtractGuidsFromFailedTransactions(List<FailedTransactionDto> failedTransactions)
        {
            var guids = new string[failedTransactions.Count];
            for (int i = 0; i < failedTransactions.Count; i++)
            {
                guids[i] = failedTransactions[i].TransactionGuid;
            }
            return guids;
        }
        
        public void ProcessHostParents(List<PollbookTransactionDto> hostTransactionsDtos)
        {
            if (hostTransactionsDtos.IsNullOrEmpty())
                return;

            try
            {
                var parentGuids = new HashSet<string>(StringComparer.Ordinal);
                var parentIds   = new HashSet<long>();

                foreach (var tx in hostTransactionsDtos)
                {
                    var guid = tx.ParentTransactionGuid;
                    if (!string.IsNullOrEmpty(guid) && long.TryParse(guid, out var id))
                    {
                        parentGuids.Add(guid);
                        parentIds.Add(id);
                    }
                }
                
                if (parentGuids.Count == 0)
                    return;

                _pollbookFailedTransactionRepository.BulkDeleteParentFailedTransaction(parentGuids);
                _pollbookTransactionRepository.BulkUpdateStatusForTransactionsById(parentIds,
                    ProcessingStatus.Completed);
            }
            catch (Exception e)
            {
                _essLogger.LogError(e, new Dictionary<string, string> {
                    { "Action", $"{GetType().Name}.{nameof(ProcessHostParents)}" },
                    { "Count", hostTransactionsDtos.Count.ToString() }
                });

            }
        }
    }
}