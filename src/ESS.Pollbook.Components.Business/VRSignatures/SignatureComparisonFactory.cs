using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.SignatureHandler;
using ESS.Pollbook.Hardware.Storage;
using ICSharpCode.SharpZipLib.Zip;
using System;
using System.Collections.Generic;
using System.IO;

namespace ESS.Pollbook.Components.Business.VRSignatures
{
    public class SignatureComparisonFactory : ISignatureComparisonFactory
    {
	    private readonly IEssLogger _essLogger;

	    public SignatureComparisonFactory(IEssLogger essLogger)
	    {
            _essLogger = essLogger;
        }

        public byte[] GetVRSignature(SignatureDto signatureDto)
        {
	        try
	        {
		        if ((string.IsNullOrWhiteSpace(signatureDto.VoterSignaturePollbookFileName)) ||
		            (string.IsNullOrWhiteSpace(signatureDto.VoterSignaturePollbookFilePath)))
			        return null;

		        if (!signatureDto.VoterSignaturePollbookFilePath.StartsWith(StorageLocator.DefaultDbLocation))
		        {
			        var fi = new FileInfo(signatureDto.VoterSignaturePollbookFilePath);
			        signatureDto.VoterSignaturePollbookFilePath =
				        Path.Combine(StorageLocator.DefaultDbLocation, fi.Name);
		        }

		        if (!File.Exists(signatureDto.VoterSignaturePollbookFilePath))
			        return null;

		        var zipFile = ZipStreamHandler.GetZipFile(signatureDto.VoterSignaturePollbookFilePath);

		        if (zipFile == null)
			        return null;

		        byte[] buffer = null;
		        using (var ms = new MemoryStream())
		        {
			        foreach (ZipEntry zipEntry in zipFile)
			        {
				        if (!zipEntry.IsFile)
					        continue; // Ignore directories

				        if (zipEntry.Name != signatureDto.VoterSignaturePollbookFileName)
					        continue;

				        buffer = new byte[zipEntry.Size];
				        using (var zipStream = zipFile.GetInputStream(zipEntry))
				        {
					        int read;
					        while ((read = zipStream.Read(buffer, 0, buffer.Length)) > 0)
					        {
						        ms.Write(buffer, 0, read);
					        }
				        }

				        break;
			        }
		        }

		        return buffer;
	        }
	        catch (Exception ex)
	        {
		        var logProps = new Dictionary<string, string> { { "Action", "Get Signature FilePath" } };
		        _essLogger.LogError(ex, logProps);
	        }

	        return null;
        }

    }

}
