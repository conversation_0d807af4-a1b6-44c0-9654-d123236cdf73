using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.Model;
using ESS.Pollbook.Core.Model.VoterEligibility;
using ESS.Pollbook.Core.StaticValues;
using System.Collections.Generic;

namespace ESS.Pollbook.Components.Business.VoterEligibility.PollStatus
{
	public class PollStatusCalculator : EligibilityCalculator, IPollStatusCalculator
	{
		#region Constants

		private const string ErrorMessage = "Polls Closed";

		private const int VoterEligibilityPriority = 1; // Priority of the voter status must be set.

		private readonly IEssLogger _logger;

		#endregion

		#region Properties

		public int SortPriority => VoterEligibilityPriority;
		bool _isActiveRule = true;

		#endregion

		public PollStatusCalculator(IEssLogger logger)
		{
			_logger = logger;
		}

		public CalculationResponse Calculate(CalculationRequest request,
			IEnumerable<PollbookTransactionDto> localTransactions)
		{
			_logger.LogDebug("Entered PollStatus Calculator");
			CalculationResponse response = new CalculationResponse()
			{
				CalculationType = nameof(CalculationTypeEnum.PollStatus),
				CalculationStatus = nameof(CalculationStatusEnum.Success)
			};

			if (LoggedInPollplaceInfo.IsPollOpen)
				return response;

			response.CalculationStatus = nameof(CalculationStatusEnum.Error);
			response.Message = ErrorMessage;
			response.DisplayMessage = ErrorMessage;

			return response;
		}

		public override bool CanRun()
		{
			return _isActiveRule;
		}
	}
}
