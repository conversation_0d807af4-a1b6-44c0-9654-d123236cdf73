using ESS.Pollbook.Components.Repository.PollbookDefinedText;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.Model;
using ESS.Pollbook.Core.Model.VoterEligibility;
using System.Collections.Generic;
using ESS.Pollbook.Core.StaticValues;

namespace ESS.Pollbook.Components.Business.VoterEligibility.VoterPollingPlaceCalculator
{
	public class VoterPollingPlaceCalculator : EligibilityCalculator, IVoterPollingPlaceCalculator
	{
		private readonly IEssLogger _logger;
		private readonly IPollbookDefinedTextRepository _definedText;
		bool _isActiveRule = true;
		private const int VoterEligibilityPriority = 2;

		public int SortPriority => VoterEligibilityPriority;

		public VoterPollingPlaceCalculator(IEssLogger logger, IPollbookDefinedTextRepository definedText)
		{
			_definedText = definedText;
			_logger = logger;
		}

		public CalculationResponse Calculate(CalculationRequest request,
			IEnumerable<PollbookTransactionDto> localTransactions)
		{
			_logger.LogDebug("Entered Wrong Poll Place Calculator");
			CalculationResponse response = new CalculationResponse
			{
				CalculationType = CalculationTypeEnum.PollingLocation.ToString(),
				CalculationStatus = CalculationStatusEnum.Success.ToString(),
				SortPriority = VoterEligibilityPriority
			};

			if (request?.pollPlaceDto != null && (
				    request.pollPlaceDto.PollingPlaceVoteCenterIndicator != 0 || request.voter.ValidPoll))
		        return response;

			response.CalculationStatus = nameof(CalculationStatusEnum.Error);

			_definedText.GetPollbookDefinedTextForLanguageWithDefault("WrongPollCommentText",
				_definedText.GetPollbookDefinedTextCurrentLanguage(), out var textMessage,
				UIText.WrongPollLocationComment);
			response.Message = textMessage;
			response.CommentText = textMessage;

			_definedText.GetPollbookDefinedTextForLanguageWithDefault("WrongPollStatusText",
				_definedText.GetPollbookDefinedTextCurrentLanguage(), out textMessage, UIText.WrongPollLocationStatus);
			response.DisplayMessage = textMessage;

			return response;
		}

        public override bool CanRun()
        {
	        return _isActiveRule;
        }
    }
}
