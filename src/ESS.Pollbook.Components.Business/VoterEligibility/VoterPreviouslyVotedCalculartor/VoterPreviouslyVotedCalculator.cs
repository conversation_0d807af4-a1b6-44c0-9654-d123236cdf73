using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.Model;
using ESS.Pollbook.Core.Model.VoterEligibility;
using ESS.Pollbook.Core.StaticValues;
using System;
using System.Collections.Generic;
using System.Linq;

namespace ESS.Pollbook.Components.Business.VoterEligibility.VoterPreviouslyVotedCalculartor
{
	public class VoterPreviouslyVotedCalculator : EligibilityCalculator, IVoterPreviouslyVotedCalculator
	{
        //NOTE and TODO:  The priority will later be configurable once the options and feature flags database has been implemented.
        private const int VOTER_ELIGIBILITY_PRIORITY = 3;//Priority must be set.

        private readonly IEssLogger _logger;

        public int SortPriority => VOTER_ELIGIBILITY_PRIORITY;
        bool _isActiveRule = true;

        public VoterPreviouslyVotedCalculator(IEssLogger logger)
        {
            _logger = logger;
        }

        public CalculationResponse Calculate(CalculationRequest request, IEnumerable<PollbookTransactionDto> localTransactions)
        {
            _logger.LogDebug($"Entered Voter Previously Voted Calculator");

            CalculationResponse calculationResponse = new CalculationResponse
            {
                CalculationType = CalculationTypeEnum.PreviouslyVoted.ToString(),
                CalculationStatus = CalculationStatusEnum.Error.ToString(),
                SortPriority = VOTER_ELIGIBILITY_PRIORITY,
                DisplayMessage = "Ballot Issued",
                IsPreviouslyVoted = true,
                IsProvisional = false,
                BallotDateTime = null

            };

            try
            {
	            // If not null, the system is connected to Host, thus we use the voter status coming from SyncPoint
	            if (VoterStatus != null)
                {
	                // If there are no ballot transactions SyncPoint api returns null. If there is a latest cancel transaction, then it returns '0'
	                if (VoterStatus.VoterBallotCredit == null || VoterStatus.VoterBallotCredit == 0)
	                {
		                calculationResponse.CalculationStatus = CalculationStatusEnum.Success.ToString();
		                calculationResponse.IsPreviouslyVoted = false;
		                calculationResponse.DisplayMessage = "";
	                }
	                else
	                {
		                calculationResponse.IsProvisional = VoterStatus.VoterBallotStatusProvisionalBallotIndicator;
		                if (VoterStatus.VoterBallotStatusEarlyVoteIndicator == true)
		                {
			                calculationResponse.DisplayMessage = SystemConfiguration.ElectionConfiguration
				                .VoterStatusEarlyBallotIssuedStatus;
			                calculationResponse.BallotDateTime = TimeZoneInfo.ConvertTimeFromUtc(
				                (DateTime)VoterStatus.BallotRecordUpdateApplicationDatetime, TimeZoneInfo.Local);
		                }
		                else
		                {
			                calculationResponse.DisplayMessage = SystemConfiguration.ElectionConfiguration
				                .VoterStatusBallotIssuedStatus;
			                calculationResponse.BallotDateTime = null; //return null on election Day
		                }
	                }
                    return calculationResponse;
                }

                //Otherwise, we need to rely on local transactions and voter information.
                var transactionDtos = localTransactions as IList<PollbookTransactionDto> ?? localTransactions.ToList();
                var transactionDtoList = transactionDtos.OrderByDescending(t => t.TransactionDate);
                var isPreviouslyVoted = false;

                if (transactionDtoList.Any())
                {
                    var latest = transactionDtoList.FirstOrDefault();
                    isPreviouslyVoted = latest.TransactionType == TransactionType.BallotIssue.ToString();
                    if (isPreviouslyVoted)
                    {
                        calculationResponse.IsProvisional = latest.IsProvisional;
                        if (latest.IsEarlyVote)
                        {
                            calculationResponse.DisplayMessage = SystemConfiguration.ElectionConfiguration.VoterStatusEarlyBallotIssuedStatus;
                            calculationResponse.BallotDateTime = TimeZoneInfo.ConvertTimeFromUtc(latest.TransactionDate, TimeZoneInfo.Local);
                        }
                        else
                        {
                            calculationResponse.DisplayMessage = SystemConfiguration.ElectionConfiguration.VoterStatusBallotIssuedStatus;
                            calculationResponse.BallotDateTime = null;
                        }
                    }
                }

                //if no local ballot transactions (or) if previous ballots are cancelled
                if (transactionDtos.Count == 0 || !isPreviouslyVoted)
                {
                    calculationResponse.CalculationStatus = CalculationStatusEnum.Success.ToString();
                    calculationResponse.IsPreviouslyVoted = isPreviouslyVoted;
                    calculationResponse.IsProvisional = false;
                    calculationResponse.DisplayMessage = "";
                    calculationResponse.BallotDateTime = null;
                }

                return calculationResponse;
            }
            catch (Exception ex)
            {
                Dictionary<string, string> logProps = new Dictionary<string, string>();
                logProps.Add("Action", "Calculating Previously Voted Status");
                if (request != null && request.voter != null)
                {
                    logProps.Add("request.voter.VoterKey", request.voter.VoterKey);
                }
                _logger.LogError(ex, logProps);

                return calculationResponse;
            }
        }

        public override bool CanRun()
        {
            return _isActiveRule;
        }
    }
}
