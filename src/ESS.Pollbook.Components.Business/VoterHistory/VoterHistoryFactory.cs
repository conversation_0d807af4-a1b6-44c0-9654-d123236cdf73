using ESS.Pollbook.Components.Business.ResourceTranslation;
using ESS.Pollbook.Components.Repository.PollbookTransaction;
using ESS.Pollbook.Components.Repository.PollPlace;
using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.StaticValues;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace ESS.Pollbook.Components.Business.VoterHistory
{
    public class VoterHistoryFactory : IVoterHistoryFactory
    {
        private readonly IPollPlaceRepository _pollPlaceRepository;
        private readonly IPollbookTransactionRepository _transactionsRepository;
        private readonly IResourceTranslationFactory _translationFactory;

        public VoterHistoryFactory(IResourceTranslationFactory translationFactory, IPollPlaceRepository pollPlaceRepository, IPollbookTransactionRepository transactionRepository)
        {
            _pollPlaceRepository = pollPlaceRepository;
            _transactionsRepository = transactionRepository;
            _translationFactory = translationFactory;
        }

        public async Task<IEnumerable<VoterHistoryDto>> GetVoterHistoryAsync(string voterKey)
        {
            var transactions = await _transactionsRepository.GetAllTransactionsForVoterAsync(voterKey);

            List<VoterHistoryDto> history = null;
            if (transactions != null && transactions.Any())
            {
                history = new List<VoterHistoryDto>();

                foreach (var item in transactions)
                {
                    VoterHistoryDto historydata = new VoterHistoryDto();

                    var data = JsonConvert.DeserializeObject<PollbookTransactionDto>(item.JSON);

                    historydata.TransactionId = item.TransactionId;
                    historydata.TransactionGuid = item.TransactionGuid;
                    historydata.VoterSourceKey = item.SourceKey;
                    historydata.DeviceId = item.DeviceName;
                    historydata.SerialNumber = item.SerialNumber;
                    historydata.ActivityTypeCode = item.TransactionType;
                    var timeZoneInfo = TimeZoneInfo.FindSystemTimeZoneById(SystemConfiguration.ElectionConfiguration.TimeZoneName);
                    historydata.TimeStamp = timeZoneInfo != null ? TimeZoneInfo.ConvertTimeFromUtc(item.TransactionDate, timeZoneInfo) : TimeZoneInfo.ConvertTimeFromUtc(item.TransactionDate, TimeZoneInfo.Local);
                    historydata.SystemIdentifier = item.SystemIdentifier;
                    historydata.Location = GetPollingPlaceDisplayName(item.PollingPlaceId);
                    historydata.BallotStyleId = Convert.ToInt32(SetPropertyFromJson(data, "BallotStyleId"));
                    historydata.PrecinctSplitBallotStyleId = Convert.ToInt32(SetPropertyFromJson(data, "PrecinctSplitBallotStyleId"));
                    historydata.PrecinctId = Convert.ToInt32(SetPropertyFromJson(data, "PrecinctId"));
                    historydata.Username = (string)SetPropertyFromJson(data, "Createdby");
                    historydata.MediaType = (string)SetPropertyFromJson(data, "mediatype");
                    historydata.IsProvisional = (bool)SetPropertyFromJson(data, "IsProvisional");
                    historydata.ProvisionalId = (string)SetPropertyFromJson(data, "ProvisionalBallotIdentifier");
                    historydata.IsReissue = (bool)(SetPropertyFromJson(data, "reissue"));
                    historydata.HasAddressChanged = (bool)SetPropertyFromJson(data, "addresschanged");
                    historydata.IsThisBallotCancelled = item.IsThisBallotCancelled;
                    historydata.Attributes = (Dictionary<string, string>)SetPropertyFromJson(data, "Attributes");
                    historydata.EmailAddress = data.EmailAddress;
                    historydata.FirstName = (string)SetPropertyFromJson(data, "FirstName");
                    historydata.MiddleName = (string)SetPropertyFromJson(data, "MiddleName");
                    historydata.MiddleInitial = (string)SetPropertyFromJson(data, "MiddleInitial");
                    historydata.LastName = (string)SetPropertyFromJson(data, "LastName");
                    historydata.NameSuffix = (string)SetPropertyFromJson(data, "NameSuffix");
                    historydata.HouseNumber = (string)SetPropertyFromJson(data, "HouseNumber");
                    historydata.HouseNumberFraction = (string)SetPropertyFromJson(data, "HouseNumberFraction");
                    historydata.StreetName = (string)SetPropertyFromJson(data, "StreetName");
                    historydata.UnitTypeEnumId = Convert.ToInt32(SetPropertyFromJson(data, "UnitTypeEnumId"));
                    historydata.UnitTypeName = (string)SetPropertyFromJson(data, "UnitTypeName");
                    historydata.UnitValue = (string)SetPropertyFromJson(data, "UnitValue");
                    historydata.City = (string)SetPropertyFromJson(data, "City");
                    historydata.Zip = (string)SetPropertyFromJson(data, "Zip");
                    historydata.PrecinctSplitId = (int?)SetPropertyFromJson(data, "PrecinctSplitId");
                    historydata.DynamicData = JsonConvert.DeserializeObject<DynamicDto>(item.JSON).DynamicData;
                    historydata.AbsenteeStatusEnumId = data.AbsenteeStatusEnumId;
                    historydata.IdentificationRequirementStatusEnumId = data.IdentificationRequirementStatusEnumId;
                    historydata.VoterStatusEnumId = data.VoterStatusEnumId;
                    historydata.Affidavits = data.Affidavits;

                    if (historydata.ActivityTypeCode == TransactionType.BallotIssue.ToString())
                    {
                        if (historydata.IsReissue)
                        {
                            historydata.ActivityTypeFriendlyDescription = data.IsEarlyVote ?
                                _translationFactory.GetTranslation(TranslationContext.VoterView_History, "earlyballotreissue") :
                                _translationFactory.GetTranslation(TranslationContext.VoterView_History, "reissuedballot");
                        }
                        else
                        {
                            historydata.ActivityTypeFriendlyDescription = data.IsEarlyVote ?
                                _translationFactory.GetTranslation(TranslationContext.VoterView_History, "earlyballotissue") :
                                _translationFactory.GetTranslation(TranslationContext.VoterView_History, historydata.ActivityTypeCode);
                        }
                    }
                    else
                    {
                        historydata.ActivityTypeFriendlyDescription = _translationFactory.GetTranslation(TranslationContext.VoterView_History, historydata.ActivityTypeCode);
                    }
                    history.Add(historydata);
                }
                history = history.ToList();
            }
            return history;
        }

        private string GetPollingPlaceDisplayName(int? pollingPlaceId)
        {
            var result = Task.Run(async () => await _pollPlaceRepository.GetPollPlaceNameByPollPlaceIDAsync(pollingPlaceId));
            return result.Result.PollingPlaceDisplayName;
        }

        /// <summary>
        /// Pass in a JSON object to read a property and hit it returned
        /// </summary>
        /// <param name="data">JSON Data/Object</param>
        /// <param name="jsonProperty">JSON Property</param>
        /// <returns></returns>
        private object SetPropertyFromJson(PollbookTransactionDto data, string jsonProperty)
        {
            object val = null;

            switch (jsonProperty.ToLower())
            {
                case "addresschanged":
                    if (data.Attributes != null && data.Attributes.ContainsKey("address_changed"))
                    {
                        val = data.Attributes["address_changed"] != "0";
                    }
                    else
                    {
                        val = false;
                    }
                    break;
                case "attributes":
                    val = data.Attributes;
                    break;
                case "city":
                    val = data.Address?.City;
                    break;
                case "firstname":
                    val = data.FirstName;
                    break;
                case "housenumber":
                    val = data.Address?.HouseNumber;
                    break;
                case "housenumberfraction":
                    val = data.Address?.HouseNumberFraction;
                    break;
                case "isprovisional":
                    val = data.IsProvisional;
                    break;
                case "lastname":
                    val = data.LastName;
                    break;
                case "mediatype":
                    var mediaType = (MediaType)Enum.ToObject(typeof(MediaType), data.MediaTypeEnumId);
                    val = mediaType.ToString();
                    break;
                case "middleinitial":
                    val = data.MiddleInitial;
                    break;
                case "middlename":
                    val = data.MiddleName;
                    break;
                case "namesuffix":
                    val = data.NameSuffix;
                    break;
                case "precinctsplitballotstyleid":
                    val = data.PrecinctSplitBallotStyleId;
                    break;
                case "precinctsplitid":
                    val = data.PrecinctSplitId;
                    break;
                case "provisionalballotidentifier":
                    val = data.ProvisionalBallotIdentifier;
                    break;
                case "reissue":
                    val = data.IsReissue;
                    break;
                case "streetname":
                    val = data.Address?.StreetName;
                    break;
                case "unittypeenumid":
                    val = data.Address?.UnitTypeEnumId;
                    break;
                case "unittypename":
                    val = data.Address?.UnitTypeName;
                    break;
                case "unitvalue":
                    val = data.Address?.UnitValue;
                    break;
                case "zip":
                    val = data.Address?.PostalCode;
                    break;
                case "ballotstyleid":
                    val = data.BallotStyleId;
                    break;
                case "precinctid":
                    val = data.PrecinctId;
                    break;
                case "createdby":
                    val = data.CreatedBy;
                    break;
            }
            return val;
        }
    }
}
