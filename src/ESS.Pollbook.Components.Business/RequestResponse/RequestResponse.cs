using ESS.ELLEGO.ServiceBus.Core.Interfaces;
using ESS.ELLEGO.ServiceBus.Core.Model;
using ESS.Pollbook.Components.Repository.PollbookTransaction;
using ESS.Pollbook.Core.Constants;
using ESS.Pollbook.Core.Logging;
using ESS.Pollbook.Core.Model;
using ESS.Pollbook.Core.StaticValues;
using NetMQ;
using NetMQ.Sockets;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;
using ESS.Pollbook.Core.Interface;
using ESS.Pollbook.Core.Utilities;

namespace ESS.Pollbook.Components.Business.RequestResponse
{
    public class RequestResponse : IRequestResponse, IDisposable
    {
        private string _passPhrase;
        private readonly IEssMessageFactory _messageFactory;
        private readonly IPollbookTransactionRepository _pollbookTransactionRepository;
        private readonly ITransactionGuidCache _transactionGuidCache;
        private readonly IEssLogger _logger;
        private ResponseSocket _responseSocket;
        private RequestSocket _requestSocket;
        private bool _disposed = false;
        private CancellationTokenSource _cancellationTokenSource;
        private readonly object _socketLock = new object();

        public RequestResponse(IEssMessageFactory messageFactory,
            IPollbookTransactionRepository pollbookTransactionRepository,
            ITransactionGuidCache transactionGuidCache,
            IEssLogger logger)
        {
            _messageFactory = messageFactory ?? throw new ArgumentNullException(nameof(messageFactory));
            _pollbookTransactionRepository = pollbookTransactionRepository ?? throw new ArgumentNullException(nameof(pollbookTransactionRepository));
            _transactionGuidCache = transactionGuidCache ?? throw new ArgumentNullException(nameof(transactionGuidCache));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _cancellationTokenSource = new CancellationTokenSource();
        }

        public void Initialize(string passPhrase)
        {
            _passPhrase = passPhrase;
            // Cache initialization is now handled by PeerSyncUtil
        }

        public async Task<ReconciliationResponse> SendRequestAsync(EssServiceBusNode node, ReconciliationMessage message, int timeoutSeconds = PeerToPeerConstants.syncResponseTimeoutSeconds)
        {
            ReconciliationResponse reconciliationResponse = new ReconciliationResponse();
            
            if (node == null)
            {
                _logger.LogWarning("Cannot send request to null node");
                return reconciliationResponse;
            }

            if (string.IsNullOrEmpty(_passPhrase))
            {
                _logger.LogWarning("PassPhrase not initialized, cannot send encrypted request");
                return reconciliationResponse;
            }

            try
            {
                using (var requestSocket = new RequestSocket($"tcp://{node.Domain}:5555"))
                {
                    // Set socket options for better reliability
                    requestSocket.Options.Linger = TimeSpan.Zero;
                    requestSocket.Options.SendBuffer = 1000;
                    requestSocket.Options.ReceiveBuffer = 1000;

                    string encryptedReconciliationMessage = _messageFactory.Encrypt(JsonConvert.SerializeObject(message), _passPhrase);
                    if (string.IsNullOrEmpty(encryptedReconciliationMessage))
                    {
                        _logger.LogWarning("Encrypting reconciliation request failed");
                        return reconciliationResponse;
                    }

                    var sendTimeout = TimeSpan.FromMinutes(1);
                    var receiveTimeout = TimeSpan.FromSeconds(timeoutSeconds);

                    if (requestSocket.TrySendFrame(sendTimeout, encryptedReconciliationMessage))
                    {
                        _logger.LogDebug($"Sent reconciliation request to {node.DeviceName}");
                        
                        if (requestSocket.TryReceiveFrameString(receiveTimeout, out string receivedMessage))
                        {
                            if (!string.IsNullOrEmpty(receivedMessage))
                            {
                                string decryptedReconciliationMessage = _messageFactory.Decrypt(receivedMessage, _passPhrase);

                                if (string.IsNullOrEmpty(decryptedReconciliationMessage))
                                {
                                    _logger.LogWarning("Decrypting reconciliation response failed");
                                    return reconciliationResponse;
                                }

                                try
                                {
                                    reconciliationResponse = JsonConvert.DeserializeObject<ReconciliationResponse>(decryptedReconciliationMessage);
                                    _logger.LogInformation($"Received reconciliation response from {node.DeviceName} containing {reconciliationResponse?.TransactionList?.Count ?? 0} transactions");
                                }
                                catch (JsonException ex)
                                {
                                    _logger.LogError(ex, new Dictionary<string, string> 
                                    { 
                                        { "Action", "Failed to deserialize reconciliation response" },
                                        { "Node", node.DeviceName }
                                    });
                                }
                            }
                            else
                            {
                                _logger.LogWarning($"Received empty response from {node.DeviceName}");
                            }
                        }
                        else
                        {
                            _logger.LogWarning($"Timeout: Unable to receive reconciliation response from {node.DeviceName} within {timeoutSeconds} seconds");
                        }
                    }
                    else
                    {
                        _logger.LogWarning($"Timeout: Unable to send reconciliation request to {node.DeviceName} within {sendTimeout.TotalSeconds} seconds");
                    }
                }

                return reconciliationResponse;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, new Dictionary<string, string>
                {
                    { "Action", "Exception in RequestResponse.SendRequestAsync" },
                    { "Node", node?.DeviceName ?? "Unknown" }
                });
                return reconciliationResponse;
            }
        }

        public async Task StartReceivingReconciliationRequests(CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("Creating response socket on port 5555");

                lock (_socketLock)
                {
                    if (_responseSocket != null && !_responseSocket.IsDisposed)
                    {
                        _logger.LogWarning("Response socket already exists and is active");
                        return;
                    }

                    _responseSocket = new ResponseSocket("@tcp://*:5555");
                    
                    // Set socket options for better performance
                    _responseSocket.Options.Linger = TimeSpan.Zero;
                    _responseSocket.Options.SendBuffer = 1000;
                    _responseSocket.Options.ReceiveBuffer = 1000;
                }

                using (_responseSocket)
                {
                    while (!cancellationToken.IsCancellationRequested && !_cancellationTokenSource.Token.IsCancellationRequested)
                    {
                        try
                        {
                            string message = string.Empty;
                            
                            lock (_socketLock)
                            {
                                if (!_responseSocket.IsDisposed)
                                {
                                    // Use TryReceiveFrameString with timeout to allow cancellation
                                    if (_responseSocket.TryReceiveFrameString(TimeSpan.FromMilliseconds(500), out message))
                                    {
                                        // Message received successfully
                                    }
                                    else
                                    {
                                        // Timeout occurred, continue loop to check cancellation
                                        continue;
                                    }
                                }
                                else
                                {
                                    break;
                                }
                            }

                            if (!string.IsNullOrEmpty(message))
                            {
                                await ProcessReconciliationRequestAsync(message);
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, new Dictionary<string, string> { { "Action", "Error processing reconciliation request" } });
                            
                            // Send empty response to prevent client hanging
                            try
                            {
                                lock (_socketLock)
                                {
                                    if (!_responseSocket.IsDisposed)
                                    {
                                        _responseSocket.TrySendFrame(TimeSpan.FromSeconds(1), string.Empty);
                                    }
                                }
                            }
                            catch (Exception sendEx)
                            {
                                _logger.LogError(sendEx, new Dictionary<string, string> { { "Action", "Error sending error response" } });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, new Dictionary<string, string> { { "Action", "Error in StartReceivingReconciliationRequests" } });
            }
            finally
            {
                lock (_socketLock)
                {
                    _responseSocket = null;
                }
                _logger.LogInformation("Stopped receiving reconciliation requests");
            }
        }

        private async Task ProcessReconciliationRequestAsync(string message)
        {
            try
            {
                if (string.IsNullOrEmpty(_passPhrase))
                {
                    _logger.LogWarning("PassPhrase not initialized, cannot decrypt request");
                    SendEmptyResponse();
                    return;
                }

                var decryptedMessage = _messageFactory.Decrypt(message, _passPhrase);
                if (string.IsNullOrEmpty(decryptedMessage))
                {
                    _logger.LogWarning("Failed to decrypt reconciliation request");
                    SendEmptyResponse();
                    return;
                }

                ReconciliationMessage reconciliationMessage;
                try
                {
                    reconciliationMessage = JsonConvert.DeserializeObject<ReconciliationMessage>(decryptedMessage);
                }
                catch (JsonException ex)
                {
                    _logger.LogError(ex, new Dictionary<string, string> { { "Action", "Failed to deserialize reconciliation request" } });
                    SendEmptyResponse();
                    return;
                }

                _logger.LogDebug("Received reconciliation request from peer");

                if (IsValidReconciliationRequest(reconciliationMessage))
                {
                    ReconciliationResponse reconciliationResponse;
                    
                    if (reconciliationMessage.CountOnly)
                    {
                        reconciliationResponse = await GetTransactionCountsAsync();
                    }
                    else
                    {
                        reconciliationResponse = await GetMissingTransactionsAsync(reconciliationMessage);
                    }

                    await SendReconciliationResponseAsync(reconciliationResponse);
                }
                else
                {
                    LogInvalidReconciliationRequest(reconciliationMessage);
                    SendEmptyResponse();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, new Dictionary<string, string> { { "Action", "Error in ProcessReconciliationRequestAsync" } });
                SendEmptyResponse();
            }
        }

        private bool IsValidReconciliationRequest(ReconciliationMessage reconciliationMessage)
        {
            return reconciliationMessage?.ReconciliationEnvelopeHeader?.ElectionDatabaseGuid == SystemConfiguration.ElectionConfiguration?.ElectionDatabaseGuid
                && reconciliationMessage?.ReconciliationEnvelopeHeader?.PollPlaceID == LoggedInPollplaceInfo.LoggedInPollPlace?.PollingPlaceId;
        }

        private void LogInvalidReconciliationRequest(ReconciliationMessage reconciliationMessage)
        {
            _logger.LogWarning(
                $"Received invalid reconciliation request - ElectionDatabaseGuid = {reconciliationMessage?.ReconciliationEnvelopeHeader?.ElectionDatabaseGuid}, PollPlaceID = {reconciliationMessage?.ReconciliationEnvelopeHeader?.PollPlaceID}");
            _logger.LogWarning(
                $"Expected values: ElectionDatabaseGuid = {SystemConfiguration.ElectionConfiguration?.ElectionDatabaseGuid}, PollPlaceID = {LoggedInPollplaceInfo.LoggedInPollPlace?.PollingPlaceId}");
        }

        private async Task<ReconciliationResponse> GetTransactionCountsAsync()
        {
            if (SystemDetails.DownloadInProgress)
            {
                return new ReconciliationResponse
                {
                    NumberOfVoterTransactions = 0,
                    NumberOfAddTransactions = 0,
                    NumberOfEditTransactions = 0,
                    NumberOfStatusTransactions = 0,
                    LastTransactionGuid = string.Empty
                };
            }

            try
            {
                // Use cache for fast transaction counts
                var voterGuids = _transactionGuidCache.GetTransactionGuids();
                var addGuids = _transactionGuidCache.GetAddTransactionGuids();
                var editGuids = _transactionGuidCache.GetEditTransactionGuids();
                var statusGuids = _transactionGuidCache.GetStatusTransactionGuids();

                var response = new ReconciliationResponse
                {
                    NumberOfVoterTransactions = voterGuids.Count,
                    NumberOfAddTransactions = addGuids.Count,
                    NumberOfEditTransactions = editGuids.Count,
                    NumberOfStatusTransactions = statusGuids.Count,
                    TransactionsChecksum = Helpers.GetMD5Checksum(voterGuids),
                    LastTransactionGuid = _pollbookTransactionRepository.GetLastTransactionGuid()
                };

                _logger.LogDebug($"Returning transaction counts - Voter: {response.NumberOfVoterTransactions}, Add: {response.NumberOfAddTransactions}, Edit: {response.NumberOfEditTransactions}, Status: {response.NumberOfStatusTransactions}");
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, new Dictionary<string, string> { { "Action", "Error getting transaction counts from cache" } });
                
                // Return empty response on error
                return new ReconciliationResponse
                {
                    NumberOfVoterTransactions = 0,
                    NumberOfAddTransactions = 0,
                    NumberOfEditTransactions = 0,
                    NumberOfStatusTransactions = 0,
                    LastTransactionGuid = string.Empty
                };
            }
        }

        private async Task<ReconciliationResponse> GetMissingTransactionsAsync(ReconciliationMessage reconciliationMessage)
        {
            var stopwatch = Stopwatch.StartNew();
            _logger.LogDebug("Getting missing transactions for reconciliation");

            try
            {
                var reconciliationResponse = new ReconciliationResponse();

                // Process each transaction type in parallel for better performance
                var tasks = new[]
                {
                    ProcessTransactionTypeAsync(
                        reconciliationMessage.TransactionGuids ?? new HashSet<string>(),
                        _transactionGuidCache.GetTransactionGuids(),
                        async (missingGuids) => {
                            reconciliationResponse.TransactionList = await _pollbookTransactionRepository.GetPollbookTransactions(missingGuids);
                        }),

                    ProcessTransactionTypeAsync(
                        reconciliationMessage.AddVoterTransactionGuids ?? new HashSet<string>(),
                        _transactionGuidCache.GetAddTransactionGuids(),
                        async (missingGuids) => {
                            reconciliationResponse.AddTransactionList = await _pollbookTransactionRepository.GetPollbookAddTransactions(missingGuids);
                        }),

                    ProcessTransactionTypeAsync(
                        reconciliationMessage.EditVoterTransactionGuids ?? new HashSet<string>(),
                        _transactionGuidCache.GetEditTransactionGuids(),
                        async (missingGuids) => {
                            reconciliationResponse.EditTransactionList = await _pollbookTransactionRepository.GetPollbookEditTransactions(missingGuids);
                        }),

                    ProcessTransactionTypeAsync(
                        reconciliationMessage.StatusVoterTransactionGuids ?? new HashSet<string>(),
                        _transactionGuidCache.GetStatusTransactionGuids(),
                        async (missingGuids) => {
                            reconciliationResponse.StatusTransactionList = await _pollbookTransactionRepository.GetPollbookStatusTransactions(missingGuids);
                        })
                };

                await Task.WhenAll(tasks);

                stopwatch.Stop();
                var totalTransactions = (reconciliationResponse.TransactionList?.Count ?? 0) +
                                      (reconciliationResponse.AddTransactionList?.Count ?? 0) +
                                      (reconciliationResponse.EditTransactionList?.Count ?? 0) +
                                      (reconciliationResponse.StatusTransactionList?.Count ?? 0);

                _logger.LogDebug($"Retrieved {totalTransactions} missing transactions in {stopwatch.ElapsedMilliseconds}ms");
                return reconciliationResponse;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, new Dictionary<string, string> { { "Action", "Error getting missing transactions" } });
                return new ReconciliationResponse();
            }
        }

        private async Task ProcessTransactionTypeAsync<T>(
            ICollection<T> remoteGuids,
            ICollection<T> localGuids,
            Func<ICollection<T>, Task> processAction)
        {
            try
            {
                if (localGuids.Count == 0)
                {
                    // No local transactions to send
                    return;
                }

                if (remoteGuids.Count == 0)
                {
                    // Remote has no transactions, send all local ones
                    await processAction(localGuids);
                }
                else
                {
                    // Find transactions that remote doesn't have
                    var missingGuids = new HashSet<T>(localGuids);
                    missingGuids.ExceptWith(remoteGuids);
                    
                    if (missingGuids.Count > 0)
                    {
                        await processAction(missingGuids);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, new Dictionary<string, string> { { "Action", "Error processing transaction type" } });
            }
        }

        private async Task SendReconciliationResponseAsync(ReconciliationResponse reconciliationResponse)
        {
            try
            {
                var serializedResponse = JsonConvert.SerializeObject(reconciliationResponse);
                var encryptedResponse = _messageFactory.Encrypt(serializedResponse, _passPhrase);
                
                if (string.IsNullOrEmpty(encryptedResponse))
                {
                    _logger.LogWarning("Failed to encrypt reconciliation response");
                    SendEmptyResponse();
                    return;
                }

                lock (_socketLock)
                {
                    if (!_responseSocket.IsDisposed)
                    {
                        if (!_responseSocket.TrySendFrame(TimeSpan.FromSeconds(5), encryptedResponse))
                        {
                            _logger.LogWarning("Timeout sending reconciliation response");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, new Dictionary<string, string> { { "Action", "Error sending reconciliation response" } });
                SendEmptyResponse();
            }
        }

        private void SendEmptyResponse()
        {
            try
            {
                lock (_socketLock)
                {
                    if (!_responseSocket.IsDisposed)
                    {
                        _responseSocket.TrySendFrame(TimeSpan.FromSeconds(1), string.Empty);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, new Dictionary<string, string> { { "Action", "Error sending empty response" } });
            }
        }

        public void StopReceivingRequests()
        {
            try
            {
                _logger.LogInformation("Stopping reconciliation request receiver");
                
                _cancellationTokenSource?.Cancel();

                lock (_socketLock)
                {
                    _responseSocket?.Dispose();
                    _responseSocket = null;
                    
                    _requestSocket?.Dispose();
                    _requestSocket = null;
                }

                _logger.LogDebug("Reconciliation sockets disposed");
            }
            catch (ObjectDisposedException ex)
            {
                _logger.LogDebug($"Handled disposed object during cleanup: {ex.Message}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, new Dictionary<string, string> { { "Action", "Error stopping reconciliation requests" } });
            }
        }
        
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                _logger.LogDebug("Disposing RequestResponse");
                
                StopReceivingRequests();
                
                _cancellationTokenSource?.Dispose();
                _cancellationTokenSource = null;

                _disposed = true;
                _logger.LogDebug("RequestResponse disposed successfully");
            }
        }
    }
}