using ESS.Pollbook.Core.DTO;
using System;
using System.Threading.Tasks;

namespace ESS.Pollbook.Components.Business.SystemStats
{
    public interface ISystemStatsFactory
    {
        Task<int> GetBallotsIssuedForThisPollingPlaceAsync();

        Task<int> GetBallotsReissuedForThisPollingPlaceAsync();

        Task<int> GetBallotsCanceledForThisPollingPlaceAsync();

        Task<int> GetLocalBallotsIssuedForThisPollingPlaceAsync();

        Task<int> GetLocalBallotsReissuedForThisPollingPlaceAsync();

        Task<int> GetLocalBallotsCanceledForThisPollingPlaceAsync();

        Task<SystemStatsDto> GetByPollingPlaceCountyAsync(int countyId, int pollingPlaceId);

        Task<BallotCountDto> GetBallotCountByPollPlaceAndTypeAndMethodAsync(int pollPlaceId, DateTime? electionDate = null);

        Task<LocalBallotCountDto> GetLocalBallotCountByPollPlaceAndTypeAndMethodAsync(int pollPlaceId,
            DateTime? electionDate = null);

        Task<bool> GetIsPollOpenAsync();
    }
}
