using ESS.Pollbook.Components.Repository.ElectionJurisdictionEnum;
using ESS.Pollbook.Components.Repository.PollbookTransaction;
using ESS.Pollbook.Components.Repository.SystemStats;
using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.StaticValues;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using ESS.Pollbook.Core.Logging;

namespace ESS.Pollbook.Components.Business.SystemStats
{
    public class SystemStatsFactory : ISystemStatsFactory
    {
        private readonly ISystemStatsRepository _systemStatsRepository;
        private readonly IPollbookTransactionRepository _transactionRepository;
        private readonly IVoterJurisdictionEnumRepository _voterJurisdictionEnumRepository;
        private readonly IEssLogger _essLogger;

        public SystemStatsFactory(ISystemStatsRepository systemStatsRepository,
           IPollbookTransactionRepository voterTransactionRepository,
           IVoterJurisdictionEnumRepository voterJurisdictionEnumRepository,
           IEssLogger essLogger)
        {
            _systemStatsRepository = systemStatsRepository;
            _transactionRepository = voterTransactionRepository;
            _voterJurisdictionEnumRepository = voterJurisdictionEnumRepository;
            _essLogger = essLogger;
        }

        public async Task<int> GetBallotsIssuedForThisPollingPlaceAsync()
        {
            int pollingPlace = LoggedInPollplaceInfo.LoggedInPollPlace.PollingPlaceId;
            return await _transactionRepository.GetBallotsIssuedCountByPollPlaceAsync(pollingPlace);
        }

        public async Task<int> GetBallotsReissuedForThisPollingPlaceAsync()
        {
            int pollingPlace = LoggedInPollplaceInfo.LoggedInPollPlace.PollingPlaceId;
            return await _transactionRepository.GetBallotsReIssuedCountAsync(pollingPlace);
        }

        public async Task<int> GetBallotsCanceledForThisPollingPlaceAsync()
        {
            int pollingPlace = LoggedInPollplaceInfo.LoggedInPollPlace.PollingPlaceId;
            return await _transactionRepository.GetBallotsCanceledCountAsync(pollingPlace);
        }

        public async Task<int> GetLocalBallotsIssuedForThisPollingPlaceAsync()
        {
            return await _transactionRepository.GetLocalBallotsIssuedCountByPollPlaceAsync(LoggedInPollplaceInfo.LoggedInPollPlace.PollingPlaceId);
        }

        public async Task<int> GetLocalBallotsReissuedForThisPollingPlaceAsync()
        {
            return await _transactionRepository.GetLocalBallotsReIssuedCountAsync(LoggedInPollplaceInfo.LoggedInPollPlace.PollingPlaceId);
        }

        public async Task<int> GetLocalBallotsCanceledForThisPollingPlaceAsync()
        {
            return await _transactionRepository.GetLocalBallotsCanceledCountAsync(LoggedInPollplaceInfo.LoggedInPollPlace.PollingPlaceId);
        }

        public async Task<SystemStatsDto> GetByPollingPlaceCountyAsync(int countyId, int pollingPlaceId)
        {
            try
            {
                var systemStats = await GetByPollingPlaceCounty(pollingPlaceId, countyId);

                var ballotsIssuedCountDto = new BallotsIssuedCountDto
                {
                    BallotsIssued = await _transactionRepository.GetBallotsIssuedCountByPollPlaceAsync(pollingPlaceId),
                    BallotsReissued = await _transactionRepository.GetBallotsReIssuedCountAsync(pollingPlaceId),
                    BallotsCanceled = await _transactionRepository.GetBallotsCanceledCountAsync(pollingPlaceId)
                };
                var localBallotsIssuedCountDto = new LocalBallotsIssuedCountDto()
                {
                    LocalBallotsIssued = await _transactionRepository.GetLocalBallotsIssuedCountByPollPlaceAsync(pollingPlaceId),
                    LocalBallotsReissued = await _transactionRepository.GetLocalBallotsReIssuedCountAsync(pollingPlaceId),
                    LocalBallotsCanceled = await _transactionRepository.GetLocalBallotsCanceledCountAsync(pollingPlaceId)
                };
                systemStats.BallotsIssuedCountDto = ballotsIssuedCountDto;
                systemStats.LocalBallotsIssuedCount = localBallotsIssuedCountDto;
                systemStats.BallotCount = await GetBallotCountByPollPlaceAndTypeAndMethodAsync(pollingPlaceId);
                systemStats.LocalBallotCount = await GetLocalBallotCountByPollPlaceAndTypeAndMethodAsync(pollingPlaceId);
                systemStats.ElectionName = SystemConfiguration.ElectionConfiguration.ElectionName;

                return systemStats;
            }
            catch (Exception ex)
            {
                _essLogger.LogError(ex,
                new Dictionary<string, string>
                    { { "Action", GetType().Name + "." + MethodBase.GetCurrentMethod()?.Name } });

            }

            return new SystemStatsDto();
        }

        public async Task<SystemStatsDto> GetByPollingPlaceCounty(int pollingPlaceId, int countyId)
        {
            var systemStatusDto = new SystemStatsDto();
            var enumInfo = await _systemStatsRepository.GetEnumInfo() ?? Enumerable.Empty<JurisdictionEnumDto>();

            var groupedInfo = enumInfo
                .Where(x => x.EnumerationValueName.Equals(nameof(AbsenteeStatus.Absentee)) || x.EnumerationValueName.Equals(nameof(AbsenteeStatus.EarlyVoteIssued)))
                .GroupBy(x => x.EnumerationValueName)
                .ToDictionary(g => g.Key, g => g.Select(x => x.EnumerationId).ToList());

            var absenteeIds = groupedInfo.TryGetValue(nameof(AbsenteeStatus.Absentee), out var ids) ? ids : new List<int>();

            var counts = await _systemStatsRepository.GetEnumerationCounts(
                LoggedInPollplaceInfo.LoggedInPollPlace.PollingPlaceVoteCenterIndicator == 0 ? pollingPlaceId : (int?)null
            );

            
            var absenteeCount = counts.Where(x => absenteeIds.Contains(x.EnumerationId)).Sum(x => x.Count);

            if (LoggedInPollplaceInfo.LoggedInPollPlace.PollingPlaceVoteCenterIndicator != 0) // is vote center
            {
                systemStatusDto.RegisteredCountyVoters = await _systemStatsRepository.GetRegisteredCountyVoters(countyId.ToString());
            }
            else
            {
                systemStatusDto.RegisteredVoters = await _systemStatsRepository.GetRegisteredVoters(pollingPlaceId.ToString());
            }

            if (SystemConfiguration.ElectionConfiguration.EnableNotInRoster)
            {
                systemStatusDto.NewVoters = await _systemStatsRepository.GetNewVoters(pollingPlaceId.ToString());
            }
            systemStatusDto.AbsenteeVoterCount = absenteeCount;
            systemStatusDto.EarlyVoterCount = await GetEarlyVoteCount();

            return systemStatusDto;
        }

        public async Task<bool> GetIsPollOpenAsync()
        {
            return await _transactionRepository.GetIsPollOpenAsync();
        }

        public async Task<int> GetEarlyVoteCount()
        {
            var ballotTransactions = await _transactionRepository.GetEarlyVoteBallotTransactionsByCurrentPollPlace();
            var pollbookTransactionDtos = ballotTransactions.ToList();
            
            //Similar to how we calculate the Check-In count
            var issueCount = pollbookTransactionDtos
                .Count(x => x.TransactionType.Equals(nameof(TransactionType.BallotIssue)));
            
            var reissuedCount = pollbookTransactionDtos
                .Count(x => x.IsReissue);

            var cancelCount = pollbookTransactionDtos
                .Count(x => x.TransactionType.Equals(nameof(TransactionType.BallotCancel)));

            return issueCount - reissuedCount - cancelCount;
        }
        public async Task<BallotCountDto> GetBallotCountByPollPlaceAndTypeAndMethodAsync(int pollPlaceId, DateTime? electionDate = null)
        {
            var ballotTransactions = await _transactionRepository.GetVoterBallotsByPollPlaceAndTransactionType(pollPlaceId);

            var enumerationDetails = await _voterJurisdictionEnumRepository.GetCommonEnumsAsync((int)EssEnumeration.MediaType);
            var electionJurisdictionEnumValueDtos = enumerationDetails as IList<ElectionJurisdictionEnumValueDto> ?? enumerationDetails.ToList();

            var expressVoteMediaTypeEnumId = electionJurisdictionEnumValueDtos
               .Where(e => e.EnumerationValueNumber == (int)MediaType.ExpressVote)
                .Select(e => e.EnumerationValueId)
               .FirstOrDefault();

            var dacMediaTypeEnumId = electionJurisdictionEnumValueDtos
               .Where(e => e.EnumerationValueNumber == (int)MediaType.Dominion)
               .Select(e => e.EnumerationValueId)
               .FirstOrDefault();

            var paperMediaTypeEnumId = electionJurisdictionEnumValueDtos
               .Where(e => e.EnumerationValueNumber == (int)MediaType.Paper)
               .Select(e => e.EnumerationValueId)
               .FirstOrDefault();

            var ballotCount = new BallotCountDto();

            if (ballotTransactions == null) return ballotCount;

            var ballotList = ballotTransactions.ToList();

            if (SystemConfiguration.ElectionConfiguration.ProvisionalBallotEnabled(LoggedInPollplaceInfo.IsEarlyVotingPollPlace))
            {
                ballotCount.ProvisionalExpressCard = SystemConfiguration.ElectionConfiguration.DacEnabled
                   ? ballotList.Count(tx => (tx.IsProvisional) && tx.MediaTypeEnumId == dacMediaTypeEnumId)
                   : ballotList.Count(tx => (tx.IsProvisional) && tx.MediaTypeEnumId == expressVoteMediaTypeEnumId);

                ballotCount.ProvisionalPaper = ballotList.Count(tx => (tx.IsProvisional) && tx.MediaTypeEnumId == paperMediaTypeEnumId);
            }

            ballotCount.StandardExpressCard = SystemConfiguration.ElectionConfiguration.DacEnabled
               ? ballotList.Count(tx => (!tx.IsProvisional) && tx.MediaTypeEnumId == dacMediaTypeEnumId)
               : ballotList.Count(tx => (!tx.IsProvisional) && tx.MediaTypeEnumId == expressVoteMediaTypeEnumId);

            ballotCount.StandardPaper = ballotList.Count(tx => (!tx.IsProvisional) && tx.MediaTypeEnumId == paperMediaTypeEnumId);

            return ballotCount;
        }

        /// <summary>
        /// Gets the Local Ballot Transaction Counts
        /// </summary>
        /// <param name="pollPlaceId"></param>
        /// <param name="electionDate"></param>
        /// <returns></returns>
        public async Task<LocalBallotCountDto> GetLocalBallotCountByPollPlaceAndTypeAndMethodAsync(int pollPlaceId, DateTime? electionDate = null)
        {
            var ballotTransactions = await _transactionRepository.GetVoterBallotsByPollPlaceAndTransactionType(pollPlaceId);

            var enumerationDetails = await _voterJurisdictionEnumRepository.GetCommonEnumsAsync((int)EssEnumeration.MediaType);
            var electionJurisdictionEnumValueDtos = enumerationDetails as IList<ElectionJurisdictionEnumValueDto> ?? enumerationDetails.ToList();

            var expressVoteMediaTypeEnumId = electionJurisdictionEnumValueDtos
               .Where(e => e.EnumerationValueNumber == (int)MediaType.ExpressVote)
                .Select(e => e.EnumerationValueId)
               .FirstOrDefault();

            var dacMediaTypeEnumId = electionJurisdictionEnumValueDtos
               .Where(e => e.EnumerationValueNumber == (int)MediaType.Dominion)
               .Select(e => e.EnumerationValueId)
               .FirstOrDefault();

            var paperMediaTypeEnumId = electionJurisdictionEnumValueDtos
               .Where(e => e.EnumerationValueNumber == (int)MediaType.Paper)
               .Select(e => e.EnumerationValueId)
               .FirstOrDefault();

            var ballotCount = new LocalBallotCountDto();

            if (ballotTransactions == null) return ballotCount;

            var ballotList = ballotTransactions.Where(tx => tx.SystemIdentifier == SystemDetails.MachineName).ToList();

            if (SystemConfiguration.ElectionConfiguration.ProvisionalBallotEnabled(LoggedInPollplaceInfo.IsEarlyVotingPollPlace))
            {
                ballotCount.LocalProvisionalExpressCard = SystemConfiguration.ElectionConfiguration.DacEnabled
                   ? ballotList.Count(tx => (tx.IsProvisional) && tx.MediaTypeEnumId == dacMediaTypeEnumId)
                   : ballotList.Count(tx => (tx.IsProvisional) && tx.MediaTypeEnumId == expressVoteMediaTypeEnumId);

                ballotCount.LocalProvisionalPaper = ballotList.Count(tx => (tx.IsProvisional) && tx.MediaTypeEnumId == paperMediaTypeEnumId);
            }

            ballotCount.LocalStandardExpressCard = SystemConfiguration.ElectionConfiguration.DacEnabled
               ? ballotList.Count(tx => (!tx.IsProvisional) && tx.MediaTypeEnumId == dacMediaTypeEnumId)
               : ballotList.Count(tx => (!tx.IsProvisional) && tx.MediaTypeEnumId == expressVoteMediaTypeEnumId);

            ballotCount.LocalStandardPaper = ballotList.Count(tx => (!tx.IsProvisional) && tx.MediaTypeEnumId == paperMediaTypeEnumId);

            return ballotCount;
        }
    }
}
