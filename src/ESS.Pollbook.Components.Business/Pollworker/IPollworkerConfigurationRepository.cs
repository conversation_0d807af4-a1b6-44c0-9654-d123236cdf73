using System;
using ESS.Pollbook.Components.Repository.Configuration;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ESS.Pollbook.Components.Business.Pollworker
{
    public interface IPollworkerConfigurationRepository
    {
        Task<IEnumerable<SqliteConfigurationDto>> LoadPollworkerConfigurationAsync();

        void UpsertConfigurations(string tableName, string keyField, List<string> lstColumns, List<List<object>> values,
            bool isNewConfig = false);

        Task<DateTime> GetLatestConfigurationAppliedDateTime();
    }
}