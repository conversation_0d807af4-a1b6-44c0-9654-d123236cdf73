using ESS.ELLEGO.Rest;
using ESS.Pollbook.Components.Repository.DeviceStats;
using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Model;
using ESS.Pollbook.Core.StaticValues;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using ESS.Pollbook.Core.Model.RegionalResults;

namespace ESS.Pollbook.Components.Business.Host
{
   public class HostSyncFactory : IHostSyncFactory
   {
	   private readonly IDeviceStatsRepository _deviceStatsRepository;

	   public HostSyncFactory(IDeviceStatsRepository deviceStatsRepository)
	   {
		   _deviceStatsRepository = deviceStatsRepository;
	   }

	   #region Public Methods

	   public async Task<bool> GetSyncPointPingResponse(CancellationToken token = default)
	   {
		   if (SystemConfiguration.ElectionConfiguration?.HostSync != true)
			   return false;

		   if (token.IsCancellationRequested)
			   return false;

		   try
		   {
			   var request = new EssRestRequest
			   {
				   UrlSegments = new List<string> { "ping" }
			   };

			   var response = await _deviceStatsRepository.GetHostConnectedStatus(request);
			   return response.IsSuccessStatusCode;
		   }
		   catch
		   {
			   return false;
		   }
	   }

	   /// <summary>
	   /// Create a ESS REST request based on the transaction request and submit it to SyncPoint.
	   /// </summary>
	   public EssRestRequest CreateRESTRequest(TransactionBatchRequest transactionBatchRequest)
	   {
		   return GetEssRestRequest(
			   SystemConfiguration.ElectionConfiguration.SyncPointUrl,
			   transactionBatchRequest,
			   "batch", "submit");
	   }

	   /// <summary>
	   /// Creates a REST request for retrieving the status for a voter or list of voters from the host service
	   /// </summary>
	   public EssRestRequest CreateRESTHostVoterSearchRequest(PollbookVoterSearchRequest pollbookVoterSearchRequest)
	   {
		   return GetEssRestRequest(
			   SystemConfiguration.ElectionConfiguration.SyncPointUrl,
			   pollbookVoterSearchRequest,
			   "voters", "GetVoterSearchResults");
	   }

	   /// <summary>
	   /// Creates a REST request for retrieving the details of the voter from the host service
	   /// </summary>
	   public EssRestRequest CreateRESTHostVoterDetailsRequest(PollbookVoterDetailsRequest pollbookVoterDetailsRequest)
	   {
		   return GetEssRestRequest(
			   SystemConfiguration.ElectionConfiguration.SyncPointUrl,
			   pollbookVoterDetailsRequest,
			   "voters", "GetVoterDetails");
	   }

	   /// <summary>
	   /// Creates a REST request for retrieving the voter history.
	   /// </summary>
	   public EssRestRequest CreateRESTHostVoterTransactionsRequest(
		   PollbookVoterHistoryRequest pollbookVoterHistoryRequest)
	   {
		   return GetEssRestRequest(
			   SystemConfiguration.ElectionConfiguration.SyncPointUrl,
			   pollbookVoterHistoryRequest,
			   "transactions", "voter");
	   }

	   /// <summary>
	   /// Creates a REST request for retrieving transactions.
	   /// </summary>
	   public EssRestRequest CreateRESTHostTransactionsRequest(long hostTransactionId,
		   long transactionsCount, long hostTransactionsCount, long failedTransactionsCount, string userIp)
	   {
		   var model = new TransactionBatchRequest()
		   {
			   LastTransaction = hostTransactionId,
			   SystemIdentifier = SystemDetails.MachineName,
			   PollbookTransactionsCount = transactionsCount,
			   HostTransactionsCount = hostTransactionsCount,
			   FailedTransactionsCount = failedTransactionsCount,
			   UserIp = userIp
		   };

		   return GetEssRestRequest(
			   SystemConfiguration.ElectionConfiguration.SyncPointUrl,
			   model,
			   "transactions");
	   }

	   public EssRestRequest CreateRESTHostTransactionTotalCountRequest(TotalTransactionRequest totalTransactionRequest)
	   {
		   return GetEssRestRequest(
			   SystemConfiguration.ElectionConfiguration.SyncPointUrl,
			   totalTransactionRequest,
			   "transactions", "total-count");
	   }

	   public EssRestRequest CreateRESTHostUsersRequest(DateTime? usersUpdateDatetime)
	   {
		   var model = new UserRequest
		   {
			   ElectionDatabaseGuid = SystemConfiguration.ElectionConfiguration.ElectionDatabaseGuid,
			   DeviceName = SystemDetails.MachineName,
			   UsersUpdatedAfter = usersUpdateDatetime
		   };

		   return new EssRestRequest
		   {
			   Body = model,
			   UrlSegments = new List<string> { "users" }
		   };
	   }

	   public EssRestRequest CreateRestRegionalResultsFilesRequest()
	   {
		   var model = new RegionalResultFilesRequest
		   {
			   ElectionDatabaseGuid = SystemConfiguration.ElectionConfiguration.ElectionDatabaseGuid,
			   DeviceName = SystemDetails.MachineName
		   };

		   return new EssRestRequest
		   {
			   Body = model,
			   UrlSegments = new List<string>() { "RegionalResults", "DownloadFiles" },
		   };
	   }

	   public EssRestRequest CreateRestRegionalResultsCheckFileRequest()
	   {
		   return GetEssRestRequest(
			   SystemConfiguration.ElectionConfiguration.SyncPointUrl,
			   new RequestBase(),
			   "RegionalResults", "CheckFile");
	   }

	   public EssRestRequest CreateRestRegionalResultsFileRequest()
	   {
		   return GetEssRestRequest(
			   SystemConfiguration.ElectionConfiguration.SyncPointUrl,
			   new RequestBase(),
			   "RegionalResults", "DownloadFile");
	   }

	   public EssRestRequest CreateRestRegionalResultsNewFileCheckRequest(DateTime? creationDate)
	   {
		   var model = new RegionalResultsFileRequest()
		   {
			   CreationDate = creationDate
		   };
		   return GetEssRestRequest(SystemConfiguration.ElectionConfiguration.SyncPointUrl,
			   model,
			   "RegionalResults", "CheckNewFile");
	   }

	   public EssRestRequest CreateRESTHostHelpRequest(DateTime? helpUpdateDatetime)
	   {
		   return new EssRestRequest()
		   {
			   Body = new HelpRequest
			   {
				   ElectionDatabaseGuid = SystemConfiguration.ElectionConfiguration.ElectionDatabaseGuid,
				   DeviceName = SystemDetails.MachineName,
				   HelpUpdatedAfter = helpUpdateDatetime
			   },
			   UrlSegments = new List<string> { "Help", "FilesInfo" },
		   };
	   }

	   public EssRestRequest CreateRESTCDNFilesRequest(string publicIp, List<string> fileList)
	   {
		   return CreateRestContentDeliveryNetworkFilesRequest(publicIp,
			   new List<string>() { "electiondata", "GetImageUri" }, fileList, styleSheetName: null);
	   }

	   public EssRestRequest CreateRESTCDNHelpFilesRequest(string publicIp, List<string> fileList,
		   string styleSheetName)
	   {
		   return CreateRestContentDeliveryNetworkFilesRequest(publicIp, new List<string> { "Help", "GetFileUri" },
			   null, filePaths: fileList, styleSheetName: styleSheetName);
	   }

	   public EssRestRequest CreateRESTHostFailedTransactionsSendToHost(FailedTransactionDto failedTransactionDto)
	   {
		   string defaultUserName = GetUserName();
		   var model = new FailedTransactionRequest()
		   {
			   TransactionGuid = failedTransactionDto.TransactionGuid,
			   ErrorMessage = failedTransactionDto.ErrorMessage,
			   ErrorSource = failedTransactionDto.ErrorSource,
			   ApplicationUserName = defaultUserName,
			   DeviceSystemIdentifier = SystemDetails.MachineName
		   };
		   return GetEssRestRequest(SystemConfiguration.ElectionConfiguration.SyncPointUrl,
			   model,
			   "transactions", "failed-transaction");
	   }

	   public EssRestRequest CreateRESTDownloadConfigurations(DateTime lastUpdatedDateTime)
	   {
		   string defaultUserName = GetUserName();
		   var model = new DownloadConfigurationsRequest()
		   {
			   ApplicationName = "ExpressPollAPI",
			   DeviceSystemIdentifier = SystemDetails.MachineName,
			   LastUpdateDateTime = lastUpdatedDateTime,
			   ApplicationUserName = defaultUserName
		   };
		   return GetEssRestRequest(SystemConfiguration.ElectionConfiguration.SyncPointUrl,
			   model,
			   "configurations", "downloadconfigurations");
	   }

	   public EssRestRequest CreateRESTConfigLog(DateTime lastUpdatedDateTime)
	   {
		   string defaultUserName = GetUserName();
		   var model = new UpdateConfigDownloadedLogRequest()
		   {
			   ApplicationName = "ExpressPollAPI",
			   DeviceSystemIdentifier = SystemDetails.MachineName,
			   DateApplied = lastUpdatedDateTime,
			   RecordUpdateUtcDatetime = DateTime.UtcNow,
			   ApplicationUserName = defaultUserName
		   };
		   return GetEssRestRequest(SystemConfiguration.ElectionConfiguration.SyncPointUrl,
			   model,
			   "configurations", "configLog");
	   }

	   public EssRestRequest CreateRESTChecklist()
	   {
		   return GetEssRestRequest(
			   SystemConfiguration.ElectionConfiguration.SyncPointUrl,
			   new RequestBase(),
			   "Help", "ChecklistInfo");
	   }

	   private string GetUserName()
	   {
		   string defaultUserName = CurrentUserInfo.LoggedInUser?.Username;
		   if (SystemDetails.AppState != ApplicationState.SignIn)
		   {
			   defaultUserName = SystemDetails.MachineName;
		   }

		   return defaultUserName;
	   }

	   private EssRestRequest CreateRestContentDeliveryNetworkFilesRequest(
		   string publicIp,
		   List<string> urlSegments,
		   List<string> imagePaths = null,
		   List<string> filePaths = null,
		   string styleSheetName = "")
	   {
		   var model = new CDNRequest
		   {
			   ElectionDatabaseGuid = SystemConfiguration.ElectionConfiguration.ElectionDatabaseGuid,
			   UserIp = publicIp
		   };

		   if (imagePaths != null)
			   model.ImagePaths = imagePaths;

		   if (filePaths != null)
			   model.FilePaths = filePaths;

		   if (!string.IsNullOrEmpty(styleSheetName))
			   model.StyleSheetName = styleSheetName;

		   return new EssRestRequest(SystemConfiguration.ElectionConfiguration.SyncPointUrl)
		   {
			   Body = model,
			   UrlSegments = urlSegments,
		   };
	   }

	   public EssRestRequest CreateRESTDevicePollPlaceRequest(PollingPlaceRequest pollingPlaceRequest)
	   {
		   var request = GetEssRestRequest(
			   SystemConfiguration.ElectionConfiguration.SyncPointUrl,
			   pollingPlaceRequest,
			   "devices", "get-device-poll-place");
		   return request;
	   }

	   #endregion

	   #region Private Methods

	   /// <summary>
	   /// Build the REST request as it is needed in order to submit the transaction to SyncPoint.
	   /// </summary>
	   private EssRestRequest GetEssRestRequest(string url, RequestBase model, params string[] urlSegments)
	   {
		   model.ElectionDatabaseGuid = SystemConfiguration.ElectionConfiguration.ElectionDatabaseGuid;
		   model.DeviceName = SystemDetails.MachineName;

		   return new EssRestRequest(url)
		   {
			   Body = model,
			   UrlSegments = urlSegments?.ToList() ?? new List<string>(),
		   };
	   }

	   #endregion
   }
}