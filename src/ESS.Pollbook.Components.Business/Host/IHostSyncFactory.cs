using ESS.ELLEGO.Rest;
using ESS.Pollbook.Core.DTO;
using ESS.Pollbook.Core.Model;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace ESS.Pollbook.Components.Business.Host
{
	public interface IHostSyncFactory
	{
      EssRestRequest CreateRESTCDNFilesRequest(string publicIp, List<string> fileList);
      EssRestRequest CreateRESTCDNHelpFilesRequest(string publicIp, List<string> fileList, string styleSheetName);
      EssRestRequest CreateRESTRequest(ESS.Pollbook.Core.Model.TransactionBatchRequest transactionBatchRequest);
      EssRestRequest CreateRESTHostVoterTransactionsRequest(PollbookVoterHistoryRequest pollbookVoterHistoryRequest);
      EssRestRequest CreateRESTHostTransactionsRequest(long hostTransactionId, long transactionsCount, long hostTransactionsCount, long failedTransactionsCount, string userIp);
      EssRestRequest CreateRESTHostVoterSearchRequest(PollbookVoterSearchRequest pollbookVoterSearchRequest);
      EssRestRequest CreateRESTHostVoterDetailsRequest(PollbookVoterDetailsRequest pollbookVoterDetailsRequest);
      EssRestRequest CreateRESTHostUsersRequest(DateTime? usersUpdateDatetime);
      EssRestRequest CreateRESTHostHelpRequest(DateTime? helpUpdateDatetime);
      EssRestRequest CreateRESTHostTransactionTotalCountRequest(TotalTransactionRequest totalTransactionRequest);
      EssRestRequest CreateRestRegionalResultsFilesRequest();
      EssRestRequest CreateRestRegionalResultsCheckFileRequest();
      EssRestRequest CreateRestRegionalResultsFileRequest();
      EssRestRequest CreateRestRegionalResultsNewFileCheckRequest(DateTime? creationDate);
	  Task<bool> GetSyncPointPingResponse(CancellationToken token = default);
      EssRestRequest CreateRESTHostFailedTransactionsSendToHost(FailedTransactionDto failedTransactionDto);
      EssRestRequest CreateRESTDownloadConfigurations(DateTime lastUpdatedDateTime);
      EssRestRequest CreateRESTConfigLog(DateTime lastUpdatedDateTime);
      EssRestRequest CreateRESTChecklist();
      EssRestRequest CreateRESTDevicePollPlaceRequest(PollingPlaceRequest pollingPlaceRequest);
    }
}
