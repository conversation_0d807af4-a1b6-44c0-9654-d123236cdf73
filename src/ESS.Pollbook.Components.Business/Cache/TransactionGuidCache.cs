using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using ESS.Pollbook.Core.Common;
using ESS.Pollbook.Core.Interface;
using ESS.Pollbook.Core.Logging;

namespace ESS.Pollbook.Components.Business.Cache
{
    /// <summary>
    /// Thread-safe transaction GUID cache with automatic refresh capabilities
    /// - Primary refresh: Every 1 minute automatically
    /// - Cache expiry: 5 minutes
    /// - Emergency refresh: 30-second timeout for synchronous calls
    /// - Background invalidation and refresh when new transactions are added
    /// </summary>
    public class TransactionGuidCache : ITransactionGuidCache
    {
        private readonly IEssLogger _logger;
        private readonly Timer _refreshTimer;
        private readonly SemaphoreSlim _refreshSemaphore;
        private readonly SemaphoreSlim _initializationSemaphore;

        // Cache configuration
        private readonly TimeSpan _cacheExpiry;
        private readonly TimeSpan _refreshInterval;

        // Cached data
        private volatile ConcurrentDictionary<string, ConcurrentHashSet<string>> _transactionGuids;
        private DateTime _lastRefreshTime;
        private volatile bool _isCacheValid;
        private volatile bool _isInitialized;
        private volatile bool _disposed;

        // Transaction type constants
        private const string TRANSACTION_TYPE_VOTER = "Voter";
        private const string TRANSACTION_TYPE_ADD = "Add";
        private const string TRANSACTION_TYPE_EDIT = "Edit";
        private const string TRANSACTION_TYPE_STATUS = "Status";

        // Database refresh functions
        private volatile Func<HashSet<string>> _getVoterGuidsFunc;
        private volatile Func<HashSet<string>> _getAddGuidsFunc;
        private volatile Func<HashSet<string>> _getEditGuidsFunc;
        private volatile Func<HashSet<string>> _getStatusGuidsFunc;

        // Performance tracking
        private volatile int _pendingInvalidations;
        private readonly Timer _invalidationTimer;

        public TransactionGuidCache(
            IEssLogger logger,
            TimeSpan? cacheExpiry = null,
            TimeSpan? refreshInterval = null)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            
            _cacheExpiry = cacheExpiry ?? TimeSpan.FromMinutes(5);
            _refreshInterval = refreshInterval ?? TimeSpan.FromMinutes(1);
            
            _refreshSemaphore = new SemaphoreSlim(1, 1);
            _initializationSemaphore = new SemaphoreSlim(1, 1);
            _transactionGuids = new ConcurrentDictionary<string, ConcurrentHashSet<string>>();
            _lastRefreshTime = DateTime.MinValue;
            _isCacheValid = false;
            _isInitialized = false;
            _pendingInvalidations = 0;
            
            InitializeCache();
            
            // Setup auto-refresh timer (starts disabled)
            _refreshTimer = new Timer(AutoRefreshCallback, null, Timeout.Infinite, Timeout.Infinite);
            
            // Setup invalidation batching timer
            _invalidationTimer = new Timer(ProcessPendingInvalidations, null, Timeout.Infinite, Timeout.Infinite);
        }

        public bool IsCacheValid
        {
            get
            {
                if (_disposed || !_isCacheValid || !_isInitialized) return false;
                var lastRefresh = _lastRefreshTime;
                return (DateTime.UtcNow - lastRefresh) < _cacheExpiry;
            }
        }

        public bool IsInitialized => _isInitialized && !_disposed;

        public DateTime LastRefreshTime => _lastRefreshTime;

        private void InitializeCache()
        {
            _transactionGuids[TRANSACTION_TYPE_VOTER] = new ConcurrentHashSet<string>();
            _transactionGuids[TRANSACTION_TYPE_ADD] = new ConcurrentHashSet<string>();
            _transactionGuids[TRANSACTION_TYPE_EDIT] = new ConcurrentHashSet<string>();
            _transactionGuids[TRANSACTION_TYPE_STATUS] = new ConcurrentHashSet<string>();
        }

        public HashSet<string> GetTransactionGuids()
        {
            if (_disposed) return new HashSet<string>();
            
            if (!EnsureCacheValidSync())
            {
                _logger?.LogWarning("Cache validation failed, returning empty set for voter transactions");
                return new HashSet<string>();
            }
            
            if (_transactionGuids.TryGetValue(TRANSACTION_TYPE_VOTER, out var voterGuids))
            {
                return new HashSet<string>(voterGuids);
            }
            
            _logger?.LogWarning("Voter transaction GUIDs not found in cache");
            return new HashSet<string>();
        }

        public HashSet<string> GetAddTransactionGuids()
        {
            if (_disposed) return new HashSet<string>();
            
            if (!EnsureCacheValidSync())
            {
                _logger?.LogWarning("Cache validation failed, returning empty set for add transactions");
                return new HashSet<string>();
            }
            
            if (_transactionGuids.TryGetValue(TRANSACTION_TYPE_ADD, out var addGuids))
            {
                return new HashSet<string>(addGuids);
            }
            
            _logger?.LogWarning("Add transaction GUIDs not found in cache");
            return new HashSet<string>();
        }

        public HashSet<string> GetEditTransactionGuids()
        {
            if (_disposed) return new HashSet<string>();
            
            if (!EnsureCacheValidSync())
            {
                _logger?.LogWarning("Cache validation failed, returning empty set for edit transactions");
                return new HashSet<string>();
            }
            
            if (_transactionGuids.TryGetValue(TRANSACTION_TYPE_EDIT, out var editGuids))
            {
                return new HashSet<string>(editGuids);
            }
            
            _logger?.LogWarning("Edit transaction GUIDs not found in cache");
            return new HashSet<string>();
        }

        public HashSet<string> GetStatusTransactionGuids()
        {
            if (_disposed) return new HashSet<string>();
            
            if (!EnsureCacheValidSync())
            {
                _logger?.LogWarning("Cache validation failed, returning empty set for status transactions");
                return new HashSet<string>();
            }
            
            if (_transactionGuids.TryGetValue(TRANSACTION_TYPE_STATUS, out var statusGuids))
            {
                return new HashSet<string>(statusGuids);
            }
            
            _logger?.LogWarning("Status transaction GUIDs not found in cache");
            return new HashSet<string>();
        }

        public void AddTransactionGuid(TransactionType transactionType, string guid)
        {
            if (string.IsNullOrEmpty(guid))
                return;

            var cacheKey = GetCacheKey(transactionType);
            if (cacheKey != null && _transactionGuids.TryGetValue(cacheKey, out var guidSet))
            {
                guidSet.Add(guid);
                _logger?.LogDebug($"Added transaction GUID {guid} to cache for type {transactionType}");
        
                // Trigger background invalidation for consistency
                ScheduleInvalidation();
            }
        }

        public void RemoveTransactionGuid(TransactionType transactionType, string guid)
        {
            if (string.IsNullOrEmpty(guid))
                return;

            var cacheKey = GetCacheKey(transactionType);
            if (cacheKey != null && _transactionGuids.TryGetValue(cacheKey, out var guidSet))
            {
                guidSet.TryRemove(guid);
                _logger?.LogDebug($"Removed transaction GUID {guid} from cache for type {transactionType}");
        
                // Trigger background invalidation for consistency
                ScheduleInvalidation();
            }
        }

        public void InvalidateCache()
        {
            if (!_disposed)
            {
                _isCacheValid = false;
                _logger?.LogDebug("Transaction GUID cache invalidated");
            }
        }

        public void InvalidateAndRefreshAsync()
        {
            if (!_disposed)
            {
                InvalidateCache();
                
                // Fire and forget refresh
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await RefreshCacheAsync();
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, new Dictionary<string, string> 
                        { 
                            { "Action", "Background cache refresh failed after invalidation" } 
                        });
                    }
                });
            }
        }

        private void ScheduleInvalidation()
        {
            if (_disposed) return;
            
            Interlocked.Increment(ref _pendingInvalidations);
            
            // Batch invalidations to avoid excessive refresh calls
            _invalidationTimer?.Change(TimeSpan.FromSeconds(5), Timeout.InfiniteTimeSpan);
        }

        private void ProcessPendingInvalidations(object state)
        {
            if (_disposed) return;
            
            var pendingCount = Interlocked.Exchange(ref _pendingInvalidations, 0);
            
            if (pendingCount > 0)
            {
                _logger?.LogDebug($"Processing {pendingCount} pending cache invalidations");
                InvalidateAndRefreshAsync();
            }
        }

        public async Task<bool> EnsureCacheValidAsync(TimeSpan? maxWaitTime = null)
        {
            if (_disposed) return false;
            
            if (IsCacheValid) return true;
            
            var waitTime = maxWaitTime ?? TimeSpan.FromSeconds(30);
            
            try
            {
                using (var cts = new CancellationTokenSource(waitTime))
                {
                    await RefreshCacheAsync();
                    return IsCacheValid;
                }
            }
            catch (OperationCanceledException)
            {
                _logger?.LogWarning("Cache validation timeout");
                return false;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, new Dictionary<string, string> 
                { 
                    { "Action", "Failed to ensure cache validity" } 
                });
                return false;
            }
        }

        private bool EnsureCacheValidSync()
        {
            if (_disposed) return false;
            
            if (IsCacheValid) return true;
            
            if (_getVoterGuidsFunc == null) return false;
            
            try
            {
                // Try async refresh with timeout
                var refreshTask = RefreshCacheAsync();
                if (refreshTask.Wait(TimeSpan.FromSeconds(30)))
                {
                    return IsCacheValid;
                }
                else
                {
                    _logger?.LogWarning("Cache refresh timeout during sync validation");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, new Dictionary<string, string> 
                { 
                    { "Action", "Failed during synchronous cache validation" } 
                });
                return false;
            }
        }

        public async Task RefreshCacheAsync()
        {
            if (_disposed || _getVoterGuidsFunc == null) return;

            if (!await _refreshSemaphore.WaitAsync(TimeSpan.FromSeconds(30)))
            {
                _logger?.LogWarning("Failed to acquire semaphore for cache refresh within timeout");
                return;
            }

            try
            {
                _logger?.LogDebug("Refreshing transaction GUID cache");
                var stopwatch = System.Diagnostics.Stopwatch.StartNew();

                // Create a cancellation token with timeout
                using (var cancellationTokenSource = new CancellationTokenSource(TimeSpan.FromMinutes(2)))
                {
                    var cancellationToken = cancellationTokenSource.Token;
                    
                    var tasks = new[]
                    {
                        Task.Run(() => SafeExecuteFunction(_getVoterGuidsFunc, "Voter"), cancellationToken),
                        Task.Run(() => SafeExecuteFunction(_getAddGuidsFunc, "Add"), cancellationToken),
                        Task.Run(() => SafeExecuteFunction(_getEditGuidsFunc, "Edit"), cancellationToken),
                        Task.Run(() => SafeExecuteFunction(_getStatusGuidsFunc, "Status"), cancellationToken)
                    };

                    await Task.WhenAll(tasks);

                    // Update cache atomically
                    var newCache = new ConcurrentDictionary<string, ConcurrentHashSet<string>>();
                    newCache[TRANSACTION_TYPE_VOTER] = new ConcurrentHashSet<string>(tasks[0].Result ?? new HashSet<string>());
                    newCache[TRANSACTION_TYPE_ADD] = new ConcurrentHashSet<string>(tasks[1].Result ?? new HashSet<string>());
                    newCache[TRANSACTION_TYPE_EDIT] = new ConcurrentHashSet<string>(tasks[2].Result ?? new HashSet<string>());
                    newCache[TRANSACTION_TYPE_STATUS] = new ConcurrentHashSet<string>(tasks[3].Result ?? new HashSet<string>());
                    
                    var refreshTime = DateTime.UtcNow;
                    _transactionGuids = newCache;
                    Thread.MemoryBarrier(); // Ensure cache is updated before time
                    _lastRefreshTime = refreshTime;
                    _isCacheValid = true;
                    _isInitialized = true;

                    stopwatch.Stop();
                    _logger?.LogDebug($"Transaction GUID cache refreshed in {stopwatch.ElapsedMilliseconds}ms. " +
                        $"Cached counts - Voter: {tasks[0].Result?.Count ?? 0}, Add: {tasks[1].Result?.Count ?? 0}, " +
                        $"Edit: {tasks[2].Result?.Count ?? 0}, Status: {tasks[3].Result?.Count ?? 0}");
                }
            }
            catch (OperationCanceledException)
            {
                _logger?.LogWarning("Cache refresh operation was cancelled due to timeout");
                _isCacheValid = false;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, new Dictionary<string, string> 
                { 
                    { "Action", $"Failed to refresh transaction GUID cache - {GetType().Name}.RefreshCacheAsync" } 
                });
                _isCacheValid = false;
            }
            finally
            {
                _refreshSemaphore.Release();
            }
        }

        private HashSet<string> SafeExecuteFunction(Func<HashSet<string>> func, string typeName)
        {
            try
            {
                return func?.Invoke() ?? new HashSet<string>();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, new Dictionary<string, string> 
                { 
                    { "Action", $"Failed to execute function for {typeName} transaction GUIDs" } 
                });
                return new HashSet<string>();
            }
        }

        public async Task RefreshFromDatabaseAsync(
            Func<HashSet<string>> getVoterGuids, 
            Func<HashSet<string>> getAddGuids,
            Func<HashSet<string>> getEditGuids, 
            Func<HashSet<string>> getStatusGuids)
        {
            if (_disposed)
            {
                throw new ObjectDisposedException(nameof(TransactionGuidCache));
            }

            if (!await _initializationSemaphore.WaitAsync(TimeSpan.FromSeconds(30)))
            {
                throw new TimeoutException("Failed to acquire initialization semaphore within timeout");
            }

            try
            {
                // Validate inputs
                if (getVoterGuids == null || getAddGuids == null || getEditGuids == null || getStatusGuids == null)
                {
                    throw new ArgumentNullException("All database function parameters must be non-null");
                }

                // Store the database functions
                _getVoterGuidsFunc = getVoterGuids;
                _getAddGuidsFunc = getAddGuids;
                _getEditGuidsFunc = getEditGuids;
                _getStatusGuidsFunc = getStatusGuids;

                // Perform initial refresh
                await RefreshCacheAsync();
                
                _logger?.LogInformation("Transaction GUID cache initialized successfully");
            }
            finally
            {
                _initializationSemaphore.Release();
            }
        }

        public void StartAutoRefresh()
        {
            if (_disposed)
            {
                _logger?.LogWarning("Cannot start auto-refresh on disposed cache");
                return;
            }

            if (_getVoterGuidsFunc != null)
            {
                _refreshTimer?.Change(_refreshInterval, _refreshInterval);
                _logger?.LogInformation($"Auto-refresh started with interval: {_refreshInterval}");
            }
            else
            {
                _logger?.LogWarning("Cannot start auto-refresh: database functions not initialized");
            }
        }

        public void StopAutoRefresh()
        {
            if (!_disposed)
            {
                _refreshTimer?.Change(Timeout.Infinite, Timeout.Infinite);
                _invalidationTimer?.Change(Timeout.Infinite, Timeout.Infinite);
                _logger?.LogInformation("Auto-refresh stopped");
            }
        }

        private void AutoRefreshCallback(object state)
        {
            if (_disposed) return;
            
            // Fire and forget - don't block the timer thread
            _ = Task.Run(async () =>
            {
                try
                {
                    await RefreshCacheAsync();
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, new Dictionary<string, string> 
                    { 
                        { "Action", $"Auto-refresh failed - {GetType().Name}.AutoRefreshCallback" } 
                    });
                }
            });
        }

        private string GetCacheKey(TransactionType transactionType)
        {
            switch (transactionType)
            {
                case TransactionType.BallotIssue:
                case TransactionType.BallotCancel:
                case TransactionType.Custom:
                    return TRANSACTION_TYPE_VOTER;
            
                case TransactionType.VoterAdd:
                    return TRANSACTION_TYPE_ADD;
            
                case TransactionType.VoterEdit:
                    return TRANSACTION_TYPE_EDIT;
            
                case TransactionType.VoterStatusChange:
                    return TRANSACTION_TYPE_STATUS;
            
                case TransactionType.Unknown:
                default:
                    return null;
            }
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _disposed = true;
                
                try
                {
                    StopAutoRefresh();
                    _refreshTimer?.Dispose();
                    _invalidationTimer?.Dispose();
                }
                catch (ObjectDisposedException)
                {
                    // Timers already disposed
                }

                try
                {
                    _refreshSemaphore?.Dispose();
                    _initializationSemaphore?.Dispose();
                }
                catch (ObjectDisposedException)
                {
                    // Semaphores already disposed
                }

                // Clear function references to prevent memory leaks
                _getVoterGuidsFunc = null;
                _getAddGuidsFunc = null;
                _getEditGuidsFunc = null;
                _getStatusGuidsFunc = null;

                _logger?.LogDebug("TransactionGuidCache disposed");
            }
        }
    }

    // Enhanced thread-safe HashSet operations
    internal class ConcurrentHashSet<T> : IEnumerable<T>, IDisposable
    {
        private readonly HashSet<T> _hashSet = new HashSet<T>();
        private readonly ReaderWriterLockSlim _lock = new ReaderWriterLockSlim();
        private volatile bool _disposed;

        public void Add(T item)
        {
            if (_disposed || item == null) return;

            _lock.EnterWriteLock();
            try
            {
                _hashSet.Add(item);
            }
            finally
            {
                _lock.ExitWriteLock();
            }
        }

        public bool TryRemove(T item)
        {
            if (_disposed || item == null) return false;

            _lock.EnterWriteLock();
            try
            {
                return _hashSet.Remove(item);
            }
            finally
            {
                _lock.ExitWriteLock();
            }
        }

        public bool Contains(T item)
        {
            if (_disposed || item == null) return false;

            _lock.EnterReadLock();
            try
            {
                return _hashSet.Contains(item);
            }
            finally
            {
                _lock.ExitReadLock();
            }
        }

        public int Count
        {
            get
            {
                if (_disposed) return 0;

                _lock.EnterReadLock();
                try
                {
                    return _hashSet.Count;
                }
                finally
                {
                    _lock.ExitReadLock();
                }
            }
        }

        public IEnumerator<T> GetEnumerator()
        {
            if (_disposed) return new List<T>().GetEnumerator();

            _lock.EnterReadLock();
            try
            {
                return new List<T>(_hashSet).GetEnumerator();
            }
            finally
            {
                _lock.ExitReadLock();
            }
        }

        System.Collections.IEnumerator System.Collections.IEnumerable.GetEnumerator()
        {
            return GetEnumerator();
        }

        public ConcurrentHashSet() { }

        public ConcurrentHashSet(IEnumerable<T> collection)
        {
            if (collection != null)
            {
                _lock.EnterWriteLock();
                try
                {
                    foreach (var item in collection)
                    {
                        if (item != null)
                        {
                            _hashSet.Add(item);
                        }
                    }
                }
                finally
                {
                    _lock.ExitWriteLock();
                }
            }
        }

        public void Clear()
        {
            if (_disposed) return;

            _lock.EnterWriteLock();
            try
            {
                _hashSet.Clear();
            }
            finally
            {
                _lock.ExitWriteLock();
            }
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _disposed = true;
                _lock?.Dispose();
            }
        }
    }
}