using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Threading.Tasks;
using ICSharpCode.SharpZipLib.Zip;

namespace ESS.Pollbook.Hardware.Storage
{
	public class ZipHandler : <PERSON><PERSON><PERSON>Hand<PERSON>, IDisposable
	{
		private bool _disposed;
		private readonly ZipInputStream _zipInputStream;

		public string Name { get; }
		public string FullPathName { get; }
		public List<string> Content { get; set; }
		public string EncryptionKey { get; set; }
		public bool HasEncryptionKey { get; }

		public ZipHandler(string fullFileName, string key = null)
		{
			if (fullFileName == null)
				throw new ArgumentNullException(nameof(fullFileName));

			FileInfo fi = new FileInfo(fullFileName);
			Name = fi.Name;
			FullPathName = fi.FullName;

			if (!fi.Exists)
				throw new FileNotFoundException("File Not Found", fullFileName);

			EncryptionKey = key ?? string.Empty;
			HasEncryptionKey = EncryptionKey != string.Empty;

			using (var fs = new FileStream(FullPathName, FileMode.Open, FileAccess.Read))
			using (var za = new ZipArchive(fs, ZipArchiveMode.Read))
			{
				Content = za.Entries.Select(x => x.Name).ToList();
			}

			_zipInputStream = new ZipInputStream(File.OpenRead(FullPathName)) { Password = EncryptionKey };
		}

		public async Task<byte[]> ExtractFileToMemoryAsync(string name)
		{
			if (!Content.Contains(name))
				throw new FileNotFoundException("File Not Found in Archive", name);

			ZipEntry entry;
			while ((entry = _zipInputStream.GetNextEntry()) != null)
			{
				if (entry.Name != name)
					continue;

				if (!entry.IsFile)
					continue;

				try
				{
					using (var stream = new MemoryStream())
					{
						var data = new byte[entry.Size];
						int read;
						while ((read = await _zipInputStream.ReadAsync(data, 0, data.Length)) > 0)
						{
							await stream.WriteAsync(data, 0, read);
						}

						return stream.ToArray();
					}
				}
				catch
				{
					return Array.Empty<byte>();
				}
			}

			return Array.Empty<byte>();
		}

		public async Task ExtractFileToFolderAsync(string name, string destination)
		{
			if (!Directory.Exists(destination))
				Directory.CreateDirectory(destination);

			var buffer = await ExtractFileToMemoryAsync(name);

			using (var fs = new FileStream(Path.Combine(destination, name), FileMode.Create))
			{
				await fs.WriteAsync(buffer, 0, buffer.Length);
			}
		}

		public async Task ExtractAllFilesToFolderAsync(string destination, bool skipExisting = false)
		{
			if (!Directory.Exists(destination))
				Directory.CreateDirectory(destination);

			ZipEntry entry;
			while ((entry = _zipInputStream.GetNextEntry()) != null)
			{
				if (!entry.IsFile)
					continue;

				var destinationFilename = Path.Combine(destination, entry.Name);

				if (skipExisting && File.Exists(destinationFilename))
					continue;

				using (var stream = new FileStream(destinationFilename, FileMode.Create, FileAccess.Write))
				{
					var data = new byte[entry.Size];
					int read;
					while ((read = await _zipInputStream.ReadAsync(data, 0, data.Length)) > 0)
					{
						await stream.WriteAsync(data, 0, read);
					}
				}
			}
		}

		public ZipEntry GetEntryFromZip(string name)
		{
			if (name == null)
				throw new ArgumentNullException(nameof(name));

			using (var fs = new FileStream(FullPathName, FileMode.Open, FileAccess.Read))
			using (var zf = new ICSharpCode.SharpZipLib.Zip.ZipFile(fs, false))
			{
				return zf.GetEntry(name);
			}
		}

		public void Dispose()
		{
			Dispose(true);
			GC.SuppressFinalize(this);
		}

		protected virtual void Dispose(bool disposing)
		{
			if (_disposed)
				return;

			if (disposing)
			{
				if (_zipInputStream != null)
					_zipInputStream.Password = null;

				_zipInputStream?.Close();
				_zipInputStream?.Dispose();
			}

			_disposed = true;
		}
	}
}
