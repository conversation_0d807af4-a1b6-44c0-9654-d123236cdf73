using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using ESS.Pollbook.Core.CustomExceptions;
using ESS.Pollbook.Core.Storage;

namespace ESS.Pollbook.Hardware.Storage
{
   public class StorageLocator : IStorageLocator
   {
      public static readonly string TempFolder = "tmp";

      public static readonly string ExpressPollLoadUSBFilePattern = "EXPRESSPOLL_LOAD_*.ZIP";

      public static string DefaultDbLocation = Path.Combine("C:\\", StorageDataName);

      public const string StorageDataName = "StorageData";

      public static readonly string POLL_NAME = "PollData.db3";

      public static readonly string TRANSACTION_NAME = "PollData_Transactions.db3";

      public string TRANSACTION_BACKUP_NAME = "PollData_Transactions_" + DateTime.Now.ToString("yyyyMMddHHmmss") + ".db3";

      public static readonly string BACKUP_TRANSACTION_LOG_ALIAS = "BackupLog";

      public static readonly string SIGNATURES_NAME = "PollData_Signatures.db3";

      public static readonly string AUDIT_LOG_NAME = "PollData_AuditLog.db3";

      public static readonly string INCREMENTAL_UPDATE_ZIP_FILE_PATTERN = "IncUpd_*.zip";

      public static readonly string INCREMENTAL_UPDATE_DB3_NAME_PATTERN = "Polldata_TransactionIncUpd*.db3";

      public static readonly string INCREMENTAL_UPDATE_SIGNATURE_NAME = "IncrementalUpdateSignatures.json";

      public static readonly string INCREMENTAL_UPDATE_VOTER_DELETE_NAME = "IncrementalUpdateVoterDeletes.json";

      public static readonly string INCREMENTAL_UPDATE_USB_USERNAME = "UsbIncUpdate";

      public static readonly string INCREMENTAL_UPDATE_AUTO_USERNAME = "AutoIncUpdate";

      public static readonly string ELECTION_SETTINGS_FILE_NAME = "ElectionSettings.ini";

      public static readonly string ELECTION_SETTINGS_BACKUP_FILE_NAME = "ElectionSettings-Backup.ini";

      public static readonly string SIGNATURES_FILE_PATH_ZIP = "PollData_Signatures.zip";

      public static readonly string REGIONAL_RESULTS_FILE_PATH_ZIP = "RegionResults.zip";

      public static readonly string SIGNATURES_FILE_WILDCARD_PATH_ZIP = "PollData_Signatures*.zip";

      public static readonly string TRANSACTION_INSERT_QUEUE_FOLDER = "TransactionInsertQueue";

      public static readonly string TRANSACTION_UPDATE_QUEUE_FOLDER = "TransactionUpdateQueue";

      public static readonly string ALERT_CREATE_QUEUE_FOLDER = "AlertCreateQueue";

      public static readonly string CONVERSATION_CREATE_QUEUE_FOLDER = "ConversationCreateQueue";

      public static readonly string CONVERSATION_RECORD_CREATE_QUEUE_FOLDER = "ConversationRecordCreateQueue";

      public static readonly string CONVERSATION_UPDATE_QUEUE_FOLDER = "ConversationUpdateQueue";

      public static readonly string CONVERSATION_UNREAD_MESSAGE_QUEUE_FOLDER = "ConversationUnreadMessageQueue";

      public static readonly string CONVERSATION_UPDATE_UNREAD_MESSAGE_QUEUE_FOLDER = "ConversationUpdateUnreadMessageQueue";

      public static readonly string BACKUP_FOLDER = "Backup";

      public static readonly string SERIAL_INSERT_QUEUE_FOLDER = "SerialInsertQueue";

      public static readonly string POLL_WORKER_MANAGEMENT_NAME = "Polldata_PollworkerManagement.db3";

      public static readonly string BALLOT_SERIAL_NUMBERS = "{0}_BALLOT_SERIAL_NUMBERS_{1}.csv";

      public static readonly string ELECTION_BALLOTS_FILE_PATH_ZIP = "Election_Ballots.zip";

      public static readonly string HELP_CENTER_FOLDER = "HelpCenter";

      public static readonly string REGIONAL_RESULTS_FOLDER = "RegionalResults";

      public static readonly string REGIONAL_RESULTS_NAME = "Polldata_RegionalResults.db3";

      public static readonly string AUTO_UPDATES_FOLDER = "AutoUpdates";

      public static readonly string FILE_WATCHER_FOLDER = "FileWatcher";

      public static readonly string TRANSACTIONS_FROM_HOST_FOLDER = "TransactionsFromHost";

      private string pollDataPath = null;

      private string transactionDataPath = null;

      private string signatureDataPath = null;

      private string signatureZipPath = null;

      private string auditLogDataPath = null;

      private string pollworkerDataPath = null;

      private string electionSettingsFilePath = null;

      //private string regionalResultsFilePath = null;

      private string regionalResultsDataPath = null;

      public static string GetJpPasswordFile => Path.Combine(DefaultDbLocation, "JP.txt");

      public StorageLocator()
      {
         if (!File.Exists(Path.Combine(DefaultDbLocation, POLL_NAME)))
            return;

         pollDataPath = Path.Combine(DefaultDbLocation, POLL_NAME);

         if (File.Exists(Path.Combine(DefaultDbLocation, TRANSACTION_NAME)))
         {
            transactionDataPath = Path.Combine(DefaultDbLocation, TRANSACTION_NAME);
         }

         if (File.Exists(Path.Combine(DefaultDbLocation, SIGNATURES_NAME)))
         {
            signatureDataPath = Path.Combine(DefaultDbLocation, SIGNATURES_NAME);
         }

         if (File.Exists(Path.Combine(DefaultDbLocation, AUDIT_LOG_NAME)))
         {
            auditLogDataPath = Path.Combine(DefaultDbLocation, AUDIT_LOG_NAME);
         }

         if (File.Exists(Path.Combine(DefaultDbLocation, POLL_WORKER_MANAGEMENT_NAME)))
         {
            pollworkerDataPath = Path.Combine(DefaultDbLocation, POLL_WORKER_MANAGEMENT_NAME);
         }

         if (File.Exists(Path.Combine(DefaultDbLocation, SIGNATURES_FILE_PATH_ZIP)))
         {
            signatureZipPath = Path.Combine(DefaultDbLocation, SIGNATURES_FILE_PATH_ZIP);
         }

         if (File.Exists(Path.Combine(DefaultDbLocation, ELECTION_SETTINGS_FILE_NAME)))
         {
            electionSettingsFilePath = Path.Combine(DefaultDbLocation, ELECTION_SETTINGS_FILE_NAME);
         }

         if (File.Exists(Path.Combine(DefaultDbLocation, REGIONAL_RESULTS_NAME)))
         {
            regionalResultsDataPath = Path.Combine(DefaultDbLocation, REGIONAL_RESULTS_NAME);
         }
      }

      public bool IsSDCardConnected()
      {
         return DriveSearcher.GetSdCardPaths().Count > 0;
      }

      public bool IsUsbMounted()
      {
         return DriveSearcher.GetUsbDrives().Count > 0;
      }

      public string GetUsbMountedPath()
      {
         var drives = DriveSearcher.GetUsbDrives();
         return drives.Count > 0 ? drives[0] : null;
      }

      public string GetIncrementalUpdateZipFileName(string fileNamePattern)
      {
         var usbDrives = DriveSearcher.GetUsbDrives();
         if (usbDrives.Count == 0)
            return string.Empty;

         foreach (var drv in usbDrives)
         {
            var files = Directory.GetFiles(drv, fileNamePattern);
            if (files.Length == 1)
            {
               return files[0];
            }
         }
         return string.Empty;
      }

      public string GetIncrementalUpdateFileName(string fileNamePattern = null, string path = null)
      {
         if (fileNamePattern == null)
            fileNamePattern = INCREMENTAL_UPDATE_DB3_NAME_PATTERN;

         if (path == null)
            path = DefaultDbLocation;

         var files = Directory.EnumerateFiles(path, fileNamePattern).ToList();

         switch (files.Count)
         {
            case 0:
               return string.Empty;
            case 1:
               return files.First();
            default:
               throw new DeviceHandlingException("Too many files matching pattern.");
         }
      }

      public string GetElectionSettingsFilePath()
      {
         string electionSettingsFilePath = string.Empty;

         if (File.Exists(Path.Combine(DefaultDbLocation, ELECTION_SETTINGS_FILE_NAME)))
         {
            electionSettingsFilePath = Path.Combine(DefaultDbLocation, ELECTION_SETTINGS_FILE_NAME);
         }
         return electionSettingsFilePath;
      }

      public string GetElectionSettingsBackupFilePath()
      {
          if (!File.Exists(Path.Combine(DefaultDbLocation, ELECTION_SETTINGS_BACKUP_FILE_NAME)))
          {
              File.Create(Path.Combine(DefaultDbLocation, ELECTION_SETTINGS_BACKUP_FILE_NAME)).Close();
          }
         
          return Path.Combine(DefaultDbLocation, ELECTION_SETTINGS_BACKUP_FILE_NAME);
        }

      public string GetPollDataPath()
      {
         return pollDataPath ?? string.Empty;
      }

      public string GetTransactionDataPath()
      {
         return transactionDataPath ?? string.Empty;
      }

      public string GetSignatureDataPath()
      {
         return signatureDataPath ?? string.Empty;
      }

      public string GetAuditLogDataPath()
      {
         return auditLogDataPath ?? string.Empty;
      }

      public string GetPollworkerDataPath()
      {
         return pollworkerDataPath ?? string.Empty;
      }

      public string GetRegionalResultsDataPath()
      {
         return regionalResultsDataPath ?? string.Empty;
      }

      public string GetRegionalResultsFilePath()
      {
         var path = GetRegionalResultsFolder();
         return Path.Combine(path, REGIONAL_RESULTS_FILE_PATH_ZIP);
      }

      public string GetRegionalResultsFolder()
      {
         var regionalResultsFolder = Path.Combine(DefaultDbLocation, REGIONAL_RESULTS_FOLDER);
         if (!Directory.Exists(regionalResultsFolder))
         {
            Directory.CreateDirectory(regionalResultsFolder);
         }
         return regionalResultsFolder;
      }

      public string GetTransactionSDCardPath()
      {
         return Path.Combine(SDCard.AttachedSdCardDrive, TRANSACTION_NAME);
      }

      public string GetBackUpTransactionSDCardPath(string sdCardPath)
      {
         return sdCardPath.Replace(TRANSACTION_NAME, TRANSACTION_BACKUP_NAME);
      }

      public static string GetTransactionInsertQueueFolder()
      {
         var transactionInsertQueuePath = Path.Combine(FILE_WATCHER_FOLDER, TRANSACTION_INSERT_QUEUE_FOLDER);
         if (!Directory.Exists(transactionInsertQueuePath))
         {
            Directory.CreateDirectory(transactionInsertQueuePath);
         }
         return transactionInsertQueuePath;
      }

      public static string GetTransactionUpdateQueueFolder()
      {
         var transactionUpdateQueuePath = Path.Combine(FILE_WATCHER_FOLDER, TRANSACTION_UPDATE_QUEUE_FOLDER);
         if (!Directory.Exists(transactionUpdateQueuePath))
         {
            Directory.CreateDirectory(transactionUpdateQueuePath);
         }
         return transactionUpdateQueuePath;
      }
      public static string GetSerialInsertQueueFolder()
      {
         var insertQueuePath = Path.Combine(FILE_WATCHER_FOLDER, SERIAL_INSERT_QUEUE_FOLDER);
         if (!Directory.Exists(insertQueuePath))
         {
            Directory.CreateDirectory(insertQueuePath);
         }
         return insertQueuePath;
      }

      public static string GetAlertCreateQueueFolder()
      {
         var alertCreateQueuePath = Path.Combine(DefaultDbLocation, ALERT_CREATE_QUEUE_FOLDER);
         if (!Directory.Exists(alertCreateQueuePath))
         {
            Directory.CreateDirectory(alertCreateQueuePath);
         }
         return alertCreateQueuePath;
      }

      public static string GetConversationCreateQueueFolder()
      {
         var conversationCreateQueuePath = Path.Combine(DefaultDbLocation, CONVERSATION_CREATE_QUEUE_FOLDER);
         if (!Directory.Exists(conversationCreateQueuePath))
         {
            Directory.CreateDirectory(conversationCreateQueuePath);
         }
         return conversationCreateQueuePath;
      }

      public static string GetConversationRecordCreateQueueFolder()
      {
         var conversationRecordCreateQueuePath = Path.Combine(DefaultDbLocation, CONVERSATION_RECORD_CREATE_QUEUE_FOLDER);
         if (!Directory.Exists(conversationRecordCreateQueuePath))
         {
            Directory.CreateDirectory(conversationRecordCreateQueuePath);
         }
         return conversationRecordCreateQueuePath;
      }

      public static string GetConversationUpdateQueueFolder()
      {
         var conversationUpdateQueuePath = Path.Combine(DefaultDbLocation, CONVERSATION_UPDATE_QUEUE_FOLDER);
         if (!Directory.Exists(conversationUpdateQueuePath))
         {
            Directory.CreateDirectory(conversationUpdateQueuePath);
         }
         return conversationUpdateQueuePath;
      }

      public static string GetConversationUnreadMessageQueueFolder()
      {
         var conversationUnreadMessageQueuePath = Path.Combine(DefaultDbLocation, CONVERSATION_UNREAD_MESSAGE_QUEUE_FOLDER);
         if (!Directory.Exists(conversationUnreadMessageQueuePath))
         {
            Directory.CreateDirectory(conversationUnreadMessageQueuePath);
         }
         return conversationUnreadMessageQueuePath;
      }

      public static string GetConversationUpdateUnreadMessageQueueFolder()
      {
         var conversationUpdateUnreadMessageQueuePath = Path.Combine(DefaultDbLocation, CONVERSATION_UPDATE_UNREAD_MESSAGE_QUEUE_FOLDER);
         if (!Directory.Exists(conversationUpdateUnreadMessageQueuePath))
         {
            Directory.CreateDirectory(conversationUpdateUnreadMessageQueuePath);
         }
         return conversationUpdateUnreadMessageQueuePath;
      }

      public static string GetBackupFolder()
      {
         var backupFolder = Path.Combine(DefaultDbLocation, BACKUP_FOLDER);
         if (!Directory.Exists(backupFolder))
         {
            Directory.CreateDirectory(backupFolder);
         }
         return backupFolder;
      }

      public static string GetBackupExpressPollZipFileName()
      {
          var files = Directory.GetFiles(GetBackupFolder(), ExpressPollLoadUSBFilePattern);

          return (files.Length > 1) ? string.Empty : files[0];
      }

      public static string GetHelpCenterFolder()
      {
         var helpCenterFolder = Path.Combine(DefaultDbLocation, HELP_CENTER_FOLDER);
         if (!Directory.Exists(helpCenterFolder))
         {
            Directory.CreateDirectory(helpCenterFolder);
         }
         return helpCenterFolder;
      }

      public static string GetSignatureZip()
      {
         var signatureFile = Path.Combine(DefaultDbLocation, SIGNATURES_FILE_PATH_ZIP);
         if (File.Exists(signatureFile))
            return signatureFile;

         return string.Empty;
      }

      public static string GetTransactionsFromHostPath()
      {
          var path = GetFileWatcherFolder();
          var fullPath = Path.Combine(path, TRANSACTIONS_FROM_HOST_FOLDER);
          if (!Directory.Exists(fullPath))
          {
              Directory.CreateDirectory(fullPath);
          }
          return fullPath;
      }

      public static string GetFileWatcherFolder()
      {
          var fileWatcherFolder = Path.Combine(DefaultDbLocation, FILE_WATCHER_FOLDER);
          if (!Directory.Exists(fileWatcherFolder))
          {
              Directory.CreateDirectory(fileWatcherFolder);
          }
          return fileWatcherFolder;
      }

      public class UsbElectionFile
      {
         public string FileName { get; set; }
         public bool IsRequiredElectionFile { get; set; }
         public bool Exists => File.Exists(FileName);
      }

      public static List<UsbElectionFile> GetUsbExpressPollLoadFiles()
      {
         var filesList = new List<UsbElectionFile>();

         var usbDrives = DriveSearcher.GetUsbDrives();
         if (usbDrives.Count == 0) return filesList;

         filesList.AddRange(from drv in usbDrives
                            from file in (Directory.GetFiles(drv, ExpressPollLoadUSBFilePattern))
                            select new UsbElectionFile() { FileName = Path.Combine(drv, file) });

         return filesList;
      }
   }
}
