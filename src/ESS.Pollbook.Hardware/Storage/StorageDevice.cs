using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace ESS.Pollbook.Hardware.Storage
{
   public class StorageDevice : IStorageDevice
   {
      private static string DefaultDatabase => StorageLocator.POLL_NAME;

      public StorageDevice()
      {
	      SetInitialSdCard();
      }

      public string GetPrimaryStorage()
      {
         var sdCardPaths = GetSdCardPaths();
         var storage1 = (sdCardPaths.Count > 0) ? sdCardPaths[0] : "";

         if (!string.IsNullOrEmpty(storage1) && File.Exists(Path.Combine(storage1, DefaultDatabase)))
            return Path.Combine(storage1, DefaultDatabase);

         return string.Empty;
      }

      public string GetSecondaryStorage()
      {
         var usbDrives = DriveSearcher.GetUsbDrives().Where(x => !string.IsNullOrEmpty(x));

         foreach (var drive in usbDrives)
         {
            var path = Path.Combine(drive, DefaultDatabase);
            if (path.Length > 0 && File.Exists(path)) return path;
         }

         return string.Empty;
      }

      private string AttachedSdCardDrive { get; set; }

      public bool IsSdCardMounted => !string.IsNullOrWhiteSpace(AttachedSdCardDrive);

      public bool IsUsbMounted => DriveSearcher.GetUsbDrives().Count > 0;

      public string GetFirstUsbPath()
      {
         var drives = GetUsbDrives();
         return drives.Count > 0 ? drives[0] : string.Empty;
      }

      public bool DoesPolldataExist(string drive)
      {
         var filepath = drive + DefaultDatabase;
         return File.Exists(filepath);
      }


      #region sdCards / usb drives

      public List<string> GetSdCardPaths()
      {
         return DriveSearcher.GetSdCardPaths();
      }

      public List<string> GetUsbDrives()
      {
         return DriveSearcher.GetUsbDrives();
      }

      #endregion

      private void SetInitialSdCard()
      {
         var sdDrives = GetSdCardPaths();

         AttachedSdCardDrive = sdDrives.Count > 0 ? sdDrives[0] : "";
      }
   }
}
