using ESS.Pollbook.Core.Logging;
using System;
using System.Collections.Generic;
using System.Management;

namespace ESS.Pollbook.Hardware.Device
{
    public class DeviceInfo : IDeviceInfo
    {
        private readonly IEssLogger _essLogger;

        public DeviceInfo(IEssLogger essLogger)
        {
            _essLogger = essLogger;
        }

        public string GetDeviceModel()
        {
            string modelName = string.Empty;

            try
            {
                using (var searcher = new ManagementObjectSearcher("SELECT Model FROM Win32_ComputerSystem"))
                {
                    foreach (var result in searcher.Get())
                    {
                        modelName = result["Model"].ToString();
                    }
                }
            }
            catch (Exception ex)
            {
                modelName = "Unknown";

                var props = new Dictionary<string, string>
                {
                    { "Action", "Getting device model number" }
                };

                _essLogger.LogError(ex, props);
            }

            return modelName;
        }

        public string GetDeviceSerialNumber()
        {
            string serialNumber = string.Empty;

            try
            {
                using (var searcher = new ManagementObjectSearcher("SELECT IdentifyingNumber FROM Win32_ComputerSystemProduct"))
                {
                    foreach (var result in searcher.Get())
                    {
                        serialNumber = result["IdentifyingNumber"].ToString();
                    }
                }
            }
            catch (Exception ex)
            {
                serialNumber = "Unknown";

                var props = new Dictionary<string, string>
                {
                    { "action", "Getting device serial number" }
                };

                _essLogger.LogError(ex, props);
            }

            return serialNumber;
        }

    }
}
