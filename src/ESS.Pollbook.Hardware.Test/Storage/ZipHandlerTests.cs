using System;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System.IO;
using System.Reflection;
using System.Threading.Tasks;
using ESS.Pollbook.Hardware.Storage;

namespace ESS.Pollbook.Hardware.Test.Storage
{
   [TestClass]
   public class ZipHandlerTests
   {
      private readonly ZipHandler _encryptedHandler;
      private readonly ZipHandler _encryptedBadPasswordHandler;
      private readonly ZipHandler _unencryptedHandler;
      private readonly string _destinationFolder;

      [ClassInitialize]
      public static void ClassInitialize(TestContext testContext)
      {
         // context comment
      }
      public ZipHandlerTests()
      {
         var unencryptedSourcePath = GetHelperFilePath(@"Support\SourceData\Unencrypted.zip");
         var encryptedSourcePath = GetHelperFilePath(@"Support\SourceData\Encrypted.zip");

         var currentAssemblyPath = Path.GetDirectoryName(Assembly.GetExecutingAssembly().CodeBase)?.Replace(@"file:\", string.Empty);
         if (currentAssemblyPath != null)
            _destinationFolder = Path.Combine(currentAssemblyPath, @"Support\Output");

         _encryptedHandler = new ZipHandler(encryptedSourcePath, "PASSWORD");
         _encryptedBadPasswordHandler = new ZipHandler(encryptedSourcePath, "BADPASSWORD");
         _unencryptedHandler = new ZipHandler(unencryptedSourcePath);
      }

      [TestInitialize]
      public void Initialize()
      {
         ClearFolder();
      }

      [TestCleanup]
      public void Cleanup()
      {
         CleanUpFolder();
      }

      [TestMethod]
      public async Task ExtractFileToFolder_FileFoundAndExtracted_Success()
      {
         var fileName = "Test1.db3";

         await _unencryptedHandler.ExtractFileToFolderAsync(fileName, _destinationFolder);

         var expected = _unencryptedHandler.GetEntryFromZip(fileName);
         var actual = new FileInfo(Path.Combine(_destinationFolder, fileName));

         Assert.IsTrue(actual.Exists);
         Assert.AreEqual(expected.Size, actual.Length);
      }

      [TestMethod]
      public async Task ExtractFileToFolder_FileFoundAndExtracted_Failed()
      {
         var fileName = "MissingTest.db3";

         await Assert.ThrowsExceptionAsync<FileNotFoundException>(() => _unencryptedHandler.ExtractFileToFolderAsync(fileName, _destinationFolder));
      }

      [TestMethod]
      public async Task ExtractFileToFolder_FileFoundAndExtractedWithPassword_Success()
      {
         var fileName = "Test2.db3";

         await _encryptedHandler.ExtractFileToFolderAsync(fileName, _destinationFolder);

         var expected = _encryptedHandler.GetEntryFromZip(fileName);
         var actual = new FileInfo(Path.Combine(_destinationFolder, fileName));

         Assert.IsTrue(actual.Exists);
         Assert.AreEqual(expected.Size, actual.Length);
      }

      [TestMethod]
      public async Task ExtractFileToFolder_FileFoundAndExtractedWithPassword_Failed()
      {
         var fileName = "Test1.db3";

         try
         {
	         await _encryptedBadPasswordHandler.ExtractFileToFolderAsync(fileName, _destinationFolder);
	         Assert.Fail("Expected exception");
         }
         catch
         {
	         Assert.IsTrue(true);
         }
      }

      [TestMethod]
      public async Task ExtractFileToFolder_ExtractAllFiles_Success()
      {
         foreach (var fileName in _encryptedHandler.Content)
         {
            await _encryptedHandler.ExtractFileToFolderAsync(fileName, _destinationFolder);
         }

         var expected = _encryptedHandler.Content.Count;
         var actual = Directory.GetFiles(Path.Combine(_destinationFolder)).Length;

         Assert.AreEqual(expected, actual);
      }

      [TestMethod]
      public void GetEntryZip_FileNotFound()
      {
         var actual = _unencryptedHandler.GetEntryFromZip("MissingTest.db3");

         Assert.IsNull(actual);
      }
      
      private string GetHelperFilePath(string relativePathToFile)
      {
         var currentAssemblyPath = Path.GetDirectoryName(Assembly.GetExecutingAssembly().CodeBase)?.Replace(@"file:\", string.Empty);
         if (currentAssemblyPath == null) return string.Empty;
         var relativePath = Path.Combine(currentAssemblyPath, relativePathToFile);
         return Path.GetFullPath(relativePath);
      }

      private void ClearFolder()
      {
         foreach (var fileName in _unencryptedHandler.Content)
            if (File.Exists(Path.Combine(_destinationFolder, fileName)))
               File.Delete(Path.Combine(_destinationFolder, fileName));
      }

      private void CleanUpFolder()
      {
         foreach (var filename in Directory.GetFiles(Path.Combine(_destinationFolder)))
            File.Delete(Path.Combine(_destinationFolder, filename));
      }
   }
}
