<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net48</TargetFramework>
		<Authors>Election Systems and Software</Authors>
		<Company>Election Systems and Software</Company>
		<Copyright>Election Systems and Software</Copyright>
		<IsPackable>false</IsPackable>
		<IsTestProject>true</IsTestProject>
		<IsPublishable>False</IsPublishable>
	</PropertyGroup>

  <ItemGroup>
    <PackageReference Include="MSTest.TestAdapter" Version="3.8.3" />
    <PackageReference Include="MSTest.TestFramework" Version="3.8.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ESS.Pollbook.Hardware\ESS.Pollbook.Hardware.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="Support\SourceData\Encrypted.zip">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Support\SourceData\IncrementalUpdateFiles\Multiple\Polldata_TransactionIncUpd20230406_130000.db3">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Support\SourceData\IncrementalUpdateFiles\Multiple\Polldata_TransactionIncUpd20230407_130000.db3">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Support\SourceData\IncrementalUpdateFiles\Single\Polldata_TransactionIncUpd20230406_130000.db3">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Support\SourceData\Unencrypted.zip">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
