using System.Collections.Generic;
using System.Threading.Tasks;

namespace ESS.Pollbook.DataAccess.Interfaces
{
    public interface IEssSqliteReaderAdapter
    {
        IEssSqliteReaderConnection Connection { get; set; }
        
        Task<IEnumerable<T>> QueryAsync<T>(string sql, object param = null) where T : class, new();
        
        Task ExecuteNonQueryAsync(string sql, object param = null);
        
        Task<T> QueryFirstOrDefaultAsync<T>(string sql, object param = null) where T : class, new();
        
        Task<T> ExecuteScalarAsync<T>(string sql, object param = null);

        Task<T> AttachAndExecuteScalarAsync<T>(string path, string dbName, string sql, object param = null);

        Task<IEnumerable<T>> QueryAsync<T>(string sql, IDictionary<string, object> paramDictionary) where T : class, new();

        Task<IEnumerable<T>> AttachAndExecuteQueryAsync<T>(string path, string dbName, string sql, object param = null) where T : class, new();

        Task CloseAsync();
    }
}