using System;
using System.Collections.Generic;

namespace ESS.Pollbook.DataAccess.Interfaces
{
    public interface IEssSqliteWriterAdapter : IDisposable
    {
        /// <summary>
        /// The connection string to the SQLite database
        /// </summary>
        IEssSqliteWriterConnection Connection { get; set; }

        IEnumerable<T> ExecutePragma<T>(string sql) where T : class, new();

        /// <summary>
        /// Runs a raw query on the database, typically used to run an update or delete statement
        /// </summary>
        /// <param name="sql">The sql to run</param>
        /// <param name="args">The arguments of the sql to run</param>
        /// <returns>Task</returns>
        void ExecuteNonQuery(string sql, object param = null);

        /// <summary>
        /// Use this to run a query that will return a count or a something that is not an object like a int or a bool
        /// </summary>
        /// <typeparam name="T">The struct T to return, typically an int or a bool</typeparam>
        /// <param name="sql">The sql to run to get the query</param>
        /// <param name="param">A dynamic containing parameter value, IE new { nameLast = "SomeLast", nameFirst = "SomeFirst" } - For SQLite users! - please note this properties MUST be in order of the parameters in the SQL statement</param>
        /// <returns>A Type of T</returns>
        T ExecuteScalar<T>(string sql, object param = null);

        /// <summary>
        /// Bulk inserts the data into SQLite
        /// </summary>
        /// <param name="sql">Sql as string, it should be formatted as Insert Into [TableName] (Col1, Col2, Col3) values (?, ?, ?)</param>
        /// <param name="itemsToInsert">The list of items to insert</param>
        void BulkInsert(string primarySql, List<List<object>> primaryItems,
            string secondarySql = null, List<List<object>> secondaryItems = null);

        int InsertAll<T>(IEnumerable<T> records, string extra, bool runInTransaction = true);

        /// <summary>
        /// Starts a nestable transaction
        /// </summary>
        string SaveTransactionPoint();

        /// <summary>
        /// Commits a transaction
        /// </summary>
        /// <param name="name"></param>
        void Release(string name = null);

        /// <summary>
        /// Rollback all transactions
        /// </summary>
        void Rollback();
    }
}
