using System.Collections.Generic;
using System.Security;

namespace ESS.Pollbook.DataAccess.Interfaces
{
    public interface IEssSqliteWriterConnection
    {
        string ConnectionString { get; }

        SecureString Pqc { get; }
        
        void Close();

        IEnumerable<T> ExecutePragma<T>(string sql) where T : class, new();
        
        void ExecuteNonQuery(string sql, params object[] args);
        
        T ExecuteScalar<T>(string sql, params object[] args);
        
        void BulkInsert(string primarySql, List<List<object>> primaryItems,
            string secondarySql = null, List<List<object>> secondaryItems = null);
        
        int InsertAll<T>(IEnumerable<T> records, string extra, bool runInTransaction = true);
        
        string SaveTransactionPoint();
        
        void Release(string name = null);
        
        void Rollback();

        void Dispose();
    }
}
