using System.Collections.Generic;
using ESS.Pollbook.DataAccess.Connection;
using ESS.Pollbook.DataAccess.Exceptions;
using ESS.Pollbook.DataAccess.Interfaces;
using SQLite;

namespace ESS.Pollbook.DataAccess.Sqlite
{
    public sealed class EssSqliteWriterAdapter : IEssSqliteWriterAdapter
    {
        public IEssSqliteWriterConnection Connection { get; set; }

        /// <summary>
        /// You Need this here for AutoFac else it will blow up at runtime.
        /// </summary>
        public EssSqliteWriterAdapter() { }

        public EssSqliteWriterAdapter(string connectionString, string password)
        {
            Connection = new EssSqliteWriterConnection(connectionString, password);
        }

        public EssSqliteWriterAdapter(IEssSqliteWriterConnection essSqliteWriterConnection)
        {
            Connection = essSqliteWriterConnection;
        }

        public IEnumerable<T> ExecutePragma<T>(string sql) where T : class, new()
        {
            return Connection.ExecutePragma<T>(sql);
        }

        public void ExecuteNonQuery(string sql, object param = null)
        {
            try
            {
                Connection.ExecuteNonQuery(sql, param.ConvertParamToObjectArray());
            }
            catch (SQLiteException ex)
            {
                throw new EssSqlException(ex);
            }
        }

        public T ExecuteScalar<T>(string sql, object param = null)
        {
            try
            {
                return Connection.ExecuteScalar<T>(sql, param.ConvertParamToObjectArray());
            }
            catch (SQLiteException ex)
            {
                throw new EssSqlException(ex);
            }
        }

        public void BulkInsert(string primarySql, List<List<object>> primaryItems, 
            string secondarySql = null, List<List<object>> secondaryItems = null)
        {
            Connection.BulkInsert(primarySql, primaryItems, secondarySql, secondaryItems);
        }
        
        public int InsertAll<T>(IEnumerable<T> records, string extra, bool runInTransaction = true)
        {
            return Connection.InsertAll(records, extra);
        }

        public string SaveTransactionPoint()
        {
            return Connection.SaveTransactionPoint();
        }

        public void Release(string name = null)
        {
            Connection.Release(name);
        }

        public void Rollback()
        {
            Connection.Rollback();
        }

        public void Dispose()
        {
            Connection?.Dispose();
        }
    }
}
