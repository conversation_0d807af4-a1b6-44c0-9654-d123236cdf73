using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Security;

namespace ESS.Pollbook.DataAccess
{
	public static class DataUtility
	{
		// 4.7.0 Fips140-2 Enterprise
		public const string SqlCipherLicenseKey =
			"OmNpZDowMDFHMDAwMDAxWEtNb0tJQVg6cGxhdGZvcm06Njg2Mjg6ZXhwaXJlOm5ldmVyOnZlcnNpb246MTpsaWJ2ZXI6NC43LjA6aG1hYzplYzRhMzc5MzAxM2JkYzVmM2IwYmJjZmVjNzU5ZTgzMDFkZWYyMWNh";

		public static object[] ConvertParamToObjectArray(this object param)
		{
            return param == null ? Array.Empty<object>() : new List<object>(param.GetType().GetRuntimeProperties().Select(property => property.GetValue(param))).ToArray();
        }

        /// <summary>
        /// Fortify will flag this method but there is no way around the storage of information
        /// inside the IntPtr.  This will need to be suppressed in Fortify.
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        [SecurityCritical]
        public static string SecureStringToString(SecureString value)
        {
	        IntPtr ptr = IntPtr.Zero;
	        try
	        {
		        ptr = Marshal.SecureStringToGlobalAllocUnicode(value);
		        return Marshal.PtrToStringUni(ptr);
	        }
	        finally
	        {
		        Marshal.ZeroFreeGlobalAllocUnicode(ptr);
	        }
        }
    }
}